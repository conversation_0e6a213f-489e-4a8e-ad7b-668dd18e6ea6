create or alter view [VWOFFEREDFORECAST]
AS
SELECT DISTINCT
ofd.<PERSON><PERSON><PERSON><PERSON>,
ofd.BUSINESSUNITID,
bu.NAME AS "BUSINESSUNITNAME",
ofd.SCHEDULEID,
scd.DESCRIPTION AS "SCHED<PERSON><PERSON>DE<PERSON>",
ofd.PLA<PERSON><PERSON><PERSON>GRO<PERSON> AS "PLANNING<PERSON>OUPID",
pgd.NAME AS "PLANNING<PERSON>OUPNAME",
ofd.SHORTTERMFORECASTID,
ofd.STARTDATE,
ofd.STARTDATELTC,
ofd.WEEKDATE,
ofd.WEEK,
ofd.AVGHANDLEPERINTERVAL,
ofd.OFFEREDPERINTERVAL,
ofd.CANUSEFORSCHEDULING,
ofd.UPDATED
FROM
[OFFEREDFORECASTDATA] ofd
    LEFT JOIN VWBUDETAILS bu ON bu.id = ofd.BUSINESSUNITID
    LEFT JOIN SCHEDULEDETAILS scd ON scd.SCHEDULEID = ofd.SCHEDULEID
    LEFT JOIN PLANNINGGROUPDETAILS pgd ON pgd.id = ofd.PLANNINGGROUP;