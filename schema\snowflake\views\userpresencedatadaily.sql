CREATE OR REPLACE VIEW vwuserpresencedatadaily AS
SELECT
    userpresencedatadaily.id,
    userpresencedatadaily.userid,
    userdetail.name AS agentname,
    userdetail.managerid,
    userdetail.managername,
    userdetail.divisionid,
    dd.name AS division_name,
    userpresencedatadaily.startdate,
    userpresencedatadaily.timetype,
    userpresencedatadaily.systempresenceid,
    CASE
        WHEN userpresencedatadaily.systempresenceid RLIKE '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$' THEN presencedetails.systempresence
        ELSE userpresencedatadaily.systempresenceid
    END AS systempresencename,
    userpresencedatadaily.presenceid,
    CASE
        WHEN userpresencedatadaily.timetype = 'Presence' THEN userpresencedatadaily.presencetime
        ELSE 0
    END AS presencetime,
    userpresencedatadaily.presencetime / 86400.00 AS presencetimeday,
    userpresencedatadaily.routingid,
    CASE
        WHEN userpresencedatadaily.timetype = 'Routing' THEN userpresencedatadaily.presencetime
        ELSE 0
    END AS routingtime,
    userpresencedatadaily.routingtime / 86400.00 AS routingtimeday,
    userpresencedatadaily.updated
FROM
    userpresencedatadaily
LEFT JOIN vwuserdetail userdetail ON userdetail.id::STRING = userpresencedatadaily.userid::STRING
LEFT JOIN divisiondetails dd ON dd.id::STRING = userdetail.divisionid::STRING
LEFT JOIN presencedetails ON presencedetails.id = userpresencedatadaily.systempresenceid;