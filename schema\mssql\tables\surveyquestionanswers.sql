IF dbo.csg_table_exists('surveyQuestionAnswers') = 0
CREATE TABLE [surveyQuestionAnswers] (
    [surveyId]              [nvarchar](50) NOT NULL,
    [conversationId]        [nvarchar](50) NOT NULL,
    [surveyFormId]          [nvarchar](50),
    [surveyName]            [nvarchar](200),
    [agentId]               [nvarchar](50),
    [agentTeamId]           [nvarchar](50),
    [queueId]               [nvarchar](50),
    [questionGroupId]       [nvarchar](50),
    [questionGroupName]     [nvarchar](200),
    [questionId]            [nvarchar](50) NOT NULL,
    [questionText]          [nvarchar](400),
    [questionType]          [nvarchar](50),
    [questionAnswerId]      [nvarchar](50),
    [questionAnswerText]    [nvarchar](200),
    [questionAnswerValue]   [decimal](20, 2),
    [questionScore]         [decimal](20, 2),
    [questionMarkedNa]      [bit],
    [updated]               [datetime],
    CONSTRAINT [PK_surveyquestionanswers] PRIMARY KEY ([surveyId], [questionId])
);
