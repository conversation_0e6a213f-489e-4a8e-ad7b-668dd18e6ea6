CREATE TABLE IF NOT EXISTS teammemberdata (
    keyid varchar(100),
    teamid varchar(50),
    userid varchar(50),
    updated timestamp without time zone,
    PRIMARY KEY (keyid)
);

COMMENT ON COLUMN teammemberdata.keyid IS 'Primary Key'; 
COMMENT ON COLUMN teammemberdata.teamid IS 'Work Team GUID'; 
COMMENT ON COLUMN teammemberdata.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN teammemberdata.userid IS 'User GUID'; 
COMMENT ON TABLE teammemberdata IS 'Work Team Membership Data'; 