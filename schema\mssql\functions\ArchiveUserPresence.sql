CREATE OR ALTER PROCEDURE [dbo].[ArchiveUserPresence]  
    @AggOffSet INT,   
    @AggType NVARCHAR(1)   
AS   
BEGIN
    SET NOCOUNT ON;
    SET ANSI_WARNINGS OFF;

    DECLARE @CurrentTimeUTC DATETIME;
    DECLAR<PERSON> @CurrentTimeLTC DATETIME;
    DECLAR<PERSON> @SystemTime DATETIME;
    DECLARE @Offset INT;
    DECLARE @CurrentDOW INT;
    DECLARE @StartDate DATETIME;
    DECLARE @EndDate DATETIME;
    DECLARE @TableName NVARCHAR(50);
    DECLARE @DelsqlCommand NVARCHAR(1000);
    DECLARE @InssqlCommand NVARCHAR(MAX);

    SET @SystemTime = GETDATE();
    SET @CurrentTimeUTC = GETUTCDATE();
    SET @CurrentTimeLTC = CONVERT(DATETIME, SWITCHOFFSET(GETUTCDATE(), DATEPART(TZOFFSET, GETUTCDATE() AT TIME ZONE 'AUS Eastern Standard Time'))); 
    SET @Offset = DATEDIFF(MINUTE, @CurrentTimeUTC, @CurrentTimeLTC);
    SET @CurrentDOW = 1 + ((5 + DATEPART(dw, @CurrentTimeLTC) + @@DATEFIRST) % 7);

    SET @StartDate = CASE @AggType
        WHEN 'M' THEN DATEADD(MONTH, @AggOffSet * -1, @CurrentTimeLTC)
        WHEN 'W' THEN DATEADD(WEEK, @AggOffSet * -1, DATEADD(DAY, (@CurrentDOW -1) * -1, @CurrentTimeLTC))
        WHEN 'D' THEN DATEADD(DAY, @AggOffSet * -1, @CurrentTimeLTC)
    END;

    SET @StartDate = CASE @AggType
        WHEN 'M' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-01 00:00:00'
        WHEN 'W' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-' + RIGHT('0' + CAST(DAY(@StartDate) AS NVARCHAR(2)), 2) + ' 00:00:00'
        WHEN 'D' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-' + RIGHT('0' + CAST(DAY(@StartDate) AS NVARCHAR(2)), 2) + ' 00:00:00'
    END;

    SET @EndDate = CASE @AggType
        WHEN 'M' THEN DATEADD(MINUTE, -1, DATEADD(MONTH, 1, @StartDate))
        WHEN 'W' THEN DATEADD(MINUTE, -1, DATEADD(WEEK, 1, @StartDate))
        WHEN 'D' THEN DATEADD(MINUTE, -1, DATEADD(DAY, 1, @StartDate))
    END;

    SET @TableName = CASE @AggType
        WHEN 'M' THEN 'userPresenceDataMonthly'
        WHEN 'W' THEN 'userPresenceDataWeekly'
        WHEN 'D' THEN 'userPresenceDataDaily'
    END;

    SET @StartDate = DATEADD(MINUTE, @Offset * -1, @StartDate);
    SET @EndDate = DATEADD(MINUTE, @Offset * -1, @EndDate);

    -- Construct DELETE command
    SET @DelsqlCommand = N'DELETE FROM ' + @TableName + N' WHERE startdate = ''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + N''' ';

    PRINT 'Delete Old Rows'; 
    PRINT @DelsqlCommand;

    EXEC sp_executesql @DelsqlCommand;

    -- Construct INSERT command
    SET @InssqlCommand = N'
        INSERT INTO ' + @TableName + N' (
            keyid,
            id,
            userid,
            startDate,
            systempresenceid,
            presenceid,
            presencetime,
            routingid,
            routingtime,
            updated
        )
        SELECT 
            id + ''|'' + userid + ''|'' + ''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + ''' + ''|'' + routingid AS keyid,
            id,
            userid,
            ''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + ''' AS startDate,
            systempresenceid,
            presenceid,
            SUM(presencetime) AS presencetime,
            routingid,
            SUM(routingtime) AS routingtime,
            GETUTCDATE() AS updated
        FROM userPresenceData
        WHERE startdate BETWEEN ''' + CAST(@StartDate AS NVARCHAR(25)) + ''' AND ''' + CAST(@EndDate AS NVARCHAR(25)) + '''
        GROUP BY 
            id + ''|'' + userid + ''|'' + ''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + '''  + ''|'' + routingid,
            id,
            userid,
            systempresenceid,
            presenceid,
            routingid
    ';

    PRINT 'Insert New Rows';
    PRINT @InssqlCommand;

    EXEC sp_executesql @InssqlCommand;
END;
GO