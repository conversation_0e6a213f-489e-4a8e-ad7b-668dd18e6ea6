namespace GenesysAdapter.UnitTests;

public class Simple3Des_Tests
{
    private readonly StandardUtils.Simple3Des _simple3Des;
    private const string _customerKeyId = "SimpleKey";

    public Simple3Des_Tests()
    {
        _simple3Des = new StandardUtils.Simple3Des(_customerKeyId);
    }

    [Theory]
    [InlineData("plain;0:1=2", "+sZgn55ptV5vxs+6LmqyCXuZVfJ0ul9N")]
    [InlineData("✅", "gdGjbzMEX14=")]
    public void Simple3Des_EncryptData(string plainText, string cipherText)
    {
        Assert.Equal(cipherText, _simple3Des.EncryptData(plainText));
    }

    [Theory]
    [InlineData("plain;0:1=2", "+sZgn55ptV5vxs+6LmqyCXuZVfJ0ul9N")]
    [InlineData("✅", "gdGjbzMEX14=")]
    public void Simple3Des_DecryptData(string plainText, string cipherText)
    {
        Assert.Equal(plainText, _simple3Des.DecryptData(cipherText));
    }

    [Theory]
    [InlineData("Aa🇦0!=&^+✅", "Aa0")]
    public void Simple3Des_ToAlphaNumbericOnly(string input, string expect)
    {
        Assert.Equal(expect, _simple3Des.ToAlphaNumericOnly(input));
    }
}

// spell-checker: ignore lmqy gjbz