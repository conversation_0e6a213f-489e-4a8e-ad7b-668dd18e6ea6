CREATE TABLE IF NOT EXISTS userpresencedataweekly (
    keyid varchar(255) NOT NULL,
    id varchar(50),
    userid varchar(50),
    startdate timestamp without time zone,
    timetype varchar(50),
    systempresenceid varchar(50),
    presenceid varchar(50),
    presencetime numeric(20, 2),
    routingid varchar(50),
    routingtime numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedataweekly_pkey PRIMARY KEY (keyid)
);