IF dbo.csg_table_exists('hoursblockdata') = 0
CREATE TABLE [hoursblockdata](
    [keyid] [nvarchar](200) NOT NULL,
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [enddate] [datetime],
    [enddateltc] [datetime],
    [totalhrs] [decimal](20, 2),
    [breakhrs] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_hoursblockdta] PRIMARY KEY ([keyid]) WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        IGNORE_DUP_KEY = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF
    )
);

IF dbo.csg_index_exists('hoursblockdata_userid', 'hoursblockdata') = 0
CREATE INDEX [hoursblockdata_userid] ON [hoursblockdata]([userid]);
IF dbo.csg_index_exists('hoursblockdata_startdate', 'hoursblockdata') = 0
CREATE INDEX [hoursblockdata_startdate] ON [hoursblockdata]([startdate]);
IF dbo.csg_index_exists('hoursblockdata_enddate', 'hoursblockdata') = 0
CREATE INDEX [hoursblockdata_enddate] ON [hoursblockdata]([enddate]);