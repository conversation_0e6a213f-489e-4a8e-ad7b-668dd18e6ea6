CREATE
OR REPLACE VIEW vwchatdata AS
SELECT
    cd.conversationid,
    cd.conversationstart,
    cd.conversationstartltc,
    cd.userid,
    ud.name,
    ud.managerid as managerid,
    ud.managername,
    cd.chatinitiatedby,
    cd.agentchatcount,
    cd.agentchattotal,
    cd.agentchatmax,
    cd.agentchatmin,
    cd.custchatcount,
    cd.custchattotal,
    cd.custchatmax,
    cd.custchatmin
FROM
    chatData cd
    left outer join vwuserdetail ud ON ud.id = cd.userid;

COMMENT ON COLUMN vwChatData.agentchatcount IS 'Agent Chat Count'; 
COMMENT ON COLUMN vwChatData.agentchatmax IS 'Agent Chat Response Max Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.agentchatmin IS 'Agent Chat Response Min Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.agentchattotal IS 'Agent Chat Response Total Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.chatinitiatedby IS 'Chat Started By (Agent/Remote)'; 
COMMENT ON COLUMN vwChatData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN vwChatData.conversationstart IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN vwChatData.conversationstartltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON COLUMN vwChatData.custchatmax IS 'Customer Chat Response Max Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.custchatmin IS 'Customer Chat Response Min Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.custchattotal IS 'Customer Chat Response Total Time (Seconds)'; 
COMMENT ON COLUMN vwChatData.managerid IS 'Manager GUID'; 
COMMENT ON COLUMN vwChatData.managername IS 'Manager Name'; 
COMMENT ON COLUMN vwChatData.name IS 'Name'; 
COMMENT ON COLUMN vwChatData.userid IS 'User GUID'; 
COMMENT ON VIEW vwChatData IS 'See Chat Data - Expands all the GUIDs with their lookups';