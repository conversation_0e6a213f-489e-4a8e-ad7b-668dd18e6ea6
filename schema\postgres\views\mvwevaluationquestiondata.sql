CREATE MATERIALIZED VIEW IF NOT EXISTS mvwevaluationquestiondata TABLESPACE pg_default AS
SELECT
  DISTINCT eq.keyid,
  eq.evaluationid,
  eq.questionid,
  eq.answerid,
  eq.score,
  ed.questionanswervalue,
  eq.markedna,
  eq.failedkillquestions,
  eq.comments,
  ed.evaluationformid,
  ed.evaluationname,
  ed.questiongroupid,
  ed.questiongroupname,
  ed.questiontext,
  ed.questionhelptext,
  ed.quesiontype,
  ed.questionanwserid,
  ed.questionanswertext,
  cd.divisionid
FROM
  evalquestiondata eq
  LEFT JOIN evaldetails ed ON ed.evaluationformid :: text = eq.evaluationformid :: text
  AND ed.questiongroupid :: text = eq.questiongroupid :: text
  AND ed.questionid :: text = eq.questionid :: text
  AND ed.questionanwserid :: text = eq.answerid :: text
  LEFT JOIN evaldata eda ON eda.evaluationid :: text = eq.evaluationid :: text
  LEFT JOIN convsummarydata cd ON cd.conversationid :: text = eda.conversationid :: text WITH DATA;

CREATE UNIQUE INDEX IF NOT EXISTS mvevalquestiondatakeyid ON mvwevaluationquestiondata USING btree (keyid);
CREATE INDEX IF NOT EXISTS mvwevalquestiondataevaluationid ON mvwevaluationquestiondata USING btree (evaluationid);

COMMENT ON COLUMN mvwevaluationquestiondata.keyid IS 'Primary Key';
COMMENT ON COLUMN mvwevaluationquestiondata.evaluationid IS 'Evaluation GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.questionid IS 'Question GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.answerid IS 'Answer GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.score IS 'Score';
COMMENT ON COLUMN mvwevaluationquestiondata.questionanswervalue IS 'Question answer value';
COMMENT ON COLUMN mvwevaluationquestiondata.markedna IS 'Marked as NA';
COMMENT ON COLUMN mvwevaluationquestiondata.failedkillquestions IS 'Failed kill questions';
COMMENT ON COLUMN mvwevaluationquestiondata.comments IS 'Comments';
COMMENT ON COLUMN mvwevaluationquestiondata.evaluationformid IS 'Evaluation form GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.evaluationname IS 'Evaluation name';
COMMENT ON COLUMN mvwevaluationquestiondata.questiongroupid IS 'Question group GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.questiongroupname IS 'Question group name';
COMMENT ON COLUMN mvwevaluationquestiondata.questiontext IS 'Question text';
COMMENT ON COLUMN mvwevaluationquestiondata.questionhelptext IS 'Question help text';
COMMENT ON COLUMN mvwevaluationquestiondata.quesiontype IS 'Question type';
COMMENT ON COLUMN mvwevaluationquestiondata.questionanwserid IS 'Question answer GUID';
COMMENT ON COLUMN mvwevaluationquestiondata.questionanswertext IS 'Question answer text';
COMMENT ON COLUMN mvwevaluationquestiondata.divisionid IS 'Division GUID';

COMMENT ON MATERIALIZED VIEW mvwevaluationquestiondata IS 'Materialized view for evaluation question data';
