﻿using System;
using System.Threading.Tasks;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using Moq;

namespace VoiceAnalysisTestProject
{
    internal static class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Basic API Tests ===");
        // Create a mock logger
        var loggerMock = new Mock<ILogger<VoiceAnalysis>>();

        // Create a VoiceAnalysis instance
        var voiceAnalysis = new VoiceAnalysis(loggerMock.Object);

        // Test the VerifyQueueAsync method
        bool result = await voiceAnalysis.VerifyQueueAsync("test-queue-id");
        Console.WriteLine($"VerifyQueueAsync result: {result}");

        // Test the GetTranscriptUrlAsync method
        string transcriptUrl = await voiceAnalysis.GetTranscriptUrlAsync("test-conversation-id", "test-peer-id");
        Console.WriteLine($"GetTranscriptUrlAsync result: {transcriptUrl ?? "null"}");

        // Test the DownloadTranscriptAsync method
        string transcript = await voiceAnalysis.DownloadTranscriptAsync("https://invalid-url");
        Console.WriteLine($"DownloadTranscriptAsync result: {transcript ?? "null"}");

        // Test the IngestTranscriptAsync method
        bool ingestResult = await voiceAnalysis.IngestTranscriptAsync("test-transcript-json");
        Console.WriteLine($"IngestTranscriptAsync result: {ingestResult}");

        // Run the queue verification cache test
        await QueueVerificationCacheTest.RunTest();
    }
}
}