CREATE TABLE IF NOT EXISTS odcontactlistdetails (
    id varchar(50),
    name varchar(200),
    datecreated timestamp without time zone,
    datecreatedltc timestamp without time zone,
    datemodified timestamp without time zone,
    version integer,
    automatictimezonemapping number,
    division varchar(50),
    divisionname varchar(200),
    updated timestamp without time zone,
    PRIMARY KEY (id)
);

-- Add datemodified column if it doesn't exist (for existing tables)
ALTER TABLE odcontactlistdetails
ADD COLUMN IF NOT EXISTS datemodified timestamp without time zone;