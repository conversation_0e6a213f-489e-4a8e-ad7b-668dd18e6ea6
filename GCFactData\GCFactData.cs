﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using DBUtils;
using GenesysCloudUtils;
using StandardUtils;

namespace GCFactData
{
    public class GCFactData
    {
        public DataSet GCControlData { get; set; }

        private Utils UCAUtils = new Utils();
        private DBUtils.DBUtils DBConnector = new DBUtils.DBUtils();
        private DataTable ClientFeatures { get; set; }

        private String TimeZoneConfig { get; set; }
        private string AggInterval { get; set; }
        private string UserAggViews { get; set; }
        private string QueueAggViews { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly Microsoft.Extensions.Logging.ILogger? _logger;

        public GCFactData(Microsoft.Extensions.Logging.ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            UCAUtils = new StandardUtils.Utils();
            DBUtil.Initialize();

            DBConnector.Initialize();

            GCUtils GCUtil = new GCUtils();
            GCUtil.Initialize();
            UCAUtils.GCControlData = GCUtil.GCControlData;
            ClientFeatures = UCAUtils.GetGCCustomerConfig();
            TimeZoneConfig = Convert.ToString(ClientFeatures.Rows[0]["datetimezone"]);

            // Get granularity from LegacyOptions if available, otherwise use the default from ClientFeatures
            string configuredGranularity = CSG.Adapter.Compatability.LegacyOptions.GetOption("interval");
            AggInterval = !string.IsNullOrEmpty(configuredGranularity) ? configuredGranularity : Convert.ToString(ClientFeatures.Rows[0]["Interval"]);

            UserAggViews = Convert.ToString(ClientFeatures.Rows[0]["useraggviews"]);
            QueueAggViews = Convert.ToString(ClientFeatures.Rows[0]["queueaggviews"]);
        }
        public bool UpdateLastSuccessDate(string Key)
        {
            bool Successful = false;
            Successful = DBConnector.SetSyncLastUpdate(DateTime.UtcNow, Key);
            return Successful;
        }
        public DataTable TeamMembers(DataTable TeamDetails)
        {
            adminData Admin = new();
            Admin.Initialize();

            DataTable TeamsData = Admin.GetTeamMembersfromGC(TeamDetails);

            return TeamsData;
        }
        public DataTable TeamDetails()
        {
            adminData Admin = new();
            Admin.Initialize();

            DataTable TeamsData = Admin.GetTeamDetailsfromGC();

            return TeamsData;

        }
        public DataTable ODContactLists()
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable ContactLists = GCBUDetails.GetODContactListsFromGC();

            return ContactLists;
        }
        public DataTable ODCampaignDetails()
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable Campaigns = GCBUDetails.GetODCampaignDetailsFromGC();

            return Campaigns;
        }
        public DataTable PlanningGroups(DataTable BUnits)
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable PlanningGroups = GCBUDetails.GetPlanningGroupsFromGC(BUnits);

            return PlanningGroups;
        }
        public DataTable ServiceGoals(DataTable BUnits)
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable ServiceGoals = GCBUDetails.GetServiceGoalsFromGC(BUnits);

            return ServiceGoals;
        }
        public DataTable ActCodesDetails(DataTable BUnits)
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable ActivityCodes = DBUtil.CreateInMemTable("activitycodeDetails");

            foreach (DataRow BURow in BUnits.Rows)
            {
                Console.WriteLine("Processing Business Unit {0}", BURow["id"].ToString());
                foreach (DataRow ActCode in GCBUDetails.GetActCodesConfigFromGC(BURow["id"].ToString()).Rows)
                    ActivityCodes.ImportRow(ActCode);
            }
            return ActivityCodes;
        }
        public DataTable BUDetails()
        {
            BUConfig GCBUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCBUDetails.Initialize();
            DataTable BUnits = GCBUDetails.GetBUConfigFromGC();

            return BUnits;
        }
        public DataTable MUDetails()
        {
            BUConfig GCManUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCManUDetails.Initialize();
            DataTable MUnits = GCManUDetails.GetMUConfigFromGC();

            return MUnits;
        }

        public DataTable MUMemberDetails()
        {
            BUConfig GCManUDetails = _logger != null ? new BUConfig(_logger) : new BUConfig();
            GCManUDetails.Initialize();
            DataTable MUMembers = GCManUDetails.GetMUMemberConfigFromGC();

            return MUMembers;
        }

        public DataTable DivisionDetails()
        {
            DivisionConfig GCDivDetails = new DivisionConfig();
            GCDivDetails.Initialize();
            DataTable Divisions = GCDivDetails.GetDivConfigFromGC();

            return Divisions;
        }

        public async Task<DataTable> UserDetails()
        {
            UserConfig GCUserDetails = new UserConfig();
            GCUserDetails.Initialize();
            DataTable Users = await GCUserDetails.GetUserDataFromGC();

            return Users;
        }

        public DataTable EvalDetails()
        {
            EvalConfig GCEvalDetails = new EvalConfig();
            GCEvalDetails.Initialize();
            DataTable EvalQuestions = new DataTable();
            EvalQuestions = GCEvalDetails.GetEvalConfigFromGC();

            return EvalQuestions;
        }

        public DataTable GroupDetails()
        {
            GroupConfig GCGroupDetails = new GroupConfig();

            GCGroupDetails.Initialize();
            DataTable Groups = new DataTable();
            Groups = GCGroupDetails.GetGroupDataFromGC();

            return Groups;
        }

        public DataTable GroupMembershipDetails(DataTable Groups)
        {
            GroupConfig GCGroupDetails = new GroupConfig();
            GCGroupDetails.Initialize();
            DataTable GroupMembership = new DataTable();
            GroupMembership = GCGroupDetails.GetGroupMembershipDataFromGC(Groups);

            return GroupMembership;
        }

        public DataTable QueueDetails()
        {
            QueueConfig GCQueueDetails = new QueueConfig();
            GCQueueDetails.Initialize();
            DataTable Queues = new DataTable();
            Queues = GCQueueDetails.GetQueueDataFromGC();
            DataTable QueueDetails = DBConnector.GetSQLTableData("select * from queuedetails", "queuedetails");
            foreach (DataRow queueDetailRow in QueueDetails.Rows)
            {
                // Assuming 'queueid' is the column name for the queue identifier
                Guid queueId = Guid.Parse(queueDetailRow["id"].ToString());

                // Check if the queueid is not present in Queues
                DataRow[] matchingRows = Queues.Select("id = '" + queueId + "'");

                if (matchingRows.Length == 0)
                {
                    // Set the isactive property to false (assuming 'isactive' is the column name)
                    queueDetailRow["isactive"] = false;
                    string updateQuery = $"UPDATE queuedetails SET isactive = false WHERE id = '{queueId}'";
                    DBConnector.ExecuteSQLQuery(updateQuery);
                }
            }
            return Queues;
        }

        public DataTable WrapupDetails()
        {
            WrapUpConfig GCWrapupDetails = new WrapUpConfig();
            GCWrapupDetails.Initialize();
            DataTable Wrapups = new DataTable();
            Wrapups = GCWrapupDetails.GetWrapUpDataFromGC();

            return Wrapups;
        }


        public async Task<DataTable> UserQueueMappings()
        {
            UserConfig GCUserQueues = new UserConfig();
            GCUserQueues.Initialize();
            DataTable Wrapups = await GCUserQueues.GetUserQueuesUpDataFromGC();

            return Wrapups;
        }


        public async Task<DataTable> SkillDetails()
        {
            //Get The Skills
            UserConfig GCSkillDetails = new UserConfig();
            GCSkillDetails.Initialize();
            DataTable Skills = await GCSkillDetails.GetSkillDetailsFromGC();
            return Skills;
        }


        public async Task<DataTable> SkillsMapping(DataTable Users)
        {
            //Get The Skills
            UserConfig GCSkillMappings = new UserConfig();
            GCSkillMappings.Initialize();
            DataTable Skills = await GCSkillMappings.GetSkillMappingsFromGC(Users);
            return Skills;
        }


        public DataTable PresenceDetails()
        {
            PresenceConfig GCPresenceDetails = new PresenceConfig();
            GCPresenceDetails.Initialize();
            DataTable Presence = new DataTable();
            Presence = GCPresenceDetails.GetPresenceDataFromGC();

            return Presence;

        }

        public DataTable LearningModuleDetails()
        {
            LearningDataConfig GCLearningModuleDetails = new LearningDataConfig(_logger);
            GCLearningModuleDetails.Initialize();
            return GCLearningModuleDetails.GetLearningModulesFromGC();
        }

        public DataTable LearningModuleAssignmentsDetails(DataTable LearningModules)
        {
            LearningDataConfig GCLearningModuleAssignmentDetails = new LearningDataConfig(_logger);
            GCLearningModuleAssignmentDetails.Initialize();
            return GCLearningModuleAssignmentDetails.GetLearningModuleAssignmentsFromGC(LearningModules);
        }

        public DataTable KnowledgeBaseDetails()
        {
            KnowledgeBaseConfig GCKnowledgeBaseDetails = new KnowledgeBaseConfig();
            GCKnowledgeBaseDetails.Initialize();
            DataTable KnowledgeBases = new DataTable();
            KnowledgeBases = GCKnowledgeBaseDetails.GetKnowledgeBaseDataFromGC();

            return KnowledgeBases;
        }

        public DataTable KnowledgeBaseCategoryDetails(DataTable KnowledgeBase)
        {
            KnowledgeBaseConfig GCKnowledgeBaseCategoryDetails = new KnowledgeBaseConfig();
            GCKnowledgeBaseCategoryDetails.Initialize();
            DataTable KnowledgeBaseCategory = new DataTable();
            KnowledgeBaseCategory = GCKnowledgeBaseCategoryDetails.GetKnowledgeBaseCategoryDataFromGC(KnowledgeBase);

            return KnowledgeBaseCategory;
        }

        public DataTable FlowOutcomeDetails()
        {
            FlowOutcomeConfig GCFlowOutcomeDetails = new FlowOutcomeConfig();
            GCFlowOutcomeDetails.Initialize();
            DataTable FlowOutcomes = new DataTable();
            FlowOutcomes = GCFlowOutcomeDetails.GetFlowOutcomeDetailsFromGC();

            return FlowOutcomes;
        }


    }
}
// spell-checker: ignore: GCBU
