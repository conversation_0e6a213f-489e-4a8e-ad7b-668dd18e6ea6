﻿using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateChatData
    {
        private readonly ILogger? _logger;

        public GCUpdateChatData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCChatData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string SyncType = "chatdata";

            GCGetData GCData = new GCGetData(_logger);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            DataTable GCChatData = GCData.ChatData(renameParticipantAttributeNames);

            Console.WriteLine("Writing Chat Data Rows {0}", GCChatData.Rows.Count);
            Successful = DBAdapter.WriteSQLData(GCChatData, "chatdata");

            Console.WriteLine("Update Date is : {0}", GCData.DetailChatLastUpdate);
            if (GCData.DetailChatLastUpdate > OldUpdateTime && Successful == true)

            {
                Console.WriteLine("Updating Max Update Date {0}", GCData.DetailChatLastUpdate);
                OldUpdateTime = GCData.DetailChatLastUpdate;
            }

            if (Successful == true)
            {
                OriginalTime = OriginalTime.Add(GCData.MaxSpanToSync).AddHours(-2);

                if (OldUpdateTime >= OriginalTime)
                    OriginalTime = OldUpdateTime;

                Successful = GCData.UpdateLastSuccessDate(OriginalTime, "chatdata");


                Console.WriteLine("Next Start Time {0} ", OriginalTime);
                Console.WriteLine("Updated The Latest Update Date Successful {0}", Successful);
            }
            else
                Console.WriteLine("Will Not update the last update date - failure in processing");

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);

            return Successful;

        }

    }
}
