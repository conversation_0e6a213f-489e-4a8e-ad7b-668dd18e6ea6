using System;
using System.Data;
using GenesysCloudUtils;
using DBUtils;
using Microsoft.Extensions.Logging;
using GCData;

namespace GenesysAdapter
{
    public class GCUpdateChatData
    {
        private readonly ILogger? _logger;

        public GCUpdateChatData(ILogger logger)
        {
            _logger = logger;
        }

        public void UpdateGCChatData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            _logger?.LogInformation("Starting Chat Data Update");

            try
            {
                GCGetData GCDataClass = new GCGetData(_logger);
                GCDataClass.Initialize("chatdata");

                DataTable ChatData = GCDataClass.ChatData(renameParticipantAttributeNames);

                if (ChatData != null && ChatData.Rows.Count > 0)
                {
                    _logger?.LogInformation("Retrieved {RowCount} chat data records", ChatData.Rows.Count);

                    DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
                    DBUtil.Initialize();

                    // Write the chat data to the database
                    bool successful = DBUtil.WriteSQLData(ChatData, "chatdata");

                    if (successful)
                    {
                        _logger?.LogInformation("Chat data update completed successfully");
                    }
                    else
                    {
                        _logger?.LogError("Failed to write chat data to database");
                    }
                }
                else
                {
                    _logger?.LogInformation("No new chat data to process");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating chat data");
                throw;
            }
        }
    }
}
