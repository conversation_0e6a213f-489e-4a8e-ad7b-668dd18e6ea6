﻿using System.Data;

namespace DBUtils
{
    public class GenUtils
    {

        public DataTable ConvertCSVtoDataTable(string StringData, string contactList)
        {
            
            DataTable dt = new DataTable();

            StringData = StringData.Replace(@"""", string.Empty);
            string[] CSVEntries = StringData.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
            string[] Headers = CSVEntries[0].Split(',');

            foreach (string header in Headers)
            {
                dt.Columns.Add(header);
            }


            int RowsProcessed = 0;

            foreach (string CSVRow in CSVEntries)
            {
               if (RowsProcessed > 0)
                {
                    string[] rows = CSVRow.Split(',');
                    DataRow dr = dt.NewRow();
                    if (rows.Length < Headers.Length)
                    {
                        Console.WriteLine("Length of columns ({0}) not equal to length of headers ({1}) for Row Number {2} in ContactList {3}, row will skipped.",
                                                            rows.Length,
                                                            Headers.Length,
                                                            RowsProcessed,
                                                            contactList);
                        RowsProcessed++;
                        continue;
                    }
                    for (int i = 0; i < Headers.Length; i++)
                    {
                        dr[i] = rows[i];
                    }
                    dt.Rows.Add(dr);
                }

                RowsProcessed++;
            }


            return dt;
        }


    }
}
