IF dbo.csg_table_exists('groupDetails') = 0
CREATE TABLE [groupDetails](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](255),
    [email] [nvarchar](255),
    [membercount] [int],
    [state] [nvarchar](50),
    [type] [nvarchar](50),
    [selfUri] [nvarchar](150),
    [updated] [datetime],
    PRIMARY KEY ([id])
);

IF dbo.csg_column_exists('groupDetails', 'selfuri') = 1
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('groupDetails') AND name = 'selfUri')
    BEGIN
        EXEC sp_rename 'groupDetails.[selfuri]', 'selfUri', 'COLUMN';
    END
END

IF dbo.csg_column_exists('groupDetails', 'email') = 1
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('groupDetails') AND name = 'description')
    BEGIN
        EXEC sp_rename 'groupDetails.[email]', 'description', 'COLUMN';
    END
END
