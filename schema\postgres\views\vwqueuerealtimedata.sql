drop view if exists vwqueuerealtimedata;

CREATE
OR REPLACE VIEW vwqueuerealtimedata AS
SELECT
    keyid,
    queueid,
    qd.name as queuename,
    media,
    statscount,
    startdate,
    startdateltc,
    COALESCE(
        DATEDIFF(
            'second',
            qc.startdate::timestamp,
            timezone('utc', now())::timestamp
        ), 0
    ) as statusSecs,
    (
        DATEDIFF(
            'second',
            qc.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        )
    ) / 86400.00 as statusDays,
    qc.updated
FROM
    queuerealtimedata qc
    left outer join queuedetails as qd on qd.id = qc.queueid
;