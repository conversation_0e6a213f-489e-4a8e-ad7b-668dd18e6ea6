CREATE
OR REPLACE VIEW vwadherenceexcdata AS
SELECT
    ad.userid,
    ad.startdate,
    ad.enddate,
    ad.startdateltc,
    ad.enddateltc,
    ad.durationsecs,
    ad.durationsecs / 86400.00 as durationsecsDay,
    ad.tolerance,
    ad.tolerance / 86400.00 as toleranceDay,
    ad.actualdurationsecs,
    ad.actualdurationsecs / 86400.00 as actualdurationsecsDay,
    ad.scheduledActivityCategory,
    ad.actualActivityCategory,
    ad.systemPresence,
    ad.routingStatus,
    ad.impact,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername
FROM
    adherenceexcData ad
    left outer join vwUserDetail ud on ud.id = ad.userid;

COMMENT ON COLUMN vwadherenceexcData.actualActivityCategory IS 'The Actual Activity Category'; 
COMMENT ON COLUMN vwadherenceexcData.actualdurationsecs IS 'The actual time of the exception'; 
COMMENT ON COLUMN vwadherenceexcData.actualdurationsecsDay IS 'The actual time of the exception in seconds'; 
COMMENT ON COLUMN vwadherenceexcData.agentname IS 'Agent Name'; 
COMMENT ON COLUMN vwadherenceexcData.durationsecs IS 'Total Duration of Exception in sec(s) - Tolerance Time in sec(s)'; 
COMMENT ON COLUMN vwadherenceexcData.durationsecsDay IS 'Total Duration of Exception in sec(s) - Tolerance Time in sec(s)'; 
COMMENT ON COLUMN vwadherenceexcData.enddateltc IS 'End Time (LTC)'; 
COMMENT ON COLUMN vwadherenceexcData.impact IS 'Impact of the Exception'; 
COMMENT ON COLUMN vwadherenceexcData.managername IS 'Manager Name'; 
COMMENT ON COLUMN vwadherenceexcData.routingStatus IS 'Routing Status GUID'; 
COMMENT ON COLUMN vwadherenceexcData.scheduledActivityCategory IS 'The Activity Category'; 
COMMENT ON COLUMN vwadherenceexcData.startdate IS 'Start Time (UTC)'; 
COMMENT ON COLUMN vwadherenceexcData.startdateltc IS 'Start Time (LTC)'; 
COMMENT ON COLUMN vwadherenceexcData.systemPresence IS 'Presense GUID'; 
COMMENT ON COLUMN vwadherenceexcData.tolerance IS 'The tolerance before exception starts'; 
COMMENT ON COLUMN vwadherenceexcData.toleranceDay IS 'The tolerance Day before exception starts'; 
COMMENT ON COLUMN vwadherenceexcData.userid IS 'User GUID'; 
COMMENT ON VIEW vwadherenceexcData IS 'See AdherenceExcData - Expands all the GUIDs with their lookups';
