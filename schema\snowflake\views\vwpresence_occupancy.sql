DROP VIEW IF EXISTS vwpresence_occupancy;

CREATE
OR REPLACE VIEW vwpresence_occupancy AS
SELECT
    STARTDATE,
    userid,
    mum.MU_NAME,
    ud.NAME AS user_name,
    COALESCE(
        (SUM(CASE WHEN ROUTINGID = 'INTERACTING' THEN PRESENCETIME ELSE 0 END) /
         NULLIF(SUM(CASE WHEN ROUTINGID IN ('INTERACTING', 'COMMUNICATING', 'IDLE') THEN ROUTINGTIME ELSE 0 END), 0)) * 100,
        0
    ) AS OCCUPANCY,
    SUM(CASE WHEN ROUTINGID = 'INTERACTING' THEN PRESENCETIME ELSE 0 END) AS TOTAL_INTERACTING_TIME,
    SUM(CASE WHEN ROUTINGID IN ('INTERACTING', 'COMMUNICATING', 'IDLE') THEN ROUTINGTIME ELSE 0 END) AS TOTAL_ON_QUEUE_TIME
FROM
    USERPRESENCEDATA up
    LEFT JOIN VWUSERDETAIL ud ON ud.ID = up.USERID 
    LEFT JOIN vwmumemberdata mum on mum.user_id = up.userid
GROUP BY
    STARTDATE,
    userid,
    ud.NAME,
    mum.MU_NAME
ORDER BY STARTDATE DESC;