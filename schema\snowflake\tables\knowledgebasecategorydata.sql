CREATE TABLE IF NOT EXISTS knowledgebasecategorydata (
    id VARCHAR(50) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(100),
    description VARCHAR(255),
    externalId VARCHAR(100),
    dateCreated timestamp without time zone,
    dateModified timestamp without time zone,
    documentCount integer null,
    knowledgeBaseId VARCHAR(100),
    parentCategoryId varchar(100),
    parentCategoryName varchar(100),
    updated timestamp without time zone,
    CONSTRAINT knowledgebasecategorydata_pkey PRIMARY KEY (id)
);
