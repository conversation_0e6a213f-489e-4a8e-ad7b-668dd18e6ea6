CREATE TABLE IF NOT EXISTS adherenceactdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    enddateltc timestamp without time zone,
    enddate timestamp without time zone,
    durationsecs integer,
    actualactivitycategory varchar(50),
    updated timestamp without time zone,
    CONSTRAINT adherenceactdata_pkey PRIMARY KEY (keyid)
);
COMMENT ON COLUMN adherenceactData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN adherenceactData.actualActivityCategory IS 'Actual Activity Category'; 
COMMENT ON COLUMN adherenceactData.durationsecs IS 'Duration in Sec(s)'; 
COMMENT ON COLUMN adherenceactData.enddate IS 'End Time (UTC)'; 
COMMENT ON COLUMN adherenceactData.enddateltc IS 'End Time (LTC)'; 
COMMENT ON COLUMN adherenceactData.startdate IS 'Start Time (UTC)'; 
COMMENT ON COLUMN adherenceactData.startdateltc IS 'Start Time (LTC)'; 
COMMENT ON COLUMN adherenceactData.updated IS 'Date Row Updated';
COMMENT ON COLUMN adherenceactData.userid IS 'User GUID'; 
COMMENT ON TABLE adherenceactData IS 'Adherence Actual Activity Data'; 

DO $$
DECLARE
    KeyidPrefix VARCHAR(255) := 'v1_';
    deleted_count INTEGER;
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM adherenceactdata
        WHERE SUBSTR(keyid, 1, LENGTH(KeyidPrefix)) <> KeyidPrefix
    ) THEN
        DELETE FROM adherenceactdata
        WHERE LEFT(keyid, LENGTH(KeyidPrefix)) <> KeyidPrefix;
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        
        UPDATE tabledefinitions
        SET datekeyfield = CURRENT_DATE - INTERVAL '12 months'
        WHERE tablename = 'adherenceactdata';
        
        RAISE NOTICE 'Records deleted: % and tabledefinitions updated.', deleted_count;
    ELSE
        RAISE NOTICE 'No records found with old keyid in use.';
    END IF;
END $$;