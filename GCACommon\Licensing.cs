using System.Data;
using System.Net.Http;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace CSG.Adapter.Licensing;

public class LicenseValidator
{
    // Define static constants for default values
    private static readonly string DefaultUrlScheme = "https://";
    private static readonly string DefaultLicenseApiHost = "csi-licensing.azurewebsites.net/api";
    private static readonly string DefaultLicenseApiKey = "LmvWskLmd9HwwQyJQDEFyMPd7yo4NYwdsOag6oShEsvfAzFu0mkd9g==";
    private static readonly string DefaultHealthCheckEndpoint = "health-check";
    private static readonly string DefaultClientCheckEndpoint = "check-client";

    private readonly string _clientId;
    private static readonly HttpClient HttpClient = new HttpClient();
    private readonly ILogger<LicenseValidator> _logger;

    // Store the client details for later access
    private ClientDetails _clientDetails;

    // Define configuration properties for API URLs
    private readonly string _urlScheme;
    private readonly string _licenseApiHost;
    private readonly string _licenseApiKey;
    private readonly string _healthCheckEndpoint;
    private readonly string _clientCheckEndpoint;

    public LicenseValidator(string clientId, ILogger<LicenseValidator> logger,
        string? urlScheme = null,
        string? licenseApiHost = null,
        string? licenseApiKey = null,
        string? healthCheckEndpoint = null,
        string? clientCheckEndpoint = null)
    {
        _clientId = clientId;
        _logger = logger;
        _urlScheme = urlScheme ?? DefaultUrlScheme;
        _licenseApiHost = licenseApiHost ?? DefaultLicenseApiHost;
        _licenseApiKey = licenseApiKey ?? DefaultLicenseApiKey;
        _healthCheckEndpoint = healthCheckEndpoint ?? DefaultHealthCheckEndpoint;
        _clientCheckEndpoint = clientCheckEndpoint ?? DefaultClientCheckEndpoint;
    }

    public async Task ValidateAsync()
    {
        if (!await CheckHealthAsync())
        {
            throw new UnauthorizedAccessException("Unable to validate program license, please contact support.");
        }

        try
        {
            _clientDetails = await GetClientDetailsAsync(_clientId);

            if (_clientDetails == null || !_clientDetails.Client_Exists)
            {
                throw new UnauthorizedAccessException("Client does not exist, please contact support.");
            }

            if (!_clientDetails.Is_Licensed)
            {
                throw new UnauthorizedAccessException("Client is not licensed, please contact support.");
            }

            _logger.LogDebug("License type: {LicenseType}, name: {LicenseName}",
                _clientDetails.License_Type,
                _clientDetails.License_Name);
        }
        catch (HttpRequestException ex)
        {
            throw new UnauthorizedAccessException(
                    "Unable to validate program license, please contact support.", ex);
        }
    }

    /// <summary>
    /// Checks if the client has the Knowledge Quest license (license type 2 or higher)
    /// </summary>
    /// <returns>True if the client has license type 2 or higher, false otherwise</returns>
    public bool HasKnowledgeQuestLicense()
    {
        if (_clientDetails == null)
            return false;

        return _clientDetails.Is_Licensed && _clientDetails.License_Type >= 2;
    }

    private async Task<bool> CheckHealthAsync()
    {
        string url = $"{_urlScheme}{_licenseApiHost}/{_healthCheckEndpoint}?code={_licenseApiKey}";
        _logger.LogDebug("Checking health status at URL: {Url}", url);
        try
        {
            var response = await HttpClient.GetStringAsync(url);
            var licenseInfo = JsonConvert.DeserializeObject<LicenseInfo>(response);
            bool healthy = licenseInfo?.Message == "Healthy";
            _logger.LogDebug("Health status: {Healthy}", healthy);
            return healthy;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Error checking health status.");
            return false;
        }
    }

    /// <summary>
    /// Gets the client details from the license API
    /// </summary>
    /// <param name="clientId">The client ID to check</param>
    /// <returns>The client details or null if not found</returns>
    private async Task<ClientDetails> GetClientDetailsAsync(string clientId)
    {
        string url = $"{_urlScheme}{_licenseApiHost}/{_clientCheckEndpoint}?code={_licenseApiKey}&client_id={clientId.ToUpper()}";
        _logger.LogDebug("Checking client existence for client ID: {ClientId} at URL: {Url}", clientId, url);
        var response = await HttpClient.GetStringAsync(url);
        var clientResponse = JsonConvert.DeserializeObject<ClientResponse>(response);
        var clientDetails = clientResponse?.Client.FirstOrDefault();

        if (clientDetails != null)
        {
            _logger.LogDebug("Client exists: {Exists} for client ID: {ClientId}", clientDetails.Client_Exists, clientId);
            _logger.LogDebug("Client is licensed: {isLicensed}", clientDetails.Is_Licensed);
        }

        return clientDetails;
    }
}

// Define a class to represent the JSON structure for health check
public class LicenseInfo
{
    public string Message { get; set; } = string.Empty;
    public string Error { get; set; } = string.Empty;
}

// Define a class to represent the JSON structure for client check
public class ClientResponse
{
    public ClientDetails[] Client { get; set; }
}

public class ClientDetails
{
    public bool Client_Exists { get; set; }
    public bool Is_Licensed { get; set; }
    public int License_Type { get; set; }
    public string License_Name { get; set; } = string.Empty;
    public string License_Description { get; set; } = string.Empty;
    public string Client_Id { get; set; } = string.Empty;
}