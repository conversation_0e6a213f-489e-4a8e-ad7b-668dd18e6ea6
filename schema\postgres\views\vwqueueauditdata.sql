CREATE
OR REPLACE VIEW vwqueueauditdata AS
SELECT 
        qa.queueid AS queueid,
        qa.userid AS userid,
        ud.name AS name,
        ud.department AS department,
        ud.managername AS managername,
        qa.addorremove AS addorremove,
		qa.activeorinactive AS activeorinactive,
        qa.datemodified AS datemodified,
        qa.modifiedby AS modifiedby,
        ud1.name AS modbyname,
        ud1.department AS modbydept,
        ud1.managername AS modbymanager,
        qd.name AS queuename,
		qd.divisionid as divisionid
    FROM queueauditdata qa
        LEFT OUTER JOIN vwUserDetail ud ON qa.userid = ud.id
        LEFT OUTER JOIN vwUserDetail ud1 ON qa.modifiedby = ud1.id
        LEFT OUTER JOIN vwQueueDetails qd ON qa.queueid = qd.id;

COMMENT ON COLUMN vwqueueauditdata.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwqueueauditdata.userid IS 'User GUID';
COMMENT ON COLUMN vwqueueauditdata.name IS 'User Name';
COMMENT ON COLUMN vwqueueauditdata.department IS 'Department';
COMMENT ON COLUMN vwqueueauditdata.managername IS 'Manager Name';
COMMENT ON COLUMN vwqueueauditdata.addorremove IS 'Action to Add or Remove';
COMMENT ON COLUMN vwqueueauditdata.activeorinactive IS 'Action to make active or inactive';
COMMENT ON COLUMN vwqueueauditdata.datemodified IS 'Date Modified(UTC)';
COMMENT ON COLUMN vwqueueauditdata.modifiedby IS 'Modified By GUID';
COMMENT ON COLUMN vwqueueauditdata.modbyname IS 'Modified By Name';
COMMENT ON COLUMN vwqueueauditdata.modbydept IS 'Modified By Department';
COMMENT ON COLUMN vwqueueauditdata.modbymanager IS 'Modified By Manager';
COMMENT ON COLUMN vwqueueauditdata.queuename IS 'Queue Name';
COMMENT ON COLUMN vwqueueauditdata.divisionid IS 'Division GUID';

COMMENT ON VIEW vwqueueauditdata IS 'Audit Table for Queue Membership changes';
