CREATE
OR REPLACE VIEW vwRealTimeUserConv AS
SELECT
    conversationid,
    media,
    queueid,
    direction,
    conversationstate,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN 'DIAL'
        WHEN ud.acwstate = true THEN 'ACW'
        WHEN ud.heldstate = true THEN 'HELD'
        WHEN ud.conversationstate = 'alerting' THEN 'ALERT'
        WHEN ud.conversationstate = 'contacting' THEN 'CONTACT'
        ELSE 'TALKING'
    END AS convstatus,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN DATEDIFF(
            'second',
            ud.updated :: timestamp,
            timezone('utc', now())
        )
        WHEN ud.acwstate = true THEN DATEDIFF(
            'second',
            ud.acwtime :: timestamp,
            timezone('utc', now())
        )
        WHEN ud.heldstate = true THEN DATEDIFF(
            'second',
            ud.heldtime :: timestamp,
            timezone('utc', now())
        )
        WHEN ud.conversationstate = 'alerting' THEN DATEDIFF(
            'second',
            ud.updated :: timestamp,
            timezone('utc', now())
        )
        ELSE DATEDIFF(
            'second',
            ud.talktime :: timestamp,
            timezone('utc', now())
        )
    END AS convstatustime,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN (
            DATEDIFF(
                'second',
                ud.updated :: timestamp,
                timezone('utc', now())
            ) / 86400.00
        )
        WHEN ud.acwstate = true THEN (
            DATEDIFF(
                'second',
                ud.acwtime :: timestamp,
                timezone('utc', now())
            ) / 86400.00
        )
        WHEN ud.heldstate = true THEN (
            DATEDIFF(
                'second',
                ud.heldtime :: timestamp,
                timezone('utc', now())
            ) / 86400.00
        )
        WHEN ud.conversationstate = 'alerting' THEN (
            DATEDIFF(
                'second',
                ud.updated :: timestamp,
                timezone('utc', now())
            ) / 86400.00
        )
        ELSE (
            DATEDIFF(
                'second',
                ud.talktime :: timestamp,
                timezone('utc', now())
            ) / 86400.00
        )
    END AS convstatustimeDay,
    acwstate,
    acwtime,
    heldstate,
    heldtime,
    talktime
FROM
    userRealTimeConvData AS ud;

COMMENT ON COLUMN vwRealTimeUserConv.conversationid IS 'GUID of the conversation';
COMMENT ON COLUMN vwRealTimeUserConv.media IS 'Type of media in the conversation';
COMMENT ON COLUMN vwRealTimeUserConv.queueid IS 'GUID of the associated queue';
COMMENT ON COLUMN vwRealTimeUserConv.direction IS 'Direction of the conversation';
COMMENT ON COLUMN vwRealTimeUserConv.conversationstate IS 'State of the conversation';
COMMENT ON COLUMN vwRealTimeUserConv.convstatus IS 'Status of the conversation';
COMMENT ON COLUMN vwRealTimeUserConv.convstatustime IS 'Time of the conversation status';
COMMENT ON COLUMN vwRealTimeUserConv.convstatustimeDay IS 'Duration of the conversation status in days';
COMMENT ON COLUMN vwRealTimeUserConv.acwstate IS 'ACW (After Call Work) state';
COMMENT ON COLUMN vwRealTimeUserConv.acwtime IS 'Time of ACW state';
COMMENT ON COLUMN vwRealTimeUserConv.heldstate IS 'Held state';
COMMENT ON COLUMN vwRealTimeUserConv.heldtime IS 'Time of held state';
COMMENT ON COLUMN vwRealTimeUserConv.talktime IS 'Talk time in the conversation';
COMMENT ON VIEW vwRealTimeUserConv IS 'Real-time user conversation data';
