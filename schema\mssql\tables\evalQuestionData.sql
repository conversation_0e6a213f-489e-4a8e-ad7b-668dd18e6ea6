IF dbo.csg_table_exists('evalQuestionData') = 0
CREATE TABLE [evalQuestionData](
    [keyid] [nvarchar](50) NOT NULL,
    [evaluationid] [nvarchar](50) NOT NULL,
    [evaluationformid] [varchar](50),
    [questiongroupid] [nvarchar](50),
    [questionid] [nvarchar](50),
    [answerid] [nvarchar](50),
    [score] [decimal](20, 2),
    [markedna] [bit],
    [failedkillquestions] [bit],
    [comments] [nvarchar](max),
    [updated] [datetime],
    CONSTRAINT [PK__evalQues__607AFDE013D8222E] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('evalquetiondata_eval', 'evalQuestionData') = 0
CREATE INDEX [evalquetiondata_eval] ON [evalQuestionData] ([evaluationid]);
IF dbo.csg_index_exists('evalquestiondata_formid', 'evalQuestionData') = 0
CREATE INDEX [evalquestiondata_formid] ON [evalQuestionData] ([evaluationformid]);
IF dbo.csg_index_exists('evalquestiondata_group', 'evalQuestionData') = 0
CREATE INDEX [evalquestiondata_group] ON [evalQuestionData] ([questiongroupid]);
