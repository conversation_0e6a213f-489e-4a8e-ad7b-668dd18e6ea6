DROP VIEW IF EXISTS vwrealtimequeue;

CREATE
OR REPLACE VIEW vwrealtimequeue AS
WITH all_media AS (
    -- Get all unique media types from the conversation data
    SELECT DISTINCT media
    FROM queuerealtimeconvdata
    UNION
    -- Include predefined media types to ensure all are represented
    SELECT unnest(ARRAY['voice', 'callback', 'chat', 'message', 'email']) AS media
    ),
    queue_media AS (
        SELECT
            qd.id AS queueid,
            qd.name AS queuename,
            qd.divisionid,
            qd.divisionname,
            m.media
        FROM vwqueuedetails qd
        CROSS JOIN all_media m
    )
SELECT
    qm.queueid,
    qm.queuename,
    qm.divisionid,
    qm.divisionname,
    qm.media,
    qr.conversationstate,
    SUM(
        CASE
            WHEN qr.actingas::text = 'acd'::text THEN 1
            ELSE 0
        END
    ) AS acd_count,
    SUM(
        CASE
            WHEN qr.actingas::text = 'agent'::text THEN 1
            ELSE 0
        END
    ) AS interacting,
    COALESCE(MIN(
        CASE
            WHEN qr.actingas::text = 'acd'::text THEN qr.startdate
            ELSE NULL::timestamp without time zone
        END
    )) AS startdate,
    COALESCE(MIN(
        CASE
            WHEN qr.actingas::text = 'acd'::text THEN qr.startdateltc
            ELSE NULL::timestamp without time zone
        END
    )) AS startdateltc,
    datediff('second', MIN(
        CASE
            WHEN qr.actingas::text = 'acd'::text THEN qr.startdate
            ELSE NULL::timestamp without time zone
        END
    ), timezone('utc', now())) AS statussecs,
    datediff('second', MIN(
        CASE
            WHEN qr.actingas::text = 'acd'::text THEN qr.startdate
            ELSE NULL::timestamp without time zone
        END
    ), timezone('utc', now()))::numeric / 86400.00 AS statusdays,
    MAX(qr.updated) AS updated
FROM
    queue_media qm
LEFT JOIN
    queuerealtimeconvdata qr
    ON qr.queueid::text = qm.queueid::text AND qr.media = qm.media
GROUP BY
    qm.queueid, qm.queuename, qm.divisionid, qm.divisionname, qm.media, qr.conversationstate
ORDER BY
    qm.queuename,qm.media;

COMMENT ON COLUMN vwrealtimequeue.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwrealtimequeue.queuename IS 'Queue name';
COMMENT ON COLUMN vwrealtimequeue.divisionid IS 'Division ID';
COMMENT ON COLUMN vwrealtimequeue.divisionname IS 'Division name';
COMMENT ON COLUMN vwrealtimequeue.media IS 'Type of media (voice, chat, email, etc.)';
COMMENT ON COLUMN vwrealtimequeue.conversationstate IS 'Current state of the conversation';
COMMENT ON COLUMN vwrealtimequeue.acd_count IS 'Count of interactions with actingas = acd';
COMMENT ON COLUMN vwrealtimequeue.interacting IS 'Total count of interactions currently being handled by agents';
COMMENT ON COLUMN vwrealtimequeue.startdate IS 'Start date of the interaction filtered to acd';
COMMENT ON COLUMN vwrealtimequeue.startdateltc IS 'Logical timestamp counter for start date filtered to acd';
COMMENT ON COLUMN vwrealtimequeue.statussecs IS 'Status duration in seconds calculated with DATEDIFF';
COMMENT ON COLUMN vwrealtimequeue.statusdays IS 'Status duration in days calculated with DATEDIFF';
COMMENT ON COLUMN vwrealtimequeue.updated IS 'Timestamp of the last update';

COMMENT ON VIEW vwrealtimequeue IS 'Real-time queue data';