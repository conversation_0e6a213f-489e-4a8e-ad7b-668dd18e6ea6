CREATE TABLE IF NOT EXISTS userpresencedetaileddata (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    starttime timestamp without time zone NOT NULL,
    starttimeltc timestamp without time zone,
    endtime timestamp without time zone,
    endtimeltc timestamp without time zone,
    systempresence varchar(50),
    orgpresence varchar(50),
    routingstatus varchar(50),
    timeinstate numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedetaileddata_pkey PRIMARY KEY (keyid, starttime)
);