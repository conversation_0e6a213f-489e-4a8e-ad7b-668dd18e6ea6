CREATE TABLE IF NOT EXISTS convvoiceoverviewdata (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50),
    sentimentscore numeric(20, 2),
    sentimenttrend numeric(20, 2),
    agentdurationpercentage numeric(20, 2),
    customerdurationpercentage numeric(20, 2),
    silencedurationpercentage numeric(20, 2),
    ivrdurationpercentage numeric(20, 2),
    acddurationpercentage numeric(20, 2),
    otherdurationpercentage numeric(20, 2),
    overtalkdurationpercentage numeric(20, 2),
    overtalkcount integer,
    sentimenttrendclass varchar(50),
    phrasecount bigint,
    peerid varchar(50),
    gettransscript varchar(5),
	transcript_processed boolean,
	transcript_processed_date timestamp without time zone,
	transcript_processed_notes varchar(255),
    updated timestamp without time zone,
    CONSTRAINT convvoiceoverviewdata_pkey PRIMARY KEY (keyid)
);
ALTER TABLE convvoiceoverviewdata
ADD column IF NOT exists gettransscript varchar(5);

ALTER TABLE convvoiceoverviewdata
ADD column IF NOT exists peerid varchar(50);

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists phrasecount  bigint;

ALTER TABLE convvoiceoverviewdata
ALTER COLUMN phrasecount TYPE bigint;

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists sentimenttrendclass  varchar(50);

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists transcript_processed boolean;

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists transcript_processed_date timestamp without time zone;

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists transcript_processed_notes varchar(255);

COMMENT ON COLUMN convVoiceOverviewData.acdDurationPercentage IS 'Conversation Queue Percentage Time (Seconds)';
COMMENT ON COLUMN convVoiceOverviewData.agentdurationpercentage IS 'Conversation Agent Percentage Talk';
COMMENT ON COLUMN convVoiceOverviewData.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN convVoiceOverviewData.customerdurationpercentage IS 'Conversation Agent Percentage Talk';
COMMENT ON COLUMN convVoiceOverviewData.gettransscript IS 'Admin - has the transcript been analysed?';
COMMENT ON COLUMN convVoiceOverviewData.ivrDurationPercentage IS 'Conversation IVR Percentage Talk';
COMMENT ON COLUMN convVoiceOverviewData.keyid IS 'Primary Key';
COMMENT ON COLUMN convVoiceOverviewData.otherdurationpercentage IS 'Conversation Other Percentage Time (Seconds)';
COMMENT ON COLUMN convVoiceOverviewData.overtalkcount IS 'Conversation Agent Overtalk Count';
COMMENT ON COLUMN convVoiceOverviewData.overtalkdurationpercentage IS 'Conversation Agent Overtalk Percentage Time (Seconds)';
COMMENT ON COLUMN convVoiceOverviewData.peerid IS 'Conversation Peer GUID (for Voice Analysis)';
COMMENT ON COLUMN convVoiceOverviewData.phrasecount IS 'Conversation Total Phrase Count';
COMMENT ON COLUMN convVoiceOverviewData.sentimentscore IS 'Convesation Sentiment Score';
COMMENT ON COLUMN convVoiceOverviewData.sentimenttrend IS 'Convesation Sentiment Trend';
COMMENT ON COLUMN convVoiceOverviewData.sentimenttrendclass IS 'Conversation Sentiment Trend';
COMMENT ON COLUMN convVoiceOverviewData.silencedurationpercentage IS 'Conversation Silence Percentage';
COMMENT ON COLUMN convvoiceoverviewdata.transcript_processed IS 'Flag indicating if transcript has been processed for ingestion to Knowledge Quest service';
COMMENT ON COLUMN convvoiceoverviewdata.transcript_processed_date IS 'Date when transcript was processed for ingestion to Knowledge Quest service (UTC)';
COMMENT ON COLUMN convvoiceoverviewdata.transcript_processed_notes IS 'Notes about transcript processing for ingestion to Knowledge Quest service';
COMMENT ON COLUMN convVoiceOverviewData.updated IS 'Date Row Updated (UTC)';
COMMENT ON TABLE convVoiceOverviewData IS 'Voice Analysis Overview Data';
