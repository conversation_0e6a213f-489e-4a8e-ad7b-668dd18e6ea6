CREATE TABLE IF NOT EXISTS surveyquestionanswers (
    surveyid varchar(50) NOT NULL,
    conversationid varchar(50) NOT NULL,
    surveyformid varchar(50),
    surveyname varchar(200),
    agentid varchar(50),
    agentteamid varchar(50),
    queueid varchar(50),
    questiongroupid varchar(50),
    questiongroupname varchar(200),
    questionid varchar(50) NOT NULL,
    questiontext varchar(400),
    questiontype varchar(50),
    questionanswerid varchar(50),
    questionanswertext varchar(200),
    questionanswervalue numeric(20, 2),
    questionscore numeric(20, 2),
    questionmarkedna number,
    updated timestamp without time zone,
    CONSTRAINT surveyquestionanswers_pkey PRIMARY KEY (surveyid, questionid)
);