
CREATE TABLE IF NOT EXISTS activeqmembersdata (
    keyid varchar(100) NOT NULL,
    queueid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    activemembers integer,
    memberusers integer,
    onqueueusers integer,
    offqueueusers integer,
    updated timestamp without time zone,
    CONSTRAINT activeqmembersdata_pkey PRIMARY KEY (keyid)
);

COMMENT ON COLUMN activeqmembersdata.activemembers IS 'Count of Active Members';
COMMENT ON COLUMN activeqmembersdata.keyid IS 'Primary Key';
COMMENT ON COLUMN activeqmembersdata.memberusers IS 'Count of Members';
COMMENT ON COLUMN activeqmembersdata.offqueueusers IS 'Count of Off-Queue Members';
COMMENT ON COLUMN activeqmembersdata.onqueueusers IS 'Count of On-Queue Members';
COMMENT ON COLUMN activeqmembersdata.queueid IS '';
COMMENT ON COLUMN activeqmembersdata.startdate IS 'Start Time (UTC)';
COMMENT ON COLUMN activeqmembersdata.startdateltc IS 'Start Time (LTC)';
COMMENT ON COLUMN activeqmembersdata.updated IS 'Date Updated';
COMMENT ON TABLE activeqmembersdata IS 'Historical Active Membership of Queues';
COMMENT ON CONSTRAINT activeqmembersdata_pkey ON activeqmembersdata IS 'Primary Key Constraint';