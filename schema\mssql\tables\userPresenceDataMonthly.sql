IF dbo.csg_table_exists('userPresenceDataMonthly') = 0
CREATE TABLE [userPresenceDataMonthly](
    [keyid] [nvarchar](255) NOT NULL,
    [id] [nvarchar](50),
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [timetype] [nvarchar](50),
    [systempresenceid] [nvarchar](50),
    [presenceid] [nvarchar](50),
    [presencetime] [decimal](20, 2),
    [routingid] [nvarchar](50),
    [routingtime] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userPresenceDataMonthly] PRIMARY KEY ([keyid])
);
IF dbo.csg_column_exists('userPresenceDataMonthly', 'timetype') = 0
    ALTER TABLE userPresenceDataMonthly ADD timetype [nvarchar](50);