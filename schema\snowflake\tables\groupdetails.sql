-- Create the table if not exists with the specified columns
CREATE TABLE IF NOT EXISTS groupdetails (
    id STRING NOT NULL,
    name STRING,
    description STRING,
    membercount INTEGER,
    state STRING,
    type STRING,
    selfuri STRING,
    updated TIMESTAMP_LTZ,
    PRIMARY KEY (id)
);

-- Drop the email column if it exists
ALTER TABLE groupdetails DROP COLUMN IF EXISTS email;

-- Add the scription column
ALTER TABLE groupdetails ADD COLUMN IF NOT EXISTS description STRING;