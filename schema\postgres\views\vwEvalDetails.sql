CREATE
OR REPLACE VIEW vwEvalDetails AS
SELECT
    id,
    evaluationid,
    evaluationformid,
    evaluationname,
    questiongroupid,
    questiongroupname,
    questiongroupToHighest,
    questiongroupToNA,
    questiongroupwieght,
    questiongroupmanwieght,
    questionid,
    questiontext,
    questionhelptext,
    quesiontype,
    questionnaenabled,
    questioncommentsreq,
    questioniskill,
    questioniscritical,
    questionanwserid,
    questionanswertext,
    questionanswervalue
FROM
    evalDetails;

COMMENT ON COLUMN vwEvalDetails.evaluationformid IS 'Evaluation Form GUID'; 
COMMENT ON COLUMN vwEvalDetails.evaluationid IS 'Evaluation GUID'; 
COMMENT ON COLUMN vwEvalDetails.evaluationname IS 'Evaluation Name'; 
COMMENT ON COLUMN vwEvalDetails.id IS 'Primary Key'; 
COMMENT ON COLUMN vwEvalDetails.questionanswervalue IS 'Question Value'; 
COMMENT ON COLUMN vwEvalDetails.questionanswertext IS 'Question Answer Text'; 
COMMENT ON COLUMN vwEvalDetails.questioncommentsreq IS 'Question Comments Required (True/False)'; 
COMMENT ON COLUMN vwEvalDetails.questiongroupid IS 'Question Group GUID'; 
COMMENT ON COLUMN vwEvalDetails.questiongroupmanwieght IS 'Question Group Man Weight'; 
COMMENT ON COLUMN vwEvalDetails.questiongroupname IS 'Question Group Name'; 
COMMENT ON COLUMN vwEvalDetails.questiongroupToHighest IS 'Question Group To Highest (True/False)'; 
COMMENT ON COLUMN vwEvalDetails.questiongroupwieght IS 'Question Group Wieght'; 
COMMENT ON COLUMN vwEvalDetails.questionhelptext IS 'Question Help Text'; 
COMMENT ON COLUMN vwEvalDetails.questionid IS 'Question Answer Value'; 
COMMENT ON COLUMN vwEvalDetails.questioniscritical IS 'Question Is a Critical Question (True/False)'; 
COMMENT ON COLUMN vwEvalDetails.questionanwserid IS 'Question Answer GUID'; 
COMMENT ON COLUMN vwEvalDetails.questioniskill IS 'Question Is a Kill Question (True/False)'; 
COMMENT ON COLUMN vwEvalDetails.questionnaenabled IS 'Question Enabled (True/False)'; 
COMMENT ON COLUMN vwEvalDetails.questiontext IS 'Question Text'; 
COMMENT ON VIEW vwEvalDetails IS 'See EvalDetails- Expands all the GUIDs with their lookups';