IF dbo.csg_table_exists('userRealTimeData') = 0
CREATE TABLE [userRealTimeData](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](255),
    [jabberId] [nvarchar](100),
    [email] [nvarchar](255),
    [state] [nvarchar](50),
    [title] [nvarchar](255),
    [username] [nvarchar](255),
    [department] [nvarchar](255),
    [routingstatus] [nvarchar](20),
    [routstarttime] [datetime],
    [systempresence] [nvarchar](50),
    [presenceid] [nvarchar](50),
    [presstarttime] [datetime],
    [cccallactive] [int],
    [cccallacw] [int],
    [othcallactive] [int],
    [othcallacw] [int],
    [cbcallactive] [int],
    [cbcallacw] [int],
    [cbothcallactive] [int],
    [cbothcallacw] [int],
    [ccemailactive] [int],
    [ccemailacw] [int],
    [othemailactive] [int],
    [othemailacw] [int],
    [ccchatactive] [int],
    [ccchatacw] [int],
    [othchatactive] [int],
    [othchatacw] [int],
    [adherencestate] [nvarchar](50),
    [adherencestarttime] [datetime],
    [impact] [nvarchar](50),
    [scheduledactivitycategory] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_userRealTimeData] PRIMARY KEY ([id])
);
