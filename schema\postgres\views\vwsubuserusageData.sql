CREATE
OR REPLACE VIEW vwsubuserusageData AS
SELECT
    clientid,
    rowdate,
    clientname,
    organizationid,
    userid,
    status200,
    status300,
    status400,
    status500,
    status429,
    updated
FROM
    oauthusageData;

COMMENT ON COLUMN vwsubuserusagedata.clientid IS 'Client GUID';
COMMENT ON COLUMN vwsubuserusagedata.rowdate IS 'Date';
COMMENT ON COLUMN vwsubuserusagedata.clientname IS 'Client Name';
COMMENT ON COLUMN vwsubuserusagedata.organizationid IS 'Organization GUID';
COMMENT ON COLUMN vwsubuserusagedata.userid IS 'User GUID';
COMMENT ON COLUMN vwsubuserusagedata.status200 IS 'Count of HTTP status code 200';
COMMENT ON COLUMN vwsubuserusagedata.status300 IS 'Count of HTTP status code 300';
COMMENT ON COLUMN vwsubuserusagedata.status400 IS 'Count of HTTP status code 400';
COMMENT ON COLUMN vwsubuserusagedata.status500 IS 'Count of HTTP status code 500';
COMMENT ON COLUMN vwsubuserusagedata.status429 IS 'Count of HTTP status code 429';
COMMENT ON COLUMN vwsubuserusagedata.updated IS 'Timestamp indicating when the data was last updated';
COMMENT ON VIEW vwsubuserusagedata IS 'View containing usage data for sub-users, including HTTP status code counts';

