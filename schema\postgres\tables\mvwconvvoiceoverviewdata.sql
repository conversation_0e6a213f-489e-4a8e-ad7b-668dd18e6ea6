CREATE TABLE IF NOT EXISTS mvwconvvoiceoverviewdata (
    conversationid varchar(50) NOT NULL UNIQUE,
    sentimentscore numeric(20, 2),
    sentimenttrend numeric(20, 2),
    agentdurationpercentage numeric(20, 2),
    customerdurationpercentage numeric(20, 2),
    silencedurationpercentage numeric(20, 2),
    overtalkdurationpercentage numeric(20, 2),
    overtalkcount int4 NULL,
    ivrdurationpercentage numeric(20, 2),
    acddurationpercentage numeric(20, 2),
    otherdurationpercentage numeric(20, 2),
    conversationstartdate timestamp NULL,
    conversationstartdateltc timestamp NULL,
    conversationenddate timestamp NULL,
    conversationenddateltc timestamp NULL,
    ttalkcomplete int4 NULL,
    ani varchar(400),
    dnis varchar(400),
    firstmediatype varchar(50),
    divisionid varchar(50),

    firstqueueid varchar(50),
    firstqueuename varchar(255),
    lastqueueid varchar(50),
    lastqueuename varchar(255),
    firstagentid varchar(50),
    firstagentname varchar(200),
    firstagentdept varchar(200),
    firstagentmanagerid varchar(50),
    firstagentmanagername varchar(200),
    lastagentid varchar(50),
    lastagentname varchar(200),
    lastagentdept varchar(200),
    lastagentmanagerid varchar(50),
    lastagentmanagername varchar(200),
    firstwrapupcode varchar(255),
    firstwrapupname varchar(255),
    lastwrapupcode varchar(255),
    lastwrapupname varchar(255) NULL,
    divisionname varchar(255)
);

ALTER TABLE mvwconvvoiceoverviewdata 
ADD column IF NOT exists divisionname varchar(255);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_agent ON mvwconvvoiceoverviewdata USING btree (firstagentid);

CREATE UNIQUE INDEX IF NOT EXISTS mvwconvvoiceoverview_conv ON mvwconvvoiceoverviewdata USING btree (conversationid);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_division ON mvwconvvoiceoverviewdata USING btree (divisionid);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_end ON mvwconvvoiceoverviewdata USING btree (conversationenddate);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_endltc ON mvwconvvoiceoverviewdata USING btree (conversationenddateltc);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_queue ON mvwconvvoiceoverviewdata USING btree (firstqueueid);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_start ON mvwconvvoiceoverviewdata USING btree (conversationstartdate);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_startltc ON mvwconvvoiceoverviewdata USING btree (conversationstartdateltc);

CREATE INDEX IF NOT EXISTS mvwconvvoiceoverview_wrapup ON mvwconvvoiceoverviewdata USING btree (firstwrapupcode);