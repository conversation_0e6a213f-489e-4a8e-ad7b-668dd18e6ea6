CREATE OR REPLACE VIEW vwRealTimeQueueConv AS
SELECT
    qc.conversationid,
    qc.media,
    qc.actingas,
    (
        DATEDIFF(
            'second',
            TO_TIMESTAMP_NTZ(qc.startdate),
            CURRENT_TIMESTAMP()
        )
    ) as statusSecs,
    (
        DATEDIFF(
            'second',
            TO_TIMESTAMP_NTZ(qc.startdate),
            CURRENT_TIMESTAMP()
        )
    ) / 86400.00 as statusDays,
    qc.skill1,
    sk1.name as SkillName1,
    qc.skill2,
    sk2.name as SkillName2,
    qc.skill3,
    sk3.name as SkillName3,
    qc.initialpriority,
    qc.participantname,
    qc.queueid,
    qd.name as queuename,
    qd.divisionid as division,
    qc.userid,
    ud.agentname,
    ud.department,
    ud.managername,
    qc.direction,
    qc.ani,
    qc.dnis,
    qc.requestedrout1,
    qc.requestedrout2,
    qc.usedrout
FROM
    queuerealtimeconvData qc
    left outer join skillDetails as sk1 on sk1.id = qc.skill1
    left outer join skillDetails as sk2 on sk2.id = qc.skill2
    left outer join skillDetails as sk3 on sk3.id = qc.skill3
    left outer join queuedetails as qd on qd.id = qc.queueid
    left outer join vwUserDetail as ud on ud.id = qc.userid;