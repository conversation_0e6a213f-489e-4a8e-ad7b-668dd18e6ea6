CREATE TABLE IF NOT EXISTS flowoutcomedata (
    keyid varchar(255) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    flowoutcomestartdate timestamp without time zone,
    flowoutcomestartdateltc timestamp without time zone,
    flowoutcomeenddate timestamp without time zone,
    flowoutcomeenddateltc timestamp without time zone,
    flowid varchar(50),
    flowname varchar(255),
    flowversion numeric(20, 2),
    flowtype varchar(50),
    flowoutcome varchar(50),
    flowoutcomeid varchar(50),
    flowoutcomevalue varchar(50),
    updated timestamp without time zone,
    CONSTRAINT flowoutcomedata_pkey PRIMARY KEY (keyid, conversationstartdate)
) PARTITION BY RANGE (conversationstartdate);

DROP TABLE IF EXISTS flowoutcomedata_default;

-- Add comments

COMMENT ON COLUMN flowoutcomedata.conversationenddateltc IS 'Conversation End Date (LTC)'; 
COMMENT ON COLUMN flowoutcomedata.conversationenddate IS 'Conversation End Date (UTC)'; 
COMMENT ON COLUMN flowoutcomedata.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN flowoutcomedata.conversationstartdateltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON COLUMN flowoutcomedata.conversationstartdate IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomestartdate IS 'Conversation Flow Outcome Start Date (UTC)'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomestartdateltc IS 'Conversation Flow Outcome Start Date (LTC)'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomeenddate IS 'Conversation Flow Outcome End Date (UTC)'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomeenddateltc IS 'Conversation Flow Outcome end Date (LTC)'; 
COMMENT ON COLUMN flowoutcomedata.flowid IS 'Conversation Segment Flow GUID'; 
COMMENT ON COLUMN flowoutcomedata.flowname IS 'Conversation Segment Flow Name'; 
COMMENT ON COLUMN flowoutcomedata.flowtype IS 'Conversation Segment Flow Type'; 
COMMENT ON COLUMN flowoutcomedata.flowversion IS 'Conversation Segment Flow Version'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcome IS 'Conversation Flow Outcome GUID Combined'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomeid IS 'Conversation Flow Outcome GUID'; 
COMMENT ON COLUMN flowoutcomedata.flowoutcomevalue IS 'Conversation Flow Outcome Value'; 
COMMENT ON COLUMN flowoutcomedata.updated IS 'Row Updated Date'; 
COMMENT ON TABLE flowoutcomedata IS 'Conversation Flow Outcome Data'; 
