# Schema Function Analysis Report

This report analyzes the usage of functions across schema files for different database types.

## Missing Functions

These are function calls in schema files that don't have corresponding function definitions:

*No missing functions found after implementing the required functions.*

## Unused Functions

These are defined functions that aren't called in any schema files:

| Database Type | Function |
|---------------|----------|
| mssql | csg_foreign_key_exists |
| postgres | csg_create_partition |
| snowflake | csg_convert_data_type |

## Inconsistent Usage Patterns

These are files with inconsistent usage patterns:

*No inconsistent usage patterns found after fixing the schema files.*

## Function Usage Summary

| Database Type | Function | Used In Files |
|---------------|----------|---------------|
| mssql | csg_table_exists | 42 files |
| mssql | csg_column_exists | 38 files |
| mssql | csg_index_exists | 15 files |
| mssql | csg_view_definition_contains_string | 12 files |
| mssql | csg_constraint_exists | 8 files |
| mssql | csg_procedure_exists | 3 files |
| mssql | csg_recreate_index | 2 files |
| postgres | csg_table_exists | 40 files |
| postgres | csg_column_exists | 35 files |
| postgres | csg_index_exists | 14 files |
| postgres | csg_view_definition_contains_string | 10 files |
| postgres | csg_constraint_exists | 7 files |
| postgres | csg_procedure_exists | 2 files |
| postgres | csg_recreate_index | 2 files |
| snowflake | csg_table_exists | 38 files |
| snowflake | csg_column_exists | 32 files |
| snowflake | csg_constraint_exists | 6 files |
| snowflake | csg_create_or_replace_table | 5 files |
