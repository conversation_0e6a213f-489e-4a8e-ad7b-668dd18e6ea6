IF dbo.csg_table_exists('userPresenceData') = 0
CREATE TABLE [userPresenceData](
    [keyid] [nvarchar](255) NOT NULL,
    [id] [nvarchar](50),
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [timetype] [nvarchar](20),
    [systempresenceid] [nvarchar](50),
    [presenceid] [nvarchar](50),
    [presencetime] [decimal](20, 2),
    [routingid] [nvarchar](50),
    [routingtime] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userPresenceData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('userPresenceDataRoutingId', 'userPresenceData') = 0
CREATE INDEX [userPresenceDataRoutingId] ON [userPresenceData] ([routingid]);
IF dbo.csg_index_exists('userPresenceDataStartDate', 'userPresenceData') = 0
CREATE INDEX [userPresenceDataStartDate] ON [userPresenceData] ([startdate]);
IF dbo.csg_index_exists('userPresenceDataStartDateLTC', 'userPresenceData') = 0
CREATE INDEX [userPresenceDataStartDateLTC] ON [userPresenceData] ([startdateltc]);
IF dbo.csg_index_exists('userPresenceDataUserId', 'userPresenceData') = 0
CREATE INDEX [userPresenceDataUserId] ON [userPresenceData] ([userid]);
IF dbo.csg_index_exists('userPresencePresenceId', 'userPresenceData') = 0
CREATE INDEX [userPresencePresenceId] ON [userPresenceData] ([presenceid]);

IF dbo.csg_column_exists('userPresenceData', 'timetype') = 0
    ALTER TABLE userPresenceData ADD timetype [nvarchar](20);