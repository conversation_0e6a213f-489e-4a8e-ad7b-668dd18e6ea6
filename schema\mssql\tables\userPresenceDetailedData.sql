IF dbo.csg_table_exists('userPresenceDetailedData') = 0
CREATE TABLE [userPresenceDetailedData](
    [keyid] [nvarchar](255) NOT NULL,
    [userid] [nvarchar](50),
    [starttime] [datetime],
    [starttimeltc] [datetime],
    [endtime] [datetime],
    [endtimeltc] [datetime],
    [systempresence] [nvarchar](50),
    [orgpresence] [nvarchar](50),
    [routingstatus] [nvarchar](50),
    [timeinstate] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userPresenceDetailedData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('userPresenceDetEnd', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetEnd] ON [userPresenceDetailedData] ([endtime]);
IF dbo.csg_index_exists('userPresenceDetEndLTC', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetEndLTC] ON [userPresenceDetailedData] ([endtimeltc]);
IF dbo.csg_index_exists('userPresenceDetOrgPres', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetOrgPres] ON [userPresenceDetailedData] ([orgpresence]);
IF dbo.csg_index_exists('userPresenceDetRouting', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetRouting] ON [userPresenceDetailedData] ([routingstatus]);
IF dbo.csg_index_exists('userPresenceDetStart', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetStart] ON [userPresenceDetailedData] ([starttime]);
IF dbo.csg_index_exists('userPresenceDetStartLTC', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetStartLTC] ON [userPresenceDetailedData] ([starttimeltc]);
IF dbo.csg_index_exists('userPresenceDetSysPres', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetSysPres] ON [userPresenceDetailedData] ([systempresence]);
IF dbo.csg_index_exists('userPresenceDetUser', 'userPresenceDetailedData') = 0
CREATE INDEX [userPresenceDetUser] ON [userPresenceDetailedData] ([userid]);
