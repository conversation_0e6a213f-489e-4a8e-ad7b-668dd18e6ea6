DROP VIEW IF EXISTS vwheadcountforecast;

CREATE
OR REPLACE VIEW vwheadcountforecast AS
select
	hcf.businessunitid,
	bu.name as businessunitname,
	hcf.planninggroup as planninggroup_id,
	pg."name" as planninggroup_name,
	pgq."name" as planninggroup_queuename,
	hcf.scheduleid,
	sd.shorttermforecastid,
	sd.description as scheduledesc,
	sd.published as sched_published,
	hcf.weekdate,
	hcf.startdate,
	hcf.startdateltc,
	hcf.requiredperinterval,
	hcf.requiredwithoutshrinkageperinterval,
	hcf.updated
from 
	headcountforecastdata hcf

LEFT JOIN budetails bu  ON hcf.businessunitid ::text = bu.id::text
LEFT JOIN scheduledetails sd ON hcf.scheduleid ::text = sd.scheduleid ::text
LEFT JOIN planninggroupdetails pg  ON hcf.planninggroup ::text = pg.id::text
LEFT JOIN queuedetails pgq ON pg.queueid ::text = pgq.id ::text;

COMMENT ON COLUMN vwheadcountforecast.businessunitid IS 'Business Unit GUID';
COMMENT ON COLUMN vwheadcountforecast.businessunitname IS 'Business Unit Name';
COMMENT ON COLUMN vwheadcountforecast.planninggroup_id IS 'Planning Group GUID';
COMMENT ON COLUMN vwheadcountforecast.planninggroup_name IS 'Planning Group Name';
COMMENT ON COLUMN vwheadcountforecast.planninggroup_queuename IS 'Planning Group Queue Name';
COMMENT ON COLUMN vwheadcountforecast.scheduleid IS 'Schedule GUID';
COMMENT ON COLUMN vwheadcountforecast.shorttermforecastid IS 'Short-Term Forecast GUID';
COMMENT ON COLUMN vwheadcountforecast.scheduledesc IS 'Schedule Description';
COMMENT ON COLUMN vwheadcountforecast.sched_published IS 'Is Schedule Published?';
COMMENT ON COLUMN vwheadcountforecast.weekdate IS 'Week Date';
COMMENT ON COLUMN vwheadcountforecast.startdate IS 'Start Date (UTC)';
COMMENT ON COLUMN vwheadcountforecast.startdateltc IS 'Start Date (LTC)';
COMMENT ON COLUMN vwheadcountforecast.requiredperinterval IS 'Required Per Interval';
COMMENT ON COLUMN vwheadcountforecast.requiredwithoutshrinkageperinterval IS 'Required Without Shrinkage Per Interval';
COMMENT ON COLUMN vwheadcountforecast.updated IS 'Last Updated';
COMMENT ON VIEW vwheadcountforecast IS 'See HeadCountForecast - Expands all the GUIDs with their lookups'; 