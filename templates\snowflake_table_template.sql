-- Snowflake Table Template
-- Use this template when creating new tables in Snowflake

-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS table_name (
    id varchar(50) NOT NULL,
    name varchar(100),
    description text,
    created_date timestamp without time zone,
    updated_date timestamp without time zone,
    CONSTRAINT table_name_pkey PRIMARY KEY (id)
);

-- Add columns if they don't exist
ALTER TABLE table_name 
ADD COLUMN IF NOT EXISTS new_column varchar(100);

-- Note: Snowflake doesn't support explicit index creation as it uses automatic indexing
-- This is just a comment to document which columns would benefit from clustering
-- CLUSTER BY (id, created_date)

-- Example of adding a foreign key
ALTER TABLE table_name
ADD CONSTRAINT IF NOT EXISTS fk_table_name_reference_table
FOREIGN KEY (reference_id) REFERENCES reference_table(id);

-- Example of a JavaScript procedure to check and update data
CREATE OR REPLACE PROCEDURE check_and_update_table_name()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  // Query to check for specific conditions
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) AS count FROM table_name WHERE condition = true`
  });
  
  resultSet.next();
  var count = resultSet.getColumnValue(1);
  
  if (count > 0) {
    // Perform update if condition is met
    snowflake.execute({ 
      sqlText: `UPDATE table_name SET column = value WHERE condition = true`
    });
    return 'Updated ' + count + ' records';
  } else {
    return 'No records needed updating';
  }
$$;
