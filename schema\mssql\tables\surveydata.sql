IF dbo.csg_table_exists('surveyData') = 0
CREATE TABLE [surveyData] (
    [surveyId]          [nvarchar](50) NOT NULL,
    [conversationId]    [nvarchar](50) NOT NULL,
    [surveyFormId]      [nvarchar](50),
    [surveyName]        [nvarchar](200),
    [agentId]           [nvarchar](50),
    [agentTeamId]       [nvarchar](50),
    [queueId]           [nvarchar](50),
    [status]            [nvarchar](20),
    [totalScore]        [decimal](20, 2),
    [completedDate]     [datetime],
    [completedDateLtc]  [datetime],
    [updated]           [datetime],
    [lastPoll]          [datetime],
    CONSTRAINT [PK_surveyData] PRIMARY KEY ([surveyId])
);

IF dbo.csg_index_exists('surveydata_conversationid', 'surveyData') = 0
CREATE INDEX surveydata_conversationid ON surveyData (conversationId);
