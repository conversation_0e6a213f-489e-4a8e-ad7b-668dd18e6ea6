IF dbo.csg_table_exists('timeoffData') = 0
CREATE TABLE [timeoffData](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),
    [businessunitdate] [datetime],
    [length] [int],
    [description] [nvarchar](200),
    [activitycode] [nvarchar](50),
    [paid] [bit],
    [timeoffrequestid] [nvarchar](50),
    [isfulldayrequest] [bit],
    [updated] [datetime],
    CONSTRAINT [PK_timeoffData] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('timeoffData', 'isfulldayrequest') = 0
ALTER TABLE dbo.timeoffData ADD isfulldayrequest bit NULL;