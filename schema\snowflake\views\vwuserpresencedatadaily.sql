DROP VIEW IF EXISTS vwuserpresencedatadaily;

CREATE
OR REPLACE VIEW vwuserpresencedatadaily AS
SELECT
    userpresencedatadaily.id,
    userpresencedatadaily.userid,
    userdetail.name AS agentname,
    userdetail.managerid,
    userdetail.managername,
    userdetail.divisionid,
    dd.name as division_name,
    userpresencedatadaily.startdate,
    DATEADD(HOUR, 
    CASE 
        WHEN get_current_timezone() = 'UTC' THEN 10  -- Adjust for Australia/Sydney timezone
        ELSE 0
    END, 
    CONVERT_TIMEZONE('UTC', 'Australia/Sydney', userpresencedatadaily.startdate)
    ) AS startdateusrtz,
    userpresencedatadaily.timetype,
    userpresencedatadaily.systempresenceid,
    IFF(REGEXP_LIKE(userpresencedatadaily.systempresenceid, '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'), presencedetails.systempresence, userpresencedatadaily.systempresenceid) AS systempresencename,
    userpresencedatadaily.presenceid,
    CASE
        userpresencedatadaily.timetype
        WHEN 'Presence' :: text THEN userpresencedatadaily.presencetime
        ELSE 0 :: numeric
    END AS presencetime,
    userpresencedatadaily.presencetime / 86400.00 AS presencetimeday,
    userpresencedatadaily.routingid,
    CASE
        userpresencedatadaily.timetype
        WHEN 'Routing' :: text THEN userpresencedatadaily.presencetime
        ELSE 0 :: numeric
    END AS routingtime,
    userpresencedatadaily.routingtime / 86400.00 AS routingtimeday,
    userpresencedatadaily.updated
FROM
    userpresencedatadaily userpresencedatadaily
    LEFT JOIN vwuserdetail userdetail ON userdetail.id :: text = userpresencedatadaily.userid :: text
    LEFT join divisiondetails dd ON dd.id :: text = userdetail.divisionid :: text
    LEFT JOIN presencedetails ON presencedetails.id = userpresencedatadaily.systempresenceid;
    