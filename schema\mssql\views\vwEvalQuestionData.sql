CREATE
OR ALTER VIEW [vwEvalQuestionData] AS
SELECT
    DISTINCT evald.conversationid,
    qd.evaluationformid + '|' + qd.questiongroupid + '|' + qd.questionid as <PERSON><PERSON><PERSON><PERSON>,
    qd.evaluationid,
    qd.evaluationformid,
    qd.questiongroupid,
    ed.questiongroupname,
    ed.evaluationname,
    qd.questionid,
    ed.questiontext,
    ed.questionhelptext,
    qd.answerid,
    ed.quesiontype,
    qd.score,
    ed.questionanswervalue as maxScore,
    qd.markedna,
    qd.failedkillquestions,
    qd.comments,
    qd.updated
FROM
    evalQuestionData qd
    inner join evalDetails ed on ed.questiongroupid = qd.questiongroupid
    and ed.evaluationformid = qd.evaluationformid
    and ed.questionanwserid = qd.answerid
    inner join evalData evald on evald.evaluationid = qd.evaluationid;