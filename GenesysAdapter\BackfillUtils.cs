using GCData;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    #nullable enable
    public class BackfillUtils

    {
        public bool ConfigureBackfill(string currentJob, string SyncType, GCGetData GCData, DBUtils.DBUtils DBAdapter, ILogger? _logger)
        {
            if (GCData.DateToSyncFrom == DateTime.MaxValue)
                return false;

            // Make sure the backfill job doesn't run over the current job.
            DateTime currentSyncWatermark = DBAdapter.GetSyncLastUpdate(currentJob);
            // DateToSyncFrom is the backfill watermark.
            if (currentSyncWatermark.Subtract(GCData.DateToSyncFrom) <= TimeSpan.FromDays(2))
            {
                _logger?.LogWarning(
                    "Backfill job has caught up to the current job, shutting down. Backfill watermark {0}, current watermark {1}",
                    GCData.DateToSyncFrom,
                    currentSyncWatermark);
                GCData.DateToSyncFrom = DateTime.MaxValue;
                GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom, SyncType);

                return false;
            }
            return true;
        }

    }
    #nullable restore
}
