-- Snowflake Install Functions
-- This file contains utility functions for Snowflake schema management

-- Function to get the current timezone
CREATE OR REPLACE FUNCTION get_current_timezone()
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
$$;

-- Function to check if a table exists
CREATE OR REPLACE FUNCTION csg_table_exists(tablename STRING)
RETURNS BOOLEAN
LANGUAGE JAVASCRIPT
AS
$$
    var sql = "SELECT COUNT(*) AS table_count " +
              "FROM information_schema.tables " +
              "WHERE table_schema = CURRENT_SCHEMA() " +
              "AND LOWER(table_name) = LOWER('" + TABLENAME + "')";
    
    var stmt = snowflake.createStatement({sqlText: sql});
    var result = stmt.execute();
    
    if (result.next()) {
        return result.getColumnValue(1) > 0;
    }
    
    return false;
$$;

-- Function to check if a column exists in a table
CREATE OR REPLACE FUNCTION csg_column_exists(tablename STRING, columnname STRING)
RETURNS BOOLEAN
LANGUAGE JAVASCRIPT
AS
$$
    var sql = "SELECT COUNT(*) AS column_count " +
              "FROM information_schema.columns " +
              "WHERE table_schema = CURRENT_SCHEMA() " +
              "AND LOWER(table_name) = LOWER('" + TABLENAME + "') " +
              "AND LOWER(column_name) = LOWER('" + COLUMNNAME + "')";
    
    var stmt = snowflake.createStatement({sqlText: sql});
    var result = stmt.execute();
    
    if (result.next()) {
        return result.getColumnValue(1) > 0;
    }
    
    return false;
$$;

-- Function to check if a constraint exists
CREATE OR REPLACE FUNCTION csg_constraint_exists(constraintname STRING, tablename STRING)
RETURNS BOOLEAN
LANGUAGE JAVASCRIPT
AS
$$
    var sql = "SELECT COUNT(*) AS constraint_count " +
              "FROM information_schema.table_constraints " +
              "WHERE table_schema = CURRENT_SCHEMA() " +
              "AND LOWER(table_name) = LOWER('" + TABLENAME + "') " +
              "AND LOWER(constraint_name) = LOWER('" + CONSTRAINTNAME + "')";
    
    var stmt = snowflake.createStatement({sqlText: sql});
    var result = stmt.execute();
    
    if (result.next()) {
        return result.getColumnValue(1) > 0;
    }
    
    return false;
$$;

-- Procedure to standardize data type conversions
CREATE OR REPLACE PROCEDURE csg_convert_data_type(
    tablename STRING,
    columnname STRING,
    new_data_type STRING
)
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    // Check if the column exists
    var checkSql = "SELECT COUNT(*) AS column_count " +
                  "FROM information_schema.columns " +
                  "WHERE table_schema = CURRENT_SCHEMA() " +
                  "AND LOWER(table_name) = LOWER('" + TABLENAME + "') " +
                  "AND LOWER(column_name) = LOWER('" + COLUMNNAME + "')";
    
    var checkStmt = snowflake.createStatement({sqlText: checkSql});
    var checkResult = checkStmt.execute();
    
    if (checkResult.next() && checkResult.getColumnValue(1) > 0) {
        // Column exists, alter its data type
        var alterSql = "ALTER TABLE " + TABLENAME + " " +
                      "ALTER COLUMN " + COLUMNNAME + " " +
                      "SET DATA TYPE " + NEW_DATA_TYPE;
        
        try {
            snowflake.execute({sqlText: alterSql});
            return "Successfully converted " + COLUMNNAME + " to " + NEW_DATA_TYPE;
        } catch (err) {
            return "Error converting data type: " + err.message;
        }
    } else {
        return "Column " + COLUMNNAME + " does not exist in table " + TABLENAME;
    }
$$;

-- Procedure to safely create or replace a table
CREATE OR REPLACE PROCEDURE csg_create_or_replace_table(
    tablename STRING,
    table_definition STRING
)
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    // Check if the table exists
    var checkSql = "SELECT COUNT(*) AS table_count " +
                  "FROM information_schema.tables " +
                  "WHERE table_schema = CURRENT_SCHEMA() " +
                  "AND LOWER(table_name) = LOWER('" + TABLENAME + "')";

    var checkStmt = snowflake.createStatement({sqlText: checkSql});
    var checkResult = checkStmt.execute();

    if (checkResult.next() && checkResult.getColumnValue(1) > 0) {
        // Table exists, create a backup and replace it
        var backupTableName = TABLENAME + "_backup_" + new Date().getTime();

        try {
            // Create backup
            snowflake.execute({sqlText: "CREATE TABLE " + backupTableName + " CLONE " + TABLENAME});

            // Drop existing table
            snowflake.execute({sqlText: "DROP TABLE " + TABLENAME});

            // Create new table
            snowflake.execute({sqlText: "CREATE TABLE " + TABLENAME + " " + TABLE_DEFINITION});

            return "Successfully replaced table " + TABLENAME + " (backup created as " + backupTableName + ")";
        } catch (err) {
            return "Error replacing table: " + err.message;
        }
    } else {
        // Table doesn't exist, create it
        try {
            snowflake.execute({sqlText: "CREATE TABLE " + TABLENAME + " " + TABLE_DEFINITION});
            return "Successfully created table " + TABLENAME;
        } catch (err) {
            return "Error creating table: " + err.message;
        }
    }
$$;

-- Generic procedure to safely rename columns in Snowflake tables
-- Handles column renaming with data preservation and comprehensive error handling
-- Supports any column rename scenario with parameterized inputs
CREATE OR REPLACE PROCEDURE csg_rename_column_safe(
    tablename STRING,
    old_column_name STRING,
    new_column_name STRING,
    column_datatype STRING
)
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    try {
        // Validate input parameters
        if (!TABLENAME || !OLD_COLUMN_NAME || !NEW_COLUMN_NAME || !COLUMN_DATATYPE) {
            return "Error: All parameters (tablename, old_column_name, new_column_name, column_datatype) are required";
        }

        // Check if the old column exists
        var checkOldColumnSql = "SELECT COUNT(*) AS column_count " +
                               "FROM information_schema.columns " +
                               "WHERE table_schema = CURRENT_SCHEMA() " +
                               "AND LOWER(table_name) = LOWER('" + TABLENAME + "') " +
                               "AND column_name = '" + OLD_COLUMN_NAME + "'";

        var checkOldStmt = snowflake.createStatement({sqlText: checkOldColumnSql});
        var checkOldResult = checkOldStmt.execute();

        var hasOldColumn = false;
        if (checkOldResult.next()) {
            hasOldColumn = checkOldResult.getColumnValue(1) > 0;
        }

        // Check if the new column exists
        var checkNewColumnSql = "SELECT COUNT(*) AS column_count " +
                               "FROM information_schema.columns " +
                               "WHERE table_schema = CURRENT_SCHEMA() " +
                               "AND LOWER(table_name) = LOWER('" + TABLENAME + "') " +
                               "AND LOWER(column_name) = LOWER('" + NEW_COLUMN_NAME + "')";

        var checkNewStmt = snowflake.createStatement({sqlText: checkNewColumnSql});
        var checkNewResult = checkNewStmt.execute();

        var hasNewColumn = false;
        if (checkNewResult.next()) {
            hasNewColumn = checkNewResult.getColumnValue(1) > 0;
        }

        // Migration logic based on column existence
        if (hasOldColumn && !hasNewColumn) {
            // Old column exists, new doesn't - need to migrate
            // Step 1: Add new column with specified data type
            var addColumnSql = "ALTER TABLE " + TABLENAME + " ADD COLUMN " + NEW_COLUMN_NAME + " " + COLUMN_DATATYPE;
            snowflake.execute({sqlText: addColumnSql});

            // Step 2: Copy data from old column to new column
            // Use quoted identifier for old column in case it has special characters or mixed case
            var copyDataSql = "UPDATE " + TABLENAME + " SET " + NEW_COLUMN_NAME + " = \"" + OLD_COLUMN_NAME + "\"";
            snowflake.execute({sqlText: copyDataSql});

            // Step 3: Drop old column
            var dropColumnSql = "ALTER TABLE " + TABLENAME + " DROP COLUMN \"" + OLD_COLUMN_NAME + "\"";
            snowflake.execute({sqlText: dropColumnSql});

            return "Successfully migrated " + OLD_COLUMN_NAME + " to " + NEW_COLUMN_NAME + " for table " + TABLENAME;

        } else if (hasOldColumn && hasNewColumn) {
            // Both columns exist - copy data and drop old column
            var updateSql = "UPDATE " + TABLENAME + " SET " + NEW_COLUMN_NAME + " = \"" + OLD_COLUMN_NAME + "\" WHERE " + NEW_COLUMN_NAME + " IS NULL";
            snowflake.execute({sqlText: updateSql});

            var dropOldSql = "ALTER TABLE " + TABLENAME + " DROP COLUMN \"" + OLD_COLUMN_NAME + "\"";
            snowflake.execute({sqlText: dropOldSql});

            return "Completed migration cleanup for table " + TABLENAME + " (removed old " + OLD_COLUMN_NAME + " column)";

        } else if (!hasOldColumn && hasNewColumn) {
            // Only new column exists - migration already complete
            return "Migration already complete for table " + TABLENAME + " (" + NEW_COLUMN_NAME + " column exists)";

        } else {
            // Neither column exists - add the new column
            var addNewSql = "ALTER TABLE " + TABLENAME + " ADD COLUMN " + NEW_COLUMN_NAME + " " + COLUMN_DATATYPE;
            snowflake.execute({sqlText: addNewSql});
            return "Added " + NEW_COLUMN_NAME + " column to table " + TABLENAME;
        }

    } catch (err) {
        return "Error during column migration for table " + TABLENAME + " (" + OLD_COLUMN_NAME + " -> " + NEW_COLUMN_NAME + "): " + err.message;
    }
$$;

-- Legacy wrapper procedure for backward compatibility
-- Maintains existing moduleId->moduleid migration functionality
CREATE OR REPLACE PROCEDURE csg_migrate_moduleid_column(tablename STRING)
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    // Call the generic column rename function with moduleId-specific parameters
    var stmt = snowflake.createStatement({
        sqlText: "CALL csg_rename_column_safe(?, 'moduleId', 'moduleid', 'VARCHAR(50)')",
        binds: [TABLENAME]
    });

    var result = stmt.execute();
    if (result.next()) {
        return result.getColumnValue(1);
    }

    return "Error calling generic column rename function";
$$;


