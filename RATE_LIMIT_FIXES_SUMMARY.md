# Rate Limiting Fixes for Genesys Adapter VoiceAnalysis Job

## Problem Summary

The Genesys Adapter VoiceAnalysis job was experiencing HTTP 429 "Too Many Requests" errors during batch processing, specifically during authentication token requests. The error occurred in the `GCUtils.OAuthKey()` method when multiple concurrent VoiceAnalysis instances attempted to authenticate simultaneously.

**Specific Error from Logs:**
```
PureCloudPlatform.Client.V2.Client.ApiException: Error calling PostToken: {"error":"invalid_request","description":"rate limit exceeded; retry after: 19","error_description":"rate limit exceeded; retry after: 19"}
```

**Impact:**
- 1 out of 9 batches failed processing
- Sync dates were not updated to prevent data gaps
- Batch processing failures affected overall job reliability

## Root Cause Analysis

1. **Missing Rate Limit Handling in Authentication**: The `GCUtils.OAuthKey()` method directly called the PureCloud SDK without any retry logic or rate limit handling.

2. **Concurrent Authentication Requests**: Each VoiceAnalysis batch created a new instance that called `Initialize()`, leading to multiple simultaneous authentication requests.

3. **No Token Caching**: Each authentication request was independent, creating unnecessary load on the Genesys Cloud authentication endpoint.

## Solutions Implemented

### 1. Enhanced OAuthKey Method with Rate Limit Handling

**File:** `GenesysCloudUtils/GCUtils.cs`

**Changes:**
- Added async `OAuthKeyAsync()` method with comprehensive rate limit handling
- Implemented retry logic with exponential backoff and jitter
- Added proper parsing of Retry-After values from error messages
- Maintained backward compatibility with synchronous `OAuthKey()` wrapper

**Key Features:**
- Up to 5 retry attempts with exponential backoff
- Extracts retry-after seconds from Genesys error messages
- Applies jitter to prevent thundering herd problems
- Comprehensive logging for debugging and monitoring

### 2. Token Caching to Prevent Concurrent Authentication

**File:** `GenesysCloudUtils/GCUtils.cs`

**Changes:**
- Added static token cache with 55-minute expiry (tokens typically expire in 60 minutes)
- Implemented thread-safe access using `SemaphoreSlim`
- Cache key based on credentials and URL to support multiple environments
- Automatic cleanup of expired tokens

**Benefits:**
- Reduces authentication requests by reusing valid tokens
- Prevents concurrent authentication requests from multiple instances
- Improves performance by avoiding unnecessary API calls

### 3. Shared Initialization for VoiceAnalysis Instances

**File:** `GenesysCloudUtils/VoiceAnalysis.cs`

**Changes:**
- Added shared initialization mechanism using static fields
- Implemented `InitializeAsync()` method for better async support
- Added initialization lock to prevent concurrent authentication
- Maintained backward compatibility with synchronous `Initialize()` method

**Benefits:**
- Only one authentication request per application lifecycle
- Eliminates concurrent authentication issues in batch processing
- Improves overall performance and reliability

### 4. Updated Batch Processing to Use Async Initialization

**File:** `GCData/GCData.cs`

**Changes:**
- Modified `VoiceAnalysisCombinedDataAsync()` to use `InitializeAsync()`
- Ensures proper async flow throughout the voice analysis pipeline

## Technical Implementation Details

### Rate Limit Handling Pattern

```csharp
catch (ApiException ex) when (ex.ErrorCode == 429)
{
    // Extract retry-after from error message
    int retryAfterSeconds = ExtractRetryAfterFromMessage(ex.Message) ?? DEFAULT_AUTH_RETRY_SECONDS;
    
    // Apply exponential backoff with jitter
    int backoffSeconds = Math.Min(retryAfterSeconds * attempts, 60);
    int jitter = new Random().Next(0, Math.Max(1, backoffSeconds / 4));
    int totalWaitSeconds = backoffSeconds + jitter;
    
    await Task.Delay(totalWaitSeconds * 1000);
}
```

### Token Caching Implementation

```csharp
// Check cache first
if (_tokenCache.TryGetValue(cacheKey, out var cachedToken))
{
    if (DateTime.UtcNow < cachedToken.expiry)
    {
        return cachedToken.token; // Use cached token
    }
}

// Cache successful token
_tokenCache[cacheKey] = (oAuth, DateTime.UtcNow.Add(_tokenCacheExpiry));
```

### Shared Initialization

```csharp
await _initializationLock.WaitAsync();
try
{
    if (!_isGloballyInitialized || _sharedGCUtilities == null)
    {
        _sharedGCUtilities = new GCUtils(_logger);
        _sharedGCUtilities.Initialize();
        _isGloballyInitialized = true;
    }
    // Copy shared data to instance
    GCUtilities = _sharedGCUtilities;
}
finally
{
    _initializationLock.Release();
}
```

## Compatibility and Safety

- **Backward Compatibility**: All existing synchronous methods maintained
- **Thread Safety**: All shared state protected with appropriate locking mechanisms
- **Error Handling**: Comprehensive error handling with proper logging
- **Graceful Degradation**: Falls back to existing behavior if new features fail

## Expected Outcomes

1. **Elimination of 429 Errors**: Rate limit handling should prevent authentication failures
2. **Improved Batch Success Rate**: Expect 100% batch success rate instead of 8/9
3. **Better Performance**: Token caching reduces unnecessary authentication requests
4. **Enhanced Reliability**: Shared initialization prevents concurrent authentication issues
5. **Proper Sync Date Updates**: Successful batches will allow sync dates to be updated correctly

## Monitoring and Verification

The implementation includes comprehensive logging to monitor:
- Authentication attempts and successes
- Rate limit encounters and retry behavior
- Token cache hits and misses
- Shared initialization usage

**Log Prefixes to Monitor:**
- `Auth:Cache:` - Token caching operations
- `Auth:Request:` - Authentication requests
- `Auth:RateLimit:` - Rate limit encounters
- `Auth:Retry:` - Retry attempts
- `Voice:Init:` - VoiceAnalysis initialization

## Testing Recommendations

1. **Load Testing**: Test with high concurrency to verify rate limit handling
2. **Rate Limit Simulation**: Artificially trigger 429 errors to test retry logic
3. **Token Expiry Testing**: Verify proper handling of expired cached tokens
4. **Concurrent Batch Testing**: Run multiple VoiceAnalysis batches simultaneously
5. **Long-Running Tests**: Verify token cache behavior over extended periods
