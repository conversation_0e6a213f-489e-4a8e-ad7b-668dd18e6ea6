CREATE TABLE IF NOT EXISTS mvwevaluationgroupdata (
    keyid VARCHAR(50) NOT NULL PRIMARY KEY,
    evaluationid VARCHAR(50),
    questiongroupid VARCHAR(50),
    questiongroupname VARCHAR(200),
    totalscore NUMERIC(20, 2),
    maxtotalscore NUMERIC(20, 2),
    markedna BOOLEAN,
    totalcriticalscore NUMERIC(20, 2),
    maxtotalcriticalscore NUMERIC(20, 2),
    totalnoncriticalscore NUMERIC(20, 2),
    maxtotalnoncriticalscore NUMERIC(20, 2),
    totalscoreunweighted NUMERIC(20, 2),
    maxtotalscoreunweighted NUMERIC(20, 2),
    failedkillquestions BOOLEAN,
    divisionid VARCHAR(50),
    "comments" TEXT,
    conversationid VARCHAR(50)
);
