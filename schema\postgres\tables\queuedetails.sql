CREATE TABLE IF NOT EXISTS queuedetails (
    id varchar(50) NOT NULL,
    name varchar(255),
    description varchar(255),
    divisionid varchar(50),
    updated timestamp without time zone,
    enabletranscription boolean,
    isactive boolean,
    callslatargetperc float,
    callslatargetduration int,
    callbackslatargetperc float,
    callbackslatargetduration int,
    chatslatargetperc float,
    chatslatargetduration int,
    emailslatargetperc float,
    emailslatargetduration int,
    messageslatargetperc float,
    messageslatargetduration int,
    CONSTRAINT queuedetails_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

ALTER TABLE queuedetails 
ADD column IF NOT exists description varchar(255);

ALTER TABLE queuedetails 
ADD column IF NOT exists isactive boolean;

ALTER TABLE queuedetails 
ADD column IF NOT exists callslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists callslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists callbackslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists callbackslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists chatslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists chatslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists emailslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists emailslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists messageslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists messageslatargetduration int;

COMMENT ON COLUMN queueDetails.isactive IS 'Active Yes/No';
COMMENT ON COLUMN queueDetails.callslatargetperc IS 'SLA Target Percentage for Calls';
COMMENT ON COLUMN queueDetails.callslatargetduration IS 'SLA Target Duraction (sec) for Calls';
COMMENT ON COLUMN queueDetails.callbackslatargetperc IS 'SLA Target Percentage for Callbacks';
COMMENT ON COLUMN queueDetails.callbackslatargetduration IS 'SLA Target Duraction (sec) for Callbacks';
COMMENT ON COLUMN queueDetails.chatslatargetperc IS 'SLA Target Percentage for Chats';
COMMENT ON COLUMN queueDetails.chatslatargetduration IS 'SLA Target Duraction (sec) for Chats';
COMMENT ON COLUMN queueDetails.emailslatargetperc IS 'SLA Target Percentage for Emails';
COMMENT ON COLUMN queueDetails.emailslatargetduration IS 'SLA Target Duraction (sec) for Emails';
COMMENT ON COLUMN queueDetails.messageslatargetperc IS 'SLA Target Percentage for Messages';
COMMENT ON COLUMN queueDetails.messageslatargetduration IS 'SLA Target Duraction (sec) for Messages';
COMMENT ON COLUMN queueDetails.divisionid IS 'Queue Division GUID'; 
COMMENT ON COLUMN queueDetails.enabletranscription IS 'Queue Transcriptions Enabled?'; 
COMMENT ON COLUMN queueDetails.name IS 'Queue Name'; 
COMMENT ON COLUMN queueDetails.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN queueDetails.id IS 'Primary Key / Queue GUID'; 
COMMENT ON TABLE queueDetails IS 'Queue Lookup data'; 