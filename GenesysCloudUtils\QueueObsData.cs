﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlTypes;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using QueueReal = GenesysCloudDefQueueRealtime;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class QueueObsData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private JsonRestChilkat GCUtilities = new JsonRestChilkat();
        //private JsonUtils JsonActions = new JsonUtils();
        public DateTime LastAPIKeyGet { get; set; }
        public DataTable DTQueueData { get; set; }
        public DataTable DTQueueDetails { get; set; }
        public string JsonSearchString { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            Console.WriteLine("GC Queue Obs - Obtaining Key");
            //GCApiKey = GCUtilities.GCApiKey;

            DTQueueData = CreateQueuesTable();
        }


        public void GetNewAPIKey()
        {
            Console.WriteLine("QC Old Queue Key:{0}", GCUtilities.APIKey);
            GCUtilities.GetAuthAPIKey();
            Console.WriteLine("QCNew Queue Key:{0}", GCUtilities.APIKey);
        }
        public void getQueueStatus()
        {
            string JsonString = String.Empty;
            //JsonUtils JsonActions = new JsonUtils();

            QueueReal.QueueRealTime QueueData = new QueueReal.QueueRealTime();

            DTQueueData.AcceptChanges();

            //Console.WriteLine("JSON:{0}", JsonSearchString);

            JsonString = GCUtilities.ReturnJson("/api/v2/analytics/queues/observations/query", JsonSearchString);

            //Console.WriteLine("JSON=\n{0}",JsonString);

            QueueData = JsonConvert.DeserializeObject<QueueReal.QueueRealTime>(JsonString,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });

            if (QueueData != null && JsonString.Length > 10)
            {

                //Console.WriteLine("Queue RealTime Returns\n:{0}",JsonString);
                foreach (QueueReal.Result QueueObsRes in QueueData.results)
                {

                    DataRow DRQueueObsData = DTQueueData.Select("id='" + QueueObsRes.group.queueId + "'").FirstOrDefault();
                    Boolean RowExists = true;
                    string ColumnTypeName = string.Empty;

                    if (DRQueueObsData == null)
                    {
                        DRQueueObsData = DTQueueData.NewRow();
                        DRQueueObsData["id"] = QueueObsRes.group.queueId;
                        RowExists = false;
                    }

                    //Console.WriteLine("QueueID:{0}", QueueObsRes.group.queueId);

                    if (QueueObsRes.group.mediaType != null)
                    {
                        //Console.WriteLine("We Have Media {0}", QueueObsRes.group.mediaType);
                        foreach (QueueReal.Datum QueueItemData in QueueObsRes.data)
                        {
                            switch (QueueItemData.metric)
                            {
                                case "oInteracting":
                                    DRQueueObsData[QueueObsRes.group.mediaType + "interacting"] = QueueItemData.stats.count;
                                    ColumnTypeName = "interacting";
                                    break;
                                case "oWaiting":
                                    DRQueueObsData[QueueObsRes.group.mediaType + "waiting"] = QueueItemData.stats.count;
                                    ColumnTypeName = "waiting";
                                    break;

                                case "oOnQueueUsers":
                                    DRQueueObsData["agent" + QueueItemData.qualifier.ToLower()] = QueueItemData.stats.count;
                                    break;
                            }

                            DRQueueObsData[QueueObsRes.group.mediaType + "oldest" + ColumnTypeName] = System.DBNull.Value;

                            if (QueueItemData.stats.count > 0)
                            {
                                DateTime ObsDate = DateTime.UtcNow;
                                foreach (QueueReal.Observation QueueObs in QueueItemData.observations)
                                {
                                    QueueObs.observationDate = new DateTime(
                                                QueueObs.observationDate.Ticks - (QueueObs.observationDate.Ticks % TimeSpan.TicksPerSecond),
                                                QueueObs.observationDate.Kind
                                    );

                                    if (QueueObs.observationDate < ObsDate && QueueObs.observationDate.ToUniversalTime().ToString() != DRQueueObsData[QueueObsRes.group.mediaType + "oldest" + ColumnTypeName].ToString())
                                    {
                                        DRQueueObsData[QueueObsRes.group.mediaType + "oldest" + ColumnTypeName] = QueueObs.observationDate.ToUniversalTime(); ;
                                        ObsDate = QueueObs.observationDate;
                                    }

                                }


                            }


                        }


                    }
                    else
                    {
                        //Console.WriteLine("We Have No Media ");
                        if (QueueObsRes.data != null)
                        {
                            foreach (QueueReal.Datum QueueItemData in QueueObsRes.data)
                            {
                                switch (QueueItemData.metric)
                                {
                                    case "oOnQueueUsers":
                                        DRQueueObsData["agent" + QueueItemData.qualifier.ToLower()] = QueueItemData.stats.count;
                                        break;
                                }
                            }
                        }

                    }

                    if (RowExists == false)
                        DTQueueData.Rows.Add(DRQueueObsData);
                    else
                    {
                        DRQueueObsData.AcceptChanges();
                        DRQueueObsData.SetModified();

                    }
                }

            }
            else
            {
                Console.Write("QO:NO Data");
            }
        }

        public void CreateJSONSelectString()
        {
            // Check if there are any queues to process
            if (DTQueueDetails.Rows.Count == 0)
            {
                // Create a default search string that won't cause API errors
                JsonSearchString = "{\"detailMetrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\"], " +
                                          "  \"metrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\" ] " +
                                          "} ";
                return;
            }

            StringBuilder JSONSelect = new StringBuilder();

            foreach (DataRow DRQueue in DTQueueDetails.Rows)
            {
                JSONSelect.Append("{\"dimension\": \"queueId\",\"value\": \"" + DRQueue["id"] + "\"},");
            }

            JSONSelect.Length = JSONSelect.Length - 1;

            #region "SearchString"
            JsonSearchString = "{\"detailMetrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\"], " +
                                      "  \"metrics\": [ \"oWaiting\", \"oInteracting\",\"oOnQueueUsers\",\"oUserPresences\" ], " +
                                      "  \"filter\": { " +
                                      "    \"type\": \"and\", " +
                                      "    \"clauses\": [ " +
                                      "      { " +
                                      "        \"type\": \"or\", " +
                                      "        \"predicates\": [ " +
                                      JSONSelect.ToString() +
                                      "        ] " +
                                      "      } " +
                                      "    ] " +
                                      "  } " +
                                      "} ";
            #endregion

            //Console.WriteLine(JsonSearchString);
        }

        public DataTable CreateQueuesTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp = DBUtil.CreateInMemTable("queueRealTimeData");
            return DTTemp;
        }

    }
}
