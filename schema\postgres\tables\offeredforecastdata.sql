CREATE TABLE IF NOT EXISTS offeredforecastdata (
    keyid varchar(150) NOT NULL,
    businessunitid varchar(50),
    scheduleid varchar(50),
    planninggroup varchar(50),
    shorttermforecastid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    weekdate date,
    week bigint,
    avghandleperinterval numeric(20, 2),
    offeredperinterval numeric(20, 2),
    canuseforscheduling bool NULL,
    updated timestamp without time zone,
    CONSTRAINT offeredforecastdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE offeredforecastdata
ADD column IF NOT exists scheduleid varchar(50);

ALTER TABLE offeredforecastdata
ADD column IF NOT exists canuseforscheduling bool NULL;

-- Add index on weekdate column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'offeredforecastdata'
        AND indexname = 'ix_offeredforecastdata_weekdate'
    ) THEN
        CREATE INDEX ix_offeredforecastdata_weekdate ON offeredforecastdata(weekdate);
        RAISE NOTICE 'Created index ix_offeredforecastdata_weekdate on offeredforecastdata.weekdate';
    END IF;
END $$;