using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.IO;
using System.Threading;
using StandardUtils;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging; // Add this line

namespace GenesysCloudUtils
{
    public class PermissionConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private ILogger _logger;
        public bool EnableForcedUpdatePermissions { get; set; } = false; // Default to false

        public PermissionConfig(ILogger? logger)
        {
            _logger = logger;
        }

        public void Initialize(JObject preferences)
        {
            GCUtilities.Initialize();
            _logger?.LogInformation("Initialization of GC Permission Config");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            // Retrieve permissions settings from Preferences.Permissions
            var permissions = preferences["Permissions"];
            if (permissions != null)
            {
                EnableForcedUpdatePermissions = permissions["ForcedUpdate"]?.Value<bool>() ?? false;
                _logger?.LogInformation("Forced update permissions enabled: {EnableForcedUpdatePermissions}", EnableForcedUpdatePermissions);
            }
        }

        public void UpdateRolePermissions(string clientId, JObject roleData)
        {
            _logger?.LogInformation("Updating Role Permissions");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            // Get the roleId from the clientId
            string clientRequestString = URI + "/api/v2/oauth/clients/" + clientId;
            _logger?.LogInformation("Calling GET {url}", clientRequestString);
            string clientJson = JsonActions.JsonReturnString(clientRequestString, GCApiKey);
            if (string.IsNullOrEmpty(clientJson))
            {
                _logger?.LogError("Failed to retrieve client data from {url}", clientRequestString);
                throw new Exception("Failed to retrieve client data.");
            }

            JObject clientData = JObject.Parse(clientJson);
            string roleId = clientData["roleDivisions"]?.FirstOrDefault()?["roleId"]?.ToString();
            if (string.IsNullOrEmpty(roleId))
            {
                _logger?.LogError("Role ID not found in the client data.");
                throw new Exception("Role ID not found in the client data.");
            }

            // Try to get the current role data
            string roleRequestString = URI + "/api/v2/authorization/roles/" + roleId;
            string roleName = "Customer Science_Genesys Cloud Integration"; // Default name if we can't get the current one

            try
            {
                // Get the current role data
                _logger?.LogInformation("Calling GET {url}", roleRequestString);
                string currentRoleJson = JsonActions.JsonReturnString(roleRequestString, GCApiKey);

                if (string.IsNullOrEmpty(currentRoleJson))
                {
                    _logger?.LogWarning("Failed to retrieve current role data from {url}", roleRequestString);

                    if (!EnableForcedUpdatePermissions)
                    {
                        _logger?.LogError("Forced permissions update is disabled. Cannot update role without current data.");
                        throw new Exception("Failed to retrieve current role data and forced permissions update is disabled.");
                    }

                    _logger?.LogWarning("Using default role name: {defaultName}", roleName);
                }
                else
                {
                    _logger?.LogInformation("Successfully retrieved current role data from {url}", roleRequestString);
                    JObject currentRoleDataFromApi = JObject.Parse(currentRoleJson); // Renamed variable
                    string extractedName = currentRoleDataFromApi["name"]?.ToString();

                    if (!string.IsNullOrEmpty(extractedName))
                    {
                        roleName = extractedName;
                        _logger?.LogInformation("Using role name from API: {roleName}", roleName);
                    }
                    else
                    {
                        _logger?.LogWarning("Role name not found in the current role data, using default name: {defaultName}", roleName);
                    }
                }
            }
            catch (Exception ex)
            {
                if (!EnableForcedUpdatePermissions)
                {
                    _logger?.LogError(ex, "Error retrieving current role data and forced permissions update is disabled.");
                    throw;
                }

                _logger?.LogWarning(ex, "Error retrieving current role data. Using default role name: {defaultName}", roleName);
            }

            // Add the name field to the roleData
            roleData["name"] = roleName;
            _logger?.LogInformation("Setting role name to: {roleName}", roleName);

            // Get current role permission policies
            JObject currentRoleData = GetRolePermissions(roleId); // Replace with the correct method to fetch role data
            JArray currentPermissionPolicies = currentRoleData["permissionPolicies"] as JArray ?? new JArray();
            int currentPolicyCount = currentPermissionPolicies.Count;

            // Override permission policies
            JArray newPermissionPolicies = roleData["permissionPolicies"] as JArray ?? new JArray();
            int sourcePolicyCount = newPermissionPolicies.Count;

            // Log both source and Genesys permission policy counts in a single log entry
            _logger?.LogInformation("Source permission policies: {sourceCount}, Current permission policies in Genesys: {currentCount}", sourcePolicyCount, currentPolicyCount);

            // Log the difference between source and current permission policies
            var currentPolicySet = new HashSet<string>(currentPermissionPolicies.Select(p => p.ToString()));
            var sourcePolicySet = new HashSet<string>(newPermissionPolicies.Select(p => p.ToString()));

            var addedPolicies = sourcePolicySet.Except(currentPolicySet).ToList();
            var removedPolicies = currentPolicySet.Except(sourcePolicySet).ToList();

            _logger?.LogDebug("Added permission policies: {addedPolicies}", addedPolicies);
            _logger?.LogDebug("Removed permission policies: {removedPolicies}", removedPolicies);

            // Skip the PUT request if there are no changes
            if (!addedPolicies.Any() && !removedPolicies.Any())
            {
                _logger?.LogInformation("No changes detected in permission policies. Skipping update for role ID {roleId}.", roleId);
                return;
            }

            // Replace the permission policies
            roleData["permissionPolicies"] = newPermissionPolicies;

            // Declare and initialize JsonString for the PUT request
            string JsonString = JsonActions.JsonPutString(roleRequestString, GCApiKey, roleData);

            if (!string.IsNullOrEmpty(JsonString))
            {
                var response = JsonConvert.DeserializeObject<JObject>(JsonString);
                if (response != null && response["id"] != null && response["id"].ToString() == roleId)
                {
                    _logger?.LogInformation("Role permission policies updated successfully for role ID {roleId}", roleId);

                    // Re-query the role to show updated count
                    JObject updatedRoleData = GetRolePermissions(roleId);
                    JArray updatedPermissionPolicies = updatedRoleData["permissionPolicies"] as JArray ?? new JArray();
                    int updatedPolicyCount = updatedPermissionPolicies.Count;

                    _logger?.LogInformation("Updated permission policies count for role ID {roleId}: {updatedCount}", roleId, updatedPolicyCount);
                }
                else if (response != null && response["error"] != null)
                {
                    _logger?.LogError("API Error: {error}. Response: {response}", response["error"], JsonString);
                }
                else
                {
                    _logger?.LogError("Failed to update role permission policies for role ID {roleId}. Response: {response}", roleId, JsonString);
                }
            }
            else
            {
                _logger?.LogError("Failed to update role permission policies for role ID {roleId}. No response received.", roleId);
            }
            return;
        }

        public JObject GetRolePermissions(string roleId)
        {
            _logger?.LogInformation("Getting Role Permissions for role ID: {roleId}", roleId);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string RequestString = URI + "/api/v2/authorization/roles/" + roleId;
            _logger?.LogDebug("Calling GET {url}", RequestString);
            string JsonString = JsonActions.JsonGetString(RequestString, GCApiKey);

            if (!string.IsNullOrEmpty(JsonString))
            {
                _logger?.LogDebug("API Response for role permissions: {response}", JsonString);

                JObject roleData = JObject.Parse(JsonString);

                // Check the 'permissions' field
                JArray permissions = roleData["permissions"] as JArray;
                if (roleData["permissionPolicies"] is JArray permissionPolicies)
                {
                    // Extract permissions from 'permissionPolicies'
                    permissions = new JArray();
                    foreach (var policy in permissionPolicies)
                    {
                        if (policy["actionSet"] is JArray actionSet)
                        {
                            permissions.Add($"{policy["domain"]}:{policy["entityName"]}:{actionSet}");
                        }
                    }
                    _logger?.LogInformation("Extracted {count} permissions from 'permissionPolicies' for role ID: {roleId}", permissions.Count, roleId);
                    roleData["permissions"] = permissions; // Update the roleData object with extracted permissions
                }
                else
                {
                    _logger?.LogWarning("No permissions or permission policies found in the API response for role ID: {roleId}");
                }

                return roleData;
            }
            else
            {
                _logger?.LogError("Failed to retrieve role permissions from the API for role ID: {roleId}");
                throw new Exception("Failed to retrieve role permissions from the API.");
            }
        }

        public void UpdateRolePermissionsFromFile(string clientId, string filePath)
        {
            try
            {
                // Read the JSON file content
                string jsonContent = File.ReadAllText(filePath);
                JObject permissionsData = JObject.Parse(jsonContent);
                _logger?.LogInformation("Updating Role Permissions from file: {filePath} for client ID {clientId}", filePath, clientId);

                // Call the method that works with clientId
                UpdateRolePermissions(clientId, permissionsData);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error updating role permissions from file: {message}", ex.Message);
                throw;
            }
        }
    }
}
