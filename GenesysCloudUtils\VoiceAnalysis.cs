﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using StandardUtils;
using CSG.Adapter.Configuration;
using VoiceD = GenesysCloudDefVoiceAnalysisDetail;
using VoiceO = GenesysCloudDefVoiceAnalysisOverview;
using VoiceU = GenesysCloudDefVoiceAnalysisDetURL;

namespace GenesysCloudUtils
{
    public class VoiceAnalysis
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private readonly ILogger<VoiceAnalysis> _logger;
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;

        // --- Rate limit fields (shared across all instances)
        private static int _callCount = 0;
        private static DateTime _callWindowStart = DateTime.UtcNow;
        private static readonly SemaphoreSlim _tokenRefreshLock = new SemaphoreSlim(1, 1);

        // Shared initialization lock to prevent concurrent authentication requests
        private static readonly SemaphoreSlim _initializationLock = new SemaphoreSlim(1, 1);
        private static bool _isGloballyInitialized = false;
        private static GCUtils _sharedGCUtilities;

        // **Aggregate Counter Fields** - Thread-safe counters
        private static int _globalConversationCounter = 0;
        private static int _overallConversationCount = 0;
        private static readonly object _counterLock = new object();

        // Properties to access counters safely
        public static int GlobalConversationCounter => Interlocked.CompareExchange(ref _globalConversationCounter, 0, 0);
        public static int OverallConversationCount
        {
            get => Interlocked.CompareExchange(ref _overallConversationCount, 0, 0);
            set => Interlocked.Exchange(ref _overallConversationCount, value);
        }

        public VoiceAnalysis(ILogger<VoiceAnalysis> logger)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            InitializeAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Async initialization with shared authentication to prevent concurrent token requests
        /// </summary>
        public async Task InitializeAsync()
        {
            _logger?.LogDebug("Voice:Init: Initializing VoiceAnalysis service");

            // Use shared initialization to prevent concurrent authentication requests
            await _initializationLock.WaitAsync();
            try
            {
                if (!_isGloballyInitialized || _sharedGCUtilities == null)
                {
                    _logger?.LogDebug("Voice:Init: Performing global initialization");
                    _sharedGCUtilities = new GCUtils(_logger);
                    _sharedGCUtilities.Initialize();
                    _isGloballyInitialized = true;
                    _logger?.LogDebug("Voice:Init: Global initialization completed");
                }
                else
                {
                    _logger?.LogDebug("Voice:Init: Using existing global initialization");
                }

                // Copy shared data to instance
                GCUtilities = _sharedGCUtilities;
                CustomerKeyID = GCUtilities.CustomerKeyID;
                UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
                GCControlData = GCUtilities.GCControlData;
                GCApiKey = GCUtilities.GCApiKey;

                URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                _logger?.LogDebug("Voice:Init: Initialized with URI: {Uri}", URI);
            }
            finally
            {
                _initializationLock.Release();
            }

            // Initialize DBUtil separately as it doesn't require authentication
            DBUtil.Initialize();
        }

        /// <summary>
        /// Clears the shared initialization state - useful for testing or when credentials change
        /// </summary>
        public static async Task ClearSharedInitializationAsync()
        {
            await _initializationLock.WaitAsync();
            try
            {
                _isGloballyInitialized = false;
                _sharedGCUtilities = null;
                // Also clear the token cache in GCUtils
                await GCUtils.ClearTokenCacheAsync();
            }
            finally
            {
                _initializationLock.Release();
            }
        }

        // Dictionary for storing transcripts in memory
        private static readonly Dictionary<string, string> _transcriptCache = new Dictionary<string, string>();

        /// <summary>
        /// Gets the stored transcript for a conversation ID if available
        /// </summary>
        /// <param name="conversationId">The conversation ID</param>
        /// <returns>The stored transcript or null if not found</returns>
        public string GetCachedTranscript(string conversationId)
        {
            if (string.IsNullOrEmpty(conversationId))
                return null;

            // Check if the transcript is in the dictionary
            lock (_transcriptCache)
            {
                if (_transcriptCache.TryGetValue(conversationId, out string transcript))
                {
                    _logger?.LogDebug("Voice:Transcript: Using stored transcript for conversation {ConvId}, length: {Length} characters",
                        conversationId, transcript.Length);
                    return transcript;
                }
            }

            return null;
        }



        /// <summary>
        /// Retrieves BOTH overview and detail data for each conversation in a single pass.
        /// Calls overview first; if valid, calls detail.
        /// Returns a DataSet containing three tables:
        ///  1) convVoiceOverviewData
        ///  2) convVoiceTopicDetailData
        ///  3) convVoiceSentimentDetailData
        ///
        /// Now uses a global (aggregate) counter for logging.
        /// Also stores downloaded transcripts in memory for reuse by Knowledge Quest processing.
        /// </summary>
        public async Task<DataSet> VoiceAnalysisCombinedDataAsync(DataTable Conversations)
        {
            TimeZoneInfo appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            // We'll collect everything in these DataTables.
            DataTable voiceOverviewData   = DBUtil.CreateInMemTable("convVoiceOverviewData");
            DataTable voiceTopicData      = DBUtil.CreateInMemTable("convVoiceTopicDetailData");
            DataTable voiceSentimentData  = DBUtil.CreateInMemTable("convVoiceSentimentDetailData");

            // Ensure transcript processing columns exist in the overview table to prevent column count mismatches
            EnsureTranscriptColumns(voiceOverviewData);

            Stopwatch watch = Stopwatch.StartNew();

            // Set up aggregate counters
            // Only reset the global counter; assume OverallConversationCount was set externally.
            Interlocked.Exchange(ref _globalConversationCounter, 0);

            using (HttpClientHandler handler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
            })
            using (HttpClient client = new HttpClient(handler))
            {
                // Set initial authorization header
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", GCApiKey);

                foreach (DataRow conversation in Conversations.Rows)
                {
                    // Increment the global counter atomically.
                    int currentCount = Interlocked.Increment(ref _globalConversationCounter);

                    // Log at debug level to reduce log volume in multi-threaded scenarios
                    _logger?.LogDebug("Voice:Process: Conversation {ConvId} ({Counter}/{Total}) - elapsed {Elapsed:hh\\:mm\\:ss}",
                        conversation["conversationid"], currentCount, OverallConversationCount, watch.Elapsed);

                    // 1) Retrieve overview data.
                    string overviewUrl = $"{URI}/api/v2/speechandtextanalytics/conversations/{conversation["conversationid"]}";
                    string overviewJsonString;
                    try
                    {
                        overviewJsonString = await client.GetStringAsync(overviewUrl);
                    }
                    catch (HttpRequestException httpEx) when (httpEx.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        _logger?.LogWarning(httpEx, "Voice:Error: Overview data not found for conversation {ConvId}", conversation["conversationid"]);
                        continue;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Voice:Error: Failed fetching overview data for conversation {ConvId}", conversation["conversationid"]);
                        continue;
                    }
                    await Task.Delay(200);

                    VoiceO.VoiceAnalysisOverview voiceOverView;
                    try
                    {
                        voiceOverView = JsonConvert.DeserializeObject<VoiceO.VoiceAnalysisOverview>(
                            overviewJsonString,
                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Voice:Error: Failed deserializing overview JSON for conversation {ConvId}", conversation["conversationid"]);
                        continue;
                    }

                    // If overview is not valid, skip calling detail.
                    if (voiceOverView.conversation == null)
                        continue;

                    // Build an overview row.
                    DataRow newOverviewRow = voiceOverviewData.NewRow();
                    newOverviewRow["keyid"] = conversation["conversationid"];
                    newOverviewRow["conversationid"] = conversation["conversationid"];
                    newOverviewRow["peerid"] = conversation["peer"];
                    newOverviewRow["sentimentscore"] = voiceOverView.sentimentScore ?? (object)DBNull.Value;
                    newOverviewRow["sentimenttrend"] = voiceOverView.sentimentTrend;
                    newOverviewRow["sentimenttrendclass"] = voiceOverView.sentimentTrendClass;
                    newOverviewRow["agentdurationpercentage"] = voiceOverView.participantMetrics.agentDurationPercentage;
                    newOverviewRow["customerdurationpercentage"] = voiceOverView.participantMetrics.customerDurationPercentage;
                    newOverviewRow["silencedurationpercentage"] = voiceOverView.participantMetrics.silenceDurationPercentage;
                    newOverviewRow["ivrdurationpercentage"] = voiceOverView.participantMetrics.ivrDurationPercentage;
                    newOverviewRow["acddurationpercentage"] = voiceOverView.participantMetrics.acdDurationPercentage;
                    newOverviewRow["otherdurationpercentage"] = voiceOverView.participantMetrics.otherDurationPercentage;
                    newOverviewRow["overtalkdurationpercentage"] = voiceOverView.participantMetrics.overtalkDurationPercentage;
                    newOverviewRow["overtalkcount"] = voiceOverView.participantMetrics.overtalkCount;
                    newOverviewRow["phrasecount"] = 0;
                    newOverviewRow["gettransscript"] = "y";

                    // Initialize transcript processing columns with default values
                    newOverviewRow[COLUMN_PROCESSED] = DBNull.Value;
                    newOverviewRow[COLUMN_PROCESSED_DATE] = DBNull.Value;
                    newOverviewRow[COLUMN_PROCESSED_NOTES] = DBNull.Value;

                    try
                    {
                        voiceOverviewData.Rows.Add(newOverviewRow);
                    }
                    catch (ConstraintException)
                    {
                        _logger?.LogWarning("Voice:Warning: Duplicate overview data for conversation {ConvId} skipped", conversation["conversationid"]);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Voice:Error: Failed adding overview data for conversation {ConvId}", conversation["conversationid"]);
                    }

                    // 2) Retrieve detail transcript URL.
                    string transcriptUrlEndpoint = $"{URI}/api/v2/speechandtextanalytics/conversations/{conversation["conversationid"]}/communications/{conversation["peer"]}/transcripturl";
                    string detailJsonString;
                    try
                    {
                        detailJsonString = await client.GetStringAsync(transcriptUrlEndpoint);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Voice:Error: Failed fetching transcript URL for conversation {ConvId} and peer {PeerId}",
                            conversation["conversationid"], conversation["peer"]);
                        continue;
                    }

                    await Task.Delay(250);

                    VoiceU.VoiceAnalysisURL voiceAURL;
                    try
                    {
                        voiceAURL = JsonConvert.DeserializeObject<VoiceU.VoiceAnalysisURL>(
                            detailJsonString, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Voice:Error: Failed deserializing transcript URL JSON for conversation {ConvId}", conversation["conversationid"]);
                        continue;
                    }

                    if (!string.IsNullOrEmpty(voiceAURL.url))
                    {
                        string transcriptJson;
                        try
                        {
                            transcriptJson = await client.GetStringAsync(voiceAURL.url);

                            // Store the transcript for reuse by Knowledge Quest processing
                            string conversationId = conversation["conversationid"].ToString();

                            // Store the transcript in the dictionary
                            lock (_transcriptCache)
                            {
                                _transcriptCache[conversationId] = transcriptJson;

                                _logger?.LogDebug("Voice:Transcript: Stored transcript for conversation {ConvId}, length: {Length} characters",
                                    conversationId, transcriptJson?.Length ?? 0);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Voice:Error: Failed downloading transcript details from URL for conversation {ConvId}", conversation["conversationid"]);
                            continue;
                        }

                        VoiceD.VoiceDetail voiceTrans;
                        try
                        {
                            voiceTrans = JsonConvert.DeserializeObject<VoiceD.VoiceDetail>(
                                transcriptJson, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Voice:Error: Failed deserializing transcript details for conversation {ConvId}", conversation["conversationid"]);
                            continue;
                        }

                        if (voiceTrans.conversationId != null)
                        {
                            foreach (VoiceD.Transcript trans in voiceTrans.transcripts)
                            {
                                // Increase phrasecount if phrases exist.
                                if (trans.phrases != null)
                                {
                                    newOverviewRow["phrasecount"] = Convert.ToInt64(newOverviewRow["phrasecount"]) + trans.phrases.Count();
                                    newOverviewRow.AcceptChanges();
                                }

                                // Process topics.
                                if (trans.analytics.topics != null)
                                {
                                    foreach (VoiceD.Topic topics in trans.analytics.topics)
                                    {
                                        DataRow newRow = voiceTopicData.NewRow();
                                        newRow["keyid"] = $"{voiceTrans.conversationId}|{topics.startTimeMs}|{topics.topicId}";
                                        newRow["conversationid"] = voiceTrans.conversationId;
                                        newRow["transcriptnumber"] = trans.transcriptId;
                                        newRow["communicationid"] = voiceTrans.communicationId;
                                        newRow["duration"] = topics.duration.milliseconds / 1000;
                                        // ConvertFromUnixTimestampMS already returns UTC DateTime, no need for ToUniversalTime()
                                        var topicStartTime = UCAUtils.ConvertFromUnixTimestampMS(topics.startTimeMs);
                                        newRow["starttime"] = topicStartTime;
                                        newRow["starttimeltc"] = TimeZoneInfo.ConvertTimeFromUtc(topicStartTime, appTimeZone);
                                        newRow["confidence"] = topics.confidence;
                                        newRow["topicname"] = topics.topicName;
                                        newRow["topicid"] = topics.topicId;
                                        newRow["participant"] = topics.participant;
                                        newRow["topicphrase"] = topics.topicPhrase;
                                        newRow["transcriptphrase"] = topics.transcriptPhrase;

                                        long phraseStartMs = topics.startTimeMs;
                                        long phraseEndMs = phraseStartMs + topics.duration.milliseconds;

                                        VoiceD.Participant matchingParticipant = voiceTrans.participants
                                            .Where(p => !string.Equals(p.participantPurpose, "customer", StringComparison.OrdinalIgnoreCase))
                                            .FirstOrDefault(p => p.startTimeMs <= phraseStartMs && p.endTimeMs >= phraseEndMs);
                                        if (matchingParticipant != null)
                                        {
                                            newRow["ani"] = matchingParticipant.ani;
                                            newRow["dnis"] = matchingParticipant.dnis;
                                            newRow["queueid"] = matchingParticipant.queueId;
                                            if (string.Equals(matchingParticipant.participantPurpose, "agent", StringComparison.OrdinalIgnoreCase))
                                            {
                                                newRow["userid"] = matchingParticipant.userId;
                                            }
                                        }
                                        try
                                        {
                                            voiceTopicData.Rows.Add(newRow);
                                            _logger?.LogDebug("Voice:Data: Added topic data for conversation {ConvId}", voiceTrans.conversationId);
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex,
                                                "Voice:Error: Failed adding topic data for conversation {ConvId}. JSON snippet: {Snippet}",
                                                voiceTrans.conversationId,
                                                transcriptJson.Substring(0, Math.Min(20, transcriptJson.Length)));
                                        }
                                    }
                                }

                                // Process sentiment.
                                if (trans.analytics.sentiment != null)
                                {
                                    foreach (VoiceD.Sentiment sentiment in trans.analytics.sentiment)
                                    {
                                        DataRow newRow = voiceSentimentData.NewRow();
                                        newRow["keyid"] = $"{voiceTrans.conversationId}|{sentiment.startTimeMs}";
                                        newRow["conversationid"] = voiceTrans.conversationId;
                                        newRow["transcriptnumber"] = trans.transcriptId;
                                        newRow["communicationid"] = voiceTrans.communicationId;
                                        newRow["duration"] = sentiment.duration.milliseconds / 1000;
                                        // ConvertFromUnixTimestampMS already returns UTC DateTime, no need for ToUniversalTime()
                                        var sentimentStartTime = UCAUtils.ConvertFromUnixTimestampMS(sentiment.startTimeMs);
                                        newRow["starttime"] = sentimentStartTime;
                                        newRow["starttimeltc"] = TimeZoneInfo.ConvertTimeFromUtc(sentimentStartTime, appTimeZone);
                                        newRow["participant"] = sentiment.participant;
                                        newRow["sentiment"] = sentiment.sentiment;
                                        newRow["phrase"] = sentiment.phrase;
                                        newRow["phraseindex"] = sentiment.phraseIndex;

                                        long sentimentStartMs = sentiment.startTimeMs;
                                        long sentimentEndMs = sentimentStartMs + sentiment.duration.milliseconds;

                                        VoiceD.Participant matchingParticipant = voiceTrans.participants
                                            .Where(p => !string.Equals(p.participantPurpose, "customer", StringComparison.OrdinalIgnoreCase))
                                            .FirstOrDefault(p => p.startTimeMs <= sentimentStartMs && p.endTimeMs >= sentimentEndMs);
                                        if (matchingParticipant != null)
                                        {
                                            newRow["ani"] = matchingParticipant.ani;
                                            newRow["dnis"] = matchingParticipant.dnis;
                                            newRow["queueid"] = matchingParticipant.queueId;
                                            if (string.Equals(matchingParticipant.participantPurpose, "agent", StringComparison.OrdinalIgnoreCase))
                                            {
                                                newRow["userid"] = matchingParticipant.userId;
                                            }
                                        }
                                        try
                                        {
                                            voiceSentimentData.Rows.Add(newRow);
                                            _logger?.LogDebug("Voice:Data: Added sentiment data for conversation {ConvId}", voiceTrans.conversationId);
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex,
                                                "Voice:Error: Failed adding sentiment data for conversation {ConvId}. JSON snippet: {Snippet}",
                                                voiceTrans.conversationId,
                                                transcriptJson.Substring(0, Math.Min(20, transcriptJson.Length)));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Build final DataSet with all 3 tables.
            var finalDataSet = new DataSet("VoiceAnalysisData");
            finalDataSet.Tables.Add(voiceOverviewData);
            finalDataSet.Tables.Add(voiceTopicData);
            finalDataSet.Tables.Add(voiceSentimentData);

            // Log batch summary at debug level to reduce log volume in multi-threaded scenarios
            _logger?.LogDebug("Voice:Summary: Processed {Total} conversations in {Elapsed:hh\\:mm\\:ss}. " +
                           "Results: {OverviewCount} overview rows, {TopicCount} topic rows, {SentimentCount} sentiment rows",
                           GlobalConversationCounter,
                           watch.Elapsed,
                           voiceOverviewData.Rows.Count,
                           voiceTopicData.Rows.Count,
                           voiceSentimentData.Rows.Count);

            return finalDataSet;
        }

        #region Knowledge Quest Integration

        /// <summary>
        /// Verifies if a queue should be processed for Knowledge Quest
        /// </summary>
        /// <param name="queueId">The queue ID to verify</param>
        /// <returns>True if the queue should be processed, false otherwise</returns>
        // Knowledge Quest API endpoints
        private const string KNOWLEDGE_QUEST_VERIFY_ENDPOINT = "https://csikq-api.azurewebsites.net/api/verify-queue";
        private const string KNOWLEDGE_QUEST_INGEST_ENDPOINT = "https://csikq-api.azurewebsites.net/api/ingest-transcript";
        private const string JWT_SUBJECT = "genesys-adapter";
        private const int JWT_EXPIRATION_MINUTES = 5;
        private const string JWT_ISSUER = "csi-adapter";
        private const string JWT_AUDIENCE = "csi-knowledgequest";

        /// <summary>
        /// Verifies if a queue should be processed for Knowledge Quest by calling the external API
        /// </summary>
        /// <param name="queueId">The queue ID to verify</param>
        /// <returns>True if the queue should be processed, false otherwise</returns>
        public async Task<bool> VerifyQueueAsync(string queueId)
        {
            // Start a stopwatch to measure execution time
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            _logger?.LogDebug("KnowledgeQuest:Verify: Verifying queue {QueueId}", queueId);

            try
            {
                if (string.IsNullOrEmpty(queueId))
                {
                    stopwatch.Stop();
                    _logger?.LogDebug("KnowledgeQuest:Verify: Queue ID is null or empty, elapsed {Duration:N3}s", stopwatch.Elapsed.TotalSeconds);
                    return CacheAndReturn(queueId, false);
                }

                // Get the secret key from configuration
                string secretKey = GetKnowledgeQuestSecretKey();
                if (string.IsNullOrEmpty(secretKey))
                {
                    stopwatch.Stop();
                    _logger?.LogDebug("KnowledgeQuest:Verify: Secret key is not configured, elapsed {Duration:N3}s", stopwatch.Elapsed.TotalSeconds);
                    return CacheAndReturn(queueId, false);
                }

                // Generate JWT token
                string token = GenerateJwtToken(secretKey);
                if (string.IsNullOrEmpty(token))
                {
                    stopwatch.Stop();
                    _logger?.LogDebug("KnowledgeQuest:Verify: Failed to generate JWT token, elapsed {Duration:N3}s", stopwatch.Elapsed.TotalSeconds);
                    return CacheAndReturn(queueId, false);
                }

                // Call the verify-queue API
                int retryCount = 0;
                bool apiCallSuccess = false;
                bool verified = false;

                while (retryCount < MAX_RETRY_ATTEMPTS && !apiCallSuccess)
                {
                    try
                    {
                        if (retryCount > 0)
                        {
                            _logger?.LogDebug("KnowledgeQuest:Verify: Retry attempt {RetryCount} for queue {QueueId}",
                                retryCount, queueId);
                            await Task.Delay(RETRY_DELAY_MS * retryCount); // Exponential backoff
                        }

                        using (HttpClientHandler handler = new HttpClientHandler
                        {
                            AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
                        })
                        using (HttpClient client = new HttpClient(handler))
                        {
                            // Set up the client with the necessary headers
                            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                            // Create the request content
                            var requestData = new { queueid = queueId };
                            string requestContent = JsonConvert.SerializeObject(requestData);
                            var content = new StringContent(requestContent, Encoding.UTF8, "application/json");

                            // Send the request to the Knowledge Quest API - log at trace level only
                            _logger?.LogTrace("KnowledgeQuest:Verify: Sending request to {Url} for queue {QueueId}",
                                KNOWLEDGE_QUEST_VERIFY_ENDPOINT, queueId);

                            HttpResponseMessage response = await client.PostAsync(KNOWLEDGE_QUEST_VERIFY_ENDPOINT, content);

                            if (response.IsSuccessStatusCode)
                            {
                                string responseContent = await response.Content.ReadAsStringAsync();
                                _logger?.LogTrace("KnowledgeQuest:Verify: Received response for queue {QueueId}: {Response}",
                                    queueId, responseContent);

                                // Parse the response
                                var responseObj = JsonConvert.DeserializeObject<VerifyQueueResponse>(responseContent);
                                if (responseObj != null)
                                {
                                    verified = responseObj.verified;
                                    stopwatch.Stop();
                                    _logger?.LogInformation("KnowledgeQuest:Verify: Queue {QueueId} verification result: {Result}, Status: {Status} | Duration: {Duration:N3}s | Retries: {RetryCount}",
                                        queueId, verified, responseObj.status, stopwatch.Elapsed.TotalSeconds, retryCount);
                                    apiCallSuccess = true;
                                }
                                else
                                {
                                    _logger?.LogDebug("KnowledgeQuest:Verify: Failed to parse response for queue {QueueId}", queueId);
                                    retryCount++;
                                }
                            }
                            else if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                // Rate limit exceeded, retry after a delay
                                _logger?.LogDebug("KnowledgeQuest:Verify: Rate limit exceeded for queue {QueueId}, will retry", queueId);
                                retryCount++;
                            }
                            else
                            {
                                await response.Content.ReadAsStringAsync(); // Read content to avoid memory leaks
                                _logger?.LogDebug("KnowledgeQuest:Verify: Failed to verify queue {QueueId}. Status: {Status}",
                                    queueId, response.StatusCode);

                                // Don't retry for client errors (4xx) except rate limiting
                                if ((int)response.StatusCode >= 400 && (int)response.StatusCode < 500 && response.StatusCode != HttpStatusCode.TooManyRequests)
                                {
                                    break;
                                }

                                retryCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogDebug("KnowledgeQuest:Verify: Error calling verify-queue API for queue {QueueId}: {Error}",
                            queueId, ex.Message);
                        retryCount++;
                    }
                }

                // If API call failed, return false (don't process this queue)
                if (!apiCallSuccess)
                {
                    stopwatch.Stop();
                    _logger?.LogDebug("KnowledgeQuest:Verify: API call failed for queue {QueueId}, not processing, elapsed {Duration:N3}s",
                        queueId, stopwatch.Elapsed.TotalSeconds);
                    return CacheAndReturn(queueId, false);
                }

                return CacheAndReturn(queueId, verified);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogDebug("KnowledgeQuest:Error: Failed to verify queue {QueueId}: {Error}, elapsed {Duration:N3}s",
                    queueId, ex.Message, stopwatch.Elapsed.TotalSeconds);
                return CacheAndReturn(queueId, false);
            }
        }

        /// <summary>
        /// Helper method to return a queue verification result
        /// </summary>
        private static bool CacheAndReturn(string queueId, bool result)
        {
            return result;
        }



        // Default JWT secret key (should be overridden by configuration)
        private const string DEFAULT_JWT_SECRET_KEY = "cd462168eff71f112356d9652887194338e01a255358c4a58accf32ce632faaecc2d6e376bf0c00c025263c7108cef122539030cbe30d34d80b2cc90e0b0e5e6df51ae02c48d6c37f293f95dcde78d41f947ccb89089fa2486c2eb6c85895c0e7d14600f3efe330d6ed41491ba9ecd6f85c14917d25456b5e6f7402b48f0b84f0fbe48ba920a03fa600a0c0bdd66d76bc58b0b04e74dfb681249c9097751a9a1f57b329c9cc87e373ca6806828fd520df47be11035787fb76f34e81942506e29e59c969c09ccd122752799fb08356434da99ac0ba8f67519f712514449aff4387333a55977b0a78522a235a17c4801bd18fd248c815aab869d0f5e3e33ee3841a579ba239193fbde5aa96bd642f6fb909209481137bb1e20da72f2e214e1e9ad91f6e20efe4da18f3e7221bb6228f7cb2563076ed22b91b770093eb40dd60507c0c3271bf8763a7175af43ef499fbcfae09e57c0416df8040a941c8863eb3e0d6dc0f99cd18241814766f853273d4166204b8fa5a5635edc05556dc181cc238ad4e23229dabb3333e0d30c3282ecd5d1c8b0de9862dfaad1da2e5363e7ba2ec5159ac80ccf0b420446c1082ec85b5bb98da9693e9e8c649d2144a68a1eaf776eb5b160e2a4ff49ab26a78812216ab3b3be5d31973b1c250b7cc609cf5fae8f3cee95b953d9c3ae7227bc834df19c6ea670fc26c55f27f21960777930e70d1ebc";

        /// <summary>
        /// Gets the Knowledge Quest secret key from configuration
        /// </summary>
        /// <returns>The Knowledge Quest secret key</returns>
        private string GetKnowledgeQuestSecretKey()
        {
            try
            {
                // Try to get the Knowledge Quest secret key from configuration
                string secretKey = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_KNOWLEDGE_QUEST_SECRET_KEY");

                // If not configured, use the default secret key
                if (string.IsNullOrEmpty(secretKey))
                {
                    secretKey = DEFAULT_JWT_SECRET_KEY;
                    _logger?.LogInformation("KnowledgeQuest:Config: Using default Knowledge Quest secret key");
                }

                return secretKey;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "KnowledgeQuest:Config: Failed to get Knowledge Quest secret key");
                return DEFAULT_JWT_SECRET_KEY; // Return the default key as a fallback
            }
        }

        /// <summary>
        /// Generates a JWT token for authenticating with the Knowledge Quest API
        /// </summary>
        /// <param name="secretKey">The secret key to use for signing the token</param>
        /// <returns>The JWT token</returns>
        private string GenerateJwtToken(string secretKey)
        {
            try
            {
                // Create the token descriptor
                var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(secretKey);

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new[] { new Claim("sub", JWT_SUBJECT) }),
                    Expires = DateTime.UtcNow.AddMinutes(JWT_EXPIRATION_MINUTES),
                    IssuedAt = DateTime.UtcNow,
                    Issuer = JWT_ISSUER,
                    Audience = JWT_AUDIENCE,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                // Create and return the token
                var token = tokenHandler.CreateToken(tokenDescriptor);
                return tokenHandler.WriteToken(token);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "KnowledgeQuest:Auth: Failed to generate JWT token");
                return null;
            }
        }

        /// <summary>
        /// Response class for the verify-queue API
        /// </summary>
        private sealed class VerifyQueueResponse
        {
            // Initialize fields with default values to avoid IDE warnings
            public string status = string.Empty;
            public bool verified = false;
        }

        /// <summary>
        /// Gets the transcript URL for a conversation
        /// </summary>
        /// <param name="conversationId">The conversation ID</param>
        /// <param name="peerId">The peer ID</param>
        /// <returns>The transcript URL</returns>
        public async Task<string> GetTranscriptUrlAsync(string conversationId, string peerId)
        {
            _logger?.LogDebug("KnowledgeQuest:Transcript: Getting transcript URL for conversation {ConvId}", conversationId);

            try
            {
                if (string.IsNullOrEmpty(conversationId) || string.IsNullOrEmpty(peerId))
                {
                    _logger?.LogDebug("KnowledgeQuest:Transcript: ConversationId or PeerId is null or empty");
                    return null;
                }

                // Use the existing API client to get the transcript URL
                string transcriptUrlEndpoint = $"{URI}/api/v2/speechandtextanalytics/conversations/{conversationId}/communications/{peerId}/transcripturl";

                using (HttpClientHandler handler = new HttpClientHandler
                {
                    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
                })
                using (HttpClient client = new HttpClient(handler))
                {
                    // Set up the client with the necessary headers
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", GCApiKey);

                    // Get the transcript URL
                    string detailJsonString = await client.GetStringAsync(transcriptUrlEndpoint);

                    // Parse the response
                    VoiceU.VoiceAnalysisURL voiceAURL = JsonConvert.DeserializeObject<VoiceU.VoiceAnalysisURL>(
                        detailJsonString, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                    if (voiceAURL == null || string.IsNullOrEmpty(voiceAURL.url))
                    {
                        _logger?.LogDebug("KnowledgeQuest:Transcript: No transcript URL found for conversation {ConvId}", conversationId);
                        return null;
                    }

                    _logger?.LogTrace("KnowledgeQuest:Transcript: Successfully retrieved transcript URL for conversation {ConvId}", conversationId);
                    return voiceAURL.url;
                }
            }
            catch (HttpRequestException httpEx) when (httpEx.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger?.LogDebug(httpEx, "KnowledgeQuest:Transcript: Transcript not found for conversation {ConvId}", conversationId);
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogDebug(ex, "KnowledgeQuest:Error: Failed to get transcript URL for conversation {ConvId}", conversationId);
                return null;
            }
        }

        /// <summary>
        /// Downloads a transcript from a URL and caches it for future use
        /// </summary>
        /// <param name="transcriptUrl">The URL to download the transcript from</param>
        /// <param name="conversationId">Optional conversation ID for caching</param>
        /// <returns>The transcript JSON</returns>
        public async Task<string> DownloadTranscriptAsync(string transcriptUrl, string conversationId = null)
        {
            _logger?.LogDebug("KnowledgeQuest:Transcript: Downloading transcript from URL");

            try
            {
                if (string.IsNullOrEmpty(transcriptUrl))
                {
                    _logger?.LogDebug("KnowledgeQuest:Transcript: Transcript URL is null or empty");
                    return null;
                }

                using (HttpClientHandler handler = new HttpClientHandler
                {
                    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
                })
                using (HttpClient client = new HttpClient(handler))
                {
                    // Download the transcript
                    string transcriptJson = await client.GetStringAsync(transcriptUrl);

                    if (string.IsNullOrEmpty(transcriptJson))
                    {
                        _logger?.LogDebug("KnowledgeQuest:Transcript: Downloaded transcript is empty");
                        return null;
                    }

                    _logger?.LogTrace("KnowledgeQuest:Transcript: Successfully downloaded transcript, length: {Length} characters",
                        transcriptJson.Length);

                    // Store the transcript in memory if conversation ID is provided
                    if (!string.IsNullOrEmpty(conversationId))
                    {
                        lock (_transcriptCache)
                        {
                            _transcriptCache[conversationId] = transcriptJson;
                        }
                    }

                    return transcriptJson;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogDebug(ex, "KnowledgeQuest:Error: Failed to download transcript from URL");
                return null;
            }
        }

        // Knowledge Quest API configuration
        private const int MAX_RETRY_ATTEMPTS = 3;
        private const int RETRY_DELAY_MS = 1000;

        // Constants for transcript processing columns - used to track transcripts sent to Knowledge Quest service
        private const string COLUMN_PROCESSED = "transcript_processed";
        private const string COLUMN_PROCESSED_DATE = "transcript_processed_date";
        private const string COLUMN_PROCESSED_NOTES = "transcript_processed_notes";

        /// <summary>
        /// Ingests a transcript for processing by Knowledge Quest
        /// This method should only be called after verifying that:
        /// 1. Knowledge Quest is enabled via license
        /// 2. The queue has been verified for Knowledge Quest processing
        /// 3. The transcript is valid and not empty
        /// </summary>
        /// <param name="transcriptJson">The transcript JSON to ingest</param>
        /// <param name="conversationId">The conversation ID for logging purposes</param>
        /// <param name="queueId">The queue ID that was already verified</param>
        /// <returns>True if the ingestion was successful, false otherwise</returns>
        public async Task<bool> IngestTranscriptAsync(string transcriptJson, string conversationId = null, string queueId = null)
        {
            _logger?.LogDebug("KnowledgeQuest:Ingest: Ingesting transcript to Knowledge Quest service for conversation {ConvId}", conversationId);

            try
            {
                if (string.IsNullOrEmpty(transcriptJson))
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Transcript JSON is null or empty for conversation {ConvId}", conversationId);
                    return false;
                }

                // Parse the transcript to get the conversation ID if not provided
                VoiceD.VoiceDetail voiceDetail;
                try
                {
                    voiceDetail = JsonConvert.DeserializeObject<VoiceD.VoiceDetail>(
                        transcriptJson, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug(ex, "KnowledgeQuest:Ingest: Failed to parse transcript JSON for conversation {ConvId}", conversationId);
                    return false;
                }

                if (voiceDetail == null || string.IsNullOrEmpty(voiceDetail.conversationId))
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Invalid transcript JSON or missing conversation ID for conversation {ConvId}", conversationId);
                    return false;
                }

                // Use the conversation ID from the transcript if not provided
                string actualConversationId = conversationId ?? voiceDetail.conversationId;

                // Get the secret key from configuration
                string secretKey = GetKnowledgeQuestSecretKey();
                if (string.IsNullOrEmpty(secretKey))
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Secret key is not configured for conversation {ConvId}", actualConversationId);
                    return false;
                }

                // Generate JWT token
                string token = GenerateJwtToken(secretKey);
                if (string.IsNullOrEmpty(token))
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Failed to generate JWT token for conversation {ConvId}", actualConversationId);
                    return false;
                }

                // Call the Knowledge Quest API to ingest the transcript
                bool apiCallSuccess = await CallKnowledgeQuestIngestApiAsync(actualConversationId, transcriptJson, token);

                if (apiCallSuccess)
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Successfully ingested transcript for conversation {ConvId} in queue {QueueId}",
                        actualConversationId, queueId);
                }
                else
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Failed to ingest transcript for conversation {ConvId} in queue {QueueId}",
                        actualConversationId, queueId);
                }

                return apiCallSuccess;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "KnowledgeQuest:Error: Failed to ingest transcript for conversation {ConvId}", conversationId);
                return false;
            }
        }

        /// <summary>
        /// Updates the transcript_processed column in the database for a specific conversation
        /// This method should only be called when the transcript has been successfully processed
        /// </summary>
        /// <param name="conversationId">The conversation ID</param>
        /// <param name="success">Whether the processing was successful</param>
        /// <param name="notes">Notes about the processing</param>
        /// <returns>True if the database update was successful</returns>
        public bool UpdateTranscriptProcessedStatus(string conversationId, bool success, string notes)
        {
            try
            {
                if (string.IsNullOrEmpty(conversationId))
                {
                    _logger?.LogWarning("KnowledgeQuest:Database: Cannot update transcript status - conversation ID is null or empty");
                    return false;
                }

                // Safely escape the conversation ID and notes to prevent SQL injection
                string safeConversationId = conversationId.Replace("'", "''");
                string safeNotes = (notes ?? string.Empty).Replace("'", "''");
                string processedValue = success ? "true" : "false";
                string currentUtc = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");

                // Build database-specific SQL
                string updateSql;
                switch (DBUtil.DBType)
                {
                    case DatabaseType.PostgreSQL:
                        updateSql = $@"UPDATE convvoiceoverviewdata
                                    SET transcript_processed = {processedValue},
                                        transcript_processed_date = '{currentUtc}'::timestamp,
                                        transcript_processed_notes = '{safeNotes}'
                                    WHERE conversationid = '{safeConversationId}'";
                        break;
                    case DatabaseType.MSSQL:
                        updateSql = $@"UPDATE convvoiceoverviewdata
                                    SET transcript_processed = {(success ? "1" : "0")},
                                        transcript_processed_date = '{currentUtc}',
                                        transcript_processed_notes = '{safeNotes}'
                                    WHERE conversationid = '{safeConversationId}'";
                        break;
                    case DatabaseType.Snowflake:
                        updateSql = $@"UPDATE convvoiceoverviewdata
                                    SET transcript_processed = {processedValue},
                                        transcript_processed_date = '{currentUtc}'::timestamp,
                                        transcript_processed_notes = '{safeNotes}'
                                    WHERE conversationid = '{safeConversationId}'";
                        break;
                    default:
                        _logger?.LogWarning("KnowledgeQuest:Database: Unsupported database type for transcript status update");
                        return false;
                }

                int rowsAffected = DBUtil.ExecuteSqlNonQuery(updateSql);

                if (rowsAffected > 0)
                {
                    _logger?.LogDebug("KnowledgeQuest:Database: Updated transcript status for conversation {ConvId}: success={Success}, notes={Notes}",
                        conversationId, success, notes);
                    return true;
                }
                else
                {
                    _logger?.LogWarning("KnowledgeQuest:Database: No rows updated for conversation {ConvId} - conversation may not exist in database",
                        conversationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "KnowledgeQuest:Database: Failed to update transcript status for conversation {ConvId}", conversationId);
                return false;
            }
        }

        // Counters for summarized logging
        private int _successfulIngestCount = 0;
        private int _alreadyExistsCount = 0;
        private int _noQueueIdCount = 0;
        private int _notCsikqQueueCount = 0;
        private int _errorCount = 0;
        private int _rateLimitCount = 0;
        private int _totalIngestAttempts = 0;

        /// <summary>
        /// Logs summarized statistics for Knowledge Quest ingestion
        /// Only shows categories with non-zero counts
        /// </summary>
        public void LogIngestSummary()
        {
            // Only include summary lines for non-zero counts to keep logs concise
            System.Text.StringBuilder summary = new System.Text.StringBuilder("KnowledgeQuest:Summary: === Knowledge Quest Ingestion Summary ===\n");

            // Always show total attempts
            summary.AppendFormat("Total ingestion attempts: {0}\n", _totalIngestAttempts);

            // Only show categories with non-zero counts
            if (_successfulIngestCount > 0)
                summary.AppendFormat("Successfully processed: {0}\n", _successfulIngestCount);

            if (_alreadyExistsCount > 0)
                summary.AppendFormat("Already exists: {0}\n", _alreadyExistsCount);

            if (_noQueueIdCount > 0)
                summary.AppendFormat("No queue ID: {0}\n", _noQueueIdCount);

            if (_notCsikqQueueCount > 0)
                summary.AppendFormat("Not a CSIKQ queue: {0}\n", _notCsikqQueueCount);

            if (_rateLimitCount > 0)
                summary.AppendFormat("Rate limit exceeded: {0}\n", _rateLimitCount);

            if (_errorCount > 0)
                summary.AppendFormat("Errors: {0}\n", _errorCount);

            summary.Append("=== End of Summary ===");

            // Log the complete summary as a single message
            _logger?.LogInformation(summary.ToString());
        }

        /// <summary>
        /// Calls the Knowledge Quest API to ingest a transcript
        /// Only returns true for actual successful ingestion that should mark transcript as processed
        /// </summary>
        /// <param name="conversationId">The conversation ID</param>
        /// <param name="transcriptJson">The transcript JSON</param>
        /// <param name="token">The JWT token for authentication</param>
        /// <returns>True if the transcript was successfully ingested and should be marked as processed</returns>
        private async Task<bool> CallKnowledgeQuestIngestApiAsync(string conversationId, string transcriptJson, string token)
        {
            int retryCount = 0;
            _totalIngestAttempts++;

            while (retryCount < MAX_RETRY_ATTEMPTS)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        _logger?.LogDebug("KnowledgeQuest:Ingest: Retry attempt {RetryCount} for conversation {ConvId}",
                            retryCount, conversationId);
                        await Task.Delay(RETRY_DELAY_MS * retryCount); // Exponential backoff
                    }

                    using (HttpClientHandler handler = new HttpClientHandler
                    {
                        AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
                    })
                    using (HttpClient client = new HttpClient(handler))
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                        var content = new StringContent(transcriptJson, Encoding.UTF8, "application/json");

                        _logger?.LogTrace("KnowledgeQuest:Ingest: Sending request to {Url} for conversation {ConvId}",
                            KNOWLEDGE_QUEST_INGEST_ENDPOINT, conversationId);

                        HttpResponseMessage response = await client.PostAsync(KNOWLEDGE_QUEST_INGEST_ENDPOINT, content);
                        string responseContent = await response.Content.ReadAsStringAsync();

                        if (response.IsSuccessStatusCode)
                        {
                            return ProcessSuccessfulResponse(conversationId, responseContent);
                        }
                        else if (response.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            _rateLimitCount++;
                            _logger?.LogDebug("KnowledgeQuest:Ingest: Rate limit exceeded for conversation {ConvId}, will retry", conversationId);
                            retryCount++;
                            continue;
                        }
                        else
                        {
                            _errorCount++;
                            _logger?.LogDebug("KnowledgeQuest:Ingest: Failed to send transcript for conversation {ConvId}. Status: {Status}, Response: {Response}",
                                conversationId, response.StatusCode, responseContent);

                            // Don't retry for client errors (4xx) except rate limiting
                            if ((int)response.StatusCode >= 400 && (int)response.StatusCode < 500)
                            {
                                return false;
                            }

                            retryCount++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _errorCount++;
                    _logger?.LogDebug(ex, "KnowledgeQuest:Ingest: Error calling Knowledge Quest API for conversation {ConvId}", conversationId);
                    retryCount++;
                }
            }

            return false; // Failed after all retries
        }

        /// <summary>
        /// Processes a successful HTTP response from the Knowledge Quest API
        /// Only returns true for responses that indicate actual successful ingestion
        /// </summary>
        private bool ProcessSuccessfulResponse(string conversationId, string responseContent)
        {
            try
            {
                var responseObj = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseContent);
                if (responseObj == null)
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Empty response for conversation {ConvId}", conversationId);
                    return false;
                }

                string status = responseObj.ContainsKey("status") ? responseObj["status"]?.ToString() ?? string.Empty : string.Empty;
                bool verified = responseObj.ContainsKey("verified") && Convert.ToBoolean(responseObj["verified"]);

                // Only consider it successfully processed if verified=true AND status indicates actual processing
                if (verified && status.StartsWith("Processed:", StringComparison.OrdinalIgnoreCase))
                {
                    _successfulIngestCount++;
                    _logger?.LogTrace("KnowledgeQuest:Ingest: Successfully processed transcript for conversation {ConvId}", conversationId);
                    return true;
                }
                else if (status.Contains("already exists", StringComparison.OrdinalIgnoreCase))
                {
                    _alreadyExistsCount++;
                    _logger?.LogTrace("KnowledgeQuest:Ingest: Transcript for conversation {ConvId} already exists", conversationId);
                    return true; // Already processed, so consider it successful
                }
                else if (status.Contains("no queueid", StringComparison.OrdinalIgnoreCase))
                {
                    _noQueueIdCount++;
                    _logger?.LogTrace("KnowledgeQuest:Ingest: No queue ID in transcript for conversation {ConvId}", conversationId);
                    return false; // Don't mark as processed since it wasn't actually ingested
                }
                else if (status.Contains("not a CSIKQ queue", StringComparison.OrdinalIgnoreCase))
                {
                    _notCsikqQueueCount++;
                    _logger?.LogTrace("KnowledgeQuest:Ingest: Queue for conversation {ConvId} is not a CSIKQ queue", conversationId);
                    return false; // Don't mark as processed since it wasn't actually ingested
                }
                else
                {
                    _logger?.LogDebug("KnowledgeQuest:Ingest: Unexpected response for conversation {ConvId}: verified={Verified}, status={Status}",
                        conversationId, verified, status);
                    return false; // Unknown response, don't mark as processed
                }
            }
            catch (Exception ex)
            {
                _logger?.LogDebug(ex, "KnowledgeQuest:Ingest: Failed to parse response for conversation {ConvId}", conversationId);
                return false; // Failed to parse, don't mark as processed
            }
        }

        /// <summary>
        /// Ensures that the transcript processing columns exist in the DataTable to prevent column count mismatches
        /// </summary>
        /// <param name="table">The DataTable to ensure columns for</param>
        private static void EnsureTranscriptColumns(DataTable table)
        {
            // Add transcript_processed column if it doesn't exist
            if (!table.Columns.Contains(COLUMN_PROCESSED))
                table.Columns.Add(COLUMN_PROCESSED, typeof(bool));

            // Add transcript_processed_date column if it doesn't exist
            if (!table.Columns.Contains(COLUMN_PROCESSED_DATE))
                table.Columns.Add(COLUMN_PROCESSED_DATE, typeof(DateTime));

            // Add transcript_processed_notes column if it doesn't exist
            if (!table.Columns.Contains(COLUMN_PROCESSED_NOTES))
                table.Columns.Add(COLUMN_PROCESSED_NOTES, typeof(string));
        }

        #endregion
    }
}