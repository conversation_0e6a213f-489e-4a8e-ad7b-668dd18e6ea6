DROP VIEW IF EXISTS vwSurveyQuestionGroupScores;
CREATE OR REPLACE VIEW vwSurveyQuestionGroupScores AS
SELECT
    surveyQuestionGroupScores.surveyid,
    surveyQuestionGroupScores.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyQuestionGroupScores.surveyformid,
    surveyQuestionGroupScores.surveyname,
    surveyQuestionGroupScores.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyQuestionGroupScores.agentteamid,
    surveyQuestionGroupScores.queueid,
    queue.name AS queuename,
    surveyQuestionGroupScores.questiongroupid,
    surveyQuestionGroupScores.questiongroupname,
    surveyQuestionGroupScores.questiongrouptotalscore,
    surveyQuestionGroupScores.questiongroupmaxtotalscore,
    surveyQuestionGroupScores.questiongroupmarkedna,
    surveyQuestionGroupScores.updated
FROM
    surveyQuestionGroupScores
    LEFT JOIN surveydata survey ON survey.surveyid::text = surveyQuestionGroupScores.surveyid::text
    LEFT JOIN userdetails agent ON agent.id::text = surveyQuestionGroupScores.agentid::text
    LEFT JOIN userdetails manager ON manager.id::text = agent.manager::text
    LEFT JOIN queuedetails queue ON queue.id::text = surveyQuestionGroupScores.queueid::text;

-- spell-checker: ignore: surveyid questiongroupid
COMMENT ON COLUMN vwSurveyQuestionGroupScores.surveyid IS 'Survey ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.conversationid IS 'Conversation ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.completeddate IS 'Survey Completed Date (UTC)';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.completeddateltc IS 'Survey Completed Date (LTC)';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.surveyformid IS 'Survey Form ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.surveyname IS 'Survey Name';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.agentid IS 'Agent ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.agentname IS 'Agent Name';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.agentdepartment IS 'Agent Department';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.agentmanager IS 'Agent Manager';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.agentteamid IS 'Agent Team ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.queueid IS 'Queue ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.queuename IS 'Queue Name';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.questiongroupid IS 'Question Group ID';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.questiongrouptotalscore IS 'Question Group Total Score';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.questiongroupmaxtotalscore IS 'Question Group Max Total Score';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.questiongroupmarkedna IS 'Question Group Marked as N/A';
COMMENT ON COLUMN vwSurveyQuestionGroupScores.updated IS 'Last Updated';

COMMENT ON VIEW vwSurveyQuestionGroupScores IS 'Survey Question Group Scores View';
