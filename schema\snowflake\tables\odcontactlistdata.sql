CREATE OR REPLACE PROCEDURE check_and_drop_table()
RETURNS STRING
LANGUAGE JAVASCRIPT
AS
$$
    // Declare variable to hold the DDL
    var table_ddl_query = `SELECT GET_DDL('TABLE', 'ODCONTACTLISTDATA') 
                           FROM INFORMATION_SCHEMA.TABLES 
                           WHERE TABLE_SCHEMA = CURRENT_SCHEMA() 
                             AND TABLE_NAME = 'ODCONTACTLISTDATA';`;
    
    // Execute the query to get the DDL
    var result = snowflake.execute({sqlText: table_ddl_query});
    var table_ddl = null;

    // Fetch the result, if any
    if (result.next()) {
        table_ddl = result.getColumnValue(1);
    }

    // Check if the DDL was retrieved
    if (table_ddl !== null) {
        // Check for quotes in the DDL
        if (table_ddl.indexOf('"') > -1) {
            // Drop the table if it contains quoted identifiers
            snowflake.execute({sqlText: 'DROP TABLE ODCONTACTLISTDATA'});
            return 'Table dropped because it contains quoted identifiers.';
        } else {
            return 'Table has no quoted identifiers; no action taken.';
        }
    } else {
        return 'Table does not exist.';
    }
$$;

CALL check_and_drop_table();


CREATE TABLE IF NOT EXISTS odcontactlistdata (
    keyid varchar(100),
    contactlistid varchar(50),
    inin_outbound_id varchar(50),
    updated timestamp without time zone,
    PRIMARY KEY (keyid)
);