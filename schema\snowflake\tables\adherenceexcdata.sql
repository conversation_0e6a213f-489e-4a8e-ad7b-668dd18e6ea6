CREATE TABLE IF NOT EXISTS adherenceexcdata (
    keyid VARCHAR(100) NOT NULL,
    userid VARCHAR(50),
    startdate TIMESTAMP WITHOUT TIME ZONE,
    startdateltc TIMESTAMP WITHOUT TIME ZONE,
    enddate TIMES<PERSON><PERSON> WITHOUT TIME ZONE,
    enddateltc TIMESTAMP WITHOUT TIME ZONE,
    durationsecs INTEGER,
    tolerance INTEGER,
    actualdurationsecs INTEGER,
    scheduledactivitycategory VARCHAR(50),
    actualactivitycategory VARCHAR(50),
    systempresence VARCHAR(50),
    routingstatus VARCHAR(50),
    impact VARCHAR(50),
    updated TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT adherenceexcdata_pkey PRIMARY KEY (keyid)
);

CREATE OR REPLACE PROCEDURE check_and_update_adherenceexcdata()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  var KeyidPrefix = 'v1_';
  
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) 
              FROM adherenceexcdata
              WHERE SUBSTR(keyid, 1, ` + KeyidPrefix.length + `) <> '` + KeyidPrefix + `'`
  });
  
  resultSet.next();
  
  var exists_flag = resultSet.getColumnValue(1);

  if (exists_flag > 0) {
    snowflake.execute({ 
      sqlText: `DELETE FROM adherenceexcdata
                WHERE LEFT(keyid, ` + KeyidPrefix.length + `) <> '` + KeyidPrefix + `'`
    });
    snowflake.execute({ 
      sqlText: `UPDATE tabledefinitions 
                SET datekeyfield = DATEADD(month, -12, CURRENT_TIMESTAMP()) 
                WHERE tablename = 'adherencedaydata'`
    });
  } else {
    return 'No records found with old keyid in use.';
  }
$$;

CALL check_and_update_adherenceexcdata();

drop procedure check_and_update_adherenceexcdata();