CREATE TABLE IF NOT EXISTS chatData(
    keyid VARCHAR(50) NOT NULL,
    conversationid VARCHAR(50) NOT NULL,
    conversationstart TIMESTAMP_NTZ,
    conversationstartltc TIMESTAMP_NTZ,
    userid VARCHAR(50),
    chatinitiatedby VARCHAR(10),
    agentchatcount INT,
    agentchattotal DECIMAL(20, 2),
    agentchatmax DECIMAL(20, 2),
    agentchatmin DECIMAL(20, 2),
    agenthasread DECIMAL(20, 2),
    custchatcount INT,
    custchattotal DECIMAL(20, 2),
    custchatmax DECIMAL(20, 2),
    custchatmin DECIMAL(20, 2),
    updated timestamp without time zone,
    CONSTRAINT PK_chatData PRIMARY KEY (keyid)
);
ALTER TABLE chatData 
ADD column IF NOT exists  agenthasread DECIMAL(20, 2);