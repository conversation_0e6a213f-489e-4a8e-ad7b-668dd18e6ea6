CREATE TABLE IF NOT EXISTS chatdata (
    keyid VARCHAR(50) NOT NULL,
    conversationid VARCHAR(50),
    conversationstart TIMESTAMP_NTZ,
    conversationstartltc TIMESTAMP_NTZ,
    userid VARCHAR(50),
    chatinitiatedby VARCHAR(10),
    agentchatcount INTEGER,
    agentchattotal DECIMAL(20, 2),
    agentchatmax DECIMAL(20, 2),
    agentchatmin DECIMAL(20, 2),
    agenthasread DECIMAL(20, 2),
    custchatcount INTEGER,
    custchattotal DECIMAL(20, 2),
    custchatmax DECIMAL(20, 2),
    custchatmin DECIMAL(20, 2),
    updated TIMESTAMP_NTZ,
    mediatype VARCHAR(10),
    CONSTRAINT chatdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE chatData 
ADD column IF NOT exists  agenthasread DECIMAL(20, 2);

ALTER TABLE chatData 
ADD column IF NOT exists  mediatype VARCHAR(10);