CREATE OR REPLACE FUNCTION timezonecalcs(tzset text) RETURNS TABLE(
    utctime timestamp without time zone,
    ltctime timestamp without time zone,
    diff integer,
    timezonechosen text
) LANGUAGE plpgsql AS $function$
BEGIN
EXECUTE 'SET LOCAL TIME ZONE ''' || tzset || ''';';

return query
select
    timezone('UTC', now()) :: timestamp as utctime,
    CAST (current_timestamp AS timestamp with time zone) :: timestamp as ltctime,
    DATEDIFF(
        'second',
        timezone('UTC', now()) :: timestamp,
        CAST (current_timestamp AS timestamp with time zone) :: timestamp
    ) as diff,
    tzset as timezonechosen;

end;

$function$;