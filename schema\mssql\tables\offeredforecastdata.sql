IF dbo.csg_table_exists('offeredforecastdata') = 0
CREATE TABLE [offeredforecastdata](
    [keyid] [nvarchar](150) NOT NULL,
    [businessunitid] [nvarchar](50),
    [planninggroup] [nvarchar](50),
    [scheduleid] [nvarchar](50),
    [shorttermforecastid] [nvarchar](50),
    [weekdate] [date],
    [week] [int],
    [startdate] [datetime],
    [startdateltc] [datetime],
    [avghandleperinterval] [decimal](20, 2),
    [offeredperinterval] [decimal](20, 2),
    canuseforscheduling [bit] NULL,
    [updated] [datetime],
    CONSTRAINT [PK_offeredforecastdata] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('offeredforecastdata', 'canuseforscheduling') = 0
    ALTER TABLE offeredforecastdata ADD canuseforscheduling [bit] NULL;

-- Add index on weekdate column if it doesn't exist
IF dbo.csg_index_exists('IX_offeredforecastdata_weekdate', 'offeredforecastdata') = 0
BEGIN
    CREATE INDEX [IX_offeredforecastdata_weekdate] ON [offeredforecastdata]([weekdate]);
    PRINT 'Created index IX_offeredforecastdata_weekdate on offeredforecastdata.weekdate';
END