﻿
using System.Data;
using StandardUtils;
using Newtonsoft.Json;
using GenesysCloudDefSurveys;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils;

#nullable enable
public class SurveyData
{
    private readonly ILogger? _logger;
    private string CustomerKeyID { get; set; }
    private string GCApiKey { get; set; }
    private Utils UCAUtils = new Utils();
    private Simple3Des UCAEncryption;
    private GCUtils GCUtilities;
    private JsonUtils JsonActions;
    private string URI = "";

    private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

    public SurveyData(ILogger? logger = null)
    {
        _logger = logger;
        GCUtilities = new GCUtils(logger);
        JsonActions = new JsonUtils(logger);

        GCUtilities.Initialize();
        UCAUtils = new StandardUtils.Utils();
        CustomerKeyID = GCUtilities.CustomerKeyID;
        UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        DataSet GCControlData = GCUtilities.GCControlData;
        if (GCControlData == null)
            throw new NullReferenceException("Failed to query control server");
        URI = GCControlData.Tables["GCControlData"]?.Rows[0]["GC_URL"].ToString() ?? "";
        if (string.IsNullOrEmpty(URI))
            throw new ArgumentException("Genesys base URI not set");
        GCApiKey = GCUtilities.GCApiKey;
        DBUtil.Initialize();
    }

    public SurveyAggregations GetSurveyAggregationDataFromGC(DateTime StartDate, DateTime EndDate)
    {
        /*
            Notes:
            - interval: surveys show only in the survey sent time window.
                        i.e., updates to the survey state won't put them into
                        multiple intervals. This is true even if grouping by
                        the survey state, the finished state will always be in
                        the same interval as the sent state. To get the sent
                        time of the survey look at the conversation end time.
        */
        var apiMethod = HttpMethod.Post;
        var apiEndpoint = "/api/v2/analytics/surveys/aggregates/query";
        string body = string.Format(
            @"{{
                ""interval"":""{0}/{1}"",
                ""metrics"":[
                    ""nSurveyErrors"",""nSurveysAbandoned"",""nSurveyResponses"",""nSurveysDeleted"",
                    ""nSurveysExpired"",""nSurveysSent"",""nSurveysStarted"",""oSurveyTotalScore""
                ],
                ""groupBy"":[
                    ""surveyId""
                ]
            }}",
            StartDate.ToString("s"),    // 0
            EndDate.ToString("s")       // 1
        );

        string? apiResult = null;
        try
        {
            apiResult = JsonActions.JsonReturnString(URI + apiEndpoint, GCApiKey, body);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Genesys Cloud API {ApiMethod} {ApiEndpoint}", apiMethod, apiEndpoint);
            throw;
        }

        if (apiResult == null)
        {
            _logger?.LogError("Empty result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint}", apiMethod, apiEndpoint);
            throw new InvalidDataException(
                $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
            );
        }

        // Check for error responses before attempting JSON deserialization
        if (apiResult.Contains("\"error\":") || apiResult.Contains("\"statusCode\":") || apiResult.Contains("\"message\":"))
        {
            _logger?.LogError("API Error response from Genesys Cloud {ApiMethod} {ApiEndpoint}: {Response}", apiMethod, apiEndpoint, apiResult);

            // Check for specific error types
            if (apiResult.Contains("\"statusCode\": \"Forbidden\"") || apiResult.Contains("Access Forbidden") || apiResult.Contains("\"status\":403"))
            {
                // Check for permanent permission issues (missing permissions)
                if (apiResult.Contains("missing.division.permission") ||
                    apiResult.Contains("missing.permission") ||
                    apiResult.Contains("You are missing the following permission") ||
                    apiResult.Contains("\"isPermanent\": true"))
                {
                    _logger?.LogError("Permanent access forbidden for survey aggregation API. Missing required permissions. This feature will be disabled.");
                    throw new UnauthorizedAccessException($"Permanent Access Forbidden for {apiEndpoint}: Missing required permissions");
                }
                else
                {
                    _logger?.LogWarning("Temporary access forbidden for survey aggregation API. Returning empty results to allow retry.");
                    return new SurveyAggregations(); // Return empty results for temporary issues
                }
            }

            // For other errors, return empty results to prevent JSON deserialization errors
            _logger?.LogWarning("API returned error response for {ApiMethod} {ApiEndpoint}. Returning empty results.", apiMethod, apiEndpoint);
            return new SurveyAggregations();
        }

        // This API returns an empty object ({}) if there were no surveys in the period.
        if (apiResult == "{}")
        {
            _logger?.LogDebug("No surveys found in the specified period for {ApiMethod} {ApiEndpoint}", apiMethod, apiEndpoint);
            return new SurveyAggregations();
        }

        SurveyAggregations? gcSurveyAggregations = null;
        try
        {
            gcSurveyAggregations = JsonConvert.DeserializeObject<SurveyAggregations>(apiResult);
        }
        catch (JsonException jsonEx)
        {
            _logger?.LogError(jsonEx, "JSON deserialization error for {ApiMethod} {ApiEndpoint}. Response: {Response}",
                apiMethod, apiEndpoint, apiResult.Length > 200 ? apiResult.Substring(0, 200) + "..." : apiResult);
            throw;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Unexpected error parsing result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint}", apiMethod, apiEndpoint);
            throw;
        }

        if (gcSurveyAggregations == null)
        {
            _logger?.LogError("Failed to parse result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint}", apiMethod, apiEndpoint);
            throw new InvalidDataException(
                $"Failed to parse result from Genesys Cloud when calling {apiMethod} {apiEndpoint}"
            );
        }

        _logger?.LogDebug("Successfully retrieved {ResultCount} survey aggregation results from {ApiMethod} {ApiEndpoint}",
            gcSurveyAggregations.results?.Length ?? 0, apiMethod, apiEndpoint);
        return gcSurveyAggregations;
    }

    public Survey GetSurveyFromGC(string surveyId)
    {
        var apiMethod = HttpMethod.Get;
        var apiEndpoint = $"/api/v2/quality/surveys/{surveyId}";

        string? apiResult = null;
        try
        {
            apiResult = JsonActions.JsonReturnString(URI + apiEndpoint, GCApiKey);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Genesys Cloud API {ApiMethod} {ApiEndpoint} for survey {SurveyId}", apiMethod, apiEndpoint, surveyId);
            throw;
        }

        if (apiResult == null)
        {
            _logger?.LogError("Empty result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint} for survey {SurveyId}", apiMethod, apiEndpoint, surveyId);
            throw new InvalidDataException(
                $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
            );
        }

        // Check for error responses before attempting JSON deserialization
        if (apiResult.Contains("\"error\":") || apiResult.Contains("\"statusCode\":") || apiResult.Contains("\"message\":"))
        {
            _logger?.LogError("API Error response from Genesys Cloud {ApiMethod} {ApiEndpoint} for survey {SurveyId}: {Response}",
                apiMethod, apiEndpoint, surveyId, apiResult);

            // Check for specific error types
            if (apiResult.Contains("\"statusCode\": \"Forbidden\"") || apiResult.Contains("Access Forbidden") || apiResult.Contains("\"status\":403"))
            {
                // Check for permanent permission issues (missing permissions)
                if (apiResult.Contains("missing.division.permission") ||
                    apiResult.Contains("missing.permission") ||
                    apiResult.Contains("You are missing the following permission") ||
                    apiResult.Contains("\"isPermanent\": true"))
                {
                    _logger?.LogError("Permanent access forbidden for survey API {SurveyId}. Missing required permissions. This survey will be skipped.", surveyId);
                    throw new UnauthorizedAccessException($"Permanent Access Forbidden for {apiEndpoint}: Missing required permissions");
                }
                else
                {
                    _logger?.LogWarning("Temporary access forbidden for survey API {SurveyId}. This survey will be skipped for now.", surveyId);
                    throw new UnauthorizedAccessException($"Temporary Access Forbidden for {apiEndpoint}");
                }
            }

            // For other errors, throw an exception with meaningful context
            throw new InvalidDataException($"API Error for {apiMethod} {apiEndpoint}: {apiResult}");
        }

        Survey? gcSurvey = null;
        try
        {
            gcSurvey = JsonConvert.DeserializeObject<Survey>(apiResult);
        }
        catch (JsonException jsonEx)
        {
            _logger?.LogError(jsonEx, "JSON deserialization error for {ApiMethod} {ApiEndpoint} survey {SurveyId}. Response: {Response}",
                apiMethod, apiEndpoint, surveyId, apiResult.Length > 200 ? apiResult.Substring(0, 200) + "..." : apiResult);
            throw;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Unexpected error parsing result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint} for survey {SurveyId}",
                apiMethod, apiEndpoint, surveyId);
            throw;
        }

        if (gcSurvey == null)
        {
            _logger?.LogError("Failed to parse result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint} for survey {SurveyId}",
                apiMethod, apiEndpoint, surveyId);
            throw new InvalidDataException(
                $"Failed to parse result from Genesys Cloud when calling {apiMethod} {apiEndpoint}"
            );
        }

        _logger?.LogDebug("Successfully retrieved survey {SurveyId} from {ApiMethod} {ApiEndpoint}", surveyId, apiMethod, apiEndpoint);
        return gcSurvey;
    }
}
#nullable restore
