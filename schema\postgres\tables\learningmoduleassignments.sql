-- Learning Module Assignments Table
-- Stores assignment data for learning modules assigned to users
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user is assigned the learning module
-- - moduleid: Links to learningmodules.id to identify which learning module is assigned
-- - Correlates with learningassignmentresults table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningassignmentresults on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

CREATE TABLE IF NOT EXISTS learningmoduleassignments (
    id VARCHAR(50) NOT NULL,
    userid VARCHAR(50), -- User ID from Genesys Cloud - links assignments to specific users
    moduleid VARCHAR(50), -- Learning Module ID - links assignments to specific learning modules
    assessmentId VARCHAR(50),
    isOverdue BOOLEAN,
    version VARCHAR(255),
    percentageScore numeric(20, 2),
    assessmentPercentageScore numeric(20, 2),
    isRule BOOLEAN,
    isManual BOOLEAN,
    isPassed BOOLEAN,
    isLatest BOOLEAN,
    assessmentCompletionPercentage numeric(20, 2),
    completionPercentage numeric(20, 2),
    dateRecommendedForCompletion TIMESTAMP WITHOUT TIME ZONE,
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    dateSubmitted TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    dateAssigned TIMESTAMP WITHOUT TIME ZONE, -- Date when assignment was assigned to user
    dateDue TIMESTAMP WITHOUT TIME ZONE, -- Due date for assignment completion
    state VARCHAR(50), -- Current state of assignment (e.g., Assigned, InProgress, Completed)
    updated timestamp without time zone,
    CONSTRAINT learningmoduleassignments_pkey PRIMARY KEY (id)
);

-- Add missing columns if they don't exist (for existing installations)
ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS userid VARCHAR(50);

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS moduleid VARCHAR(50);

-- Add missing assignment-specific columns if they don't exist (for existing installations)
ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS dateAssigned TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS dateDue TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS state VARCHAR(50);

-- Add comprehensive table and column comments for documentation
COMMENT ON TABLE learningmoduleassignments IS
'Learning Module Assignments Table - Stores assignment data for learning modules assigned to users.

This table tracks the complete lifecycle of learning module assignments from initial assignment
through completion. It serves as the primary source for assignment tracking and correlates with
learningassignmentresults via userid+moduleid for comprehensive learning analytics.

Key Performance Optimizations:
- Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
- User IDs extracted from assignment response data rather than query parameters
- Enables efficient user-module assignment and completion analytics

Cross-table Relationships:
- userid: Links to userdetails.id for user identification and demographics
- moduleid: Links to learningmodules.id for module details and metadata
- Correlates with learningassignmentresults on userid+moduleid for assignment-to-completion tracking

Analytics Capabilities:
- Supports user assignment summaries and module completion rate analysis
- Enables performance metrics and learning analytics across user-module relationships
- Provides foundation for learning dashboard and reporting functionality';

-- Column-level comments for detailed documentation
COMMENT ON COLUMN learningmoduleassignments.id IS
'Primary key - Unique identifier for each learning module assignment record from Genesys Cloud';

COMMENT ON COLUMN learningmoduleassignments.userid IS
'User ID from Genesys Cloud - Links assignments to specific users.
Enables user-centric analytics and correlates with userdetails.id for user demographics.
Populated from assignment response data during module-based API retrieval for optimal performance.';

COMMENT ON COLUMN learningmoduleassignments.moduleid IS
'Learning Module ID - Links assignments to specific learning modules.
Standardized to lowercase for consistency across all database types (MSSQL, PostgreSQL, Snowflake).
Correlates with learningmodules.id for module metadata and learningassignmentresults.moduleid for completion tracking.';

COMMENT ON COLUMN learningmoduleassignments.assessmentId IS
'Assessment identifier associated with the learning module assignment';

COMMENT ON COLUMN learningmoduleassignments.isOverdue IS
'Boolean flag indicating whether the assignment is past its due date';

COMMENT ON COLUMN learningmoduleassignments.version IS
'Version identifier for the learning module assignment';

COMMENT ON COLUMN learningmoduleassignments.percentageScore IS
'Overall percentage score achieved for the assignment';

COMMENT ON COLUMN learningmoduleassignments.assessmentPercentageScore IS
'Specific assessment percentage score for the assignment';

COMMENT ON COLUMN learningmoduleassignments.isRule IS
'Boolean flag indicating whether the assignment was created by a rule';

COMMENT ON COLUMN learningmoduleassignments.isManual IS
'Boolean flag indicating whether the assignment was manually created';

COMMENT ON COLUMN learningmoduleassignments.isPassed IS
'Boolean flag indicating whether the assignment has been successfully passed';

COMMENT ON COLUMN learningmoduleassignments.isLatest IS
'Boolean flag indicating whether this is the latest version of the assignment';

COMMENT ON COLUMN learningmoduleassignments.assessmentCompletionPercentage IS
'Percentage completion of the assessment component';

COMMENT ON COLUMN learningmoduleassignments.completionPercentage IS
'Overall completion percentage of the learning module assignment';

COMMENT ON COLUMN learningmoduleassignments.dateRecommendedForCompletion IS
'Recommended completion date for the assignment';

COMMENT ON COLUMN learningmoduleassignments.dateCreated IS
'Timestamp when the assignment was created in Genesys Cloud';

COMMENT ON COLUMN learningmoduleassignments.dateModified IS
'Timestamp when the assignment was last modified in Genesys Cloud';

COMMENT ON COLUMN learningmoduleassignments.dateSubmitted IS
'Timestamp when the assignment was submitted for completion';

COMMENT ON COLUMN learningmoduleassignments.lengthInMinutes IS
'Duration of the learning module in minutes';

COMMENT ON COLUMN learningmoduleassignments.dateAssigned IS
'Timestamp when the assignment was assigned to the user';

COMMENT ON COLUMN learningmoduleassignments.dateDue IS
'Due date for the assignment completion';

COMMENT ON COLUMN learningmoduleassignments.state IS
'Current state of the assignment (e.g., Assigned, InProgress, Completed)';

COMMENT ON COLUMN learningmoduleassignments.updated IS
'Timestamp when this record was last updated in the local database';
