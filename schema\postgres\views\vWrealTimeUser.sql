DROP VIEW IF EXISTS vwrealtimeuser;

CREATE
OR REPLACE VIEW vwrealtimeuser as
SELECT
    rd.name as user_name,
    rd.id AS user_id,
    rd.divisionid,
    rd.groupname,
    rd.managername,
    rd.managerid,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatus
        WHEN rd.systempresence = 'ON_QUEUE' :: text THEN rd.routingstatus :: text
        ELSE upper(
            replace(
                rd.orgpresence,
                'On Queue' :: text,
                'ON_QUEUE' :: text
            )
        )
    END AS agentstatus,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE' :: text THEN rd.routstattime
        ELSE rd.presencetime
    END AS agenttime,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE' :: text THEN rd.routstattime * interval '1 sec'
        ELSE rd.presencetime * interval '1 sec'
    END AS agenttime_formatted,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE' :: text THEN rd.routstattime
        ELSE rd.presencetime
    END :: numeric / 86400.00 AS agenttimeday,
    rd.systempresence,
    rd.orgpresence,
    rd.routingstatus,
    rd.routstarttime,
    rd.routstattime,
    rd.routstattimeday,
    rd.presenceid,
    rd.presstarttime,
    rd.presencetime,
    rd.presencetimeday,
    rd.queuename,
    rd.conversationid,
    rd.media,
    rd.direction,
    --rd.convstatus,
    --rd.convstatustime,
    rd.adherencestate,
    rd.adherencestarttime,
    rd.impact,
    rd.scheduledactivitycategory
FROM
    (
        SELECT
            rl.id,
            rl.name,
            ud.managerid,
            ud.managername,
            ud.divisionid,
            upper(
                replace(
                    rl.systempresence :: text,
                    'On Queue' :: text,
                    'ON_QUEUE' :: text
                )
            ) AS systempresence,
            upper(
                replace(
                    pr.orgpresence :: text,
                    'On Queue' :: text,
                    'ON_QUEUE' :: text
                )
            ) AS orgpresence,
            CASE
                WHEN rl.routingstatus :: text = 'IDLE' :: text
                AND (
                    rl.cccallactive + rl.othcallactive + rl.cbcallactive + rl.cbothcallactive + rl.ccemailactive
                ) > 0 THEN 'ALERT' :: varchar
                ELSE rl.routingstatus
            END AS routingstatus,
            rl.routstarttime,
            datediff(
                'second' :: varchar,
                rl.routstarttime,
                now_utc()
            ) AS routstattime,
            datediff(
                'second' :: varchar,
                rl.routstarttime,
                now_utc()
            ) :: numeric / 86400.00 AS routstattimeday,
            rl.presenceid,
            rl.presstarttime,
            datediff(
                'second' :: varchar,
                rl.presstarttime,
                now_utc()
            ) AS presencetime,
            datediff(
                'second' :: varchar,
                rl.presstarttime,
                now_utc()
            ) :: numeric / 86400.00 AS presencetimeday,
            qd.name AS queuename,
            qd.id AS queueid,
            uc.conversationid,
            uc.media,
            uc.direction,
            uc.conversationstate,
            --uc.talktimeltc  * '-1'::integer AS convstatustime,
            gd.name AS groupname,
            rl.adherencestate,
            rl.adherencestarttime,
            rl.impact,
            rl.scheduledactivitycategory
        FROM
            userrealtimedata rl
            LEFT JOIN presencedetails pr ON pr.id :: text = rl.presenceid :: text
            LEFT JOIN userrealtimeconvdata uc ON uc.userid :: text = rl.id :: text
            LEFT JOIN queuedetails qd ON qd.id :: text = uc.queueid :: text
            LEFT JOIN vwuserdetail ud ON ud.id :: text = rl.id :: text
            LEFT JOIN usergroupmappings ug ON ug.userid :: text = rl.id :: text
            LEFT JOIN groupdetails gd ON gd.id :: text = ug.groupid :: text    ) rd;

COMMENT ON COLUMN vwRealTimeUser.user_name IS 'Name of the user';
COMMENT ON COLUMN vwRealTimeUser.user_id IS 'GUID of the user';
COMMENT ON COLUMN vwRealTimeUser.divisionid IS 'GUID of the division';
COMMENT ON COLUMN vwRealTimeUser.groupname IS 'Name of the user group';
COMMENT ON COLUMN vwRealTimeUser.managername IS 'Name of the user manager';
COMMENT ON COLUMN vwRealTimeUser.managerid IS 'ID of the user manager';
COMMENT ON COLUMN vwRealTimeUser.agentstatus IS 'Status of the user (agent)';
COMMENT ON COLUMN vwRealTimeUser.agenttime IS 'Time of the user status';
COMMENT ON COLUMN vwRealTimeUser.agenttime_formatted IS 'Formatted time of the user status';
COMMENT ON COLUMN vwRealTimeUser.agenttimeday IS 'Duration of the user status in days';
COMMENT ON COLUMN vwRealTimeUser.systempresence IS 'System presence status of the user';
COMMENT ON COLUMN vwRealTimeUser.orgpresence IS 'Original presence status of the user';
COMMENT ON COLUMN vwRealTimeUser.routingstatus IS 'Routing status of the user';
COMMENT ON COLUMN vwRealTimeUser.routstarttime IS 'Start time of the user routing status';
COMMENT ON COLUMN vwRealTimeUser.routstattime IS 'Time of the user routing status';
COMMENT ON COLUMN vwRealTimeUser.routstattimeday IS 'Duration of the user routing status in days';
COMMENT ON COLUMN vwRealTimeUser.presenceid IS 'Presence GUID';
COMMENT ON COLUMN vwRealTimeUser.presstarttime IS 'Start time of the user presence';
COMMENT ON COLUMN vwRealTimeUser.presencetime IS 'Time of the user presence';
COMMENT ON COLUMN vwRealTimeUser.presencetimeday IS 'Duration of the user presence in days';
COMMENT ON COLUMN vwRealTimeUser.queuename IS 'Name of the associated queue';
COMMENT ON COLUMN vwRealTimeUser.conversationid IS 'ID of the associated conversation';
COMMENT ON COLUMN vwRealTimeUser.media IS 'Type of media';
COMMENT ON COLUMN vwRealTimeUser.direction IS 'Direction of the conversation';
COMMENT ON COLUMN vwRealTimeUser.adherencestate IS 'Adherence state of the user';
COMMENT ON COLUMN vwRealTimeUser.adherencestarttime IS 'Start time of adherence';
COMMENT ON COLUMN vwRealTimeUser.impact IS 'Impact of the user on the conversation';
COMMENT ON COLUMN vwRealTimeUser.scheduledactivitycategory IS 'Category of scheduled activity';
COMMENT ON VIEW vwRealTimeUser IS 'Real-time user data';

