-- MSSQL Table Template
-- Use this template when creating new tables in MSSQL

-- Check if table exists before creating
IF dbo.csg_table_exists('table_name') = 0
CREATE TABLE [table_name](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](100),
    [description] [nvarchar](max),
    [created_date] [datetime],
    [updated_date] [datetime],
    CONSTRAINT [PK_table_name] PRIMARY KEY ([id])
);

-- Add columns if they don't exist
IF dbo.csg_column_exists('table_name', 'new_column') = 0
    ALTER TABLE table_name ADD new_column [nvarchar](100);

-- Create indexes if they don't exist
IF dbo.csg_index_exists('IX_table_name_column', 'table_name') = 0
CREATE INDEX [IX_table_name_column] ON [table_name]([column]);

-- Example of adding a foreign key
IF NOT EXISTS (
    SELECT * FROM sys.foreign_keys 
    WHERE object_id = OBJECT_ID('FK_table_name_reference_table')
    AND parent_object_id = OBJECT_ID('table_name')
)
ALTER TABLE [table_name]
ADD CONSTRAINT [FK_table_name_reference_table] FOREIGN KEY ([reference_id])
REFERENCES [reference_table] ([id]);
