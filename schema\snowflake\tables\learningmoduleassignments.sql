-- Learning Module Assignments Table
-- Stores assignment data for learning modules assigned to users
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user is assigned the learning module
-- - moduleid: Links to learningmodules.id to identify which learning module is assigned
-- - Correlates with learningassignmentresults table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningassignmentresults on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

CREATE TABLE IF NOT EXISTS learningmoduleassignments (
    id VARCHAR(50) NOT NULL,
    userid VARCHAR(50), -- User ID from Genesys Cloud - links assignments to specific users
    moduleid VARCHAR(50), -- Learning Module ID - links assignments to specific learning modules
    assessmentId VARCHAR(50),
    isOverdue BOOLEAN,
    version VARCHAR(255),
    percentageScore numeric(20, 2),
    assessmentPercentageScore numeric(20, 2),
    isRule BOOLEAN,
    isManual BOOLEAN,
    isPassed BOOLEAN,
    isLatest BOOLEAN,
    assessmentCompletionPercentage numeric(20, 2),
    completionPercentage numeric(20, 2),
    dateRecommendedForCompletion TIMESTAMP WITHOUT TIME ZONE,
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    dateSubmitted TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    dateAssigned TIMESTAMP WITHOUT TIME ZONE,
    dateDue TIMESTAMP WITHOUT TIME ZONE,
    state VARCHAR(50),
    updated timestamp without time zone,
    CONSTRAINT learningmoduleassignments_pkey PRIMARY KEY (id)
);

-- Add missing columns if they don't exist (for existing installations)
ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS userid VARCHAR(50);

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS moduleid VARCHAR(50);

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS dateAssigned TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS dateDue TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE learningmoduleassignments
ADD COLUMN IF NOT EXISTS state VARCHAR(50);

-- Handle moduleId to moduleid standardization for existing installations
-- This uses the generic column rename procedure to safely migrate the column while preserving data
CALL csg_rename_column_safe('learningmoduleassignments', 'moduleId', 'moduleid', 'VARCHAR(50)');
