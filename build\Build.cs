using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Nuke.Common;
using Nuke.Common.CI.AzurePipelines;
using Nuke.Common.Execution;
using Nuke.Common.Git;
using Nuke.Common.IO;
using Nuke.Common.ProjectModel;
using Nuke.Common.Tooling;
using Nuke.Common.Tools.Coverlet;
using Nuke.Common.Tools.Docker;
using Nuke.Common.Tools.DotNet;
using Nuke.Common.Tools.GitVersion;
using Nuke.Common.Tools.ReportGenerator;
using Nuke.Common.Tools.Xunit;
using Nuke.Common.Utilities.Collections;
using Serilog;
using static Nuke.Common.Git.GitRepositoryExtensions;
using static Nuke.Common.IO.CompressionTasks;
using static Nuke.Common.IO.FileSystemTasks;
using static Nuke.Common.IO.PathConstruction;
using static Nuke.Common.Tools.Docker.DockerTasks;
using static Nuke.Common.Tools.DotNet.DotNetTasks;
using static Nuke.Common.Tools.ReportGenerator.ReportGeneratorTasks;

[AzurePipelines(
    AzurePipelinesImage.UbuntuLatest,
    FetchDepth = 0,
    Submodules = false,
    TriggerBranchesInclude = new[] { "'*'" },
    ImportSecrets = new[] { nameof(DockerPassword) },
    InvokedTargets = new[] { nameof(Test), nameof(Publish), nameof(DockerAll) },
    CachePaths = new[]
    {
        //AzurePipelinesCachePaths.Docker,
        AzurePipelinesCachePaths.NuGet,
        AzurePipelinesCachePaths.Nuke
    }
    )]
class Build : NukeBuild
{
    /// Support plugins are available for:
    ///   - JetBrains ReSharper        https://nuke.build/resharper
    ///   - JetBrains Rider            https://nuke.build/rider
    ///   - Microsoft VisualStudio     https://nuke.build/visualstudio
    ///   - Microsoft VSCode           https://nuke.build/vscode

    public static int Main () => Execute<Build>(x => x.Default);

    [Parameter("Configuration to build - Default is 'Debug' (non-release) or 'Release' (release)")]
    Configuration Configuration = IsLocalBuild ? Configuration.Debug : Configuration.Release;

    [Parameter("Path to the docker registry")]
    readonly string DockerRegistryUrl = "docker.io";

    [Parameter("Name of the Docker registry")]
    readonly string DockerRegistryName = "genesys_adapter";
    [Parameter("Prefix for the Docker image tag")]
    readonly string DockerTagPrefix = "adapter-";

    [Parameter("Username for docker registry")]
    readonly string DockerUsername = "customerscience";
    [Parameter("Password for docker registry")][Secret]
    readonly string DockerPassword;
    [Parameter("Runtimes to publish")]
    readonly string[] PublishRuntimes = {"win-x64", "linux-x64"};
    [Parameter("Runtime to use in Docker image")]
    readonly string DockerRuntime = "linux-musl-x64";
    [Parameter("Minimum code coverage percentage")]
    readonly int MinCoverage = 0;

    [Solution(GenerateProjects = true)]
    readonly Solution Solution;
    [GitVersion(
        NoFetch = true,
        UpdateAssemblyInfo = true)]
    readonly GitVersion GitVersion;

    Project MainProject => Solution.GetProject("GenesysAdapter");

    [GitRepository]
    readonly GitRepository Repository;

    AzurePipelines AzurePipelines => AzurePipelines.Instance;

    AbsolutePath SourceDirectory => RootDirectory;
    AbsolutePath TestsDirectory => RootDirectory / "tests";
    AbsolutePath TestResultDirectory => TestsDirectory / "results";
    AbsolutePath OutputDirectory => RootDirectory / "release";
    AbsolutePath ArtifactsDirectory => RootDirectory / "artifacts";
    AbsolutePath CoverageReportDirectory => TestResultDirectory / "coverage-report";
    string CoverageReportArchive => Path.ChangeExtension(CoverageReportDirectory, ".zip");

    private bool hasRestored = false;
    private bool hasBuilt = false;

    Target Clean => _ => _
        .Description("Cleans all output directories")
        .Before(Restore, Compile, Test)
        .Executes(() =>
        {
            SourceDirectory
                .GlobDirectories("**/bin", "**/obj")
                .Where(s => s.Parent.Name != "build")
                .ForEach(FileSystemTasks.DeleteDirectory);
            TestsDirectory.GlobDirectories("**/bin", "**/obj").ForEach(DeleteDirectory);
            EnsureCleanDirectory(TestResultDirectory);
            EnsureCleanDirectory(OutputDirectory);
            EnsureCleanDirectory(ArtifactsDirectory);
        });

    Target Restore => _ => _
        .Description("Restores all nuget packages")
        .After(Clean)
        .Executes(() =>
        {
            DotNetRestore(s => s
                .SetProjectFile(Solution));

            hasRestored = true;
        });

    Target Compile => _ => _
        .Description("Compile binaries")
        .After(Clean, Restore)
        .Executes(() =>
        {
            var buildRuntimes = PublishRuntimes.ToList(); //TargetRuntimes();

            DotNetBuild(s => s
                .SetProjectFile(MainProject)
                .SetConfiguration(Configuration)
                .When(hasRestored, _ => _
                    .EnableNoRestore())
                .SetPublishSingleFile(true)
                .CombineWith(buildRuntimes, (_, runtime) => _
                    .SetRuntime(runtime)
                    .SetSelfContained(runtime != "win-x64")));

            hasRestored = true;
            hasBuilt = true;

            ReportSummary(_ => _
                .AddPair("Version", VersionInfoSemVer)
                .AddPair("Runtimes", String.Join(",", buildRuntimes)));
        });

    Target Publish => _ => _
        .Description("Create packages for deployment")
        .After(Compile)
        .After(Test)
        .When(IsServerBuild, _ => _
            .DependsOn(Test))
        .Produces(ArtifactsDirectory)
        .Executes(() =>
        {
            var buildRuntimes = TargetRuntimes();

            RunPublish(buildRuntimes);
        });

    void RunPublish(List<string> Runtimes)
    {
        DotNetPublish(s => s
            .SetProject(MainProject)
            .SetConfiguration(Configuration)
            .When(hasRestored, _ => _
                .EnableNoRestore())
            .SetPublishSingleFile(true)
            .SetNoBuild(hasBuilt)
            .CombineWith(Runtimes, (_, runtime) => _
                .SetRuntime(runtime)
                .SetOutput(OutputDirectory / runtime)
                .SetSelfContained(runtime != "win-x64")));

        Runtimes
            .ForEach(runtime =>
            CompressZip(
                directory: OutputDirectory / runtime,
                archiveFile: ArtifactsDirectory / $"{runtime}.zip",
                fileMode: FileMode.Create));

        if (!hasBuilt)
            ReportSummary(_ => _
                .AddPair("Version", VersionInfoSemVer)
                .AddPair("Runtimes", String.Join(",", Runtimes)));

        hasRestored = true;
        hasBuilt = true;
    }

    Target DockerBuild => _ => _
        .Description("Build Docker image")
        .Requires(() => !DockerRegistryName.IsNullOrEmpty())
        .DependsOn(Publish)
        .Consumes(Publish, ArtifactsDirectory / $"{DockerRuntime}.zip")
        .Executes(() =>
        {
            RunDockerBuild();
        });

    void RunDockerBuild()
    {
        // Show all files under the project root
        // RootDirectory
        //     .GlobDirectories("**")
        //     .ForEach(s => Log.Information(s.ToString()));

        var dockerFile = RootDirectory / "Dockerfile";
        var tags = DockerTags;

        // DockerHub does not currently support OCI images, set buildah
        // output format in case podman is in use.
        // https://github.com/docker/hub-feedback/issues/1871
        Environment.SetEnvironmentVariable("BUILDAH_FORMAT", "docker", EnvironmentVariableTarget.Process);

        DockerTasks.DockerBuild(s => s
            .SetPath(RootDirectory)
            .SetFile(dockerFile)
            .SetTag(tags)
            .SetBuildArg($"RUNTIME={DockerRuntime}"));
    }

    Target DockerLogin => _ => _
        .Description("Log in to DockerHub")
        .Requires(() => !DockerRegistryUrl.IsNullOrEmpty())
        .Requires(() => !DockerUsername.IsNullOrEmpty())
        .Requires(() => !DockerPassword.IsNullOrEmpty())
        .Before(DockerPush)
        .Executes(() =>
        {
            RunDockerLogin();
        });

    void RunDockerLogin()
    {
        DockerTasks.DockerLogin(s => s
            .SetServer(DockerRegistryUrl)
            .SetUsername(DockerUsername)
            .SetPassword(DockerPassword)
            .DisableProcessLogInvocation()
            .DisableProcessLogOutput());
    }

    Target DockerPush => _ => _
        .Description("Push Docker image to DockerHub")
        .Requires(() => !DockerRegistryName.IsNullOrEmpty())
        .DependsOn(DockerBuild)
        .Consumes(DockerBuild)
        .When(IsServerBuild, _ => _
            .DependsOn(DockerLogin))
        .Executes(() =>
        {
            RunDockerPush();
        });

    void RunDockerPush()
    {
        var tags = DockerTags;

        DockerImagePush(s => s
            .DisableProcessLogInvocation()
            .DisableProcessLogOutput()
            .CombineWith(tags, (_, tag) => _
                .SetName(tag)));
    }

    Target DockerAll => _ => _
        .Description("Build and push Docker image to DockerHub")
        .Requires(() => !DockerRegistryUrl.IsNullOrEmpty())
        .Requires(() => !DockerUsername.IsNullOrEmpty())
        .Requires(() => !DockerPassword.IsNullOrEmpty())
        .DependsOn(Test)
        .After(Publish)
        .Executes(() =>
        {
            // HACK: .Consumes() does not work to pass artifacts between steps.
            // https://github.com/nuke-build/nuke/issues/562
            // The AzurePipelinesDownloadStep class now exists, but isn't referenced.
            // Need to raise an issue for this. In the meantime, perform all actions in one step so objects don't need
            // to be passed between steps.
            RunDockerLogin();
            RunPublish(new List<string>{DockerRuntime});
            RunDockerBuild();
            RunDockerPush();
        });

    Target Test => _ => _
        .Description("Runs unit tests and code coverage")
        .Before(Publish)
        .Produces(TestResultDirectory / "*.trx")
        .Produces(TestResultDirectory / "*/*.xml")
        .Executes(() =>
        {
            EnsureCleanDirectory(TestResultDirectory);
            var projects = Solution.GetProjects("*.Tests");
            try
            {
                DotNetTest(c => c
                    .SetConfiguration(Configuration)
                    .SetDataCollector("XPlat Code Coverage")
                    .SetResultsDirectory(TestResultDirectory)
                    .SetExcludeByFile(RootDirectory / "ControlServCore" / "Connected Services")
                    .CombineWith(projects, (_, project) => _
                        .SetProjectFile(project)
                        .AddLoggers($"trx;LogFileName={project.Name}.trx")),
                    completeOnFailure: true);
            }
            finally
            {
                ReportTestResults();
                ReportTestSummary();
            }
        });

    void ReportTestResults()
    {
        var trxFiles = TestResultDirectory.GlobFiles("*.trx");
        if (trxFiles.Count == 0)
        {
            Log.Warning("No test result files found. Skipping test result reporting.");
        }
        else
        {
            trxFiles.ForEach(x =>
                AzurePipelines.Instance?.PublishTestResults(
                    type: AzurePipelinesTestResultsType.VSTest,
                    title: $"{Path.GetFileNameWithoutExtension(x)} ({AzurePipelines.Instance.StageDisplayName})",
                    files: new string[] { x }));
        }

        var xmlFiles = TestResultDirectory.GlobFiles("*/*.xml");
        if (xmlFiles.Count == 0)
        {
            Log.Warning("No coverage result files found. Skipping code coverage reporting.");
        }
        else
        {
            xmlFiles.ForEach(x =>
                AzurePipelines.Instance?.PublishCodeCoverage(
                    AzurePipelinesCodeCoverageToolType.Cobertura,
                    x,
                    CoverageReportDirectory));
        }
    }

    void ReportTestSummary()
    {
        IEnumerable<string> GetOutcomes(AbsolutePath file)
            => XmlTasks.XmlPeek(
                file,
                "/xn:TestRun/xn:Results/xn:UnitTestResult/@outcome",
                ("xn", "http://microsoft.com/schemas/VisualStudio/TeamTest/2010"));

        var resultFiles = TestResultDirectory.GlobFiles("*.trx");
        if (resultFiles.Count == 0)
        {
            Log.Warning("No test result files found. Skipping test summary.");
            return;
        }

        var outcomes = resultFiles.SelectMany(GetOutcomes).ToList();
        var passedTests = outcomes.Count(x => x == "Passed");
        var failedTests = outcomes.Count(x => x == "Failed");
        var skippedTests = outcomes.Count(x => x == "NotExecuted");

        IEnumerable<string> GetLinesCovered(AbsolutePath file)
            => XmlTasks.XmlPeek(file, "/coverage/@lines-covered");
        IEnumerable<string> GetLinesValid(AbsolutePath file)
            => XmlTasks.XmlPeek(file, "/coverage/@lines-valid");
        IEnumerable<string> GetBranchesCovered(AbsolutePath file)
            => XmlTasks.XmlPeek(file, "/coverage/@branches-covered");
        IEnumerable<string> GetBranchesValid(AbsolutePath file)
            => XmlTasks.XmlPeek(file, "/coverage/@branches-valid");

        resultFiles = TestResultDirectory.GlobFiles("*/*.xml");
        if (resultFiles.Count == 0)
        {
            Log.Warning("No coverage result files found. Skipping coverage summary.");
            ReportSummary(_ => _
                .When(failedTests > 0, _ => _
                    .AddPair("Failed", failedTests.ToString()))
                .AddPair("Passed", passedTests.ToString())
                .When(skippedTests > 0, _ => _
                    .AddPair("Skipped", skippedTests.ToString()))
                .AddPair("Line Coverage", "N/A")
                .AddPair("Branch Coverage", "N/A"));
            return;
        }

        var linesCovered = resultFiles.SelectMany(GetLinesCovered).Select(x => double.Parse(x)).Sum();
        var linesValid = resultFiles.SelectMany(GetLinesValid).Select(x => double.Parse(x)).Sum();
        var lineRate = linesValid == 0 ? 0 : linesCovered / linesValid;
        var branchesCovered = resultFiles.SelectMany(GetBranchesCovered).Select(x => double.Parse(x)).Sum();
        var branchesValid = resultFiles.SelectMany(GetBranchesValid).Select(x => double.Parse(x)).Sum();
        var branchRate = branchesValid == 0 ? 0 : branchesCovered / branchesValid;

        ReportSummary(_ => _
            .When(failedTests > 0, _ => _
                .AddPair("Failed", failedTests.ToString()))
            .AddPair("Passed", passedTests.ToString())
            .When(skippedTests > 0, _ => _
                .AddPair("Skipped", skippedTests.ToString()))
            .AddPair("Line Coverage", lineRate.ToString("0.00%"))
            .AddPair("Branch Coverage", branchRate.ToString("0.00%")));

        // Skip coverage check if minimum coverage is 0
        if (MinCoverage > 0)
        {
            Assert.True(lineRate * 100 >= MinCoverage, $"Line coverage {lineRate.ToString("0.00%")} < {MinCoverage}%");
        }
    }

    Target Coverage => _ => _
        .Description("Generates HTML code coverage report")
        .DependsOn(Test)
        .Consumes(Test)
        .Produces(CoverageReportArchive)
        .AssuredAfterFailure()
        .Executes(() =>
        {
            ReportGenerator(_ => _
                .SetReports(TestResultDirectory / "*/*.xml")
                .SetReportTypes(ReportTypes.HtmlInline_AzurePipelines)
                .SetFramework(MainProject.GetTargetFrameworks().First())
                .SetTargetDirectory(CoverageReportDirectory));

            CompressZip(
                directory: CoverageReportDirectory,
                archiveFile: CoverageReportArchive,
                fileMode: FileMode.Create);
        });

    Target Default => _ => _
        .Description("Default targets")
        .DependsOn(Test, Publish, DockerBuild)
        .When(IsServerBuild, _ => _
            .DependsOn(DockerAll));

    protected override void OnBuildCreated()
    {
        //Logging.Level = Nuke.Common.LogLevel.Trace; // Trace, Normal, Warning, Error

        base.OnBuildCreated();
    }

    protected override void OnBuildInitialized()
    {
        Log.Information("🚀 Build process started, v{0}", VersionInfoSemVer);
        Log.Information("Informational version: {0}", VersionInfoInformationalVersion);
        Log.Information(
            "Git branch: {0}. Branch is main ({1}), release ({2}), develop ({3}), feature ({4}), hotfix ({5})",
            Repository.Branch,                      // 0
            Repository.IsOnMainOrMasterBranch(),    // 1
            Repository.IsOnReleaseBranch(),         // 2
            Repository.IsOnDevelopBranch(),         // 3
            Repository.IsOnFeatureBranch(),         // 4
            Repository.IsOnHotfixBranch());         // 5
        Log.Information(
            "Git commit {0}, tags {1}",
            Repository.Commit,
            Repository.Tags
        );
        Log.Information("IsLocalBuild: {0}, IsServerBuild: {1}.", IsLocalBuild, IsServerBuild);
        Log.Information("Target runtimes: {0}", String.Join(", ", TargetRuntimes()));
        if (Repository.IsOnMainOrMasterBranch() || Repository.IsOnReleaseBranch())
        {
            Configuration = Configuration.Release;
        }
        else if (IsServerBuild )
        {
            Configuration = Configuration.Debug;
        }
        Log.Information("Configuration: {0}", Configuration);
        AzurePipelines?.WriteCommand("build.updatebuildnumber", VersionInfoSemVer);

        base.OnBuildInitialized();
    }

    public string VersionInfoSemVer
    {
        get
        {
            return GitVersion.SemVer;
        }
    }

    public string VersionInfoInformationalVersion
    {
        get
        {
            return GitVersion.InformationalVersion;
        }
    }

    public string VersionInfoMajorVersion
    {
        get
        {
            return GitVersion.Major.ToString();
        }
    }

    private List<string> TargetRuntimes()
    {
        var runtimes = new List<string>();
        var isPublishing =
            InvokedTargets.Contains(Default)
            || InvokedTargets.Contains(Compile)
            || InvokedTargets.Contains(Publish)
            || IsServerBuild;
        var isBuildingDocker =
            InvokedTargets.Contains(Default)
            || InvokedTargets.Contains(DockerBuild)
            || InvokedTargets.Contains(DockerPush)
            || IsServerBuild;
        if (isPublishing)
            runtimes.AddRange(PublishRuntimes.ToList());
        if (isBuildingDocker && !runtimes.Contains(DockerRuntime))
            runtimes.Add(DockerRuntime);
        return runtimes;
    }

    private List<string> DockerTags
    {
        get
        {
            var prefix = $"{DockerUsername}/{DockerRegistryName}:{DockerTagPrefix}";
            var tags = new List<string>
                {
                    $"{prefix}{VersionInfoSemVer}"
                };

            if (Repository.IsOnMainOrMasterBranch())
            {
                tags.Add($"{prefix}latest");
                tags.Add($"{prefix}{VersionInfoMajorVersion}");
            }
            else if (Repository.IsOnReleaseBranch())
            {
                tags.Add($"{prefix}beta");
            }
            else if (Repository.IsOnDevelopBranch())
            {
                tags.Add($"{prefix}unstable");
            }
            else if (Repository.IsOnFeatureBranch())
            {
                tags.Add($"{prefix}alpha");
            }

            return tags;
        }
    }
}
// spell-checker: ignore buildah cobertura runtimes serilog UpdateBuildNumber
