CREATE
OR REPLACE VIEW vwtimeoffData AS
SELECT
	td.userid,
	td.businessunitdate,
	td.length,
	td.description,
	td.activitycode,
	td.paid,
	td.timeoffrequestid,
	ud.name AS agentname,
	ud.managerid AS managerid,
	ud.managername,
	td.isfulldayrequest
FROM
	timeoffData td
	LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = td.userid;

COMMENT ON COLUMN vwtimeoffdata.userid IS 'User GUID';
COMMENT ON COLUMN vwtimeoffdata.businessunitdate IS 'Business Unit Date';
COMMENT ON COLUMN vwtimeoffdata.length IS 'Length of Time Off';
COMMENT ON COLUMN vwtimeoffdata.description IS 'Description of Time Off';
COMMENT ON COLUMN vwtimeoffdata.activitycode IS 'Activity Code';
COMMENT ON COLUMN vwtimeoffdata.paid IS 'Paid Time Off';
COMMENT ON COLUMN vwtimeoffdata.timeoffrequestid IS 'Time Off Request GUID';
COMMENT ON COLUMN vwtimeoffdata.agentname IS 'Agent Name';
COMMENT ON COLUMN vwtimeoffdata.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwtimeoffdata.managername IS 'Manager Name';
 
COMMENT ON VIEW vwtimeoffData IS 'See TimeOffData - Expands all the GUIDs with their lookups';