CREATE TABLE IF NOT EXISTS subuserusagedata (
    keyid varchar(100) NOT NULL,
    date timestamp without time zone,
    userlogin varchar(50),
    licensename varchar(200),
    secs numeric(20, 2),
    hoursstr varchar(50),
    updated timestamp without time zone,
    CONSTRAINT subuserusagedata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN subuserusageData.date IS 'Date'; 
COMMENT ON COLUMN subuserusageData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN subuserusageData.licensename IS 'License Used'; 
COMMENT ON COLUMN subuserusageData.secs IS 'Time in Secs'; 
COMMENT ON COLUMN subuserusageData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN subuserusageData.userlogin IS 'User GUID'; 
COMMENT ON COLUMN subuserusageData.hoursstr IS ' '; 
COMMENT ON TABLE subuserusageData IS 'Subscription Detailed Data'; 