CREATE TABLE IF NOT EXISTS adherencedaydata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    daystartoffsetsecs integer,
    adherenceperc numeric(20, 2),
    conformperc numeric(20, 2),
    impact varchar(50),
    adherenceschedulesecs integer,
    conformanceschedulesecs integer,
    conformanceactualsecs integer,
    exceptioncount integer,
    exceptiondurationsecs integer,
    impactseconds integer,
    schedulelengthsecs integer,
    actuallengthsecs integer,
    updated timestamp without time zone,
    CONSTRAINT adherencedaydata_pkey PRIMARY KEY (keyid)
);
COMMENT ON COLUMN adherencedayData.actualLengthSecs IS 'Total Actual Scheduled Time length'; 
COMMENT ON COLUMN adherencedayData.adherencePerc IS 'Adherence Percentage'; 
COMMENT ON COLUMN adherencedayData.adherenceschedulesecs IS 'The Total Adherence Time that adherence is measured against'; 
COMMENT ON COLUMN adherencedayData.conformanceActualSecs IS 'Total Time Agent in Conformance'; 
COMMENT ON COLUMN adherencedayData.conformanceScheduleSecs IS 'The Total Conformance Time that adherence is measured against'; 
COMMENT ON COLUMN adherencedayData.conformPerc IS 'Conformance Percentage'; 
COMMENT ON COLUMN adherencedayData.dayStartOffsetSecs IS 'Start of Day Offset'; 
COMMENT ON COLUMN adherencedayData.exceptionCount IS 'Total Exception Count'; 
COMMENT ON COLUMN adherencedayData.exceptionDurationSecs IS 'Impacted Seconds'; 
COMMENT ON COLUMN adherencedayData.impact IS 'What was the overall impact of adherence?'; 
COMMENT ON COLUMN adherencedayData.impactSeconds IS 'Impacted Seconds'; 
COMMENT ON COLUMN adherencedayData.schedulelengthsecs IS 'Total Scheduled Time (Seconds)'; 
COMMENT ON COLUMN adherencedayData.startdate IS 'Start Time (UTC)'; 
COMMENT ON COLUMN adherencedayData.startdateltc IS 'Start Time (LTC)'; 
COMMENT ON COLUMN adherencedayData.updated IS 'Date Row was updated (UTC)'; 
COMMENT ON COLUMN adherencedayData.userid IS 'User GUID'; 
COMMENT ON TABLE adherencedayData IS 'Adherence Summarised Daily Data';

DO $$
DECLARE
    KeyidPrefix VARCHAR(255) := 'v1_';
    deleted_count INTEGER;
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM adherencedaydata
        WHERE SUBSTR(keyid, 1, LENGTH(KeyidPrefix)) <> KeyidPrefix
    ) THEN
        DELETE FROM adherencedaydata
        WHERE LEFT(keyid, LENGTH(KeyidPrefix)) <> KeyidPrefix;
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        
        UPDATE tabledefinitions
        SET datekeyfield = CURRENT_DATE - INTERVAL '12 months'
        WHERE tablename = 'adherencedaydata';
        
        RAISE NOTICE 'Records deleted: % and tabledefinitions updated.', deleted_count;
    ELSE
        RAISE NOTICE 'No records found with old keyid in use.';
    END IF;
END $$;