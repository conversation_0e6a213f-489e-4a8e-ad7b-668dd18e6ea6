IF dbo.csg_table_exists('chatData') = 0
CREATE TABLE [chatData](
    [keyid] [nvarchar](50) NOT NULL,
    [conversationid] [nvarchar](50) NOT NULL,
    [conversationstart] [datetime],
    [conversationstartltc] [datetime],
    [userid] [nvarchar](50),
    [chatinitiatedby] [nvarchar](10),
    [agentchatcount] [int],
    [agentchattotal] [decimal](20, 2),
    [agentchatmax] [decimal](20, 2),
    [agentchatmin] [decimal](20, 2),
    [agenthasread] [decimal](20, 2),
    [custchatcount] [int],
    [custchattotal] [decimal](20, 2),
    [custchatmax] [decimal](20, 2),
    [custchatmin] [decimal](20, 2),
    [updated] [datetime],
    [mediatype] [nvarchar](10),
    CONSTRAINT [PK__chatData__607AFDE06773F5F1] PRIMARY KEY ([keyid])
);

-- Add mediatype column if it doesn't exist (for existing installations)
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('chatData') AND name = 'mediatype')
BEGIN
    ALTER TABLE [chatData] ADD [mediatype] [nvarchar](10);
END
