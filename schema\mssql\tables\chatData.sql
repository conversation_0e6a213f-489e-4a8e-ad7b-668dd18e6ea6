IF dbo.csg_table_exists('chatData') = 0
CREATE TABLE [chatData](
    [keyid] [nvarchar](50) NOT NULL,
    [conversationid] [nvarchar](50) NOT NULL,
    [conversationstart] [datetime],
    [conversationstartltc] [datetime],
    [userid] [nvarchar](50),
    [chatinitiatedby] [nvarchar](10),
    [agentchatcount] [int],
    [agentchattotal] [decimal](20, 2),
    [agentchatmax] [decimal](20, 2),
    [agentchatmin] [decimal](20, 2),
    [agenthasread] [decimal](20, 2),
    [custchatcount] [int],
    [custchattotal] [decimal](20, 2),
    [custchatmax] [decimal](20, 2),
    [custchatmin] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK__chatData__607AFDE06773F5F1] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('chatData', 'agenthasread') = 0
    ALTER TABLE chatData ADD agenthasread DECIMAL(20, 2);
ELSE
    ALTER TABLE chatData ALTER COLUMN agenthasread DECIMAL(20, 2);
