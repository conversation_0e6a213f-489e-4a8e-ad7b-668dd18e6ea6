using Microsoft.Extensions.Logging;

namespace CSG.Adapter.Licensing
{
    /// <summary>
    /// Static class to hold the license validator instance
    /// </summary>
    public static class LicenseManager
    {
        private static LicenseValidator? _licenseValidator;

        /// <summary>
        /// Initialize the license manager with a license validator
        /// </summary>
        /// <param name="licenseValidator">The license validator instance</param>
        public static void Initialize(LicenseValidator licenseValidator)
        {
            _licenseValidator = licenseValidator;
        }

        /// <summary>
        /// Get the license validator instance
        /// </summary>
        /// <returns>The license validator instance</returns>
        public static LicenseValidator? GetLicenseValidator()
        {
            return _licenseValidator;
        }

        /// <summary>
        /// Check if the client has the Knowledge Quest license (license type 2 or higher)
        /// </summary>
        /// <param name="logger">Logger to use for logging</param>
        /// <returns>True if the client has license type 2 or higher, false otherwise</returns>
        public static bool HasKnowledgeQuestLicense(ILogger? logger = null)
        {
            if (_licenseValidator == null)
            {
                logger?.LogWarning("License:KnowledgeQuest: License validator not initialized, Knowledge Quest will be disabled");
                return false;
            }

            return _licenseValidator.HasKnowledgeQuestLicense();
        }
    }
}
