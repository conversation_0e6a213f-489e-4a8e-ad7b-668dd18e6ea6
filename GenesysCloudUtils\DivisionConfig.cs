﻿using System;
using System.Data;
using System.Linq;
using System.Net;
using Divs = GenesysCloudDefDivisionDetails;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class DivisionConfig
    {

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string OAuthUser { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();


        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            OAuthUser = GCControlData.Tables["GCControlData"].Rows[0]["GC_USERId"].ToString();
        }


        public DataTable GetDivConfigFromGC()
        {
            Console.WriteLine("Get Division Data");

            DataTable Divisions = DBUtil.CreateInMemTable("divisionDetails");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.Write("*");

            int CurrentPage = 1;
            int MaxPage = 1;


            while (CurrentPage <= MaxPage)
            {
                // Use JsonReturnHttpResponseGet for proper rate limit handling
                var response = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/authorization/divisions?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                // Check HTTP status code before processing JSON
                if (response.StatusCode != 200)
                {
                    if (response.StatusCode == 429)
                    {
                        Console.WriteLine($"Rate limit encountered for divisions page {CurrentPage}. Response: {response.Content}");
                        throw new Exception($"Rate limiting exceeded retry limit for divisions page {CurrentPage}");
                    }
                    else
                    {
                        throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                    }
                }

                string JsonString = response.Content;


                if (JsonString.Length > 30)
                {
                    Divs.Divisions DivisionDetails = new Divs.Divisions();

                    DivisionDetails = JsonConvert.DeserializeObject<Divs.Divisions>(JsonString,
                                  new JsonSerializerSettings
                                  {
                                      NullValueHandling = NullValueHandling.Ignore
                                  });

                    foreach (Divs.Entity JSON in DivisionDetails.entities)
                    {
                        Console.Write("F");
                        DataRow checkRow = Divisions.Select("id='" + JSON.id + "'").FirstOrDefault();

                        if (checkRow == null)
                        {
                            DataRow DivRow = Divisions.NewRow();
                            DivRow["id"] = JSON.id;
                            DivRow["name"] = JSON.name;
                            DivRow["homedivision"] = JSON.homeDivision;
                            Divisions.Rows.Add(DivRow);
                        }
                    }

                    MaxPage = DivisionDetails.total;
                    CurrentPage++;

                }

            }



            Console.WriteLine("\nTotal Division(s) Found:{0} ", Divisions.Rows.Count);

            return Divisions;

        }

    }
}
// spell-checker: ignore: divs