IF dbo.csg_table_exists('flowoutcomedata') = 0
CREATE TABLE [flowoutcomedata](
    [keyid] [nvarchar](255) NOT NULL,
    [conversationid] [nvarchar](50),
    [conversationstartdate] [datetime] NOT NULL,
    [conversationstartdateltc] [datetime],
    [conversationenddate] [datetime],
    [conversationenddateltc] [datetime],
    [flowoutcomestartdate] [datetime],
    [flowoutcomestartdateltc] [datetime],
    [flowoutcomeenddate] [datetime],
    [flowoutcomeenddateltc] [datetime],
    [flowid] [nvarchar](50),
    [flowname] [nvarchar](50),
    [flowversion] [decimal](20, 2),
    [flowtype] [nvarchar](50),
    [flowoutcome] [nvarchar](50),
    [flowoutcomeid] [nvarchar](50),
    [flowoutcomevalue] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK__flowoutcomedata] PRIMARY KEY ([keyid], [conversationstartdate])
);
