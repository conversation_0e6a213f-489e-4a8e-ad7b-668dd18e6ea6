DROP  VIEW IF EXISTS vwcallsummary;

CREATE
OR REPLACE VIEW vwcallsummary AS
SELECT
    di.conversationid AS Fact_ConversationId,
    qd.name AS Fact_QueueName,
    di.conversationstartdate AS Date_ConversationStartDate,
    di.conversationenddate AS Date_ConversationEndDate,
    di.ani AS Fact_ANI,
    di.dnis AS Fact_DNIS,
    di.originaldirection AS Fact_OriginalDirection,
    SUM(di.ttalkcomplete) AS Raw_Talk,
    SUM(di.tacw) AS Raw_ACW,
    SUM(di.theldcomplete) AS Raw_Hold,
    SUM(di.tvoicemail) AS Raw_Voicemail,
    SUM(di.ntransferred) AS Count_Transferred,
    SUM(di.tabandon) AS Raw_Abandon,
    SUM(di.ttalkcomplete) / 86400.00 AS Raw_TalkDay,
    SUM(di.tacw) / 86400.00 AS Raw_ACWDay,
    SUM(di.theldcomplete) / 86400.00 AS Raw_HoldDay,
    SUM(di.tvoicemail) / 86400.00 AS Raw_VoicemailDay,
    SUM(di.ntransferred) / 86400.00 AS Count_TransferredDay,
    SUM(di.tabandon) / 86400.00 AS Raw_AbandonDay
FROM
    detailedInteractionData AS di
    LEFT OUTER JOIN queueDetails AS qd ON qd.id = di.queueid
GROUP BY
    di.conversationid,
    qd.name,
    di.conversationstartdate,
    di.conversationenddate,
    di.ani,
    di.dnis,
    di.originaldirection;