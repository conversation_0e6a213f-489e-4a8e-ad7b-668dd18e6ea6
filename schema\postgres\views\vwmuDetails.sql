CREATE
OR REPLACE VIEW vwmuDetails AS
SELECT
    id,
    name,
    startofweek,
    timezone,
    severeAlertThresholdMinutes,
    adherenceTargetPercent,
    adherenceExceptionThresholdSeconds,
    nonOnQueueActivitiesEquivalent,
    trackOnQueueActivity,
    updated
FROM
    muDetails;

COMMENT ON COLUMN vwmuDetails.adherenceTargetPercent IS 'Adherence Target Percentage';
COMMENT ON COLUMN vwmuDetails.id IS 'Primary Key';
COMMENT ON COLUMN vwmuDetails.name IS 'Name';
COMMENT ON COLUMN vwmuDetails.nonOnQueueActivitiesEquivalent IS 'Non-On-Queue Activities Equivalent';
COMMENT ON COLUMN vwmuDetails.severeAlertThresholdMinutes IS 'Severe Alert Threshold Minutes';
COMMENT ON COLUMN vwmuDetails.startofweek IS 'Start of Week';
COMMENT ON COLUMN vwmuDetails.timezone IS 'Timezone';
COMMENT ON COLUMN vwmuDetails.trackOnQueueActivity IS 'Track On-Queue Activity';
COMMENT ON COLUMN vwmuDetails.updated IS 'Last Updated';
COMMENT ON VIEW vwmuDetails IS 'See MuDetails'; 