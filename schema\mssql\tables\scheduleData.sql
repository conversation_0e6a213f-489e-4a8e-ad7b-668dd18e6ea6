IF dbo.csg_table_exists('scheduleData') = 0
CREATE TABLE scheduleData (
    keyid nvarchar(100) NOT NULL,
    userid nvarchar(50),
    buid nvarchar(50),
    scheduleid nvarchar(50),
    shiftid integer,
    shiftstartdate datetime,
    shiftstartdateltc datetime,
    shiftlengthtime integer,
    activitystartdate datetime,
    activitystartdateltc datetime,
    activitylengthtime int,
    activitydescription nvarchar(200),
    activitycodeid nvarchar(50),
    activitypaid bit,
    shiftmanuallyeditted bit,
    updated datetime,
    CONSTRAINT [PK_scheduleData] PRIMARY KEY ([keyid])
);
IF dbo.csg_column_exists('scheduleData', 'shiftstartdateltc') = 0
    ALTER TABLE dbo.scheduleData ADD shiftstartdateltc datetime;
ELSE
    ALTER TABLE dbo.scheduleData ALTER COLUMN shiftstartdateltc datetime;
