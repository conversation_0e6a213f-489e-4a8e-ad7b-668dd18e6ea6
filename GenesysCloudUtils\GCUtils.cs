﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using PureCloudPlatform.Client.V2.Client;
using PureCloudPlatform.Client.V2.Extensions;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class GCUtils
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = DateTime.UnixEpoch;
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;

        public DataSet GCControlData { get; set; }
        private string _genesysBaseUri;
        private readonly ILogger? _logger;

        // Shared token cache to prevent concurrent authentication requests
        private static readonly Dictionary<string, (string token, DateTime expiry)> _tokenCache = new Dictionary<string, (string, DateTime)>();
        private static readonly SemaphoreSlim _tokenCacheLock = new SemaphoreSlim(1, 1);
        private static readonly TimeSpan _tokenCacheExpiry = TimeSpan.FromMinutes(55); // Tokens typically expire in 60 minutes

        // Rate limiting constants
        private const int MAX_AUTH_RETRY_ATTEMPTS = 5;
        private const int DEFAULT_AUTH_RETRY_SECONDS = 5;

        public GCUtils()
        {
        }

        public GCUtils(ILogger? logger)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            UCAUtils = new StandardUtils.Utils();

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            // Reduced log noise - moved to debug level
            System.Diagnostics.Debug.WriteLine("Enabling Encryption");
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            System.Diagnostics.Debug.WriteLine("Creating Admin Data");
            CreateGCAdminData();
            System.Diagnostics.Debug.WriteLine("Getting Key");
            GetGCAPIKey();
            System.Diagnostics.Debug.WriteLine("Retrieved Key");
        }

#nullable enable
        public GCUtils Initialize(CSG.Adapter.Configuration.GenesysApi genesysOptions)
        {
            if (genesysOptions.ClientId is null || genesysOptions.ClientSecret is null)
                throw new ArgumentNullException("Genesys credentials");
            UCAUtils = new StandardUtils.Utils();
            GetGCAPIKey(
                genesysOptions.ClientId,
                new Secret(genesysOptions.ClientId, genesysOptions.ClientSecret).PlainText,
                genesysOptions.Endpoint?.ToString());
            return this;
        }
#nullable restore

        /// <summary>
        /// Gets an OAuth token with rate limit handling and token caching to prevent concurrent authentication requests
        /// </summary>
        public async Task<string> OAuthKeyAsync(string userID, string password, string URL)
        {
            // Create a cache key based on credentials and URL
            string cacheKey = $"{userID}:{URL}";

            // Check if we have a valid cached token first
            await _tokenCacheLock.WaitAsync();
            try
            {
                if (_tokenCache.TryGetValue(cacheKey, out var cachedToken))
                {
                    if (DateTime.UtcNow < cachedToken.expiry)
                    {
                        _logger?.LogDebug("Auth:Cache: Using cached token for {UserId}", userID);
                        return cachedToken.token;
                    }
                    else
                    {
                        // Remove expired token
                        _tokenCache.Remove(cacheKey);
                        _logger?.LogDebug("Auth:Cache: Removed expired token for {UserId}", userID);
                    }
                }
            }
            finally
            {
                _tokenCacheLock.Release();
            }

            // Transform API URL to login URL for authentication
            string loginUrl = URL.Replace("api.", "login.");
            _logger?.LogDebug("Auth:Request: Using login URL: {LoginUrl}", loginUrl);

            string oAuth = string.Empty;
            int attempts = 1;

            while (attempts <= MAX_AUTH_RETRY_ATTEMPTS)
            {
                try
                {
                    PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.RestClient.BaseUrl = new Uri(loginUrl);
                    var accessTokenInfo = PureCloudPlatform.Client.V2.Client.Configuration.Default.ApiClient.PostToken(userID, password);
                    oAuth = accessTokenInfo.AccessToken.ToString();

                    // Cache the successful token
                    await _tokenCacheLock.WaitAsync();
                    try
                    {
                        _tokenCache[cacheKey] = (oAuth, DateTime.UtcNow.Add(_tokenCacheExpiry));
                        _logger?.LogDebug("Auth:Cache: Cached new token for {UserId}", userID);
                    }
                    finally
                    {
                        _tokenCacheLock.Release();
                    }

                    _logger?.LogDebug("Auth:Success: Retrieved token for {UserId} on attempt {Attempt}", userID, attempts);
                    return oAuth;
                }
                catch (ApiException ex) when (ex.ErrorCode == 429)
                {
                    _logger?.LogWarning("Auth:RateLimit: Rate limit exceeded on attempt {Attempt} for {UserId}: {Message}",
                        attempts, userID, ex.Message);

                    if (attempts >= MAX_AUTH_RETRY_ATTEMPTS)
                    {
                        _logger?.LogError("Auth:Failed: Max retry attempts ({MaxAttempts}) exceeded for {UserId}",
                            MAX_AUTH_RETRY_ATTEMPTS, userID);
                        throw;
                    }

                    // Extract retry-after from the error message or use default
                    int retryAfterSeconds = ExtractRetryAfterFromMessage(ex.Message) ?? DEFAULT_AUTH_RETRY_SECONDS;

                    // Apply exponential backoff with jitter
                    int backoffSeconds = Math.Min(retryAfterSeconds * attempts, 60); // Cap at 60 seconds
                    int jitter = new Random().Next(0, Math.Max(1, backoffSeconds / 4)); // Add up to 25% jitter
                    int totalWaitSeconds = backoffSeconds + jitter;

                    _logger?.LogInformation("Auth:Retry: Waiting {WaitSeconds}s before retry {NextAttempt}/{MaxAttempts} for {UserId}",
                        totalWaitSeconds, attempts + 1, MAX_AUTH_RETRY_ATTEMPTS, userID);

                    await Task.Delay(totalWaitSeconds * 1000);
                    attempts++;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Auth:Error: Unexpected error during authentication for {UserId} on attempt {Attempt}",
                        userID, attempts);
                    throw;
                }
            }

            throw new InvalidOperationException($"Failed to obtain OAuth token after {MAX_AUTH_RETRY_ATTEMPTS} attempts");
        }

        /// <summary>
        /// Synchronous wrapper for OAuthKeyAsync to maintain backward compatibility
        /// </summary>
        public string OAuthKey(string userID, string password, string URL)
        {
            return OAuthKeyAsync(userID, password, URL).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Extracts retry-after seconds from Genesys Cloud error message
        /// </summary>
        private int? ExtractRetryAfterFromMessage(string errorMessage)
        {
            try
            {
                // Look for "retry after: X" pattern in the error message
                var match = System.Text.RegularExpressions.Regex.Match(errorMessage, @"retry after:\s*(\d+)");
                if (match.Success && int.TryParse(match.Groups[1].Value, out int retryAfter))
                {
                    return retryAfter;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogDebug(ex, "Auth:Parse: Failed to extract retry-after from error message: {Message}", errorMessage);
            }
            return null;
        }

        private void CreateGCAdminData()
        {
            GCControlData = new DataSet();
            var settings = new DataTable("GCControlData");
            settings.Columns.Add("GC_USERId", typeof(System.String));
            settings.Columns.Add("GC_Secret", typeof(System.String));
            settings.Columns.Add("GC_URL", typeof(System.String));
            var row = settings.NewRow();
            row["GC_USERId"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID");
            row["GC_Secret"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET");
            row["GC_URL"] = CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL");
            settings.Rows.Add(row);
            GCControlData.Tables.Add(settings);
        }

        public bool GetGCAPIKey()
        {
            return GetGCAPIKey(
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_USERID"),
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_SECRET"),
                CSG.Adapter.Compatability.LegacyOptions.GetOption("CSG_GENESYS_URL"));
        }

        public bool GetGCAPIKey(string userId, string password, string uri)
        {
            try
            {
                _genesysBaseUri = uri.TrimEnd('/');
                GCApiKey = OAuthKey(userId, password, uri);
                GCApiKeyLastUpdate = DateTime.UtcNow;
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Auth:Failed: Failed to get API key for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Async version of GetGCAPIKey for better performance in concurrent scenarios
        /// </summary>
        public async Task<bool> GetGCAPIKeyAsync(string userId, string password, string uri)
        {
            try
            {
                _genesysBaseUri = uri.TrimEnd('/');
                GCApiKey = await OAuthKeyAsync(userId, password, uri);
                GCApiKeyLastUpdate = DateTime.UtcNow;
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Auth:Failed: Failed to get API key for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// Clears the token cache - useful when tokens become invalid or for testing
        /// </summary>
        public static async Task ClearTokenCacheAsync()
        {
            await _tokenCacheLock.WaitAsync();
            try
            {
                _tokenCache.Clear();
            }
            finally
            {
                _tokenCacheLock.Release();
            }
        }

#nullable enable
        public CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me GetOrganization()
        {
            var apiMethod = HttpMethod.Post;
            var apiEndpoint = "/api/v2/organizations/me";

            GenesysCloudUtils.JsonUtils ju = new(_logger);
            string? apiResult = null;
            try
            {
                apiResult = ju.JsonReturnString(_genesysBaseUri + apiEndpoint, GCApiKey);
            }
            catch
            {
                Console.WriteLine($"Error calling Genesys Cloud API {apiMethod} {apiEndpoint}");
                Console.WriteLine(apiResult);
                throw;
            }
            if (apiResult == null)
                throw new InvalidDataException(
                    $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
                );

            CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me? gcOrganisation = null;
            try
            {
                gcOrganisation = Newtonsoft.Json.JsonConvert.DeserializeObject<CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me>(apiResult);
            }
            catch
            {
                if (_logger != null)
                {
                    _logger?.LogWarning(
                        "Error parsing result from Genesys Cloud when calling {ApiMethod} {ApiEndpoint} {ApiResult}",
                        apiMethod,
                        apiEndpoint,
                        apiResult);
                }
                else
                {
                    Console.WriteLine($"Error parsing result from Genesys Cloud when calling {apiMethod} {apiEndpoint}");
                    Console.WriteLine(apiResult);
                }
                throw;
            }

            if (gcOrganisation == null)
                throw new InvalidDataException(
                    $"Failed to parse result from Genesys Cloud when calling {apiMethod} {apiEndpoint}"
                );

            return gcOrganisation;
        }
#nullable restore
    }
}
