FROM docker.io/library/alpine:latest

# Build arguments with default values
ARG RUNTIME=linux-musl-x64
ARG TZ=Australia/Sydney
# Environment variables
ENV CSG_GENESYS_JOB=information \
    TZ=${TZ}

# Metadata for Portainer
LABEL maintainer="Customer Science Team" \
      org.opencontainers.image.title="Genesys Adapter" \
      org.opencontainers.image.description="Genesys Cloud Adapter Service" \
      org.opencontainers.image.vendor="Customer Science" \
      org.opencontainers.image.created="$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
WORKDIR /opt/GenesysAdapter

# Install dependencies in a single layer to reduce image size
# icu-data-full is not required, add to suppress warnings from icu-libs
RUN apk update && \
    apk add --no-cache \
        bash \
        gcompat \
        icu-libs \
        icu-data-full \
        libgcc \
        libstdc++ \
        postgresql-client \
        tzdata \
        zlib && \
    mkdir -p /etc/localtime && \
    cp /usr/share/zoneinfo/${TZ} /etc/localtime && \
    adduser -D csganalytics -g csganalytics

# Set up health check for Portainer monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ps aux | grep GenesysAdapter || exit 1
# Use COPY instead of ADD for better practices
COPY --chown=csganalytics:csganalytics release/${RUNTIME} /opt/GenesysAdapter
COPY --chown=csganalytics:csganalytics schema/ /opt/GenesysAdapter/schema

# Set correct permissions
RUN chown -R csganalytics:csganalytics /opt/GenesysAdapter && \
    chmod +x schema/Install_PG_Schema.sh

# Switch to non-root user for security
USER csganalytics

# Command to run when container starts
CMD ["/bin/bash", "-c", "/opt/GenesysAdapter/GenesysAdapter $CSG_GENESYS_JOB"]

# spell-checker: ignore: adduser csganalytics localtime tzdata libgcc libstdc gcompat zoneinfo