FROM docker.io/library/alpine:latest
ARG RUNTIME=linux-musl-x64
ARG TZ=Australia/Sydney
ENV CSG_GENESYS_JOB=information \
    TZ=${TZ}
WORKDIR /opt/GenesysAdapter
# icu-data-full is not required, add to suppress warnings from icu-libs
RUN \
    apk update && \
    apk add --no-cache \
        bash \
        gcompat \
        icu-libs \
        icu-data-full \
        libgcc \
        libstdc++ \
        postgresql-client \
        tzdata \
        zlib && \
    mkdir /etc/localtime && \
    cp /usr/share/zoneinfo/${TZ} /etc/localtime && \
    adduser -D csganalytics -g csganalytics
ADD --chown=csganalytics:csganalytics release/${RUNTIME} /opt/GenesysAdapter
ADD --chown=csganalytics:csganalytics schema/ /opt/GenesysAdapter/schema
RUN chown -R csganalytics:csganalytics /opt/GenesysAdapter
RUN chmod +x schema/Install_PG_Schema.sh
USER csganalytics
CMD /opt/GenesysAdapter/GenesysAdapter ${CSG_GENESYS_JOB}
# spell-checker: ignore: adduser csganalytics localtime tzdata libgcc libstdc gcompat zoneinfo