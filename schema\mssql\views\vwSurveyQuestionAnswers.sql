CREATE OR ALTER VIEW [vwSurveyQuestionAnswers] AS
SELECT surveyquestionanswers.surveyid,
    surveyquestionanswers.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyquestionanswers.surveyformid,
    surveyquestionanswers.surveyname,
    surveyquestionanswers.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyquestionanswers.agentteamid,
    surveyquestionanswers.queueid,
    queue.name AS queuename,
    surveyquestionanswers.questiongroupid,
    surveyquestionanswers.questiongroupname,
    surveyquestionanswers.questionid,
    surveyquestionanswers.questiontext,
    surveyquestionanswers.questiontype,
    surveyquestionanswers.questionanswerid,
    surveyquestionanswers.questionanswertext,
    surveyquestionanswers.questionanswervalue,
    surveyquestionanswers.questionscore,
    surveyquestionanswers.questionmarkedna,
    surveyquestionanswers.updated
FROM surveyquestionanswers
    LEFT JOIN surveydata survey ON survey.surveyid = surveyquestionanswers.surveyid
    LEFT JOIN userdetails agent ON agent.id = surveyquestionanswers.agentid
    LEFT JOIN userdetails manager ON manager.id = agent.manager
    LEFT JOIN queuedetails queue ON queue.id = surveyquestionanswers.queueid;
