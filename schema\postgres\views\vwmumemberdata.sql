DROP VIEW IF EXISTS vwmumemberdata;

CREATE
OR REPLACE VIEW vwmumemberdata AS
SELECT 
	MUDETAILS.ID AS mu_id,
	MUDETAILS.NAME AS mu_name,
	MUMEMBERDATA.ID AS user_id,
	vwuserdetail.NAME AS user_name,
	vwuserdetail.MANAGERNAME AS manager_name
FROM 
	MUMEMBERDATA
		LEFT JOIN MUDETAILS ON MUDETAILS.id = MUMEMBERDATA.muid
		LEFT JOIN vwuserdetail ON vwuserdetail.id = MUMEMBERDATA.id;

COMMENT ON COLUMN vwmumemberdata.mu_id IS 'MU GUID';
COMMENT ON COLUMN vwmumemberdata.mu_name IS 'MU Name';
COMMENT ON COLUMN vwmumemberdata.user_id IS 'User ID';
COMMENT ON COLUMN vwmumemberdata.user_name IS 'User Name';
COMMENT ON COLUMN vwmumemberdata.manager_name IS 'Manager Name';

COMMENT ON VIEW vwmumemberdata IS 'MU Member Data';
