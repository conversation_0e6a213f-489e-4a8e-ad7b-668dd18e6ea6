CREATE TABLE IF NOT EXISTS participantsummarydata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    participantid varchar(50),
    purpose varchar(50) NOT NULL,
    mediatype varchar(50),
    userid varchar(50),
    queueid varchar(50),
    wrapupcode varchar(50),
    wrapupnote text,
    nflow integer,
    tivr numeric(20, 2),
    tflow numeric(20, 2),
    tflowdisconnect numeric(20, 2),
    tflowexit numeric(20, 2),
    tflowout numeric(20, 2),
    tacd numeric(20, 2),
    tacw numeric(20, 2),
    talert numeric(20, 2),
    tanswered numeric(20, 2),
    ttalk numeric(20, 2),
    ttalkcomplete numeric(20, 2),
    thandle numeric(20, 2),
    tconnected numeric(20, 2),
    tfirstconnect numeric(20, 2),
    tcontacting numeric(20, 2),
    tdialing numeric(20, 2),
    tfirstdial numeric(20, 2),
    tnotresponding numeric(20, 2),
    tabandon numeric(20, 2),
    theld numeric(20, 2),
    theldcomplete numeric(20, 2),
    tvoicemail numeric(20, 2),
    tmonitoring numeric(20, 2),
    tmonitoringcomplete numeric(20, 2),
    tshortabandon numeric(20, 2),
    tagentresponsetime numeric(20, 2),
    tuserresponsetime numeric(20, 2),
    tActiveCallback numeric(20, 2),
    tActiveCallbackComplete numeric(20, 2),
    noffered integer,
    nconnected integer,
    nconsult integer,
    nconsulttransferred integer,
    ntransferred integer,
    nblindtransferred integer,
    nerror integer,
    noutbound integer,
    nstatetransitionerror integer,
    noversla integer,
    noutboundattempted integer,
    noutboundconnected integer,
    nflowoutcome integer,
    tflowoutcome numeric(20, 2),
    nflowoutcomefailed integer,
    nbotinteractions integer,
    tPark numeric(20, 2),
    tParkComplete numeric(20, 2),
    divisionid varchar(100),
    divisionid2 varchar(100),
    divisionid3 varchar(100),
    updated timestamp without time zone,
    CONSTRAINT participantsummarydata_pkey PRIMARY KEY (keyid, purpose)
) PARTITION BY LIST (purpose);

CREATE INDEX IF NOT EXISTS participantsummaryconv ON participantsummarydata USING btree (
    conversationid ASC NULLS LAST
);

CREATE TABLE IF NOT EXISTS partsumm_acd PARTITION OF participantsummarydata FOR
VALUES
    IN ('acd');

CREATE TABLE IF NOT EXISTS partsumm_agent PARTITION OF participantsummarydata FOR
VALUES
    IN ('agent');

CREATE TABLE IF NOT EXISTS partsumm_customer PARTITION OF participantsummarydata FOR
VALUES
    IN ('customer');

CREATE TABLE IF NOT EXISTS partsumm_default PARTITION OF participantsummarydata DEFAULT;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  tconnected numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists tfirstconnect numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  tfirstdial numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  tmonitoringcomplete numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists wrapupnote varchar(255);

ALTER TABLE participantsummarydata 
ALTER COLUMN wrapupnote TYPE text;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists tActiveCallback numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  tActiveCallbackComplete numeric(20, 2);

ALTER TABLE participantsummarydata 
ADD column IF NOT exists conversationstartdate timestamp without time zone;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  conversationstartdateltc timestamp without time zone;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists conversationenddate timestamp without time zone;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  conversationenddateltc timestamp without time zone;

ALTER TABLE participantsummarydata 
ADD column IF NOT exists  nbotinteractions integer;

ALTER TABLE participantsummarydata
ADD column IF NOT exists tPark numeric(20, 2);

ALTER TABLE participantsummarydata
ADD column IF NOT exists  tParkComplete numeric(20, 2);

ALTER TABLE participantsummarydata
ADD column IF NOT exists nCobrowseSessions integer;