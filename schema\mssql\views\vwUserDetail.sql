IF dbo.csg_view_definition_contains_string('vwUserDetail', 'lower(ud.email) AS email,') = 0 BEGIN
    DROP VIEW IF EXISTS vwdetailedinteractiondata;
    DROP VIEW IF EXISTS vwscheduledata;
    DROP VIEW IF EXISTS vwuserinteractionpresencedetaileddata;
    DROP VIEW IF EXISTS vwuserpresencedata;
    DROP VIEW IF EXISTS vwadherenceactdata;
    DROP VIEW IF EXISTS vwadherencedaydata;
    DROP VIEW IF EXISTS vwadherenceexcdata;
    DROP VIEW IF EXISTS vwchatdata;
    DROP VIEW IF EXISTS vwtimeoffdata;
    DROP VIEW IF EXISTS vwtimeoffrequestdata;
    DROP VIEW IF EXISTS vwuserpresencedatadaily;
    DROP VIEW IF EXISTS vwrealtimeuser;
    DROP VIEW IF EXISTS vwqueueconvrealtime;
    DROP VIEW IF EXISTS vwuserinteractiondata;
    DROP VIEW IF EXISTS vwqueueauditdata;
    DROP VIEW IF EXISTS vwrealtimeuser_groups;
    DROP VIEW IF EXISTS vwuserqueuemappings;
    DROP VIEW IF EXISTS vwUserDetail;
END;
GO

CREATE OR ALTER VIEW vwUserDetail AS
SELECT
    ud.id,
    ud.name,
    ud.jabberid,
    ud.state,
    ud.title,
    LOWER(CAST(ud.email AS NVARCHAR(MAX))) AS email,
    ud.username,
    ud.department,
    ud.manager as managerid,
    md.name as managername,
    ud.username as agentname,
    ud.divisionid,
    ud.updated,
    ud.employeeid,
    ud.dateHire,
    dd.name as divisionname,
    dd.homedivision
FROM
    userdetails AS ud
    LEFT JOIN userdetails AS md ON md.id = ud.manager
    LEFT JOIN divisiondetails AS dd ON ud.divisionid = dd.id;
