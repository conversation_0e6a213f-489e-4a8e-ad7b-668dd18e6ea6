CREATE TABLE IF NOT EXISTS flowoutcomedata (
    keyid varchar(255) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    flowoutcomestartdate timestamp without time zone,
    flowoutcomestartdateltc timestamp without time zone,
    flowoutcomeenddate timestamp without time zone,
    flowoutcomeenddateltc timestamp without time zone,
    flowid varchar(50),
    flowname varchar(255),
    flowversion numeric(20, 2),
    flowtype varchar(50),
    flowoutcome varchar(50),
    flowoutcomeid varchar(50),
    flowoutcomevalue varchar(50),
    updated timestamp without time zone,
    CONSTRAINT flowoutcomedata_pkey PRIMARY KEY (keyid, conversationstartdate)
);
