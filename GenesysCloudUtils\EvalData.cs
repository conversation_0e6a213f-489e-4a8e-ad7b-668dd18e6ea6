﻿using System;
using System.Data;
using System.Net;
using System.Security.Cryptography;
using System.Threading;
using System.Web;
using Evals = GenesysCloudDefEvaluations;
using Newtonsoft.Json;
using StandardUtils;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    public class EvalData
    {
        private readonly ILogger? _logger;

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueEvalLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private readonly GCUtils GCUtilities;
        private readonly JsonUtils JsonActions;
        private string URI = string.Empty;
        public DateTime DetailEvaluationLastUpdate { get; set; }
        public DataTable EvalsInPending { get; set; }
        public string TimeZoneConfig { get; set; }

        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public EvalData() : this(null)
        {
        }

        public EvalData(ILogger? logger)
        {
            _logger = logger;
            GCUtilities = new GCUtils(logger);
            JsonActions = new JsonUtils(logger);
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            DBUtil.Initialize();
        }

        public DataTable GetEvalAggregationDataFromGC(String StartDate, String EndDate)
        {
            DataTable EvalData = CreateEvalDataTable();

            Console.WriteLine("Retrieving Eval Aggregation Data, Date from {0} ", StartDate);

            DateTime TempEvalLastUpdate = QueueEvalLastUpdate;
            //string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            string RequestBody = "{ " +
                                 "  \"interval\": \"" + StartDate + "/" + EndDate + "\"," +
                                 "  \"granularity\": \"PT30M\"," +
                                 "  \"groupBy\": [" +
                                 "    \"queueId\"," +
                                 "    \"userId\"," +
                                 "  ]" +
                                 "}";

            HttpApiResponse apiResponse;
            try
            {
                apiResponse = JsonActions.JsonReturnHttpResponse(URI + "/api/v2/analytics/evaluations/aggregates/query", GCApiKey, RequestBody);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "API Call Error while retrieving evaluation aggregation data: {Message}", ex.Message);
                Console.WriteLine("Error retrieving evaluation aggregation data: {0}", ex.Message);
                return EvalData;
            }

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                _logger?.LogWarning("Empty response received for evaluation aggregation data");
                Console.WriteLine("Warning: Empty response for evaluation aggregation data");
                return EvalData;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess)
            {
                _logger?.LogError("API Error while retrieving evaluation aggregation data: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types
                if (apiResponse.StatusCode == 403)
                {
                    _logger?.LogWarning("Permission denied (HTTP 403) when retrieving evaluation aggregation data");
                    Console.WriteLine("Permission denied when retrieving evaluation aggregation data");
                }
                else
                {
                    Console.WriteLine("API Error: HTTP {0} when retrieving evaluation aggregation data", apiResponse.StatusCode);
                }
                return EvalData;
            }

            Evals.EvaluationAggregations EvalJson;
            try
            {
                EvalJson = JsonConvert.DeserializeObject<Evals.EvaluationAggregations>(apiResponse.Content,
                           new JsonSerializerSettings
                           {
                               NullValueHandling = NullValueHandling.Ignore
                           });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "JSON deserialization error for evaluation aggregation data: {Message}", ex.Message);
                Console.WriteLine("JSON error for evaluation aggregation data: {0}", ex.Message);
                return EvalData;
            }

            if (EvalJson != null && EvalJson.results != null)
            {
                foreach (Evals.Result Results in EvalJson.results)
                {

                    foreach (Evals.Datum ResultsData in Results.data)
                    {
                        string TimeInterval = ResultsData.interval.Split('/')[0];
                        DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                        if (MaxUpdateDateTest > QueueEvalLastUpdate)
                            QueueEvalLastUpdate = MaxUpdateDateTest;

                        if (Results.group.queueId != null)
                        {
                            DataRow DRNewRow = EvalData.NewRow();
                            DRNewRow["queueId"] = Results.group.queueId;

                            DateTime IntervalStart = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                            IntervalStart = new DateTime(
                                   IntervalStart.Ticks - (IntervalStart.Ticks % TimeSpan.TicksPerSecond),
                                   IntervalStart.Kind
                               );

                            DRNewRow["startdate"] = IntervalStart;


                            foreach (DataColumn DCTemp in EvalData.Columns)
                            {
                                switch (DCTemp.DataType.ToString())
                                {
                                    case "System.Int32":
                                    case "System.Single":
                                    case "System.Double":
                                        DRNewRow[DCTemp.ColumnName] = 0;
                                        break;
                                }
                            }

                            EvalData.Rows.Add(DRNewRow);
                            Console.Write("#");

                        }

                    }

                }
            }

            return EvalData;

        }

        public DataSet GetEvalDetailsFromGC(String StartDate, String EndDate)
        {
            DataSet EvaluationDetailsSet = new DataSet();

            DateTime TempDetailEvaluationLastUpdate = DetailEvaluationLastUpdate;




            Console.WriteLine("Retrieving Eval Detail Data, Date from {0} ", StartDate);

            Console.WriteLine("Retrieving Eval Evaluators");

            DataTable Evaluators = GetEvaluatorsListFromGC(StartDate, EndDate);

            Console.WriteLine("\nGot {0} Active Evaluator", Evaluators.Rows.Count);

            if (Evaluators != null)
            {

                Console.WriteLine("Retrieving Finished Evaluations");
                DataTable EvaluationsList = GetEvaluationListFromGC(Evaluators, StartDate, EndDate);
                EvaluationDetailsSet = GetEvaluationDetailsFromGC(EvaluationsList, StartDate, EndDate);
            }
            else
            {
                Console.WriteLine("No Evaluators or Evaluations to be Found");

            }

            Console.Write("\n");
            return EvaluationDetailsSet;
        }

        public DataSet GetEvaluationDetailsFromGC(DataTable EvaluationsList,String StartDate, String EndDate)
        {

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataSet EvaluationDetailsSet = new DataSet();
            DataTable EvaluationDetails = DBUtil.CreateInMemTable("evalData");
            DataTable EvaluationQuesGroupDetails = DBUtil.CreateInMemTable("evalQuestionGroupData");
            DataTable EvaluationQuesDetails = DBUtil.CreateInMemTable("evalQuestionData");

            if (EvaluationsList != null)
            {

                int Counter = 1;
                foreach (DataRow Eval in EvaluationsList.Rows)
                {

                    if (Counter % 250 == 0)
                    {
                        Console.WriteLine("\nOld Key {0}", GCApiKey.Substring(0, 5));
                        GCUtilities.GetGCAPIKey();
                        GCApiKey = GCUtilities.GCApiKey;
                        Console.WriteLine("New Key {0}", GCApiKey.Substring(0, 5));
                    }

                    string evaluationUrl = URI + "/api/v2/quality/conversations/"
                                                                     + Eval["conversationid"]
                                                                     + "/evaluations/"
                                                                     + Eval["id"];

                    HttpApiResponse apiResponse = null;
                    bool evaluationProcessed = false;
                    int retryAttempts = 0;
                    const int maxRetryAttempts = 5;

                    // Retry loop for handling rate limiting and transient errors
                    while (!evaluationProcessed && retryAttempts < maxRetryAttempts)
                    {
                        try
                        {
                            apiResponse = JsonActions.JsonReturnHttpResponseGet(evaluationUrl, GCApiKey);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "API Call Error while retrieving evaluation details for evaluation {EvaluationId} (attempt {Attempt}/{MaxAttempts}): {Message}",
                                Eval["id"], retryAttempts + 1, maxRetryAttempts, ex.Message);
                            Console.WriteLine("Error retrieving evaluation {0} (attempt {1}/{2}): {3}", Eval["id"], retryAttempts + 1, maxRetryAttempts, ex.Message);

                            retryAttempts++;
                            if (retryAttempts >= maxRetryAttempts)
                            {
                                Counter++;
                                break; // Exit retry loop and continue to next evaluation
                            }

                            // Wait before retrying
                            Thread.Sleep(TimeSpan.FromSeconds(Math.Pow(2, retryAttempts) + new Random().Next(1, 3)));
                            continue;
                        }

                        // Validate response using proper HTTP status code detection
                        if (string.IsNullOrWhiteSpace(apiResponse.Content))
                        {
                            _logger?.LogWarning("Empty response received for evaluation {EvaluationId} (attempt {Attempt}/{MaxAttempts}) - retrying",
                                Eval["id"], retryAttempts + 1, maxRetryAttempts);
                            Console.WriteLine("Empty response for evaluation {0} (attempt {1}/{2})", Eval["id"], retryAttempts + 1, maxRetryAttempts);

                            retryAttempts++;
                            if (retryAttempts >= maxRetryAttempts)
                            {
                                Counter++;
                                break; // Exit retry loop and continue to next evaluation
                            }

                            Thread.Sleep(TimeSpan.FromSeconds(Math.Pow(2, retryAttempts) + new Random().Next(1, 3)));
                            continue;
                        }

                        // Handle different HTTP status codes appropriately
                        if (!apiResponse.IsSuccess)
                        {
                            // Check for specific error types that should halt processing immediately
                            if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                            {
                                _logger?.LogError("Critical API error detected while retrieving evaluation details (HTTP {StatusCode}) - skipping evaluation {EvaluationId}",
                                    apiResponse.StatusCode, Eval["id"]);
                                Console.WriteLine("Critical error for evaluation {0}: HTTP {1}", Eval["id"], apiResponse.StatusCode);
                                Counter++;
                                evaluationProcessed = true; // Mark as processed to exit retry loop
                                break;
                            }

                            // Handle rate limiting (HTTP 429) with proper retry logic
                            if (apiResponse.StatusCode == 429)
                            {
                                retryAttempts++;

                                _logger?.LogWarning("Rate limit encountered for evaluation {EvaluationId} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                    Eval["id"], retryAttempts, maxRetryAttempts);
                                Console.WriteLine("Rate limit for evaluation {0} (attempt {1}/{2}) - retrying with backoff", Eval["id"], retryAttempts, maxRetryAttempts);

                                if (retryAttempts >= maxRetryAttempts)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit for evaluation {EvaluationId} after {Attempts} attempts - skipping evaluation",
                                        Eval["id"], maxRetryAttempts);
                                    Console.WriteLine("Rate limiting exceeded retry limit for evaluation {0} after {1} attempts - skipping", Eval["id"], maxRetryAttempts);
                                    Counter++;
                                    break; // Exit retry loop and continue to next evaluation
                                }

                                // Get a new API key for rate limiting issues
                                try
                                {
                                    _logger?.LogInformation("Refreshing API key due to rate limiting for evaluation {EvaluationId}", Eval["id"]);
                                    GCUtilities.GetGCAPIKey();
                                    GCApiKey = GCUtilities.GCApiKey;
                                    Console.WriteLine("Refreshed API key for evaluation {0}", Eval["id"]);
                                }
                                catch (Exception keyEx)
                                {
                                    _logger?.LogError(keyEx, "Failed to refresh API key for evaluation {EvaluationId}: {Message}", Eval["id"], keyEx.Message);
                                }

                                // Implement exponential backoff with jitter
                                int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                _logger?.LogInformation("Waiting {WaitSeconds} seconds before retrying evaluation {EvaluationId}", waitSeconds, Eval["id"]);
                                Console.WriteLine("Waiting {0} seconds before retrying evaluation {1}", waitSeconds, Eval["id"]);
                                Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));
                                continue;
                            }

                            // For other non-retryable errors, log and skip
                            _logger?.LogError("API Error while retrieving evaluation details: HTTP {StatusCode} - {StatusDescription} for evaluation {EvaluationId}. Response: {Response}",
                                apiResponse.StatusCode, apiResponse.StatusDescription, Eval["id"], apiResponse.Content);
                            Console.WriteLine("Non-retryable error for evaluation {0}: HTTP {1}", Eval["id"], apiResponse.StatusCode);
                            Counter++;
                            evaluationProcessed = true; // Mark as processed to exit retry loop
                            break;
                        }

                        // If we reach here, the API call was successful
                        evaluationProcessed = true;
                    }

                    // If we couldn't process the evaluation after all retries, continue to next evaluation
                    if (!evaluationProcessed || apiResponse == null || !apiResponse.IsSuccess)
                    {
                        continue;
                    }

                    Evals.EvaluationDetails EvalDetails;
                    try
                    {
                        EvalDetails = JsonConvert.DeserializeObject<Evals.EvaluationDetails>(apiResponse.Content,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "JSON deserialization error for evaluation {EvaluationId}: {Message}", Eval["id"], ex.Message);
                        Console.WriteLine("JSON error for evaluation {0}: {1}", Eval["id"], ex.Message);
                        Counter++;
                        continue;
                    }

                    if (EvalDetails != null)
                    {

                        string TempKeyid = EvalDetails.id + "|";

                        DataRow EvalRow = EvaluationDetails.NewRow();

                        EvalRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                        EvalRow["conversationid"] = EvalDetails.conversation.id;
                        EvalRow["evaluationid"] = EvalDetails.id;
                        EvalRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                        EvalRow["evaluatorid"] = EvalDetails.evaluator.id;
                        EvalRow["userid"] = EvalDetails.agent.id;
                        EvalRow["status"] = EvalDetails.status;

                        EvalDetails.assignedDate = new DateTime(
                                EvalDetails.assignedDate.Ticks - (EvalDetails.assignedDate.Ticks % TimeSpan.TicksPerSecond),
                                EvalDetails.assignedDate.Kind
                                );


                        if (EvalDetails.calibration != null)
                        {
                            EvalRow["calibrationid"] = EvalDetails.calibration.id;
                            EvalRow["averagescore"] = EvalDetails.calibration.averageScore;
                            EvalRow["highscore"] = EvalDetails.calibration.highScore;
                            EvalRow["lowscore"] = EvalDetails.calibration.lowScore;
                        }

                        EvalRow["assigneddate"] = EvalDetails.assignedDate.ToUniversalTime();
                        EvalRow["assigneddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.assignedDate.ToUniversalTime(), AppTimeZone);
                        if (EvalDetails.status == "FINISHED")
                        {
                            EvalRow["totalscore"] = decimal.Round(EvalDetails.answers.totalScore, 2);
                            EvalRow["totalcriticalscore"] = decimal.Round(EvalDetails.answers.totalCriticalScore, 2);
                            EvalRow["totalnoncriticalscore"] = decimal.Round(EvalDetails.answers.totalNonCriticalScore, 2);
                            EvalRow["agenthasread"] = EvalDetails.agentHasRead;


                            EvalDetails.releaseDate = new DateTime(
                                   EvalDetails.releaseDate.Ticks - (EvalDetails.releaseDate.Ticks % TimeSpan.TicksPerSecond),
                                   EvalDetails.releaseDate.Kind
                               );

                            if (EvalDetails.releaseDate.Year < 2000)
                            {
                                EvalRow["releasedate"] = EvalDetails.assignedDate.ToUniversalTime();
                                EvalRow["releasedateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.assignedDate.ToUniversalTime(), AppTimeZone);
                            }
                            else
                            {
                                EvalRow["releasedate"] = EvalDetails.releaseDate.ToUniversalTime();
                                EvalRow["releasedateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.releaseDate.ToUniversalTime(), AppTimeZone);
                            }

                        }
                        else
                        {
                            EvalRow["totalscore"] = 0;
                            EvalRow["totalcriticalscore"] = 0;
                            EvalRow["totalnoncriticalscore"] = 0;
                            EvalRow["agenthasread"] = false;
                            EvalRow["releasedate"] = System.DBNull.Value;
                        }

                        DateTime MaxDateTest = EvalDetails.assignedDate.ToUniversalTime();
                        if (MaxDateTest > DetailEvaluationLastUpdate)
                        {
                            DetailEvaluationLastUpdate = MaxDateTest;
                            Console.Write("@");
                        }
                        //Now Do the AnswerGroup Stuff

                        EvaluationDetails.Rows.Add(EvalRow);

                        if (EvalDetails.status == "FINISHED")
                        {
                            Console.Write("F");
                            foreach (Evals.Questiongroupscore QuestionGroup in EvalDetails.answers.questionGroupScores)
                            {
                                DataRow EvalGroupRow = EvaluationQuesGroupDetails.NewRow();

                                TempKeyid = EvalDetails.id + "|"
                                                  + QuestionGroup.questionGroupId + "|";

                                EvalGroupRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                                EvalGroupRow["evaluationid"] = EvalDetails.id;
                                EvalGroupRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                                EvalGroupRow["questiongroupid"] = QuestionGroup.questionGroupId;
                                EvalGroupRow["totalscore"] = decimal.Round(QuestionGroup.totalScore, 2);
                                EvalGroupRow["maxtotalscore"] = decimal.Round(QuestionGroup.maxTotalScore, 2);
                                EvalGroupRow["markedna"] = QuestionGroup.markedNA;
                                EvalGroupRow["totalcriticalscore"] = decimal.Round(QuestionGroup.totalCriticalScore, 2);
                                EvalGroupRow["maxtotalcriticalscore"] = decimal.Round(QuestionGroup.maxTotalCriticalScore, 2);
                                EvalGroupRow["totalnoncriticalscore"] = decimal.Round(QuestionGroup.totalNonCriticalScore, 2);
                                EvalGroupRow["maxtotalnoncriticalscore"] = decimal.Round(QuestionGroup.maxTotalNonCriticalScore, 2);
                                EvalGroupRow["totalscoreunweighted"] = decimal.Round(QuestionGroup.totalCriticalScoreUnweighted, 2);
                                EvalGroupRow["totalscoreunweighted"] = decimal.Round(QuestionGroup.maxTotalCriticalScoreUnweighted, 2);
                                EvalGroupRow["maxtotalscoreunweighted"] = decimal.Round(QuestionGroup.maxTotalCriticalScoreUnweighted, 2);
                                EvalGroupRow["failedkillquestions"] = EvalDetails.answers.anyFailedKillQuestions;
                                EvalGroupRow["comments"] = EvalDetails.answers.comments;

                                try
                                {
                                    EvaluationQuesGroupDetails.Rows.Add(EvalGroupRow);
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("Error on Group EvaluationId {0}. Error {1}", EvalDetails.id, ex.ToString());
                                }

                                foreach (Evals.Questionscore Question in QuestionGroup.questionScores)
                                {

                                    TempKeyid = EvalDetails.id + "|"
                                                       + QuestionGroup.questionGroupId + "|"
                                                       + Question.questionId + "|"
                                                       + Question.answerId;

                                    DataRow QuestionRow = EvaluationQuesDetails.NewRow();

                                    QuestionRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                                    QuestionRow["evaluationid"] = EvalDetails.id;
                                    QuestionRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                                    QuestionRow["questiongroupid"] = QuestionGroup.questionGroupId;
                                    QuestionRow["questionid"] = Question.questionId;
                                    QuestionRow["answerid"] = Question.answerId;
                                    QuestionRow["score"] = decimal.Round(Question.score, 2);
                                    QuestionRow["markedna"] = Question.markedNA;
                                    QuestionRow["failedkillquestions"] = Question.failedKillQuestion;
                                    QuestionRow["comments"] = Question.comments;

                                    try
                                    {
                                        EvaluationQuesDetails.Rows.Add(QuestionRow);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine("Error on Details EvaluationId {0}. Error {1}", EvalDetails.id, ex.ToString());
                                    }
                                }
                            }
                        }
                        else
                            Console.Write("P");

                    }
                    else
                    {
                        _logger?.LogWarning("Failed to deserialize evaluation details for evaluation {EvaluationId} - evaluation data is null", Eval["id"]);
                        Console.WriteLine("Failed to process evaluation {0} - data is null", Eval["id"]);
                    }

                    Counter++;

                }
                Console.Write("\n");
                EvaluationDetailsSet.Tables.Add(EvaluationDetails);
                EvaluationDetailsSet.Tables.Add(EvaluationQuesGroupDetails);
                EvaluationDetailsSet.Tables.Add(EvaluationQuesDetails);
            }
            else
            {
                Console.WriteLine("No Evaluators or Evaluations to be Found");
                EvaluationDetails = null;
            }


            return EvaluationDetailsSet;

        }

        private DataTable GetEvaluationListFromGC(DataTable Evaluators, String StartDate, String EndDate)
        {
            DataTable EvaluationList = CreateEvaluationList();

            try
            {
                foreach (DataRow Evaluator in Evaluators.Rows)
                {
                    int CurrentPage = 1;

                    string nextURI = "";
                    string ToSend = "";

                    while (nextURI != null)
                    {
                        if (nextURI == "")
                        {
                            ToSend = URI + "/api/v2/quality/evaluations/query?&evaluatorUserId="
                                            + Evaluator["userid"]
                                            + "&pageSize=100&pageNumber=" + CurrentPage
                                            + "&startTime=" + HttpUtility.UrlEncode(StartDate)
                                            + "&endTime=" + HttpUtility.UrlEncode(EndDate);
                        }
                        else
                        {
                            ToSend = URI + nextURI + "&evaluatorUserId="
                                            + Evaluator["userid"]
                                            + "&startTime=" + HttpUtility.UrlEncode(StartDate)
                                            + "&endTime=" + HttpUtility.UrlEncode(EndDate);
                        }

                        HttpApiResponse apiResponse;
                        try
                        {
                            apiResponse = JsonActions.JsonReturnHttpResponseGet(ToSend, GCApiKey);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "API Call Error while retrieving evaluation list for evaluator {EvaluatorId}: {Message}", Evaluator["userid"], ex.Message);
                            Console.WriteLine("Error retrieving evaluations for evaluator {0}: {1}", Evaluator["userid"], ex.Message);
                            break; // Exit the loop for this evaluator
                        }

                        // Check if we got a valid response
                        if (string.IsNullOrEmpty(apiResponse.Content) || apiResponse.Content.Length < 2)
                        {
                            _logger?.LogWarning("Empty or invalid response for evaluator {EvaluatorId}", Evaluator["userid"]);
                            Console.WriteLine("Warning: Empty or invalid JSON response for evaluator: " + Evaluator["userid"]);
                            break; // Exit the loop for this evaluator
                        }

                        // Handle different HTTP status codes appropriately
                        if (!apiResponse.IsSuccess)
                        {
                            _logger?.LogError("API Error while retrieving evaluation list: HTTP {StatusCode} - {StatusDescription} for evaluator {EvaluatorId}. Response: {Response}",
                                apiResponse.StatusCode, apiResponse.StatusDescription, Evaluator["userid"], apiResponse.Content);

                            // Check for specific error types
                            if (apiResponse.StatusCode == 403)
                            {
                                _logger?.LogWarning("Permission denied (HTTP 403) for evaluator {EvaluatorId} - skipping", Evaluator["userid"]);
                                Console.WriteLine("Permission denied for evaluator: " + Evaluator["userid"]);
                            }
                            else
                            {
                                Console.WriteLine("API Error: HTTP {0} for evaluator: {1}", apiResponse.StatusCode, Evaluator["userid"]);
                            }
                            break; // Exit the loop for this evaluator
                        }

                        Evals.EvaluationOverview EvalOverview = null;

                        try
                        {
                            EvalOverview = JsonConvert.DeserializeObject<Evals.EvaluationOverview>(apiResponse.Content,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "JSON deserialization error for evaluator {EvaluatorId}: {Message}", Evaluator["userid"], ex.Message);
                            Console.WriteLine("Error deserializing JSON for evaluator: " + Evaluator["userid"] + ". Error: " + ex.Message);
                            break; // Exit the loop for this evaluator
                        }

                        // Check if deserialization was successful
                        if (EvalOverview == null)
                        {
                            Console.WriteLine("Failed to deserialize evaluation data for evaluator: " + Evaluator["userid"]);
                            break; // Exit the loop for this evaluator
                        }

                        // Set nextURI to null if it's empty to exit the loop
                        nextURI = EvalOverview.nextUri;

                        // Check if entities is null
                        if (EvalOverview.entities == null)
                        {
                            Console.WriteLine("No entities found in evaluation data for evaluator: " + Evaluator["userid"]);
                            break; // Exit the loop for this evaluator
                        }

                        foreach (Evals.EvaluationItem EvalItem in EvalOverview.entities)
                        {
                            if (EvalItem != null && EvalItem.conversation != null)
                            {
                                DataRow EvalDetail = EvaluationList.NewRow();
                                EvalDetail["id"] = EvalItem.id;
                                EvalDetail["conversationid"] = EvalItem.conversation.id;
                                EvaluationList.Rows.Add(EvalDetail);
                            }
                        }

                        Console.WriteLine("Processing Evaluator:" + Evaluator["userid"] + "Data Page Number: " + CurrentPage);
                        CurrentPage++;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in GetEvaluationListFromGC: " + ex.ToString());
            }

            return EvaluationList;
        }

        private DataTable GetEvaluatorsListFromGC(String StartDate, String EndDate)
        {
            DataTable Evaluators = CreateEvaluators();
            Console.WriteLine("Retrieving Active Evaluators");

            try
            {
                string nextURI = "";
                string ToSend = "";

                int CurrentPage = 1;

                while (nextURI != null)
                {
                    if (nextURI == "")
                    {
                        ToSend = URI + "/api/v2/quality/evaluators/activity?pageSize=100&pageNumber=" + CurrentPage +
                                                    "&startTime=" + StartDate +
                                                    "&endTime=" + EndDate;
                    }
                    else
                    {
                        ToSend = URI + nextURI;
                    }
                    HttpApiResponse apiResponse;
                    try
                    {
                        apiResponse = JsonActions.JsonReturnHttpResponseGet(ToSend, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "API Call Error while retrieving evaluators list: {Message}", ex.Message);
                        Console.WriteLine("Error retrieving evaluators list: {0}", ex.Message);
                        break; // Exit the loop
                    }

                    // Check if we got a valid response
                    if (string.IsNullOrEmpty(apiResponse.Content) || apiResponse.Content.Length < 2)
                    {
                        _logger?.LogWarning("Empty or invalid response for evaluators list");
                        Console.WriteLine("Warning: Empty or invalid JSON response for evaluators list");
                        break; // Exit the loop
                    }

                    // Handle different HTTP status codes appropriately
                    if (!apiResponse.IsSuccess)
                    {
                        _logger?.LogError("API Error while retrieving evaluators list: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                            apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                        // Check for specific error types
                        if (apiResponse.StatusCode == 403)
                        {
                            _logger?.LogWarning("Permission denied (HTTP 403) when retrieving evaluators list");
                            Console.WriteLine("Permission denied when retrieving evaluators list");
                        }
                        else
                        {
                            Console.WriteLine("API Error: HTTP {0} when retrieving evaluators list", apiResponse.StatusCode);
                        }
                        break; // Exit the loop
                    }

                    Evals.Evaluators UserEvaluators = null;

                    try
                    {
                        UserEvaluators = JsonConvert.DeserializeObject<Evals.Evaluators>(apiResponse.Content,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "JSON deserialization error for evaluators list: {Message}", ex.Message);
                        Console.WriteLine("Error deserializing JSON for evaluators list: " + ex.Message);
                        break; // Exit the loop
                    }

                    // Check if deserialization was successful
                    if (UserEvaluators == null)
                    {
                        Console.WriteLine("Failed to deserialize evaluators data");
                        break; // Exit the loop
                    }

                    // Set nextURI to null if it's empty to exit the loop
                    nextURI = UserEvaluators.nextUri;

                    // Check if entities is null
                    if (UserEvaluators.entities == null)
                    {
                        Console.WriteLine("No entities found in evaluators data");
                        break; // Exit the loop
                    }

                    foreach (Evals.Entity JSON in UserEvaluators.entities)
                    {
                        if (JSON != null && JSON.evaluator != null)
                        {
                            int Assigned = JSON.numEvaluationsAssigned + JSON.numEvaluationsStarted +
                                          JSON.numCalibrationsAssigned + JSON.numCalibrationsStarted +
                                          JSON.numCalibrationsCompleted + JSON.numEvaluationsCompleted;

                            if (Assigned > 0)
                            {
                                DataRow UserRow = Evaluators.NewRow();
                                UserRow["userid"] = JSON.evaluator.id;
                                Evaluators.Rows.Add(UserRow);
                            }
                        }
                    }

                    Console.WriteLine("Processing Evaluators Data Page Number: " + CurrentPage);
                    CurrentPage++;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in GetEvaluatorsListFromGC: " + ex.ToString());
            }

            return Evaluators;
        }

        private DataTable CreateEvalDataTable()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evalData";
            DTTemp.Columns.Add("keyid", typeof(String));
            DTTemp.Columns.Add("queueid", typeof(String));
            DTTemp.Columns.Add("userid", typeof(String));
            DTTemp.Columns.Add("startdate", typeof(DateTime));
            DTTemp.Columns.Add("nevaluationsdeleted", typeof(int));
            DTTemp.Columns.Add("nevaluations", typeof(int));
            DTTemp.Columns.Add("nevaluationsdeleted", typeof(int));


            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };
            return DTTemp;
        }

        private DataTable CreateEvaluators()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evaluators";
            DTTemp.Columns.Add("userid", typeof(String));

            return DTTemp;
        }

        private DataTable CreateEvaluationList()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evaluationlist";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("conversationid", typeof(String));

            return DTTemp;
        }


    }
}
// spell-checker: ignore: ques, evaluatorid, calibrationid, assigneddateltc, releasedateltc, questiongroupid, questionid
// spell-checker: ignore: answerid, nevaluationsdeleted, nevaluations
