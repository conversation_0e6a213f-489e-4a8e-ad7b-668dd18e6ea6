CREATE TABLE IF NOT EXISTS userrealtimedata(
    id varchar(50) NOT NULL,
    name varchar(255),
    jabberId varchar(100),
    email varchar(255),
    state varchar(50),
    title varchar(255),
    username varchar(255),
    department varchar(255),
    routingstatus varchar(20),
    routstarttime timestamp without time zone,
    systempresence varchar(50),
    presenceid varchar(50),
    presstarttime timestamp without time zone,
    cccallactive integer,
    cccallacw integer,
    othcallactive integer,
    othcallacw integer,
    cbcallactive integer,
    cbcallacw integer,
    cbothcallactive integer,
    cbothcallacw integer,
    ccemailactive integer,
    ccemailacw integer,
    othemailactive integer,
    othemailacw integer,
    ccchatactive integer,
    ccchatacw integer,
    othchatactive integer,
    othchatacw integer,
    adherencestate varchar(50),
    adherencestarttime timestamp without time zone,
    impact varchar(50),
    scheduledactivitycategory varchar(50),
    updated timestamp without time zone,
    CONSTRAINT userrealtimedata_key PRIMARY KEY (id)
);