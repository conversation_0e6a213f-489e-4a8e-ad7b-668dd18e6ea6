using System.Reflection;
using System.Data;
using CSG.Adapter.Configuration;

namespace CSG.Common.ExtensionMethods;

public static class ExtensionMethods
{
    public static bool HasAttribute<T>(this MemberInfo member)
        where T : Attribute
    {
        return GetAttributes<T>(member).Length > 0;
    }

    public static T[] GetAttributes<T>(this MemberInfo member)
        where T : Attribute
    {
        var attributes =
            member.GetCustomAttributes(typeof(T), inherit: false);

        return (T[])attributes;
    }

    public static T? GetFirstAttributeOrNull<T>(this MemberInfo member)
        where T : Attribute
    {
        T[] attributes = GetAttributes<T>(member);

        return attributes.Length > 0 ? attributes[0] : null;
    }

    public static void SetFieldValue(this DataRow row, string rowKey, string columnName, object? value)
    {
        if (!row.Table.Columns.Contains(columnName))
            throw new MissingFieldException($"Column {columnName} does not exist in table");

        DataColumn column = row.Table.Columns[columnName]!;

        if (value == null || value == DBNull.Value)
        {
            row[columnName] = DBNull.Value;
        }
        else if (column.DataType == typeof(string))
        {
            string stringValue = value.ToString() ?? "";
            int maxLength = column.MaxLength;

            if (stringValue.Length > maxLength)
            {
                Console.WriteLine("Length of column {0} exceeds maximum length ({1}>{2}) for ID {3}, field will be truncated.",
                    columnName,
                    stringValue.Length,
                    maxLength,
                    rowKey);
                stringValue = stringValue.Substring(0, maxLength);
            }

            row[columnName] = stringValue;
        }
        else
        {
            row[columnName] = value;
        }
    }

	public static string GetDescription<TEnum>(this TEnum enumValue) where TEnum : Enum
    {
        Type enumType = typeof(TEnum);
        MemberInfo[] memberInfo = enumType.GetMember(enumValue.ToString());
        DescriptionAttribute[]? descriptionAttributes = memberInfo[0]
            .GetCustomAttributes(typeof(DescriptionAttribute), false)
            as DescriptionAttribute[];

        if (descriptionAttributes == null || descriptionAttributes.Length == 0)
            return "";

        return descriptionAttributes[0].Description;
    }
}
