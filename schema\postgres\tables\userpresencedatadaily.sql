CREATE TABLE IF NOT EXISTS userpresencedatadaily (
    keyid varchar(255) NOT NULL,
    id varchar(50),
    userid varchar(50),
    startdate timestamp without time zone,
    timetype varchar(50),
    systempresenceid varchar(50),
    presenceid varchar(50),
    presencetime numeric(20, 2),
    routingid varchar(50),
    routingtime numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedatadaily_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS userpresencedailydatatype ON userpresencedatadaily USING btree (
    timetype ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS userpresencedailystart ON userpresencedatadaily USING btree (startdate ASC NULLS LAST) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS userpresencedailyuser ON userpresencedatadaily USING btree (
    userid ASC NULLS LAST
) TABLESPACE pg_default;

COMMENT ON COLUMN userPresenceDataDaily.id IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.keyid IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.presenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.presencetime IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.routingid IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.routingtime IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.startdate IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.systempresenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.timetype IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.updated IS ' '; 
COMMENT ON COLUMN userPresenceDataDaily.userid IS ' ';
COMMENT ON TABLE userPresenceDataDaily IS 'User Presence Data Daily Data - LTC - See UserPresenceData for Descriptions';