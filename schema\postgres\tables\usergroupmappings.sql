CREATE TABLE IF NOT EXISTS usergroupmappings (
    id varchar(100) NOT NULL,
    name varchar(100),
    userid varchar(50),
    groupid varchar(100),
    updated timestamp without time zone,
    CONSTRAINT usergroupmappings_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

COMMENT ON COLUMN usergroupmappings.id IS 'Primary Key / Unique Identifier for User-Group Mapping';
COMMENT ON COLUMN usergroupmappings.name IS 'Name of the User-Group Mapping';
COMMENT ON COLUMN usergroupmappings.userid IS 'Unique Identifier for the User (Does not include owners)';
COMMENT ON COLUMN usergroupmappings.groupid IS 'Unique Identifier for the Group';
COMMENT ON COLUMN usergroupmappings.updated IS 'Timestamp of the Last Update';