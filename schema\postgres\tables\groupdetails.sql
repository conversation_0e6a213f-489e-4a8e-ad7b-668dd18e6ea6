CREATE FUNCTION pg_temp.rename_column_if_exists(p_table text, p_existing_column_name text, p_new_column_name text)
RETURNS integer
LANGUAGE plpgsql
AS
$func$
DECLARE
	v_parent_table_exists text;
BEGIN
    IF EXISTS (
        SELECT *
        FROM information_schema.columns
        WHERE table_name=p_table and column_name=p_existing_column_name
    ) THEN
        EXECUTE format('ALTER TABLE %s RENAME COLUMN %I TO %I', p_table, p_existing_column_name, p_new_column_name);
        RETURN 1;
    END IF;
    RETURN 0;
END
$func$;

CREATE TABLE IF NOT EXISTS groupdetails (
    id varchar(50) NOT NULL,
    name varchar(255),
    description varchar(255),
    membercount integer,
    state varchar(50),
    type varchar(50),
    selfuri varchar(150),
    updated timestamp without time zone,
    CONSTRAINT groupdetails_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

SELECT pg_temp.rename_column_if_exists('groupdetails', 'selfUri', 'selfuri');
SELECT pg_temp.rename_column_if_exists('groupdetails', 'email', 'description');

-- Add comments

COMMENT ON COLUMN groupDetails.description IS 'Agent Group Description'; 
COMMENT ON COLUMN groupDetails.id IS 'Primary Key / Agent Group GUID'; 
COMMENT ON COLUMN groupDetails.selfuri IS 'Agent Group URI'; 
COMMENT ON COLUMN groupDetails.membercount IS 'Agent Group Member Count (Includes all members and Owners if flag set in GC)'; 
COMMENT ON COLUMN groupDetails.name IS 'Agent Group Name'; 
COMMENT ON COLUMN groupDetails.state IS 'Agent Group State'; 
COMMENT ON COLUMN groupDetails.type IS 'Agent Group Type'; 
COMMENT ON COLUMN groupDetails.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE groupDetails IS 'Agent Group Lookup Data'; 