DROP VIEW IF EXISTS vwPresenceDetails;

CREATE
OR REPLACE VIEW vwPresenceDetails AS
SELECT
    pd.id,
    pd.systempresence,
    pd.orgpresence,
    pd.deactivated,
    pd.type,
    CASE
        WHEN pd.divisionid = '*' THEN dd.id
        ELSE pd.divisionid
    END as divisionid
FROM
    presenceDetails pd
LEFT JOIN
    divisiondetails dd
ON
    pd.divisionid = '*';

COMMENT ON COLUMN vwPresenceDetails.deactivated IS 'Presence Active ?';
COMMENT ON COLUMN vwPresenceDetails.id IS 'Primary Key'; 
COMMENT ON COLUMN vwPresenceDetails.orgpresence IS 'Presence Organisation Presence Name'; 
COMMENT ON COLUMN vwPresenceDetails.systempresence IS 'Presence System Presence Name'; 
COMMENT ON COLUMN vwPresenceDetails.type IS 'Presence Type'; 
COMMENT ON COLUMN vwPresenceDetails.divisionid IS 'Presence Division ID'; 
COMMENT ON VIEW vwPresenceDetails IS 'See PresenceDetails: Presence Code Lookup Data';