{"version": "2.0.0", "tasks": [{"label": "test", "command": "dotnet", "type": "process", "args": ["test", "/p:CollectCoverage=true", "/p:CoverletOutputFormat=cobertura"], "group": {"kind": "test", "isDefault": true}}, {"label": "build", "command": "dotnet", "type": "process", "args": ["build", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}