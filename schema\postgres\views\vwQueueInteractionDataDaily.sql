CREATE
OR REPLACE VIEW vwQueueInteractionDataDaily AS
SELECT
    queueinteractiondatadaily.keyid,
    queueinteractiondatadaily.startdate,
    queueinteractiondatadaily.direction,
    queueinteractiondatadaily.queueid,
    queuedetails.name as queuename,
    queuedetails.divisionid AS queue_divisionid,
    divisiondetails.name as queue_divisionname,
    queueinteractiondatadaily.mediatype,
    queueinteractiondatadaily.wrapupcode,
    wrapupdetails.name as wrapupdesc,
    queueinteractiondatadaily.talertcount,
    queueinteractiondatadaily.talerttimesum,
    queueinteractiondatadaily.talerttimesum / 86400.00 as talerttimesumDay,
    queueinteractiondatadaily.talerttimemax,
    queueinteractiondatadaily.talerttimemax / 86400.00 as talerttimemaxDay,
    queueinteractiondatadaily.talerttimemin,
    queueinteractiondatadaily.talerttimemin / 86400.00 as talerttimeminDay,
    queueinteractiondatadaily.tansweredcount,
    queueinteractiondatadaily.tansweredtimesum,
    queueinteractiondatadaily.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    queueinteractiondatadaily.tansweredtimemax,
    queueinteractiondatadaily.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    queueinteractiondatadaily.tansweredtimemin,
    queueinteractiondatadaily.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    queueinteractiondatadaily.ttalkcount,
    queueinteractiondatadaily.ttalktimesum,
    queueinteractiondatadaily.ttalktimesum / 86400.00 as ttalktimesumDay,
    queueinteractiondatadaily.ttalktimemax,
    queueinteractiondatadaily.ttalktimemax / 86400.00 as ttalktimemaxDay,
    queueinteractiondatadaily.ttalktimemin,
    queueinteractiondatadaily.ttalktimemin / 86400.00 as ttalktimeminDay,
    queueinteractiondatadaily.ttalkcompletecount,
    queueinteractiondatadaily.ttalkcompletetimesum,
    queueinteractiondatadaily.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    queueinteractiondatadaily.ttalkcompletetimemax,
    queueinteractiondatadaily.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    queueinteractiondatadaily.ttalkcompletetimemin,
    queueinteractiondatadaily.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    queueinteractiondatadaily.tnotrespondingcount,
    queueinteractiondatadaily.tnotrespondingtimesum,
    queueinteractiondatadaily.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    queueinteractiondatadaily.tnotrespondingtimemax,
    queueinteractiondatadaily.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    queueinteractiondatadaily.tnotrespondingtimemin,
    queueinteractiondatadaily.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    queueinteractiondatadaily.theldcount,
    queueinteractiondatadaily.theldtimesum,
    queueinteractiondatadaily.theldtimesum / 86400.00 as theldtimesumDay,
    queueinteractiondatadaily.theldtimemax,
    queueinteractiondatadaily.theldtimemax / 86400.00 as theldtimemaxDay,
    queueinteractiondatadaily.theldtimemin,
    queueinteractiondatadaily.theldtimemin / 86400.00 as theldtimeminDay,
    queueinteractiondatadaily.theldcompletecount,
    queueinteractiondatadaily.theldcompletetimesum,
    queueinteractiondatadaily.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    queueinteractiondatadaily.theldcompletetimemax,
    queueinteractiondatadaily.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    queueinteractiondatadaily.theldcompletetimemin,
    queueinteractiondatadaily.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    queueinteractiondatadaily.thandlecount,
    queueinteractiondatadaily.thandletimesum,
    queueinteractiondatadaily.thandletimesum / 86400.00 as thandletimesumDay,
    queueinteractiondatadaily.thandletimemax,
    queueinteractiondatadaily.thandletimemax / 86400.00 as thandletimemaxDay,
    queueinteractiondatadaily.thandletimemin,
    queueinteractiondatadaily.thandletimemin / 86400.00 as thandletimeminDay,
    queueinteractiondatadaily.tacwcount,
    queueinteractiondatadaily.tacwtimesum,
    queueinteractiondatadaily.tacwtimesum / 86400.00 as tacwtimesumDay,
    queueinteractiondatadaily.tacwtimemax,
    queueinteractiondatadaily.tacwtimemax / 86400.00 as tacwtimemaxDay,
    queueinteractiondatadaily.tacwtimemin,
    queueinteractiondatadaily.tacwtimemin / 86400.00 as tacwtimeminDay,
    queueinteractiondatadaily.nconsult,
    queueinteractiondatadaily.nconsulttransferred,
    queueinteractiondatadaily.noutbound,
    queueinteractiondatadaily.nerror,
    queueinteractiondatadaily.ntransferred,
    queueinteractiondatadaily.nblindtransferred,
    queueinteractiondatadaily.nconnected,
    queueinteractiondatadaily.noffered,
    queueinteractiondatadaily.noversla,
    queueinteractiondatadaily.tacdcount,
    queueinteractiondatadaily.tacdtimesum,
    queueinteractiondatadaily.tacdtimesum / 86400.00 as tacdtimesumDay,
    queueinteractiondatadaily.tacdtimemax,
    queueinteractiondatadaily.tacdtimemax / 86400.00 as tacdtimemaxDay,
    queueinteractiondatadaily.tacdtimemin,
    queueinteractiondatadaily.tacdtimemin / 86400.00 as tacdtimeminDay,
    queueinteractiondatadaily.tdialingcount,
    queueinteractiondatadaily.tdialingtimesum,
    queueinteractiondatadaily.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    queueinteractiondatadaily.tdialingtimemax,
    queueinteractiondatadaily.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    queueinteractiondatadaily.tdialingtimemin,
    queueinteractiondatadaily.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    queueinteractiondatadaily.tcontactingcount,
    queueinteractiondatadaily.tcontactingtimesum,
    queueinteractiondatadaily.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    queueinteractiondatadaily.tcontactingtimemax,
    queueinteractiondatadaily.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    queueinteractiondatadaily.tcontactingtimemin,
    queueinteractiondatadaily.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    queueinteractiondatadaily.tvoicemailcount,
    queueinteractiondatadaily.tvoicemailtimesum,
    queueinteractiondatadaily.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    queueinteractiondatadaily.tvoicemailtimemax,
    queueinteractiondatadaily.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    queueinteractiondatadaily.tvoicemailtimemin,
    queueinteractiondatadaily.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    queueinteractiondatadaily.tflowoutcount,
    queueinteractiondatadaily.tflowouttimesum,
    queueinteractiondatadaily.tflowouttimesum / 86400.00 as tflowouttimesumDay,
    queueinteractiondatadaily.tflowouttimemax,
    queueinteractiondatadaily.tflowouttimemax / 86400.00 as tflowouttimemaxDay,
    queueinteractiondatadaily.tflowouttimemin,
    queueinteractiondatadaily.tflowouttimemin / 86400.00 as tflowouttimeminDay,
    queueinteractiondatadaily.twaitcount,
    queueinteractiondatadaily.twaittimesum,
    queueinteractiondatadaily.twaittimesum / 86400.00 as twaittimesumDay,
    queueinteractiondatadaily.twaittimemax,
    queueinteractiondatadaily.twaittimemax / 86400.00 as twaittimemaxDay,
    queueinteractiondatadaily.twaittimemin,
    queueinteractiondatadaily.twaittimemin / 86400.00 as twaittimeminDay,
    queueinteractiondatadaily.tabandoncount,
    queueinteractiondatadaily.tabandontimesum,
    queueinteractiondatadaily.tabandontimesum / 86400.00 as tabandontimesumDay,
    queueinteractiondatadaily.tabandontimemax,
    queueinteractiondatadaily.tabandontimemax / 86400.00 as tabandontimemaxDay,
    queueinteractiondatadaily.tabandontimemin,
    queueinteractiondatadaily.tabandontimemin / 86400.00 as tabandontimeminDay,
    queueinteractiondatadaily.servicelevelnumerator,
    queueinteractiondatadaily.serviceleveldenominator,
    queueinteractiondatadaily.av1count,
    queueinteractiondatadaily.av1timesum,
    queueinteractiondatadaily.av1timesum / 86400.00 as av1timesumDay,
    queueinteractiondatadaily.av1timemax,
    queueinteractiondatadaily.av1timemax / 86400.00 as av1timemaxDay,
    queueinteractiondatadaily.av1timemin,
    queueinteractiondatadaily.av1timemin / 86400.00 as av1timeminDay,
    queueinteractiondatadaily.av2count,
    queueinteractiondatadaily.av2timesum,
    queueinteractiondatadaily.av2timesum / 86400.00 as av2timesumDay,
    queueinteractiondatadaily.av2timemax,
    queueinteractiondatadaily.av2timemax / 86400.00 as av2timemaxDay,
    queueinteractiondatadaily.av2timemin,
    queueinteractiondatadaily.av2timemin / 86400.00 as av2timeminDay,
    queueinteractiondatadaily.av3count,
    queueinteractiondatadaily.av3timesum,
    queueinteractiondatadaily.av3timesum / 86400.00 as av3timesumDay,
    queueinteractiondatadaily.av3timemax,
    queueinteractiondatadaily.av3timemax / 86400.00 as av3timemaxDay,
    queueinteractiondatadaily.av3timemin,
    queueinteractiondatadaily.av3timemin / 86400.00 as av3timeminDay,
    queueinteractiondatadaily.av4count,
    queueinteractiondatadaily.av4timesum,
    queueinteractiondatadaily.av4timesum / 86400.00 as av4timesumDay,
    queueinteractiondatadaily.av4timemax,
    queueinteractiondatadaily.av4timemax / 86400.00 as av4timemaxDay,
    queueinteractiondatadaily.av4timemin,
    queueinteractiondatadaily.av4timemin / 86400.00 as av4timeminDay,
    queueinteractiondatadaily.av5count,
    queueinteractiondatadaily.av5timesum,
    queueinteractiondatadaily.av5timesum / 86400.00 as av5timesumDay,
    queueinteractiondatadaily.av5timemax,
    queueinteractiondatadaily.av5timemax / 86400.00 as av5timemaxDay,
    queueinteractiondatadaily.av5timemin,
    queueinteractiondatadaily.av5timemin / 86400.00 as av5timeminDay,
    queueinteractiondatadaily.av6count,
    queueinteractiondatadaily.av6timesum,
    queueinteractiondatadaily.av6timesum / 86400.00 as av6timesumDay,
    queueinteractiondatadaily.av6timemax,
    queueinteractiondatadaily.av6timemax / 86400.00 as av6timemaxDay,
    queueinteractiondatadaily.av6timemin,
    queueinteractiondatadaily.av6timemin / 86400.00 as av6timeminDay,
    queueinteractiondatadaily.av7count,
    queueinteractiondatadaily.av7timesum,
    queueinteractiondatadaily.av7timesum / 86400.00 as av7timesumDay,
    queueinteractiondatadaily.av7timemax,
    queueinteractiondatadaily.av7timemax / 86400.00 as av7timemaxDay,
    queueinteractiondatadaily.av7timemin,
    queueinteractiondatadaily.av7timemin / 86400.00 as av7timeminDay,
    queueinteractiondatadaily.av8count,
    queueinteractiondatadaily.av8timesum,
    queueinteractiondatadaily.av8timesum / 86400.00 as av8timesumDay,
    queueinteractiondatadaily.av8timemax,
    queueinteractiondatadaily.av8timemax / 86400.00 as av8timemaxDay,
    queueinteractiondatadaily.av8timemin,
    queueinteractiondatadaily.av8timemin / 86400.00 as av8timeminDay,
    queueinteractiondatadaily.av9count,
    queueinteractiondatadaily.av9timesum,
    queueinteractiondatadaily.av9timesum / 86400.00 as av9timesumDay,
    queueinteractiondatadaily.av9timemax,
    queueinteractiondatadaily.av9timemax / 86400.00 as av9timemaxDay,
    queueinteractiondatadaily.av9timemin,
    queueinteractiondatadaily.av9timemin / 86400.00 as av9timeminDay,
    queueinteractiondatadaily.av10count,
    queueinteractiondatadaily.av10timesum,
    queueinteractiondatadaily.av10timesum / 86400.00 as av10timesumDay,
    queueinteractiondatadaily.av10timemax,
    queueinteractiondatadaily.av10timemax / 86400.00 as av10timemaxDay,
    queueinteractiondatadaily.av10timemin,
    queueinteractiondatadaily.av10timemin / 86400.00 as av10timeminDay,
    queueinteractiondatadaily.updated
FROM
    queueInteractiondatadaily as queueinteractiondatadaily
    left outer join queuedetails as queuedetails on queuedetails.id = queueinteractiondatadaily.queueid
    left outer join wrapupdetails as wrapupdetails on wrapupdetails.id = queueinteractiondatadaily.wrapupcode
    left outer join divisiondetails as divisiondetails on queuedetails.divisionid = divisiondetails.id;

COMMENT ON COLUMN vwQueueInteractionDataDaily.keyid IS 'Primary Key';
COMMENT ON COLUMN vwQueueInteractionDataDaily.startdate IS 'Start Datez(UTC)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.direction IS 'Direction of the interaction';
COMMENT ON COLUMN vwQueueInteractionDataDaily.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwQueueInteractionDataDaily.queuename IS 'Name of the queue';
COMMENT ON COLUMN vwQueueInteractionDataDaily.queue_divisionid IS 'Queue Division GUID';
COMMENT ON COLUMN vwQueueInteractionDataDaily.queue_divisionname IS 'Name of the division of the queue';
COMMENT ON COLUMN vwQueueInteractionDataDaily.mediatype IS 'Type of media for the interaction';
COMMENT ON COLUMN vwQueueInteractionDataDaily.wrapupcode IS 'Wrap-up code associated with the interaction';
COMMENT ON COLUMN vwQueueInteractionDataDaily.wrapupdesc IS 'Description of the wrap-up code';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talertcount IS 'Total count of alert events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimesum IS 'Total duration of alert events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimesumDay IS 'Total duration of alert events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimemax IS 'Maximum duration of a single alert event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimemaxDay IS 'Maximum duration of a single alert event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimemin IS 'Minimum duration of a single alert event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.talerttimeminDay IS 'Minimum duration of a single alert event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredcount IS 'Total count of answered events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimesum IS 'Total duration of answered events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimesumDay IS 'Total duration of answered events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimemax IS 'Maximum duration of a single answered event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimemaxDay IS 'Maximum duration of a single answered event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimemin IS 'Minimum duration of a single answered event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tansweredtimeminDay IS 'Minimum duration of a single answered event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcount IS 'Total count of talk events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimesum IS 'Total duration of talk events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimesumDay IS 'Total duration of talk events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimemax IS 'Maximum duration of a single talk event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimemaxDay IS 'Maximum duration of a single talk event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimemin IS 'Minimum duration of a single talk event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalktimeminDay IS 'Minimum duration of a single talk event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletecount IS 'Total count of completed talk events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimesum IS 'Total duration of completed talk events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimesumDay IS 'Total duration of completed talk events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimemax IS 'Maximum duration of a single completed talk event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimemaxDay IS 'Maximum duration of a single completed talk event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimemin IS 'Minimum duration of a single completed talk event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ttalkcompletetimeminDay IS 'Minimum duration of a single completed talk event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingcount IS 'Total count of not responding events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimesum IS 'Total duration of not responding events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimesumDay IS 'Total duration of not responding events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimemax IS 'Maximum duration of a single not responding event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimemaxDay IS 'Maximum duration of a single not responding event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimemin IS 'Minimum duration of a single not responding event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tnotrespondingtimeminDay IS 'Minimum duration of a single not responding event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcount IS 'Total count of held events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimesum IS 'Total duration of held events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimesumDay IS 'Total duration of held events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimemax IS 'Maximum duration of a single held event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimemaxDay IS 'Maximum duration of a single held event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimemin IS 'Minimum duration of a single held event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldtimeminDay IS 'Minimum duration of a single held event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletecount IS 'Total count of completed held events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimesum IS 'Total duration of completed held events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimesumDay IS 'Total duration of completed held events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimemax IS 'Maximum duration of a single completed held event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimemaxDay IS 'Maximum duration of a single completed held event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimemin IS 'Minimum duration of a single completed held event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.theldcompletetimeminDay IS 'Minimum duration of a single completed held event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandlecount IS 'Total count of handle events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimesum IS 'Total duration of handle events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimesumDay IS 'Total duration of handle events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimemax IS 'Maximum duration of a single handle event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimemaxDay IS 'Maximum duration of a single handle event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimemin IS 'Minimum duration of a single handle event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.thandletimeminDay IS 'Minimum duration of a single handle event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwcount IS 'Total count of after-call work (ACW) events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimesum IS 'Total duration of after-call work (ACW) events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimesumDay IS 'Total duration of after-call work (ACW) events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimemax IS 'Maximum duration of a single after-call work (ACW) event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimemaxDay IS 'Maximum duration of a single after-call work (ACW) event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimemin IS 'Minimum duration of a single after-call work (ACW) event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacwtimeminDay IS 'Minimum duration of a single after-call work (ACW) event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.nconsult IS 'Total count of consultation events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.nconsulttransferred IS 'Total count of consultations transferred';
COMMENT ON COLUMN vwQueueInteractionDataDaily.noutbound IS 'Total count of outbound interactions';
COMMENT ON COLUMN vwQueueInteractionDataDaily.nerror IS 'Total count of error events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.ntransferred IS 'Total count of interactions transferred';
COMMENT ON COLUMN vwQueueInteractionDataDaily.nblindtransferred IS 'Total count of interactions blindly transferred';
COMMENT ON COLUMN vwQueueInteractionDataDaily.nconnected IS 'Total count of connected interactions';
COMMENT ON COLUMN vwQueueInteractionDataDaily.noffered IS 'Total count of interactions offered';
COMMENT ON COLUMN vwQueueInteractionDataDaily.noversla IS 'Total count of interactions oversla';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdcount IS 'Total count of Automatic Call Distributor (ACD) events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimesum IS 'Total duration of Automatic Call Distributor (ACD) events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimesumDay IS 'Total duration of Automatic Call Distributor (ACD) events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimemax IS 'Maximum duration of a single Automatic Call Distributor (ACD) event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimemaxDay IS 'Maximum duration of a single Automatic Call Distributor (ACD) event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimemin IS 'Minimum duration of a single Automatic Call Distributor (ACD) event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tacdtimeminDay IS 'Minimum duration of a single Automatic Call Distributor (ACD) event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingcount IS 'Total count of dialing events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimesum IS 'Total duration of dialing events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimesumDay IS 'Total duration of dialing events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimemax IS 'Maximum duration of a single dialing event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimemaxDay IS 'Maximum duration of a single dialing event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimemin IS 'Minimum duration of a single dialing event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tdialingtimeminDay IS 'Minimum duration of a single dialing event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingcount IS 'Total count of contacting events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimesum IS 'Total duration of contacting events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimesumDay IS 'Total duration of contacting events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimemax IS 'Maximum duration of a single contacting event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimemaxDay IS 'Maximum duration of a single contacting event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimemin IS 'Minimum duration of a single contacting event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tcontactingtimeminDay IS 'Minimum duration of a single contacting event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailcount IS 'Total count of voicemail events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimesum IS 'Total duration of voicemail events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimesumDay IS 'Total duration of voicemail events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimemax IS 'Maximum duration of a single voicemail event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimemaxDay IS 'Maximum duration of a single voicemail event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimemin IS 'Minimum duration of a single voicemail event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tvoicemailtimeminDay IS 'Minimum duration of a single voicemail event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowoutcount IS 'Total count of flow out events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimesum IS 'Total duration of flow out events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimesumDay IS 'Total duration of flow out events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimemax IS 'Maximum duration of a single flow out event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimemaxDay IS 'Maximum duration of a single flow out event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimemin IS 'Minimum duration of a single flow out event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tflowouttimeminDay IS 'Minimum duration of a single flow out event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaitcount IS 'Total count of wait events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimesum IS 'Total duration of wait events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimesumDay IS 'Total duration of wait events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimemax IS 'Maximum duration of a single wait event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimemaxDay IS 'Maximum duration of a single wait event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimemin IS 'Minimum duration of a single wait event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.twaittimeminDay IS 'Minimum duration of a single wait event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandoncount IS 'Total count of abandon events';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimesum IS 'Total duration of abandon events (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimesumDay IS 'Total duration of abandon events (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimemax IS 'Maximum duration of a single abandon event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimemaxDay IS 'Maximum duration of a single abandon event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimemin IS 'Minimum duration of a single abandon event (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.tabandontimeminDay IS 'Minimum duration of a single abandon event (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.servicelevelnumerator IS 'Numerator for calculating service level';
COMMENT ON COLUMN vwQueueInteractionDataDaily.serviceleveldenominator IS 'Denominator for calculating service level';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1count IS 'Total count of custom average events 1';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timesum IS 'Total duration of custom average events 1 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timesumDay IS 'Total duration of custom average events 1 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timemax IS 'Maximum duration of a single custom average event 1 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timemaxDay IS 'Maximum duration of a single custom average event 1 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timemin IS 'Minimum duration of a single custom average event 1 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av1timeminDay IS 'Minimum duration of a single custom average event 1 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2count IS 'Total count of custom average events 2';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timesum IS 'Total duration of custom average events 2 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timesumDay IS 'Total duration of custom average events 2 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timemax IS 'Maximum duration of a single custom average event 2 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timemaxDay IS 'Maximum duration of a single custom average event 2 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timemin IS 'Minimum duration of a single custom average event 2 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av2timeminDay IS 'Minimum duration of a single custom average event 2 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3count IS 'Total count of custom average events 3';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timesum IS 'Total duration of custom average events 3 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timesumDay IS 'Total duration of custom average events 3 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timemax IS 'Maximum duration of a single custom average event 3 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timemaxDay IS 'Maximum duration of a single custom average event 3 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timemin IS 'Minimum duration of a single custom average event 3 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av3timeminDay IS 'Minimum duration of a single custom average event 3 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4count IS 'Total count of custom average events 4';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timesum IS 'Total duration of custom average events 4 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timesumDay IS 'Total duration of custom average events 4 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timemax IS 'Maximum duration of a single custom average event 4 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timemaxDay IS 'Maximum duration of a single custom average event 4 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timemin IS 'Minimum duration of a single custom average event 4 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av4timeminDay IS 'Minimum duration of a single custom average event 4 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5count IS 'Total count of custom average events 5';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timesum IS 'Total duration of custom average events 5 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timesumDay IS 'Total duration of custom average events 5 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timemax IS 'Maximum duration of a single custom average event 5 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timemaxDay IS 'Maximum duration of a single custom average event 5 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timemin IS 'Minimum duration of a single custom average event 5 (in seconds)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av5timeminDay IS 'Minimum duration of a single custom average event 5 (in days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6count IS 'AV6 Count';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timesum IS 'Total AV6 Time Sum';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timesumDay IS 'Total AV6 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timemax IS 'Max AV6 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timemaxDay IS 'Max AV6 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timemin IS 'Min AV6 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av6timeminDay IS 'Min AV6 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7count IS 'AV7 Count';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timesum IS 'Total AV7 Time Sum';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timesumDay IS 'Total AV7 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timemax IS 'Max AV7 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timemaxDay IS 'Max AV7 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timemin IS 'Min AV7 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av7timeminDay IS 'Min AV7 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8count IS 'AV8 Count';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timesum IS 'Total AV8 Time Sum';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timesumDay IS 'Total AV8 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timemax IS 'Max AV8 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timemaxDay IS 'Max AV8 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timemin IS 'Min AV8 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av8timeminDay IS 'Min AV8 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9count IS 'AV9 Count';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timesum IS 'Total AV9 Time Sum';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timesumDay IS 'Total AV9 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timemax IS 'Max AV9 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timemaxDay IS 'Max AV9 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timemin IS 'Min AV9 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av9timeminDay IS 'Min AV9 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10count IS 'AV10 Count';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timesum IS 'Total AV10 Time Sum';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timesumDay IS 'Total AV10 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timemax IS 'Max AV10 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timemaxDay IS 'Max AV10 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timemin IS 'Min AV10 Time';
COMMENT ON COLUMN vwQueueInteractionDataDaily.av10timeminDay IS 'Min AV10 Time (Days)';

COMMENT ON VIEW vwQueueInteractionDataDaily IS 'Queue Interaction Data Daily Data';
