{"permissionPolicies": [{"domain": "analytics", "entityName": "botAggregate", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "conversationAggregate", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "conversationDetail", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "conversationProperties", "actionSet": ["*"], "allowConditions": false}, {"domain": "analytics", "entityName": "dataExport", "actionSet": ["add", "view", "edit", "delete"], "allowConditions": false}, {"domain": "analytics", "entityName": "evaluationAggregate", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "interactionEvaluationDetails", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "interactionSurveyDetails", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "queueObservation", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "surveyAggregate", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "userAggregate", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "userDetail", "actionSet": ["view"], "allowConditions": false}, {"domain": "analytics", "entityName": "userObservation", "actionSet": ["view"], "allowConditions": false}, {"domain": "audits", "entityName": "audit", "actionSet": ["view"], "allowConditions": false}, {"domain": "audits", "entityName": "interactionDetails", "actionSet": ["view"], "allowConditions": false}, {"domain": "authorization", "entityName": "orgT<PERSON><PERSON>", "actionSet": ["view"], "allowConditions": false}, {"domain": "authorization", "entityName": "orgTrusteeGroup", "actionSet": ["view"], "allowConditions": false}, {"domain": "authorization", "entityName": "orgTrusteeUser", "actionSet": ["view"], "allowConditions": false}, {"domain": "authorization", "entityName": "role", "actionSet": ["view", "edit"], "allowConditions": false}, {"domain": "architect", "entityName": "datatable", "actionSet": ["search", "view"], "allowConditions": false}, {"domain": "architect", "entityName": "datatableRow", "actionSet": ["view"], "allowConditions": false}, {"domain": "architect", "entityName": "dependencyTracking", "actionSet": ["view"], "allowConditions": false}, {"domain": "architect", "entityName": "flow", "actionSet": ["search", "view"], "allowConditions": false}, {"domain": "architect", "entityName": "flowOutcome", "actionSet": ["view"], "allowConditions": false}, {"domain": "architect", "entityName": "systemPrompt", "actionSet": ["view"], "allowConditions": false}, {"domain": "architect", "entityName": "ui", "actionSet": ["view"], "allowConditions": false}, {"domain": "architect", "entityName": "userPrompt", "actionSet": ["view"], "allowConditions": false}, {"domain": "assistants", "entityName": "assistant", "actionSet": ["view"], "allowConditions": false}, {"domain": "assistants", "entityName": "copilot", "actionSet": ["view"], "allowConditions": false}, {"domain": "assistants", "entityName": "queue", "actionSet": ["view"], "allowConditions": false}, {"domain": "billing", "entityName": "enterpriseAgreement", "actionSet": ["view"], "allowConditions": false}, {"domain": "billing", "entityName": "subscription", "actionSet": ["view"], "allowConditions": false}, {"domain": "coaching", "entityName": "appointment", "actionSet": ["participate"], "allowConditions": false}, {"domain": "conversation", "entityName": "communication", "actionSet": ["view"], "allowConditions": false}, {"domain": "conversation", "entityName": "message", "actionSet": ["view"], "allowConditions": false}, {"domain": "directory", "entityName": "user", "actionSet": ["view"], "allowConditions": false}, {"domain": "directory", "entityName": "userProfile", "actionSet": ["view"], "allowConditions": false}, {"domain": "externalContacts", "entityName": "conversation", "actionSet": ["viewAll"], "allowConditions": false}, {"domain": "groups", "entityName": "team", "actionSet": ["view"], "allowConditions": false}, {"domain": "knowledge", "entityName": "category", "actionSet": ["view"], "allowConditions": false}, {"domain": "knowledge", "entityName": "document", "actionSet": ["view"], "allowConditions": false}, {"domain": "knowledge", "entityName": "knowledgebase", "actionSet": ["view"], "allowConditions": false}, {"domain": "learning", "entityName": "assignment", "actionSet": ["view", "participate"], "allowConditions": false}, {"domain": "learning", "entityName": "module", "actionSet": ["view"], "allowConditions": false}, {"domain": "o<PERSON>h", "entityName": "client", "actionSet": ["view"], "allowConditions": false}, {"domain": "outbound", "entityName": "campaign", "actionSet": ["search", "view"], "allowConditions": false}, {"domain": "outbound", "entityName": "campaignSequence", "actionSet": ["view"], "allowConditions": false}, {"domain": "outbound", "entityName": "contact", "actionSet": ["view"], "allowConditions": false}, {"domain": "outbound", "entityName": "contactList", "actionSet": ["search", "view"], "allowConditions": false}, {"domain": "outbound", "entityName": "responseSet", "actionSet": ["view"], "allowConditions": false}, {"domain": "outbound", "entityName": "wrapUpCodeMapping", "actionSet": ["view"], "allowConditions": false}, {"domain": "quality", "entityName": "calibration", "actionSet": ["view"], "allowConditions": false}, {"domain": "quality", "entityName": "evaluation", "actionSet": ["view"], "allowConditions": false}, {"domain": "quality", "entityName": "evaluationForm", "actionSet": ["view"], "allowConditions": false}, {"domain": "recording", "entityName": "annotation", "actionSet": ["view"], "allowConditions": false}, {"domain": "recording", "entityName": "<PERSON><PERSON><PERSON>", "actionSet": ["view"], "allowConditions": false}, {"domain": "recording", "entityName": "orphanRecording", "actionSet": ["view"], "allowConditions": false}, {"domain": "recording", "entityName": "recording", "actionSet": ["view", "access"], "allowConditions": false}, {"domain": "reporting", "entityName": "quality", "actionSet": ["view"], "allowConditions": false}, {"domain": "routing", "entityName": "queue", "actionSet": ["search", "view"], "allowConditions": false}, {"domain": "routing", "entityName": "skill", "actionSet": ["view"], "allowConditions": false}, {"domain": "routing", "entityName": "wrapupCode", "actionSet": ["view"], "allowConditions": false}, {"domain": "speechAndTextAnalytics", "entityName": "data", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "activityCode", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "adhocForecast", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "agent", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "agentAdherence", "actionSet": ["notify"], "allowConditions": false}, {"domain": "wfm", "entityName": "agentSchedule", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "agentScheduleNotification", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "businessUnit", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "historicalAdherence", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "intraday", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "managementUnit", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "planningGroup", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "publishedSchedule", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "realtimeAdherence", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "schedule", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "serviceGoalGroup", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "serviceGoalTemplate", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "shiftTradeRequest", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "shortTermForecast", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "shrinkage", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "timeOffRequest", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "workPlan", "actionSet": ["view"], "allowConditions": false}, {"domain": "wfm", "entityName": "workPlanRotation", "actionSet": ["view"], "allowConditions": false}]}