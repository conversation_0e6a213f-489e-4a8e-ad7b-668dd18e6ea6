DROP VIEW IF EXISTS vwUserPresenceData CASCADE;
CREATE OR REPLACE VIEW vwUserPresenceData AS
SELECT
    pd.id,
    pd.userid,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername,
    ud.divisionid as divisionid,
    dd.name as divisionname,
    pd.startdate,
    pd.startdateltc,
    pd.timetype AS timetype,
    pd.systempresenceid,
    pd.presenceid,
    case
        pd.timetype
        when 'Presence' then pd.presencetime
        else 0
    end AS presencetime,
    pd.presencetime / 86400.00 as presencetimeDay,
    pd.routingid,
    case
        pd.timetype
        when 'Routing' then pd.presencetime
        else 0
    end AS routingtime,
    pd.routingtime / 86400.00 as routingtimeDay,
    pd.updated
FROM
    userPresenceData pd
    left outer join vwUserDetail ud on ud.id = pd.userid
    left outer join divisiondetails dd on dd.id = ud.divisionid;
