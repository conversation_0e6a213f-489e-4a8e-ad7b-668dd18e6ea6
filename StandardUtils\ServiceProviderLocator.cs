using System;

namespace StandardUtils
{
    /// <summary>
    /// Provides access to the current service provider
    /// </summary>
    public static class ServiceProviderLocator
    {
        private static IServiceProvider? _serviceProvider;

        /// <summary>
        /// Gets the current service provider
        /// </summary>
        public static IServiceProvider? Current => _serviceProvider;

        /// <summary>
        /// Sets the current service provider
        /// </summary>
        /// <param name="serviceProvider">The service provider to set</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }
    }
}
