IF dbo.csg_table_exists('knowledgebasedocument') = 0
CREATE TABLE [knowledgebasedocument] (
    [id] VARCHAR(50)  NOT NULL,
    [title] VARCHAR(255)  NULL,
    [visible] [BIT] NULL,
    [state] VARCHAR(50)  NULL,
    [dateCreated] [DATETIME] NULL,
    [dateModified] [DATETIME] NULL,
    [dateImported] [DATETIME] NULL,
    [datePublished] [DATETIME] NULL,
    [lastPublishedVersionNumber] [INT] NULL,
    documentVersion VARCHAR(50) NULL,
    [externalId] [INT] NULL,
    [categoryId] VARCHAR(50) NULL,
    [knowledgeBaseId] varchar(50) NULL,
    [readonly] [BIT] NULL,
    [updated] [datetime],
    CONSTRAINT [knowledgebasedocument_pkey] PRIMARY KEY ([id])
);
