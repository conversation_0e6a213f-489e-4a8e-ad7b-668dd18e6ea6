using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using GenesysCloudUtils;

namespace GenesysAdapter.UnitTests
{
    public class VoiceAnalysis_Tests
    {
        private readonly Mock<ILogger<VoiceAnalysis>> _loggerMock;
        private readonly VoiceAnalysis _voiceAnalysis;

        public VoiceAnalysis_Tests()
        {
            _loggerMock = new Mock<ILogger<VoiceAnalysis>>();
            _voiceAnalysis = new VoiceAnalysis(_loggerMock.Object);
        }

        [Fact]
        public async Task VerifyQueueAsync_ReturnsTrueForValidQueue()
        {
            // Act
            bool result = await _voiceAnalysis.VerifyQueueAsync("test-queue-id");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetTranscriptUrlAsync_ReturnsNullWhenHttpClientFails()
        {
            // Act
            string result = await _voiceAnalysis.GetTranscriptUrlAsync("test-conversation-id", "test-peer-id");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task DownloadTranscriptAsync_ReturnsNullWhenHttpClientFails()
        {
            // Act
            string result = await _voiceAnalysis.DownloadTranscriptAsync("https://invalid-url");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task IngestTranscriptAsync_ReturnsTrueForValidTranscript()
        {
            // Act
            bool result = await _voiceAnalysis.IngestTranscriptAsync("test-transcript-json");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task QueueVerificationCache_ReducesApiCalls()
        {
            // Arrange
            Dictionary<string, bool> queueVerificationCache = new Dictionary<string, bool>();
            DataTable overviewTable = new DataTable();
            overviewTable.Columns.Add("conversationid", typeof(string));
            overviewTable.Columns.Add("queueid", typeof(string));

            // Add rows with the same queue ID to simulate multiple conversations using the same queue
            string queueId = "test-queue-id";
            for (int i = 1; i <= 5; i++)
            {
                DataRow row = overviewTable.NewRow();
                row["conversationid"] = $"conversation-{i}";
                row["queueid"] = queueId;
                overviewTable.Rows.Add(row);
            }

            // Act
            int apiCallCount = 0;
            foreach (DataRow row in overviewTable.Rows)
            {
                // We don't need the conversation ID for this test, just the queue ID
                string currentQueueId = row["queueid"].ToString();

                // Check if we've already verified this queue
                if (!queueVerificationCache.TryGetValue(currentQueueId, out _))
                {
                    // If not in cache, perform the verification
                    bool queueVerified = await _voiceAnalysis.VerifyQueueAsync(currentQueueId);
                    apiCallCount++;

                    // Cache the result for future use
                    queueVerificationCache[currentQueueId] = queueVerified;
                }
            }

            // Assert
            Assert.Equal(1, apiCallCount); // Only one API call should be made
            Assert.Equal(5, overviewTable.Rows.Count); // But we processed 5 conversations
        }
    }
}
