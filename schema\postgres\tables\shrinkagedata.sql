CREATE TABLE IF NOT EXISTS shrinkagedata (
    keyid varchar(100) NOT NULL,
    id varchar(50),
    startdate timestamp without time zone,
    enddate timestamp without time zone,
    activitycategory varchar(255),
    scheduledShrinkageSeconds numeric(20, 2),
    scheduledShrinkagePercent numeric(20, 2),
    actualShrinkageSeconds numeric(20, 2),
    actualShrinkagePercent numeric(20, 2),
    paidShrinkageSeconds numeric(20, 2),
    unpaidShrinkageSeconds numeric(20, 2),
    plannedShrinkageSeconds numeric(20, 2),
    unplannedShrinkageSeconds numeric(20, 2),
    updated timestamp without time zone,
    businessUnitIds varchar(100),
    CONSTRAINT shrinkagedata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;
