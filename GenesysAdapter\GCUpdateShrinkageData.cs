﻿using System.Data;
using GCData;
using StandardUtils;
using System.Globalization;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace GenesysAdapter
{
    #nullable enable
    class GCUpdateShrinkageData
    {

        private readonly ILogger? _logger;

        public GCUpdateShrinkageData(ILogger logger)
        {
            _logger = logger;
        }
        public Boolean UpdateGCShrinkageData()
        {
            Boolean Successful = false;

            string SyncType = "shrinkagedata";
            DateTime Start = DateTime.Now;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable ShrinkageData = GCData.ShrinkageData();

            Successful = DBAdapter.WriteSQLData(ShrinkageData, SyncType);

            Console.WriteLine("Update Date is : {0}", GCData.ShrinkageLastUpdate);

            if (Successful == true)
            {
                Console.WriteLine("Updating Max Update Date {0}", GCData.ShrinkageLastUpdate);
                Successful = GCData.UpdateLastSuccessDate(GCData.ShrinkageLastUpdate, SyncType);
            }
            else
            {
                Console.WriteLine("Will Not update the last update date - failure in processing");
            }

            Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);

            return true;
        }

    }
}
