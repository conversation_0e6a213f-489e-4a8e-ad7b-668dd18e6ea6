﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlTypes;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Interactions = GenesysCloudDefInteractions;
using Newtonsoft.Json;
using StandardUtils;
using UserReal = GenesysCloudDefUserRealtime;

namespace GenesysCloudUtils
{
    public class UserObsData
    {
        public string CustomerKeyID { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private JsonRestChilkat GCUtilities = new JsonRestChilkat();
        //private JsonUtils JsonActions = new JsonUtils();
        public DateTime LastAPIKeyGet { get; set; }
        public DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();


        public void Initialize()
        {
            DBUtil.Initialize();
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        }

        public void getUserStatus(ref DataTable Users)
        {
            string[] userStates = { "active", "inactive" };
            JsonUtils JsonActions = new JsonUtils();

            Users.AcceptChanges();

            //DataTable Users = CreateUsersTable();
// TODO Fix user call to handle pages - hardcoded to 1 page
            string JsonString = GCUtilities.ReturnJson("/api/v2/users?state=active&pageSize=500&pageNumber=1&expand=presence%2CroutingStatus%2Cgeolocation%2CconversationSummary&sortOrder=asc");

            UserReal.UserRealTime UserData = new UserReal.UserRealTime();

            UserData = JsonConvert.DeserializeObject<UserReal.UserRealTime>(JsonString,
                   new JsonSerializerSettings
                   {
                       NullValueHandling = NullValueHandling.Ignore
                   });

            JsonActions.MaxPages = UserData.pageCount;
            int UserCounter = 1;
            foreach (UserReal.Entity JSON in UserData.entities)
            {
                if (UserCounter % 100 == 0)
                    Console.Write("#");

                DataRow UserRow = Users.Select("id='" + JSON.id + "'").FirstOrDefault();


                if (UserRow != null)
                    UserRow.AcceptChanges();
                else
                {
                    UserRow = Users.NewRow();
                    Console.Write("+");
                }


                JSON.routingStatus.startTime = new DateTime(
                                  JSON.routingStatus.startTime.Ticks - (JSON.routingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                  JSON.routingStatus.startTime.Kind
                                );
                if (JSON.routingStatus.startTime.Year < 2000)
                    JSON.routingStatus.startTime = DateTime.Parse("2000-01-01");

                if (UserRow.RowState == DataRowState.Detached || ((string)UserRow["routingstatus"] != JSON.routingStatus.status
                         || (string)UserRow["systempresence"] != JSON.presence.presenceDefinition.systemPresence
                         || (string)UserRow["presenceid"] != JSON.presence.presenceDefinition.id
                         || (DateTime)UserRow["routstarttime"] != JSON.routingStatus.startTime)
                   )
                {
                    // Using standardized null value handling with ternary operator pattern
                    UserRow["id"] = JSON.id != null ? JSON.id : DBNull.Value;
                    UserRow["name"] = JSON.name != null ? JSON.name : DBNull.Value;
                    UserRow["email"] = JSON.email != null ? JSON.email : DBNull.Value;
                    UserRow["jabberId"] = JSON.chat?.jabberId != null ? JSON.chat.jabberId : DBNull.Value;
                    UserRow["state"] = JSON.state != null ? JSON.state : DBNull.Value;
                    UserRow["title"] = JSON.title != null ? JSON.title : DBNull.Value;
                    UserRow["username"] = JSON.username != null ? JSON.username : DBNull.Value;
                    UserRow["department"] = JSON.department != null ? JSON.department : DBNull.Value;
                    UserRow["routingstatus"] = JSON.routingStatus?.status != null ? JSON.routingStatus.status : DBNull.Value;
                    UserRow["routstarttime"] = JSON.routingStatus?.startTime != DateTime.MinValue ? JSON.routingStatus.startTime : DBNull.Value;
                    UserRow["systempresence"] = JSON.presence?.presenceDefinition?.systemPresence != null ? JSON.presence.presenceDefinition.systemPresence : DBNull.Value;
                    UserRow["presenceid"] = JSON.presence?.presenceDefinition?.id != null ? JSON.presence.presenceDefinition.id : DBNull.Value;
                    UserRow["presstarttime"] = JSON.presence?.modifiedDate != DateTime.MinValue ? JSON.presence.modifiedDate : DBNull.Value;
                    UserRow["cccallactive"] = JSON.conversationSummary?.call?.contactCenter != null ? JSON.conversationSummary.call.contactCenter.active : DBNull.Value;
                    UserRow["cccallacw"] = JSON.conversationSummary?.call?.contactCenter != null ? JSON.conversationSummary.call.contactCenter.acw : DBNull.Value;
                    // Using standardized null value handling with ternary operator pattern for all properties
                    UserRow["othcallactive"] = JSON.conversationSummary?.call?.enterprise != null ? JSON.conversationSummary.call.enterprise.active : DBNull.Value;
                    UserRow["cbcallactive"] = JSON.conversationSummary?.callback?.contactCenter != null ? JSON.conversationSummary.callback.contactCenter.active : DBNull.Value;
                    UserRow["cbcallacw"] = JSON.conversationSummary?.callback?.contactCenter != null ? JSON.conversationSummary.callback.contactCenter.acw : DBNull.Value;
                    UserRow["cbothcallactive"] = JSON.conversationSummary?.callback?.enterprise != null ? JSON.conversationSummary.callback.enterprise.active : DBNull.Value;
                    UserRow["ccemailactive"] = JSON.conversationSummary?.email?.contactCenter != null ? JSON.conversationSummary.email.contactCenter.active : DBNull.Value;
                    UserRow["ccemailacw"] = JSON.conversationSummary?.email?.contactCenter != null ? JSON.conversationSummary.email.contactCenter.acw : DBNull.Value;
                    UserRow["othemailactive"] = JSON.conversationSummary?.email?.enterprise != null ? JSON.conversationSummary.email.enterprise.active : DBNull.Value;
                    UserRow["ccchatactive"] = JSON.conversationSummary?.chat?.contactCenter != null ? JSON.conversationSummary.chat.contactCenter.active : DBNull.Value;
                    UserRow["ccchatacw"] = JSON.conversationSummary?.chat?.contactCenter != null ? JSON.conversationSummary.chat.contactCenter.acw : DBNull.Value;
                    UserRow["othchatactive"] = JSON.conversationSummary?.chat?.enterprise != null ? JSON.conversationSummary.chat.enterprise.active : DBNull.Value;
                }



                if (UserRow.RowState == DataRowState.Detached)
                {
                    Users.Rows.Add(UserRow);
                }

                ++UserCounter;
            }

            Console.WriteLine("\nWe Have Returned {0} Row(s)", UserCounter);

        }

        // Removed unused CreateUsersTable method
    }
}
// spell-checker: ignore: crouting, cgeolocation, cconversation
