IF dbo.csg_table_exists('presenceDetails') = 0
CREATE TABLE [presenceDetails](
    [id] [nvarchar](50) NOT NULL,
    [systempresence] [nvarchar](255),
    [orgpresence] [nvarchar](255),
    [deactivated] [bit],
    [type] [nvarchar](50),
    [divisionid] [nvarchar](50),
    [updated] [datetime],
    PRIMARY KEY ([id])
);

IF dbo.csg_column_exists('presenceDetails', 'type') = 0
    ALTER TABLE dbo.presenceDetails ADD type nvarchar(50);
ELSE
    ALTER TABLE presenceDetails ALTER COLUMN type nvarchar(50);

IF dbo.csg_column_exists('presenceDetails', 'divisionid') = 0
    ALTER TABLE dbo.presenceDetails ADD divisionid nvarchar(50);
ELSE
    ALTER TABLE presenceDetails ALTER COLUMN divisionid nvarchar(50);

IF dbo.csg_column_exists('presenceDetails', 'primaryfield') = 1
    ALTER TABLE dbo.presenceDetails DROP COLUMN primaryfield;
