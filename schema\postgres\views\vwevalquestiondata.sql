CREATE
OR REPLACE VIEW vwEvalquestiondata AS
SELECT
    DISTINCT eq.evaluationid,
    eq.questionid,
    eq.answerid,
    eq.score,
    ed.questionanswervalue,
    eq.markedna,
    eq.failedkillquestions,
    eq.comments,
    ed.evaluationformid,
    ed.evaluationname,
    ed.questiongroupid,
    ed.questiongroupname,
    ed.questiontext,
    ed.questionhelptext,
    ed.quesiontype,
    ed.questionanwserid,
    ed.questionanswertext,
    cd.divisionid
FROM
    evalquestiondata eq
    LEFT OUTER JOIN evaldetails ed on ed.evaluationformid = eq.evaluationformid
    AND ed.questiongroupid = eq.questiongroupid
    AND ed.questionid = eq.questionid
    AND ed.questionanwserid = eq.answerid
    LEFT OUTER JOIN evaldata eda on eda.evaluationid = eq.evaluationid
    LEFT OUTER JOIN convsummarydata cd on cd.conversationid = eda.conversationid
;

COMMENT ON COLUMN vwEvalquestiondata.evaluationid IS 'Evaluation GUID';
COMMENT ON COLUMN vwEvalquestiondata.questionid IS 'Question GUID';
COMMENT ON COLUMN vwEvalquestiondata.answerid IS 'Answer GUID';
COMMENT ON COLUMN vwEvalquestiondata.score IS 'Score';
COMMENT ON COLUMN vwEvalquestiondata.questionanswervalue IS 'Question Answer Value';
COMMENT ON COLUMN vwEvalquestiondata.markedna IS 'Marked Not Applicable';
COMMENT ON COLUMN vwEvalquestiondata.failedkillquestions IS 'Failed Kill Questions';
COMMENT ON COLUMN vwEvalquestiondata.comments IS 'Comments';
COMMENT ON COLUMN vwEvalquestiondata.evaluationformid IS 'Evaluation Form GUID';
COMMENT ON COLUMN vwEvalquestiondata.evaluationname IS 'Evaluation Name';
COMMENT ON COLUMN vwEvalquestiondata.questiongroupid IS 'Question Group GUID';
COMMENT ON COLUMN vwEvalquestiondata.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN vwEvalquestiondata.questiontext IS 'Question Text';
COMMENT ON COLUMN vwEvalquestiondata.questionhelptext IS 'Question Help Text';
COMMENT ON COLUMN vwEvalquestiondata.quesiontype IS 'Question Type';
COMMENT ON COLUMN vwEvalquestiondata.questionanwserid IS 'Question Answer GUID';
COMMENT ON COLUMN vwEvalquestiondata.questionanswertext IS 'Question Answer Text';
COMMENT ON COLUMN vwEvalquestiondata.divisionid IS 'Division GUID';

COMMENT ON VIEW vwEvalquestiondata IS 'See EvalQuestionData: View for evaluation question data with additional details';
