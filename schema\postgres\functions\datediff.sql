CREATE OR REPLACE FUNCTION datediff(
    units varchar,
    start_t timestamp without time zone,
    end_t timestamp without time zone
) RETURNS integer LANGUAGE plpgsql COST 100 VOLATILE PARALLEL UNSAFE
AS $function$
   DECLARE
     diff_interval INTERVAL; 
     diff INT = 0;
     years_diff INT = 0;
   BEGIN
     -- Handling non-finite and NULL timestamp values
     IF start_t IS NULL OR end_t IS NULL OR 
        start_t = 'infinity' OR start_t = '-infinity' OR 
        end_t = 'infinity' OR end_t = '-infinity' THEN
       RETURN 0; -- Return 0 or consider another default behavior
     END IF;

     -- Handle year and month differences for specific units
     IF units IN ('yy', 'yyyy', 'year', 'mm', 'm', 'month') THEN
       years_diff = DATE_PART('year', end_t) - DATE_PART('year', start_t);
 
       IF units IN ('yy', 'yyyy', 'year') THEN
         -- Difference between year parts, not counting incomplete years
         RETURN years_diff;
       ELSE
         -- Monthly difference, adjusts for years and checks month cross-over
         RETURN years_diff * 12 + (DATE_PART('month', end_t) - DATE_PART('month', start_t)); 
       END IF;
     END IF;

     -- Calculating difference in smaller units
     diff_interval = end_t - start_t;
     diff = diff + DATE_PART('day', diff_interval);
 
     IF units IN ('wk', 'ww', 'week') THEN
       diff = diff / 7;
       RETURN diff;
     END IF;

     IF units IN ('dd', 'd', 'day') THEN
       RETURN diff;
     END IF;

     diff = diff * 24 + DATE_PART('hour', diff_interval); 
 
     IF units IN ('hh', 'hour') THEN
        RETURN diff;
     END IF;
 
     diff = diff * 60 + DATE_PART('minute', diff_interval);
 
     IF units IN ('mi', 'n', 'minute') THEN
        RETURN diff;
     END IF;
 
     diff = diff * 60 + DATE_PART('second', diff_interval);
 
     RETURN diff;
   END;
$function$
;