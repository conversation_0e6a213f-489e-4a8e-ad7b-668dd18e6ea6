CREATE
OR REPLACE VIEW vwadherenceactData AS
SELECT
    ad.userid,
    ad.startdate,
    ad.enddate,
    ad.startdateltc,
    ad.enddateltc,
    ad.durationsecs,
    ad.durationsecs / 86400.00 AS durationsecsday,
    ad.actualActivityCategory,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername
FROM
    adherenceactData AS ad
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = ad.userid;

COMMENT ON COLUMN vwadherenceactData.actualActivityCategory IS 'Actual Activity Category'; 
COMMENT ON COLUMN vwadherenceactData.agentname IS 'Agent Name'; 
COMMENT ON COLUMN vwadherenceactData.durationsecs IS 'Duration in Sec(s)'; 
COMMENT ON COLUMN vwadherenceactData.durationsecsday IS 'Durations seconds'; 
COMMENT ON COLUMN vwadherenceactData.enddate IS 'End Time (UTC)'; 
COMMENT ON COLUMN vwadherenceactData.enddateltc IS 'End Time (LTC)'; 
COMMENT ON COLUMN vwadherenceactData.managerid IS 'Manager GUID'; 
COMMENT ON COLUMN vwadherenceactData.managername IS 'Manager Name'; 
COMMENT ON COLUMN vwadherenceactData.startdate IS 'Start Date (UTC)'; 
COMMENT ON COLUMN vwadherenceactData.startdateltc IS 'Start Date (LTC)'; 
COMMENT ON COLUMN vwadherenceactData.userid IS 'User GUID'; 
COMMENT ON VIEW vwadherenceactData IS 'See AdherenceActData - Expands all the GUIDs with their lookups';