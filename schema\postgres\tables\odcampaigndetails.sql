CREATE TABLE IF NOT EXISTS odcampaigndetails (
    id varchar(50),
    name varchar(200),
    datecreated timestamp without time zone,
    datecreatedltc timestamp without time zone,
    datemodified timestamp without time zone,
    datemodifiedltc timestamp without time zone,
    version integer,
    automatictimezonemapping bit,
    division varchar(50),
    divisionname varchar(200),
    contactlist varchar(50),
    contactlistname varchar(200),
    script varchar(50),
    scriptname varchar(200),
    queue varchar(50),
    dialingmode varchar(50),
    campaignstatus varchar(50),
    abandonrate numeric(20, 2),
    callanalysisresponseset varchar(50),
    callanalysisresponsesetname varchar(200),
    callername varchar(200),
    calleraddress varchar(200),
    outboundlinecount int,
    skippreviewdisabled bit,
    previewtimeoutseconds numeric(20, 2),
    singlenumberpreview bit,
    alwaysrunning bit,
    noanswertimeout decimal(20, 2),
    priority int,
    updated timestamp without time zone,
    PRIMARY KEY (id)
);

ALTER TABLE odcampaigndetails
ADD column IF NOT exists datemodified timestamp without time zone;

ALTER TABLE odcampaigndetails
ADD column IF NOT exists datemodifiedltc timestamp without time zone;

COMMENT ON COLUMN odcampaigndetails.id IS 'Primary Key / Campaign GUID'; 
COMMENT ON COLUMN odcampaigndetails.abandonrate IS 'Campaign Abandon Rate'; 
COMMENT ON COLUMN odcampaigndetails.alwaysrunning IS 'Campaign Always Running'; 
COMMENT ON COLUMN odcampaigndetails.callanalysisresponseset IS 'Campaign No Answer Time Out'; 
COMMENT ON COLUMN odcampaigndetails.callanalysisresponsesetname IS 'Campaign Call Analysis Response Set Call Analysis'; 
COMMENT ON COLUMN odcampaigndetails.calleraddress IS 'Campaign Call Analysis Response Set Caller Address'; 
COMMENT ON COLUMN odcampaigndetails.callername IS 'Campaign Call Analysis Response Set Caller Name'; 
COMMENT ON COLUMN odcampaigndetails.campaignstatus IS 'Campaign Status'; 
COMMENT ON COLUMN odcampaigndetails.contactlistname IS 'Campaign Contact List Name'; 
COMMENT ON COLUMN odcampaigndetails.datecreated IS 'Campaign Date Created (UTC)'; 
COMMENT ON COLUMN odcampaigndetails.datecreatedltc IS 'Campaign Date Created (LTC)'; 
COMMENT ON COLUMN odcampaigndetails.datemodified IS 'Campaign Date Modified (UTC)'; 
COMMENT ON COLUMN odcampaigndetails.datemodifiedltc IS 'Campaign Date Modified (LTC)'; 
COMMENT ON COLUMN odcampaigndetails.dialingmode IS 'Campaign Dialling Mode GUID'; 
COMMENT ON COLUMN odcampaigndetails.division IS 'Campaign Division GUID'; 
COMMENT ON COLUMN odcampaigndetails.divisionname IS 'Campaign Division Name'; 
COMMENT ON COLUMN odcampaigndetails.name IS 'Campaign Name'; 
COMMENT ON COLUMN odcampaigndetails.noanswertimeout IS 'Campaign Call Analysis Response Set'; 
COMMENT ON COLUMN odcampaigndetails.outboundlinecount IS 'Campaign Name'; 
COMMENT ON COLUMN odcampaigndetails.previewtimeoutseconds IS 'Campaign Call Analysis Response Set Preview Timeout Seconds'; 
COMMENT ON COLUMN odcampaigndetails.priority IS 'Campaign Priority'; 
COMMENT ON COLUMN odcampaigndetails.queue IS 'Campaign Queue GUID'; 
COMMENT ON COLUMN odcampaigndetails.script IS 'Campaign Script GUID'; 
COMMENT ON COLUMN odcampaigndetails.scriptname IS 'Campaign Script Name'; 
COMMENT ON COLUMN odcampaigndetails.singlenumberpreview IS 'Campaign Call Analysis Response Set Single Number Preview'; 
COMMENT ON COLUMN odcampaigndetails.skippreviewdisabled IS 'Campaign Call Analysis Response Set Skip Preview Disabled'; 
COMMENT ON COLUMN odcampaigndetails.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN odcampaigndetails.version IS 'Campaign Version'; 
COMMENT ON TABLE odcampaigndetails IS 'Outbound Dialling Campaign Lookup Data'; 