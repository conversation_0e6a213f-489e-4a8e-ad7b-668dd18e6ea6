CREATE TABLE IF NOT EXISTS userdetails (
    id varchar(50) NOT NULL,
    name varchar(200),
    jabberid varchar(100),
    state varchar(50),
    title varchar(200),
    email varchar(200),
    username varchar(200),
    department varchar(200),
    manager varchar(50),
    divisionid varchar(50),
    employeeid varchar(50),
    dateHire varchar(50),
    updated timestamp without time zone,
    CONSTRAINT userdetails_pkey PRIMARY KEY (id)
);

ALTER TABLE userdetails 
ADD column IF NOT exists  employeeid varchar(50);

ALTER TABLE userdetails 
ADD column IF NOT exists  dateHire varchar(50);