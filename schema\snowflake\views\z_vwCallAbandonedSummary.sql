CREATE OR REPLACE VIEW z_vwCallAbandonedSummary AS
SELECT
    det.conversationid,
    det.conversationstartdate,
    det.conversationstartdateltc,
    det.conversationenddate,
    det.conversationenddateltc,
    det.segmentstartdate,
    det.segmentstartdateltc,
    det.segmentenddate,
    det.segmentenddateltc,
    det.convtosegmentendtime as TotalCallTime,
    det.segmenttime as QueueTime,
    det.ani,
    det.dnis,
    det.queueid,
    que.name as queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    detailedInteractionData det
    LEFT OUTER JOIN queueDetails que ON det.queueid = que.id
WHERE
    det.segmenttype IN ('delay', 'Interact', 'alert', 'ivr')
    AND det.purpose IN ('acd', 'ivr')
    AND det.disconnectiontype = 'peer'
    AND det.conversationenddate = det.segmentenddate;
