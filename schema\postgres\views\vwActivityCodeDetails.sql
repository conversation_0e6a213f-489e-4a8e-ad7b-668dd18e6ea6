CREATE
OR REPLACE VIEW vwActivityCodeDetails AS
SELECT
    keyid,
    id,
    businessunitid,
    name,
    active,
    defaultcode,
    category,
    lengthinminutes,
    countsaspaidwork,
    countsasworktime,
    updated
FROM
    activitycodeDetails;

COMMENT ON COLUMN vwActivityCodeDetails.active IS 'Active';
COMMENT ON COLUMN vwActivityCodeDetails.businessunitid IS 'Business Unit ID';
COMMENT ON COLUMN vwActivityCodeDetails.category IS 'Category';
COMMENT ON COLUMN vwActivityCodeDetails.countsaspaidwork IS 'Counts as Paid Work';
COMMENT ON COLUMN vwActivityCodeDetails.countsasworktime IS 'Counts as Work Time';
COMMENT ON COLUMN vwActivityCodeDetails.defaultcode IS 'Default Code';
COMMENT ON COLUMN vwActivityCodeDetails.id IS 'Primary Key';
COMMENT ON COLUMN vwActivityCodeDetails.keyid IS 'Key ID';
COMMENT ON COLUMN vwActivityCodeDetails.lengthinminutes IS 'Length in Minutes';
COMMENT ON COLUMN vwActivityCodeDetails.name IS 'Name';
COMMENT ON COLUMN vwActivityCodeDetails.updated IS 'Last Updated';

COMMENT ON VIEW vwActivityCodeDetails IS 'See activitycodes table';