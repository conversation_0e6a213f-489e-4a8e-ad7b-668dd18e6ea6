﻿using System;
using System.Data;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    public class PresenceConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public PresenceConfig(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetPresenceDataFromGC()
        {

            _logger?.LogInformation("Getting presence data");

            DataTable Presence = DBUtil.CreateInMemTable("presenceDetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            try
            {
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/presence/definitions", GCApiKey);

                PresenceObject PresenceData = new PresenceObject();

                PresenceData = JsonConvert.DeserializeObject<PresenceObject>(JsonString,
                                new JsonSerializerSettings
                                {
                                    NullValueHandling = NullValueHandling.Ignore
                                });

                foreach (Entity JSON in PresenceData.entities)
                {

                    DataRow PresenceRow = Presence.NewRow();

                    PresenceRow["id"] = JSON.id;
                    PresenceRow["type"] = JSON.type;
                    PresenceRow["systemPresence"] = JSON.systemPresence;
                    PresenceRow["updated"] = DateTime.UtcNow;
                    PresenceRow["deactivated"] = JSON.deactivated;
                    PresenceRow["divisionId"] = JSON.divisionId;

                    if (JSON.type == "User")
                    {
                        PresenceRow["orgPresence"] = JSON.languageLabels.en_US;
                    }
                    else
                    {
                        PresenceRow["orgPresence"] = JSON.systemPresence;
                    }

                    Presence.Rows.Add(PresenceRow);

                }
            }
            catch(Exception ex)
            {
                Environment.ExitCode = -15000;
            }

            _logger?.LogInformation("Total presence codes: {PresenceCodeCount}", Presence.Rows.Count);

            return Presence;

        }
    }

    public class PresenceObject
    {
        public Entity[] entities { get; set; }
        public int total { get; set; }
        public string selfUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public Languagelabels languageLabels { get; set; }
        public string systemPresence { get; set; }
        public string divisionId { get; set; }
        public bool deactivated { get; set; }
        public string selfUri { get; set; }
    }

    public class Languagelabels
    {
        public string en_US { get; set; }
        public string en { get; set; }
    }
}
// spell-checker: ignore: createdby