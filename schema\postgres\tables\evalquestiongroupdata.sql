CREATE TABLE IF NOT EXISTS evalquestiongroupdata (
    keyid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    evaluationformid varchar(50) NOT NULL,
    questiongroupid varchar(50),
    totalscore numeric(20, 2),
    maxtotalscore numeric(20, 2),
    markedna bit(1),
    totalcriticalscore numeric(20, 2),
    maxtotalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    maxtotalnoncriticalscore numeric(20, 2),
    totalscoreunweighted numeric(20, 2),
    maxtotalscoreunweighted numeric(20, 2),
    failedkillquestions bit(1),
    comments text,
    updated timestamp without time zone,
    CONSTRAINT evalquestiongroupdata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evaluationgroupdataevaluationid ON evalquestiongroupdata USING btree (
    evaluationid ASC NULLS LAST
) TABLESPACE pg_default;

COMMENT ON COLUMN evalQuestionGroupData.comments IS 'Evaluation Question Group Comments'; 
COMMENT ON COLUMN evalQuestionGroupData.evaluationformid IS 'Evaluation Form GUID'; 
COMMENT ON COLUMN evalQuestionGroupData.evaluationid IS 'Evaluation GUID'; 
COMMENT ON COLUMN evalQuestionGroupData.failedkillquestions IS 'Evaluation Question Group Falled Kill Questions'; 
COMMENT ON COLUMN evalQuestionGroupData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN evalQuestionGroupData.markedna IS 'Evaluation Question Group Marked Not Applicable (True/False)'; 
COMMENT ON COLUMN evalQuestionGroupData.maxtotalcriticalscore IS 'Evaluation Question Group Total Max Critical Score'; 
COMMENT ON COLUMN evalQuestionGroupData.maxtotalnoncriticalscore IS 'Evaluation Question GroupTotal Max Non Critical Score'; 
COMMENT ON COLUMN evalQuestionGroupData.maxtotalscore IS 'Evaluation Question Group Max Total Score'; 
COMMENT ON COLUMN evalQuestionGroupData.maxtotalscoreunweighted IS 'Evaluation Question Group Max Total Score Un-Weighted'; 
COMMENT ON COLUMN evalQuestionGroupData.questiongroupid IS 'Evaluation Question Group Comments'; 
COMMENT ON COLUMN evalQuestionGroupData.totalcriticalscore IS 'Evaluation Question Group Total Critical Score'; 
COMMENT ON COLUMN evalQuestionGroupData.totalnoncriticalscore IS 'Evaluation Question Group Total Non Critical Score'; 
COMMENT ON COLUMN evalQuestionGroupData.totalscore IS 'Evaluation Question Group Total Score'; 
COMMENT ON COLUMN evalQuestionGroupData.totalscoreunweighted IS 'Evaluation Question Group Total Score Un-Weighted'; 
COMMENT ON COLUMN evalQuestionGroupData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE evalQuestionGroupData IS 'Evaluation Question Group Detailed Data'; 