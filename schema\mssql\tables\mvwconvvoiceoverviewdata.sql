IF dbo.csg_table_exists('mvwconvvoiceoverviewdata') = 0
CREATE TABLE mvwconvvoiceoverviewdata (
    conversationid VARCHAR(50) NOT NULL PRIMARY KEY,
    sentimentscore DECIMAL(20, 2),
    sentimenttrend DECIMAL(20, 2),
    agentdurationpercentage DECIMAL(20, 2),
    customerdurationpercentage DECIMAL(20, 2),
    silencedurationpercentage DECIMAL(20, 2),
    overtalkdurationpercentage DECIMAL(20, 2),
    overtalkcount INT NULL,
    ivrdurationpercentage DECIMAL(20, 2),
    acddurationpercentage DECIMAL(20, 2),
    otherdurationpercentage DECIMAL(20, 2),
    conversationstartdate DATETIME NULL,
    conversationstartdateltc DATETIME NULL,
    conversationenddate DATETIME NULL,
    conversationenddateltc DATETIME NULL,
    ttalkcomplete INT NULL,
    ani VARCHAR(400),
    dnis VARCHAR(400),
    firstmediatype VARCHAR(50),
    divisionid VARCHAR(50),
    firstqueueid VARCHAR(50),
    firstqueuename VARCHAR(255),
    lastqueueid VARCHAR(50),
    lastqueuename VARCHAR(255),
    firstagentid VARCHAR(50),
    firstagentname VARCHAR(200),
    firstagentdept VARCHAR(200),
    firstagentmanagerid VARCHAR(50),
    firstagentmanagername VARCHAR(200),
    lastagentid VARCHAR(50),
    lastagentname VARCHAR(200),
    lastagentdept VARCHAR(200),
    lastagentmanagerid VARCHAR(50),
    lastagentmanagername VARCHAR(200),
    firstwrapupcode VARCHAR(255),
    firstwrapupname VARCHAR(255),
    lastwrapupcode VARCHAR(255),
    lastwrapupname VARCHAR(255) NULL
);


IF dbo.csg_index_exists('mvwconvvoiceoverview_conv', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_conv] ON [mvwconvvoiceoverviewdata] ([conversationid]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_agent', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_agent] ON [mvwconvvoiceoverviewdata] ([firstagentid]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_division', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_division] ON [mvwconvvoiceoverviewdata] ([divisionid]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_end', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_end] ON [mvwconvvoiceoverviewdata] ([conversationenddate]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_endltc', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_endltc] ON [mvwconvvoiceoverviewdata] ([conversationenddateltc]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_queue', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_queue] ON [mvwconvvoiceoverviewdata] ([firstqueueid]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_start', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_start] ON [mvwconvvoiceoverviewdata] ([conversationstartdate]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_startltc', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_startltc] ON [mvwconvvoiceoverviewdata] ([conversationstartdateltc]);

IF dbo.csg_index_exists('mvwconvvoiceoverview_wrapup', 'mvwconvvoiceoverviewdata') = 0
CREATE INDEX [mvwconvvoiceoverview_wrapup] ON [mvwconvvoiceoverviewdata] ([firstwrapupcode]);
