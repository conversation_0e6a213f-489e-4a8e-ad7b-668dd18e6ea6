CREATE TABLE IF NOT EXISTS evaldetails (
    id varchar(200) NOT NULL,
    evaluationid varchar(50),
    evaluationformid varchar(50),
    evaluationname varchar(200),
    questiongroupid varchar(50),
    questiongroupname varchar(200),
    questiongrouptohighest number,
    question<PERSON><PERSON>a number,
    questiongroupwieght numeric(20, 2),
    questiongroup<PERSON>wieght number,
    questionid varchar(50),
    questiontext varchar(400),
    questionhelptext text,
    quesiontype varchar(50),
    questionnaenabled number,
    questioncommentsreq number,
    questioniskill number,
    questioniscritical number,
    questionanwserid varchar(50),
    questionanswertext varchar(200),
    questionanswervalue numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT evaldetails_pkey PRIMARY KEY (id)
) ;

COMMENT ON COLUMN evaldetails.evaluationformid IS 'Evaluation Form GUID';
COMMENT ON COLUMN evaldetails.evaluationid IS 'Evaluation Form GUID (Historical column name - this actually contains the form ID, not the evaluation ID)';
COMMENT ON COLUMN evaldetails.evaluationname IS 'Evaluation Name';
COMMENT ON COLUMN evaldetails.id IS 'Primary Key';
COMMENT ON COLUMN evaldetails.quesiontype IS 'Question Type';
COMMENT ON COLUMN evaldetails.questionanswertext IS 'Question Answer Text';
COMMENT ON COLUMN evaldetails.questionanswervalue IS 'Question Value';
COMMENT ON COLUMN evaldetails.questionanwserid IS 'Question Answer GUID';
COMMENT ON COLUMN evaldetails.questioncommentsreq IS 'Question Comments Required (True/False)';
COMMENT ON COLUMN evaldetails.questiongroupmanwieght IS 'Question Group Man Weight';
COMMENT ON COLUMN evaldetails.questiongroupid IS 'Question Group GUID';
COMMENT ON COLUMN evaldetails.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN evaldetails.questiongrouptohighest IS 'Question Group To Highest (True/False)';
COMMENT ON COLUMN evaldetails.questiongrouptona IS 'Question Group Not Applicable (True/False)';
COMMENT ON COLUMN evaldetails.questiongroupwieght IS 'Question Group Wieght';
COMMENT ON COLUMN evaldetails.questionhelptext IS 'Question Help Text';
COMMENT ON COLUMN evaldetails.questionid IS 'Question Answer Value';
COMMENT ON COLUMN evaldetails.questioniskill IS 'Question Is a Kill Question (True/False)';
COMMENT ON COLUMN evaldetails.questionnaenabled IS 'Question Enabled (True/False)';
COMMENT ON COLUMN evaldetails.questiontext IS 'Question Text';
COMMENT ON COLUMN evaldetails.updated IS 'Date Row Updated (UTC)';
COMMENT ON TABLE evaldetails IS 'Evaluation Lookup Data';