CREATE TABLE IF NOT EXISTS mvwconvvoicesentimentdetaildata (
    keyid varchar(100) NOT NULL UNIQUE,
    conversationid varchar(50),
    starttime timestamp NULL,
    starttimeltc timestamp NULL,
    duration numeric(20, 2),
    participant varchar(50),
    phrase varchar(400),
    sentiment numeric(20, 2),
    phraseindex int4 NULL,
    updated timestamp NULL,
    conversationstartdate timestamp NULL,
    conversationstartdateltc timestamp NULL,
    conversationenddate timestamp NULL,
    conversationenddateltc timestamp NULL,
    ttalkcomplete int4 NULL,
    ani varchar(400),
    dnis varchar(400),
    firstmediatype varchar(50),
    divisionid varchar(50),
    divisionname varchar(255),
    firstqueueid varchar(50),
    firstqueuename varchar(255),
    lastqueueid varchar(50),
    lastqueuename varchar(255),
    firstagentid varchar(50),
    firstagentname varchar(200),
    firstagentdept varchar(200),
    firstagentmanagerid varchar(50),
    firstagentmanagername varchar(200),
    lastagentid varchar(50),
    lastagentname varchar(200),
    lastagentdept varchar(200),
    lastagentmanagerid varchar(50),
    lastagentmanagername varchar(200),
    firstwrapupcode varchar(255),
    firstwrapupname varchar(255),
    lastwrapupcode varchar(255),
    lastwrapupname varchar(255) NULL
);

ALTER TABLE mvwconvvoicesentimentdetaildata 
ADD column IF NOT exists divisionname varchar(255);

CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_agent ON mvwconvvoicesentimentdetaildata USING btree (firstagentid);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_conv ON mvwconvvoicesentimentdetaildata USING btree (conversationid);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_division ON mvwconvvoicesentimentdetaildata USING btree (divisionid);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_end ON mvwconvvoicesentimentdetaildata USING btree (conversationenddate);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_endltc ON mvwconvvoicesentimentdetaildata USING btree (conversationenddateltc);
CREATE UNIQUE INDEX IF NOT EXISTS mvconvvoicesentiment_keyid ON mvwconvvoicesentimentdetaildata USING btree (keyid);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_queue ON mvwconvvoicesentimentdetaildata USING btree (firstqueueid);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_sentiment ON mvwconvvoicesentimentdetaildata USING btree (sentiment);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_start ON mvwconvvoicesentimentdetaildata USING btree (conversationstartdate);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_startltc ON mvwconvvoicesentimentdetaildata USING btree (conversationstartdateltc);
CREATE INDEX IF NOT EXISTS mvconvvoicesentiment_wrapup ON mvwconvvoicesentimentdetaildata USING btree (firstwrapupcode);
CREATE INDEX IF NOT EXISTS mvwconvvoicesentiment_participant ON mvwconvvoicesentimentdetaildata USING btree (participant);