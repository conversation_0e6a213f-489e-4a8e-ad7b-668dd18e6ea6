DROP FUNCTION IF EXISTS csg_table_exists;
GO
CREATE FUNCTION csg_table_exists(@tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = SCHEMA_NAME()
          AND lower(table_name) = lower(@tablename)
    ) THEN 1 ELSE 0 END)
END;
GO

DROP FUNCTION IF EXISTS csg_index_exists;
GO
CREATE FUNCTION csg_index_exists(@indexname nvarchar(255), @tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.indexes
        WHERE object_id = object_id(@tablename)
            AND lower(name) = lower(@indexname)
    ) THEN 1 ELSE 0 END)
END;
GO

DROP FUNCTION IF EXISTS csg_view_definition_contains_string;
GO
CREATE FUNCTION csg_view_definition_contains_string(@view_name varchar(255), @expected_definition varchar(max))
RETURNS integer AS
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_schema = schema_name() AND lower(table_name) = lower(@view_name)
    )
    BEGIN
        -- View doesn't exist
        RETURN 2;
    END

    IF CHARINDEX(@expected_definition, OBJECT_DEFINITION(OBJECT_ID(@view_name))) > 0
    BEGIN
        -- Definition matches expectations.
        RETURN 1;
    END

    -- Definition doesn't match expectations.
    RETURN 0;
END;
GO

DROP FUNCTION IF EXISTS csg_column_exists;
GO
CREATE OR ALTER FUNCTION csg_column_exists(@tablename nvarchar(255), @columnname nvarchar(255))
RETURNS bit AS
BEGIN
   RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = SCHEMA_NAME()
        	AND LOWER(table_name) = LOWER(@tablename)
        	AND LOWER(column_name) = LOWER(@columnname)
    ) THEN 1 ELSE 0 END)
END;
GO

-- Function to check if a constraint exists
DROP FUNCTION IF EXISTS csg_constraint_exists;
GO
CREATE FUNCTION csg_constraint_exists(@constraintname nvarchar(255), @tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.objects o
        INNER JOIN sys.tables t ON o.parent_object_id = t.object_id
        WHERE o.type_desc LIKE '%CONSTRAINT'
            AND LOWER(o.name) = LOWER(@constraintname)
            AND LOWER(t.name) = LOWER(@tablename)
    ) THEN 1 ELSE 0 END)
END;
GO

-- Function to check if a stored procedure exists
DROP FUNCTION IF EXISTS csg_procedure_exists;
GO
CREATE FUNCTION csg_procedure_exists(@procedurename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.procedures
        WHERE LOWER(name) = LOWER(@procedurename)
    ) THEN 1 ELSE 0 END)
END;
GO

-- Function to safely drop and recreate an index
DROP PROCEDURE IF EXISTS csg_recreate_index;
GO
CREATE PROCEDURE csg_recreate_index
    @indexname nvarchar(255),
    @tablename nvarchar(255),
    @columnlist nvarchar(max),
    @is_unique bit = 0,
    @include_columns nvarchar(max) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql nvarchar(max);

    -- Drop the index if it exists
    IF dbo.csg_index_exists(@indexname, @tablename) = 1
    BEGIN
        SET @sql = 'DROP INDEX [' + @indexname + '] ON [' + @tablename + ']';
        EXEC sp_executesql @sql;
    END

    -- Create the index
    SET @sql = 'CREATE ' + CASE WHEN @is_unique = 1 THEN 'UNIQUE ' ELSE '' END +
               'INDEX [' + @indexname + '] ON [' + @tablename + '] (' + @columnlist + ')';

    -- Add INCLUDE clause if specified
    IF @include_columns IS NOT NULL
    BEGIN
        SET @sql = @sql + ' INCLUDE (' + @include_columns + ')';
    END

    EXEC sp_executesql @sql;
END;
GO

-- Function to check if a foreign key exists
DROP FUNCTION IF EXISTS csg_foreign_key_exists;
GO
CREATE FUNCTION csg_foreign_key_exists(@fkname nvarchar(255), @tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.foreign_keys
        WHERE LOWER(name) = LOWER(@fkname)
            AND LOWER(OBJECT_NAME(parent_object_id)) = LOWER(@tablename)
    ) THEN 1 ELSE 0 END)
END;
GO

-- Function to check if a MS_Description property exists for a column
DROP FUNCTION IF EXISTS csg_column_description_exists;
GO
CREATE FUNCTION csg_column_description_exists(@tablename nvarchar(255), @columnname nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.extended_properties ep
        JOIN sys.columns c ON ep.major_id = c.object_id AND ep.minor_id = c.column_id
        JOIN sys.tables t ON c.object_id = t.object_id
        WHERE ep.name = 'MS_Description'
        AND LOWER(t.name) = LOWER(@tablename)
        AND LOWER(c.name) = LOWER(@columnname)
    ) THEN 1 ELSE 0 END)
END;
GO