CREATE
OR REPLACE VIEW vwcalldetail AS
SELECT
    di.conversationid,
    di.originaldirection,
    di.mediatype,
    di.ani,
    di.dnis,
    di.purpose,
    di.sessiondirection,
    di.conversationstartdate,
    di.userid,
    di.queueid,
    qd.name AS queueName,
    di.conversationenddate,
    DATEDIFF(
        'second',
        di.conversationstartdate :: timestamp,
        di.conversationenddate :: timestamp
    ) AS CallDuration,
    SUM(
        DATEDIFF(
            'second',
            di.segmentstartdate :: timestamp,
            di.segmentenddate :: timestamp
        )
    ) AS TalkDuration,
    DATEDIFF(
        'second',
        di.conversationstartdate :: timestamp,
        di.conversationenddate :: timestamp
    ) / 86400.00 AS CallDurationDay,
    SUM(
        DATEDIFF(
            'second',
            di.segmentstartdate :: timestamp,
            di.segmentenddate :: timestamp
        )
    ) / 86400.00 AS TalkDurationDay,
    ud.name,
    di.segmenttype
FROM
    detailedInteractionData AS di
    LEFT OUTER JOIN userDetails AS ud ON ud.id = di.userid
    LEFT OUTER JOIN queueDetails AS qd ON qd.id = di.queueid
WHERE
    (NOT (di.userid IS NULL))
    AND (di.purpose IN ('agent', 'user', 'voicemail'))
    AND (
        di.segmenttype IN ('Interact', 'contacting', 'dialing')
    )
GROUP BY
    di.conversationid,
    di.originaldirection,
    di.mediatype,
    di.ani,
    di.dnis,
    di.purpose,
    di.sessiondirection,
    di.conversationstartdate,
    di.userid,
    di.queueid,
    qd.name,
    di.conversationenddate,
    ud.name,
    di.segmenttype;