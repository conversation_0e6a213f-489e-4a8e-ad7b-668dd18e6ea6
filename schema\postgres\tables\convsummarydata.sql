CREATE TABLE IF NOT EXISTS convsummarydata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationenddate timestamp without time zone,
    conversationstartdateltc timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    originaldirection varchar(50),
    firstmediatype varchar(50),
    lastmediatype varchar(50),
    peer varchar(50),
    ani varchar(400),
    dnis varchar(400),
    firstagentid varchar(50),
    lastagentid varchar(50),
    firstqueueid varchar(50),
    lastqueueid varchar(50),
    ttalkcomplete numeric(20, 2),
    tqueuetime numeric(20, 2),
    tacw numeric(20, 2),
    tansweredcount integer,
    tanswered numeric(20, 2),
    tabandonedcount integer,
    tresponsecount integer,
    tresponse numeric(20, 2),
    thandlecount integer,
    thandle numeric(20, 2),
    firstwrapupcode varchar(255),
    lastwrapupcode varchar(255),
    theldcompletecount integer,
    theldcomplete numeric(20, 2),
    nconsulttransferred integer,
    nblindtransferred integer,
    lastdisconnect varchar(50),
    lastpurpose varchar(50),
    lastsegmenttime numeric(20, 2),
    divisionid varchar(50),
    divisionid2 varchar(50),
    divisionid3 varchar(50),
    updated timestamp without time zone,
    CONSTRAINT convsummarynwdata_pkey PRIMARY KEY (keyid, conversationstartdate)
) PARTITION BY RANGE (conversationstartdate);

CREATE INDEX IF NOT EXISTS "ConvSummaryDiscType" ON convsummarydata USING btree (
    lastdisconnect ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "ConvSummaryDivision" ON convsummarydata USING btree (
    divisionid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "ConvSummaryNwConvId" ON convsummarydata USING btree (
    conversationid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "ConvSummaryNwEndDate" ON convsummarydata USING btree (conversationenddate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "ConvSummaryNwEndDateLTC" ON convsummarydata USING btree (conversationenddateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "ConvSummaryNwStartDate" ON convsummarydata USING btree (conversationstartdate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "ConvSummaryNwStartDateLTC" ON convsummarydata USING btree (conversationstartdateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "ConvSummaryUserId" ON convsummarydata USING btree (
    lastagentid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "ConvSummaryWrap" ON convsummarydata USING btree (
    lastwrapupcode ASC NULLS LAST
);

-- Partitions SQL
ALTER TABLE IF EXISTS convsumm_2020_01 RENAME TO convsummarydata_p2020_01;
ALTER TABLE IF EXISTS convsumm_2020_02 RENAME TO convsummarydata_p2020_02;
ALTER TABLE IF EXISTS convsumm_2020_03 RENAME TO convsummarydata_p2020_03;
ALTER TABLE IF EXISTS convsumm_2020_04 RENAME TO convsummarydata_p2020_04;
ALTER TABLE IF EXISTS convsumm_2020_05 RENAME TO convsummarydata_p2020_05;
ALTER TABLE IF EXISTS convsumm_2020_06 RENAME TO convsummarydata_p2020_06;
ALTER TABLE IF EXISTS convsumm_2020_07 RENAME TO convsummarydata_p2020_07;
ALTER TABLE IF EXISTS convsumm_2020_08 RENAME TO convsummarydata_p2020_08;
ALTER TABLE IF EXISTS convsumm_2020_09 RENAME TO convsummarydata_p2020_09;
ALTER TABLE IF EXISTS convsumm_2020_10 RENAME TO convsummarydata_p2020_10;
ALTER TABLE IF EXISTS convsumm_2020_11 RENAME TO convsummarydata_p2020_11;
ALTER TABLE IF EXISTS convsumm_2020_12 RENAME TO convsummarydata_p2020_12;
ALTER TABLE IF EXISTS convsumm_2021_01 RENAME TO convsummarydata_p2021_01;
ALTER TABLE IF EXISTS convsumm_2021_02 RENAME TO convsummarydata_p2021_02;
ALTER TABLE IF EXISTS convsumm_2021_03 RENAME TO convsummarydata_p2021_03;
ALTER TABLE IF EXISTS convsumm_2021_04 RENAME TO convsummarydata_p2021_04;
ALTER TABLE IF EXISTS convsumm_2021_05 RENAME TO convsummarydata_p2021_05;
ALTER TABLE IF EXISTS convsumm_2021_06 RENAME TO convsummarydata_p2021_06;
ALTER TABLE IF EXISTS convsumm_2021_07 RENAME TO convsummarydata_p2021_07;
ALTER TABLE IF EXISTS convsumm_2021_08 RENAME TO convsummarydata_p2021_08;
ALTER TABLE IF EXISTS convsumm_2021_09 RENAME TO convsummarydata_p2021_09;
ALTER TABLE IF EXISTS convsumm_2021_10 RENAME TO convsummarydata_p2021_10;
ALTER TABLE IF EXISTS convsumm_2021_11 RENAME TO convsummarydata_p2021_11;
ALTER TABLE IF EXISTS convsumm_2021_12 RENAME TO convsummarydata_p2021_12;
ALTER TABLE IF EXISTS convsumm_2022_01 RENAME TO convsummarydata_p2022_01;
ALTER TABLE IF EXISTS convsumm_2022_02 RENAME TO convsummarydata_p2022_02;
ALTER TABLE IF EXISTS convsumm_2022_03 RENAME TO convsummarydata_p2022_03;
ALTER TABLE IF EXISTS convsumm_2022_04 RENAME TO convsummarydata_p2022_04;
ALTER TABLE IF EXISTS convsumm_2022_05 RENAME TO convsummarydata_p2022_05;
ALTER TABLE IF EXISTS convsumm_2022_06 RENAME TO convsummarydata_p2022_06;
ALTER TABLE IF EXISTS convsumm_2022_07 RENAME TO convsummarydata_p2022_07;
ALTER TABLE IF EXISTS convsumm_2022_08 RENAME TO convsummarydata_p2022_08;
ALTER TABLE IF EXISTS convsumm_2022_09 RENAME TO convsummarydata_p2022_09;
ALTER TABLE IF EXISTS convsumm_2022_10 RENAME TO convsummarydata_p2022_10;
ALTER TABLE IF EXISTS convsumm_2022_11 RENAME TO convsummarydata_p2022_11;
ALTER TABLE IF EXISTS convsumm_2022_12 RENAME TO convsummarydata_p2022_12;

-- Add comments

COMMENT ON COLUMN convSummaryData.ani IS 'Conversation ANI'; 
COMMENT ON COLUMN convSummaryData.conversationenddate IS 'Conversation End Date (UTC)'; 
COMMENT ON COLUMN convSummaryData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN convSummaryData.conversationstartdate IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN convSummaryData.conversationstartdateltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON COLUMN convSummaryData.conversationenddateltc IS 'Conversation End Date (LTC)'; 
COMMENT ON COLUMN convSummaryData.divisionid IS 'Conversation Division GUID'; 
COMMENT ON COLUMN convSummaryData.divisionid2 IS 'Conversation Division GUID 2'; 
COMMENT ON COLUMN convSummaryData.divisionid3 IS 'Conversation Division GUID 3'; 
COMMENT ON COLUMN convSummaryData.dnis IS 'Conversation DNIS'; 
COMMENT ON COLUMN convSummaryData.firstagentid IS 'Conversation First Agent GUID'; 
COMMENT ON COLUMN convSummaryData.firstmediatype IS 'Conversation First Media Type'; 
COMMENT ON COLUMN convSummaryData.firstqueueid IS 'Conversation First Queue GUID'; 
COMMENT ON COLUMN convSummaryData.firstwrapupcode IS 'Conversation First Wrap Up GUID'; 
COMMENT ON COLUMN convSummaryData.lastagentid IS 'Conversation Final Agent GUID'; 
COMMENT ON COLUMN convSummaryData.lastdisconnect IS 'Conversation Last Disconnect Type'; 
COMMENT ON COLUMN convSummaryData.lastmediatype IS 'Conversation Final Media Type'; 
COMMENT ON COLUMN convSummaryData.lastpurpose IS 'Conversation Last Purpose'; 
COMMENT ON COLUMN convSummaryData.lastqueueid IS 'Conversation Final Queue GUID'; 
COMMENT ON COLUMN convSummaryData.lastsegmenttime IS 'Conversation Last Segment Total Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.lastwrapupcode IS 'Conversation Final Wrap Up GUID'; 
COMMENT ON COLUMN convSummaryData.nblindtransferred IS 'Conversation Blind Transfer Count'; 
COMMENT ON COLUMN convSummaryData.nconsulttransferred IS 'Conversation Consult Transfer Count'; 
COMMENT ON COLUMN convSummaryData.originaldirection IS 'Conversation Original Direction'; 
COMMENT ON COLUMN convSummaryData.peer IS 'Conversation Peer GUID (for Voice Analysis)'; 
COMMENT ON COLUMN convSummaryData.tabandonedcount IS 'Conversation Total Abandon Count'; 
COMMENT ON COLUMN convSummaryData.tacw IS 'Conversation ACW'; 
COMMENT ON COLUMN convSummaryData.tanswered IS 'Conversation Total Answer Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.tansweredcount IS 'Conversation Total Answered Count'; 
COMMENT ON COLUMN convSummaryData.thandlecount IS 'Conversation Total Hold Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.theldcomplete IS 'Conversation Total'; 
COMMENT ON COLUMN convSummaryData.theldcompletecount IS 'Conversation Total'; 
COMMENT ON COLUMN convSummaryData.tqueuetime IS 'Conversation Queue Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.tresponse IS 'Conversation Total Response Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.tresponseCount IS 'Conversation Total Response Count'; 
COMMENT ON COLUMN convSummaryData.ttalkcomplete IS 'Conversation Talk Time (Seconds)'; 
COMMENT ON COLUMN convSummaryData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE convSummaryData IS 'Conversation Summary Data'; 
