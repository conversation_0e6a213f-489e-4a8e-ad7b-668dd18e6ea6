CREATE
OR REPLACE VIEW vwEvalData AS
SELECT DISTINCT
    ed.keyid,
    ed.agent<PERSON><PERSON>,
    ed.assigneddate,
    ed.assigneddateLTC,
    ed.conversationid,
    ed.evaluationformid,
    ed.evaluationid,
    ed.evaluatorid,
    ed.releasedate,
    ed.releasedateLTC,
    ed.status,
    ed.totalcriticalscore,
    ed.totalnoncriticalscore,
    ed.totalscore,
    ed.calibrationid,
    ed.averagescore,
    ed.highscore,
    ed.lowscore,
    ed.updated,
    ed.userid,
    evDet.evaluationname,
    ud.managerid as managerid,
    ud.managername,
    ud.name as agentname
FROM
    evalData ed
    left outer join vwUserDetail ud on ud.id = ed.userid
    -- Note: evalDetails.evaluationid actually contains the form ID, not the evaluation ID
    left outer join evalDetails evDet on evDet.evaluationid = ed.evaluationformid;

COMMENT ON COLUMN vwEvalData.keyid IS 'Key ID';
COMMENT ON COLUMN vwEvalData.agenthasread IS 'Agent Has Read';
COMMENT ON COLUMN vwEvalData.assigneddate IS 'Assigned Date (UTC)';
COMMENT ON COLUMN vwEvalData.assigneddateLTC IS 'Assigned Date (LTC)';
COMMENT ON COLUMN vwEvalData.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwEvalData.evaluationformid IS 'Evaluation Form GUID';
COMMENT ON COLUMN vwEvalData.evaluationid IS 'Evaluation GUID (This is the actual evaluation ID from evaldata table, not to be confused with evaldetails.evaluationid which contains form IDs)';
COMMENT ON COLUMN vwEvalData.evaluatorid IS 'Evaluator GUID';
COMMENT ON COLUMN vwEvalData.releasedate IS 'Release Date(UTC)';
COMMENT ON COLUMN vwEvalData.releasedateLTC IS 'Release Date (LTC)';
COMMENT ON COLUMN vwEvalData.status IS 'Status';
COMMENT ON COLUMN vwEvalData.totalcriticalscore IS 'Total Critical Score';
COMMENT ON COLUMN vwEvalData.totalnoncriticalscore IS 'Total Non-Critical Score';
COMMENT ON COLUMN vwEvalData.totalscore IS 'Total Score';
COMMENT ON COLUMN vwEvalData.calibrationid IS 'Calibration GUID';
COMMENT ON COLUMN vwEvalData.averagescore IS 'Average Score';
COMMENT ON COLUMN vwEvalData.highscore IS 'High Score';
COMMENT ON COLUMN vwEvalData.lowscore IS 'Low Score';
COMMENT ON COLUMN vwEvalData.updated IS 'Updated';
COMMENT ON COLUMN vwEvalData.userid IS 'User GUID';
COMMENT ON COLUMN vwEvalData.evaluationname IS 'Evaluation Name';
COMMENT ON COLUMN vwEvalData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwEvalData.managername IS 'Manager Name';
COMMENT ON COLUMN vwEvalData.agentname IS 'Agent Name';
COMMENT ON VIEW vwEvalData IS 'See Evaluation Data';
