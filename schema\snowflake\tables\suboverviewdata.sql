CREATE TABLE IF NOT EXISTS suboverviewdata (
    keyid varchar(400) NOT NULL,
    rowdate timestamp without time zone,
    startdate timestamp without time zone,
    enddate timestamp without time zone,
    licname varchar(200),
    partnumber varchar(50),
    "grouping" varchar(50),
    unitofmeasuretype varchar(50),
    usagequantity numeric(20, 2),
    prepayQuantity numeric(20, 2),
    overageprice numeric(20, 2),
    iscancellable number,
    bundlequantity integer,
    isthirdparty number,
    updated timestamp without time zone,
    CONSTRAINT suboverviewdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE suboverviewData ALTER COLUMN usagequantity TYPE numeric(20, 2);
ALTER TABLE suboverviewData
ADD column IF NOT exists startdate timestamp without time zone;

ALTER TABLE suboverviewData
ADD column IF NOT exists enddate timestamp without time zone;

ALTER TABLE suboverviewData
ADD column IF NOT exists prepayQuantity numeric(20, 2);


CREATE OR REPLACE PROCEDURE check_and_update_suboverviewdata()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) AS count FROM suboverviewData WHERE startdate IS NULL`
  });
  resultSet.next();
  var exists_flag = resultSet.getColumnValue(1);
  
  if (exists_flag > 0) {
    snowflake.execute({ sqlText: `TRUNCATE TABLE suboverviewdata` });
    snowflake.execute({ 
      sqlText: `UPDATE tabledefinitions 
                SET datekeyfield = DATEADD(month, -12, CURRENT_TIMESTAMP()) 
                WHERE tablename = 'suboverviewdata'`
    });
    return 'Table truncated and updated successfully';
  } else {
    return 'No records found with startdate as NULL.';
  }
$$;

-- Call the procedure to execute the logic
CALL check_and_update_suboverviewdata();

CREATE OR REPLACE PROCEDURE check_and_delete_suboverviewdata()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  // Query to count records where keyid contains fewer than 3 pipes
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) AS count 
              FROM suboverviewdata 
              WHERE LENGTH(keyid) - LENGTH(REPLACE(keyid, '|', '')) < 3`
  });
  
  resultSet.next(); // Move to the first row of the result
  var exists_flag = resultSet.getColumnValue(1); // Get the count value
  
  if (exists_flag > 0) {
    // Perform the DELETE action if such records exist
    snowflake.execute({
      sqlText: `DELETE FROM suboverviewdata 
                WHERE LENGTH(keyid) - LENGTH(REPLACE(keyid, '|', '')) < 3`
    });
    
    return 'Records with fewer than 3 pipes in keyid deleted successfully';
  } else {
    return 'No records found with fewer than 3 pipes in keyid.';
  }
$$;

-- Call the procedure to execute the logic
CALL check_and_delete_suboverviewdata();
