IF dbo.csg_table_exists('userinteractionpresencedetaileddata') = 0
CREATE TABLE userinteractionpresencedetaileddata (
    keyid nvarchar(255) NOT NULL,
    userid nvarchar(50),
    starttime datetime NULL,
    starttimeltc datetime NULL,
    endtime datetime NULL,
    endtimeltc datetime NULL,
    systempresence nvarchar(50),
    orgpresence nvarchar(50),
    routingstatus nvarchar(50),
    conversationId nvarchar(50),
    mediatype nvarchar(50),
    timeinstate decimal(20, 2),
    updated datetime NULL,
    CONSTRAINT PK_userinteractionpresencedetaileddata PRIMARY KEY (keyid ASC)
);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_starttime', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_starttime ON userinteractionpresencedetaileddata (starttime);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_starttimeltc', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_starttimeltc ON userinteractionpresencedetaileddata (starttimeltc);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_endtime', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_endtime ON userinteractionpresencedetaileddata (endtime);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_endtimeltc', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_endtimeltc ON userinteractionpresencedetaileddata (endtimeltc);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_orgpresence', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_orgpresence ON userinteractionpresencedetaileddata (orgpresence);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_routingstatus', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_routingstatus ON userinteractionpresencedetaileddata (routingstatus);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_systempresence', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_systempresence ON userinteractionpresencedetaileddata (systempresence);

IF dbo.csg_index_exists('userinteractionpresencedetaileddata_userid', 'userinteractionpresencedetaileddata') = 0
CREATE INDEX userinteractionpresencedetaileddata_userid ON userinteractionpresencedetaileddata (userid);
