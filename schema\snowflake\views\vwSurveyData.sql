CREATE
OR REPLACE VIEW vwSurveyData AS
SELECT
    surveyid,
    conversationid,
    surveyformid,
    surveyname,
    agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    agentteamid,
    queueid,
    queue.name AS queuename,
    status,
    totalscore,
    completeddate,
    completeddateltc,
    surveyData.updated,
    lastpoll
FROM
    surveyData
    LEFT JOIN userdetails agent ON agent.id :: text = surveyData.agentid :: text
    LEFT JOIN userdetails manager ON manager.id :: text = agent.manager :: text
    LEFT JOIN queuedetails queue ON queue.id :: text = surveyData.queueid :: text;

-- spell-checker: ignore: surveyid completeddateltc