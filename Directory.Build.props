<Project>
    <PropertyGroup>
        <DefineConstants>OPTIONS_V3_COMPAT;MYSQL</DefineConstants>
    </PropertyGroup>

    <PropertyGroup>
        <Company>Customer Science</Company>
        <Authors>Customer Science</Authors>
        <Copyright>Copyright © Customer Science $([System.DateTime]::UtcNow.Year)</Copyright>
        <Product>Genesys Cloud Data Adapter</Product>
    </PropertyGroup>

    <PropertyGroup Condition="'$(TF_BUILD)' == 'true'">
        <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DebugSymbols>true</DebugSymbols>
        <Optimize>false</Optimize>
        <Obfuscate>false</Obfuscate>
        <DefineConstants>$(DefineConstants);TRACE;DEBUG</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="GitVersion.MsBuild" Version="5.*">
          <PrivateAssets>All</PrivateAssets>
        </PackageReference>
    </ItemGroup>
    
</Project> 