IF dbo.csg_table_exists('queuerealtimeconvData') = 0
CREATE TABLE [queuerealtimeconvData](
    [keyid] [nvarchar](100) NOT NULL,
    [conversationid] [nvarchar](50),
    [queueid] [nvarchar](50),
    [conversationstate] [nvarchar](50),
    [ani] [nvarchar](400),
    [dnis] [nvarchar](400),
    [participantname] [nvarchar](200),
    [userid] [nvarchar](50),
    [media] [nvarchar](50),
    [direction] [nvarchar](50),
    [state] [nvarchar](50),
    [skill1] [nvarchar](50),
    [skill2] [nvarchar](50),
    [skill3] [nvarchar](50),
    [usedrout] [nvarchar](50),
    [requestedrout1] [nvarchar](50),
    [requestedrout2] [nvarchar](50),
    [segmenttime] [datetime],
    [talktime] [datetime],
    [talktimeltc] [datetime],
    [startdate] [datetime],
    [startdateltc] [datetime],
    [heldstate] [nvarchar](50),
    [heldtime] [datetime],
    [actingas] [nvarchar](50),
    [acwstate] [bit],
    [acwstring] [nvarchar](50),
    [acwtime] [datetime],
    [initialpriority] [int],
    [manuallychecked] [bit],
    [updated] [datetime],
    CONSTRAINT [PK_queuerealtimeconvData] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('queuerealtimeconvdata', 'manuallychecked') = 0
    ALTER TABLE queuerealtimeconvdata ADD manuallychecked BIT;
ELSE
    ALTER TABLE queuerealtimeconvdata ALTER COLUMN manuallychecked BIT;