CREATE
OR REPLACE VIEW vwcallnotrespondingdetails AS
SELECT
    det.conversationid,
    det.userid,
    ud.name as agentname,
    md.id as managerid,
    md.name as managername,
    det.mediatype,
    det.conversationstartdate,
    det.conversationstartdateLTC,
    det.conversationenddate,
    det.conversationenddateLTC,
    det.segmentstartdate,
    det.segmentstartdateLTC,
    det.segmentenddate,
    det.segmentenddateLTC,
    det.convtosegmentendtime AS TotalCallTime,
    det.segmenttime AS QueueTime,
    (det.convtosegmentendtime / 86400.00) AS TotalCallTimeDay,
    (det.segmenttime / 86400.00) AS QueueTimeDay,
    det.ani,
    det.dnis,
    det.queueid,
    que.name AS queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    detailedInteractionData det
    LEFT OUTER JOIN queueDetails que ON que.id = det.queueid
    LEFT OUTER JOIN userDetails ud ON ud.id = det.userid
    LEFT OUTER JOIN userDetails md ON md.id = ud.manager
WHERE
    det.segmenttype IN ('alert')
    AND det.purpose = 'agent'
    AND det.disconnectiontype in ('client', 'endpoint', 'noAnswerTransfer');