CREATE
OR ALTER VIEW [vwqueuedetails] AS
SELECT
    qd.id,
    qd.name,
    qd.description,
    -- Queue group extraction with error handling
    CASE
        WHEN PATINDEX('%queuegroup=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%queuegroup=%', qd.description) + <PERSON><PERSON>('queuegroup='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%queuegroup=%', qd.description) + <PERSON><PERSON>('queuegroup='),
                        LEN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS queuegroup,
    -- Region extraction with error handling
    CASE
        WHEN PATINDEX('%region=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%region=%', qd.description) + <PERSON>EN('region='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%region=%', qd.description) + <PERSON><PERSON>('region='),
                        <PERSON>EN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS region,
    -- Group type extraction with error handling
    CASE
        WHEN PATINDEX('%grouptype=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%grouptype=%', qd.description) + LEN('grouptype='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%grouptype=%', qd.description) + LEN('grouptype='),
                        LEN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS grouptype,
    -- Country extraction with error handling
    CASE
        WHEN PATINDEX('%country=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%country=%', qd.description) + LEN('country='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%country=%', qd.description) + LEN('country='),
                        LEN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS country,
    -- Language extraction with error handling
    CASE
        WHEN PATINDEX('%language=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%language=%', qd.description) + LEN('language='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%language=%', qd.description) + LEN('language='),
                        LEN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS language,
    -- Account owner extraction with error handling
    CASE
        WHEN PATINDEX('%accountowner=%', qd.description) > 0 THEN SUBSTRING(
            qd.description,
            PATINDEX('%accountowner=%', qd.description) + LEN('accountowner='),
            NULLIF(
                PATINDEX(
                    '%;%',
                    SUBSTRING(
                        qd.description,
                        PATINDEX('%accountowner=%', qd.description) + LEN('accountowner='),
                        LEN(qd.description)
                    )
                ) - 1,
                -1
            )
        )
        ELSE NULL
    END AS accountowner,
    qd.divisionid,
    divisiondetails.name AS divisionname,
    divisiondetails.name AS vwuserpresencedata,
    qd.enabletranscription,
    qd.isactive
    ,qd.callslatargetperc
    ,qd.callslatargetduration
    ,qd.callbackslatargetperc
    ,qd.callbackslatargetduration
    ,qd.chatslatargetperc
    ,qd.chatslatargetduration
    ,qd.emailslatargetperc
    ,qd.emailslatargetduration
    ,qd.messageslatargetperc
    ,qd.messageslatargetduration
FROM
    queueDetails AS qd
    INNER JOIN divisiondetails AS divisiondetails ON qd.divisionid = divisiondetails.id;