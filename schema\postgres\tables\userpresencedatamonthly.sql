CREATE TABLE IF NOT EXISTS userpresencedatamonthly (
    keyid varchar(255) NOT NULL,
    id varchar(50),
    userid varchar(50),
    startdate timestamp without time zone,
    timetype varchar(50),
    systempresenceid varchar(50),
    presenceid varchar(50),
    presencetime numeric(20, 2),
    routingid varchar(50),
    routingtime numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedatamonthly_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN userPresenceDataMonthly.id IS ' ';
COMMENT ON COLUMN userPresenceDataMonthly.keyid IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.presenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.presencetime IS ' ';
COMMENT ON COLUMN userPresenceDataMonthly.routingid IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.routingtime IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.startdate IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.systempresenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.timetype IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.updated IS ' '; 
COMMENT ON COLUMN userPresenceDataMonthly.userid IS ' ';
COMMENT ON TABLE userPresenceDataMonthly IS 'User Presence Data Monthly Data - LTC - See UserPresenceData for Descriptions'; 