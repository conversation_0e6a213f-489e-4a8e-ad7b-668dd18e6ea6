CREATE OR REPLACE PROCEDURE update_mvwevaluationgroupdata(
    IN from_date date = now() - interval '2 day'
)
LANGUAGE SQL
AS $procedure$
    INSERT INTO mvwevaluationgroupdata
    (
        SELECT
            egd.keyid,
            egd.evaluationid,
            egd.questiongroupid,
            (SELECT ed.questiongroupname
                FROM evaldetails ed
                WHERE ed.questiongroupid::text = egd.questiongroupid::text
                LIMIT 1) AS questiongroupname,
            egd.totalscore,
            egd.maxtotalscore,
            egd.markedna,
            egd.totalcriticalscore,
            egd.maxtotalcriticalscore,
            egd.totalnoncriticalscore,
            egd.maxtotalnoncriticalscore,
            egd.totalscoreunweighted,
            egd.maxtotalscoreunweighted,
            egd.failedkillquestions,
            ud.divisionid,
            egd.comments,
            eda.conversationid
        FROM evalquestiongroupdata egd
        LEFT JOIN evaldata eda ON eda.evaluationid::text = egd.evaluationid::text
        LEFT JOIN userdetails ud ON ud.id::text = eda.userid::text
        WHERE eda.releasedate >= from_date OR eda.updated >= from_date
    )
    ON CONFLICT ON CONSTRAINT mvwevaluationgroupdata_keyid_key
    DO UPDATE SET
        keyid = EXCLUDED.keyid,
        evaluationid = EXCLUDED.evaluationid,
        questiongroupid = EXCLUDED.questiongroupid,
        questiongroupname = EXCLUDED.questiongroupname,
        totalscore = EXCLUDED.totalscore,
        maxtotalscore = EXCLUDED.maxtotalscore,
        markedna = EXCLUDED.markedna,
        totalcriticalscore = EXCLUDED.totalcriticalscore,
        maxtotalcriticalscore = EXCLUDED.maxtotalcriticalscore,
        totalnoncriticalscore = EXCLUDED.totalnoncriticalscore,
        maxtotalnoncriticalscore = EXCLUDED.maxtotalnoncriticalscore,
        totalscoreunweighted = EXCLUDED.totalscoreunweighted,
        maxtotalscoreunweighted = EXCLUDED.maxtotalscoreunweighted,
        failedkillquestions = EXCLUDED.failedkillquestions,
        divisionid = EXCLUDED.divisionid,
        comments = EXCLUDED.comments,
        conversationid = EXCLUDED.conversationid;
$procedure$;
call update_mvwevaluationgroupdata('2000-01-01');
