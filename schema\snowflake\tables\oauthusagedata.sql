CREATE TABLE IF NOT EXISTS oauthusagedata (
    keyid varchar(100) NOT NULL,
    clientid varchar(50),
    rowdate timestamp without time zone,
    clientname varchar(200),
    organizationid varchar(50),
    userid varchar(50),
    status200 integer,
    status300 integer,
    status400 integer,
    status500 integer,
    status429 integer,
    updated timestamp without time zone,
    CONSTRAINT oauthusagedata_pkey PRIMARY KEY (keyid)
)