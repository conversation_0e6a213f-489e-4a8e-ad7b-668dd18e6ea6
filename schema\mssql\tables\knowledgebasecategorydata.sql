IF dbo.csg_table_exists('knowledgebasecategorydata') = 0
CREATE TABLE [knowledgebasecategorydata] (
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](100),
    [description] [nvarchar](255),
    [externalId] [nvarchar](100),
    [dateCreated] [datetime],
    [dateModified] [datetime],
    [documentCount] [int],
    [knowledgeBaseId] [nvarchar](100),
    [parentCategoryId] [nvarchar](100),
    [parentCategoryName] [nvarchar](100),
    [updated] [datetime],
    CONSTRAINT knowledgebasecategorydata_pkey PRIMARY KEY (id)
);
