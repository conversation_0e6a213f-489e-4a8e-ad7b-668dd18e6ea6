CREATE OR REPLACE VIEW vwdetailedinteractiondata AS
SELECT
    di.keyid,
    di.conversationid,
    di.conversationstartdate,
    di.conversationstartdateLTC,
    di.conversationenddate,
    di.conversationenddateLTC,
    di.conversationminmos,
    di.conversationminrfactor,
    di.originaldirection,
    di.participantid,
    di.participantname,
    di.purpose,
    di.mediatype,
    di.externaltag,
    di.ani,
    di.dnis,
    di.sessiondnis,
    di.edgeId,
    di.gencode,
    di.remotedisplayable,
    di.segmentstartdate,
    di.segmentstartdateLTC,
    di.segmentenddate,
    di.segmentenddateLTC,
    di.segmenttime,
    di.convtosegmentstarttime,
    di.convtosegmentendtime,
    di.segmenttime / 86400.00 as segmenttimeDay,
    di.convtosegmentstarttime / 86400.00 as convtosegmentstarttimeDay,
    di.convtosegmentendtime / 86400.00 as convtosegmentendtimeDay,
    di.segmenttype,
    di.conference,
    di.disconnectiontype,
    di.wrapupcode,
    di.wrapupnote,
    wd.name as wrapupdesc,
    di.recordingexists,
    di.sessionprovider,
    di.flowid,
    di.flowname,
    di.flowversion,
    di.flowtype,
    di.exitreason,
    di.entryreason,
    di.entrytype,
    di.transfertype,
    di.transfertargetname,
    di.queueid,
    qd.name as queuename,
    di.userid,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername,
    di.issuedcallback,
    di.nflow,
    di.tivr,
    di.tivr / 86400.00 as tivrDay,
    di.tflow,
    di.tflow / 86400.00 as tflowDay,
    di.tflowdisconnect,
    di.tflowdisconnect / 86400.00 as tflowdisconnectDay,
    di.tflowexit,
    di.tflowexit / 86400.00 as tflowexitDay,
    di.tflowout,
    di.tflowout / 86400.00 as tflowoutDay,
    di.tacd,
    di.tacd / 86400.00 as tacdDay,
    di.tacw,
    di.tacw / 86400.00 as tacwDay,
    di.talert,
    di.talert / 86400.00 as talertDay,
    di.tanswered,
    di.tanswered / 86400.00 as tansweredDay,
    di.ttalk,
    di.ttalk / 86400.00 as ttalkDay,
    di.ttalkcomplete,
    di.ttalkcomplete / 86400.00 as ttalkcompleteDay,
    di.thandle,
    di.thandle / 86400.00 as thandleDay,
    di.tcontacting,
    di.tcontacting / 86400.00 as tcontactingDay,
    di.tdialing,
    di.tdialing / 86400.00 as tdialingDay,
    di.tnotresponding,
    di.tnotresponding / 86400.00 as tnotrespondingDay,
    di.tabandon,
    di.tabandon / 86400.00 as tabandonDay,
    di.theld,
    di.theld / 86400.00 as theldDay,
    di.theldcomplete,
    di.theldcomplete / 86400.00 as theldcompleteDay,
    di.tvoicemail,
    di.tvoicemail / 86400.00 as tvoicemailDay,
    di.tmonitoring,
    di.tmonitoring / 86400.00 as tmonitoringDay,
    di.noffered,
    di.nconnected,
    di.nconsult,
    di.nconsulttransferred,
    di.ntransferred,
    di.nblindtransferred,
    di.nerror,
    di.noutbound,
    di.nstatetransitionerror,
    di.noversla,
    di.nbotinteractions,
    di.tpark,
    di.tparkcomplete,
    di.omessagecount,
    di.omessagesegmentcount,
    di.omessageturn,
    di.sessiondirection,
    di.segdestinationConversationId,
    di.divisionid,
    dd.name as divisionname,
    di.updated,
    -- New Session-level fields
    di.sessionid,
    di.protocolcallid,
    di.remotenamedisplayable,
    di.callbackusername,
    di.callbacknumbers,
    di.scriptid,
    di.skipenabled,
    di.timeoutseconds,
    di.flowouttype,
    di.roomid,
    di.callbackscheduledtime,
    -- New Flow-level fields
    di.transfertargetaddress,
    di.startinglanguage,
    di.endinglanguage,
    -- New Segment-level fields
    di.requestedroutingskillids,
    di.sipresponsecodes,
    di.q850responsecodes,
    di.errorcode,
    di.requestedlanguageid,
    -- New Participant-level fields
    di.externalcontactid,
    di.externalorganizationid,
    -- New Media Endpoint Statistics fields
    di.codecs,
    di.minmos,
    di.minrfactor,
    di.maxlatencyms,
    di.receivedpackets,
    di.discardedpackets,
    di.overrunpackets,
    di.invalidpackets,
    di.duplicatepackets
FROM
    detailedinteractiondata di
    left outer join vwUserDetail ud on ud.id = di.userid
    left outer join queueDetails qd on qd.id = di.queueid
    left outer join wrapupDetails wd on wd.id = di.wrapupcode
    left outer join divisionDetails dd on dd.id = di.divisionid;