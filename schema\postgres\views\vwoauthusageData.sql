CREATE
OR REPLACE VIEW vwoauthusageData AS
SELECT
    clientid,
    rowdate,
    clientname,
    organizationid,
    userid,
    status200,
    status300,
    status400,
    status500,
    status429,
    updated
FROM
    oauthusageData;

COMMENT ON COLUMN vwoauthusageData.clientid IS 'Oauth Client GUID'; 
COMMENT ON COLUMN vwoauthusageData.clientname IS 'Oauth Client Name'; 
COMMENT ON COLUMN vwoauthusageData.organizationid IS 'Home Organization GUID'; 
COMMENT ON COLUMN vwoauthusageData.rowdate IS 'Date'; 
COMMENT ON COLUMN vwoauthusageData.status200 IS 'Count of 200 Response Codes'; 
COMMENT ON COLUMN vwoauthusageData.status300 IS 'Count of 300 Response Codes'; 
COMMENT ON COLUMN vwoauthusageData.status400 IS 'Count of 300 Response Codes'; 
COMMENT ON COLUMN vwoauthusageData.status429 IS 'Count of 429 Response Codes - Rate limiting Responses'; 
COMMENT ON COLUMN vwoauthusageData.status500 IS 'Count of 500 Response Codes'; 
COMMENT ON COLUMN vwoauthusageData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN vwoauthusageData.userid IS 'Oauth Agent GUID'; 
COMMENT ON VIEW vwoauthusageData IS 'See OauthUsageData: Oauth Usage by Client ID per Day';