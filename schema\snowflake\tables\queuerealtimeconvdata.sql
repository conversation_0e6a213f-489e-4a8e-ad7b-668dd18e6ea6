CREATE TABLE IF NOT EXISTS queuerealtimeconvdata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    queueid varchar(50),
    conversationstate varchar(50),
    userid varchar(50),
    media varchar(50),
    direction varchar(50),
    state varchar(50),
    skill1 varchar(50),
    skill2 varchar(50),
    skill3 varchar(50),
    segementtime timestamp without time zone,
    talktime timestamp without time zone,
    heldstate varchar(50),
    heldtime timestamp without time zone,
    actingas varchar(50),
    acwstate boolean,
    manuallychecked boolean,
    acwstring varchar(50),
    acwtime timestamp without time zone,
    initialpriority integer,
    updated timestamp without time zone,
    segmenttime timestamp without time zone,
    usedrout varchar(50),
    requestedrout1 varchar(50),
    requestedrout2 varchar(50),
    ani varchar(400),
    dnis varchar(400),
    participantname varchar(200),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    talktimeltc timestamp without time zone,
    CONSTRAINT queuerealtimeconvdata_key PRIMARY KEY (keyid)
);

ALTER TABLE queuerealtimeconvdata
ADD COLUMN IF NOT exists manuallychecked boolean;

