CREATE TABLE IF NOT EXISTS kq_analysis_question (
    kq_analysisquestionid UUID NOT NULL,
    kq_analysisid UUID,
    question TEXT,
    answer TEXT,
    taxonomy VARCHAR(200),
    knowledgeid UUID,
    knowledge_confidence DECIMAL(5,2),
    CONSTRAINT kq_analysis_question_pkey PRIMARY KEY (kq_analysisquestionid)
);



-- Create indexes on foreign key columns for performance
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'kq_analysis_question'
        AND indexname = 'idx_kq_analysis_question_kq_analysisid'
    ) THEN
        CREATE INDEX idx_kq_analysis_question_kq_analysisid ON kq_analysis_question (kq_analysisid);
    END IF;
END $$;
