CREATE TABLE IF NOT EXISTS evalquestiongroupdata (
    keyid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    evaluationformid varchar(50) NOT NULL,
    questiongroupid varchar(50),
    totalscore numeric(20, 2),
    maxtotalscore numeric(20, 2),
    markedna number,
    totalcriticalscore numeric(20, 2),
    maxtotalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    maxtotalnoncriticalscore numeric(20, 2),
    totalscoreunweighted numeric(20, 2),
    maxtotalscoreunweighted numeric(20, 2),
    failedkillquestions number,
    comments text,
    updated timestamp without time zone,
    CONSTRAINT evalquestiongroupdata_pkey PRIMARY KEY (keyid)
) ;