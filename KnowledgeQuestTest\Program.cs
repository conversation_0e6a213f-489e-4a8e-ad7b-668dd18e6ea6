using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using GenesysCloudUtils;

namespace KnowledgeQuestTest
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Knowledge Quest API Test ===");
            
            // Create a logger factory
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddFilter("Microsoft", LogLevel.Warning)
                    .AddFilter("System", LogLevel.Warning)
                    .AddFilter("KnowledgeQuestTest", LogLevel.Debug)
                    .AddConsole();
            });
            
            // Create a logger for VoiceAnalysis
            var logger = loggerFactory.CreateLogger<VoiceAnalysis>();
            
            // Create a VoiceAnalysis instance
            var voiceAnalysis = new VoiceAnalysis(logger);
            voiceAnalysis.Initialize();
            
            // Test the VerifyQueueAsync method
            Console.WriteLine("\nTesting VerifyQueueAsync...");
            string testQueueId = "test-queue-id";
            bool verifyResult = await voiceAnalysis.VerifyQueueAsync(testQueueId);
            Console.WriteLine($"VerifyQueueAsync result for queue {testQueueId}: {verifyResult}");
            
            // Test the IngestTranscriptAsync method with a simple transcript
            Console.WriteLine("\nTesting IngestTranscriptAsync...");
            string simpleTranscript = @"{
                ""conversationId"": ""test-conversation-id"",
                ""communicationId"": ""test-communication-id"",
                ""transcripts"": [
                    {
                        ""transcriptId"": ""test-transcript-id"",
                        ""phrases"": [
                            {
                                ""text"": ""Hello, this is a test transcript."",
                                ""speaker"": ""agent""
                            },
                            {
                                ""text"": ""Thank you for your help."",
                                ""speaker"": ""customer""
                            }
                        ]
                    }
                ],
                ""participants"": [
                    {
                        ""participantId"": ""test-participant-id"",
                        ""participantPurpose"": ""agent"",
                        ""userId"": ""test-user-id"",
                        ""queueId"": ""test-queue-id"",
                        ""startTimeMs"": 1617235200000,
                        ""endTimeMs"": 1617235800000
                    }
                ]
            }";
            
            bool ingestResult = await voiceAnalysis.IngestTranscriptAsync(simpleTranscript);
            Console.WriteLine($"IngestTranscriptAsync result: {ingestResult}");
            
            Console.WriteLine("\nTest completed. Press any key to exit.");
            Console.ReadKey();
        }
    }
}
