CREATE TABLE IF NOT EXISTS convvoicetopicdetaildata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    starttime timestamp without time zone NOT NULL,
    starttimeltc timestamp without time zone,
    participant varchar(50),
    duration numeric(20, 2),
    confidence numeric(20, 2),
    topicname varchar(200),
    topicid varchar(50),
    topicphrase varchar(200),
    transcriptphrase varchar(200),
    updated timestamp without time zone,
    transcriptnumber varchar(50),
	communicationid varchar(50),
	ani varchar(200),
	dnis varchar(200),
	queueid varchar(50),
	userid varchar(50),
    CONSTRAINT convvoicetopicdetaildata_new_pkey PRIMARY KEY (keyid, starttime)
);
