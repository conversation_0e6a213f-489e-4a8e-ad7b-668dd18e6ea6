CREATE
OR REPLACE VIEW vwEvalQuestionGroupData AS
SELECT
    concat(egd.evaluationformid,'|',egd.questiongroupid) as QuestionGroupfactkey,
    egd.keyid,
    egd.evaluationid,
    egd.questiongroupid,
    egd.totalscore,
    egd.maxtotalscore,
    egd.markedna,
    egd.totalcriticalscore,
    egd.maxtotalcriticalscore,
    egd.totalnoncriticalscore,
    egd.maxtotalnoncriticalscore,
    egd.totalscoreunweighted,
    egd.maxtotalscoreunweighted,
    egd.failedkillquestions,
    egd.comments,
    egd.updated
FROM
    evalQuestionGroupData egd;

COMMENT ON COLUMN vwEvalQuestionGroupData.comments IS 'Evaluation Question Group Comments'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.evaluationid IS 'Evaluation GUID'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.failedkillquestions IS 'Evaluation Question Group Falled Kill Questions'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.markedna IS 'Evaluation Question Group Marked Not Applicable (True/False)'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.maxtotalcriticalscore IS 'Evaluation Question Group Total Max Critical Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.maxtotalnoncriticalscore IS 'Evaluation Question GroupTotal Max Non Critical Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.maxtotalscore IS 'Evaluation Question Group Max Total Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.maxtotalscoreunweighted IS 'Evaluation Question Group Max Total Score Un-Weighted'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.questiongroupid IS 'Evaluation Question Group Comments'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.totalcriticalscore IS 'Evaluation Question Group Total Critical Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.totalnoncriticalscore IS 'Evaluation Question Group Total Non Critical Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.totalscore IS 'Evaluation Question Group Total Score'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.totalscoreunweighted IS 'Evaluation Question Group Total Score Un-Weighted'; 
COMMENT ON COLUMN vwEvalQuestionGroupData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON VIEW vwEvalQuestionGroupData IS 'Evaluation Question Group Detailed Data'; 