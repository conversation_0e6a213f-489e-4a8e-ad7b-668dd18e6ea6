CREATE OR ALTER VIEW vwUserInteractionPresenceDetailedData AS
SELECT
    userid,
    userdetail.name as agentname,
    userdetail.managerid as managerid,
    userdetail.managername,
    --userdetail.divisionid as divisionid,
    starttime,
    starttimeltc,
    endtime,
    endtimeltc,
    systempresence,
    orgpresence,
    routingstatus,
    conversationid,
    mediatype,
    timeinstate,
    timeinstate / 86400.00 as timeinstateDay
FROM
    userinteractionpresencedetaileddata
    left outer join vwUserDetail userdetail on userdetail.id = userid;