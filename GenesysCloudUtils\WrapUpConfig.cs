﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;

namespace GenesysCloudUtils
{
    public class WrapUpConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
         private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Wrapup Config V2.00.00");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetWrapUpDataFromGC()
        {
            Console.WriteLine("Get WrapUp Data");

            DataTable WrapUps = DBUtil.CreateInMemTable("wrapupDetails");
            int CurrentPage = 1;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int MaxPages = 1;

            while (CurrentPage <= MaxPages)
            {
                Console.Write("*");

                // Use JsonReturnHttpResponseGet for proper rate limit handling
                var response = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/routing/wrapupcodes?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                // Check HTTP status code before processing JSON
                if (response.StatusCode != 200)
                {
                    if (response.StatusCode == 429)
                    {
                        Console.WriteLine($"Rate limit encountered for wrap-up codes page {CurrentPage}. Response: {response.Content}");
                        throw new Exception($"Rate limiting exceeded retry limit for wrap-up codes page {CurrentPage}");
                    }
                    else
                    {
                        throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                    }
                }

                string JsonString = response.Content;
                JArray json;

                try
                {
                    // Parse the full JSON response first
                    var fullResponse = JObject.Parse(JsonString);

                    // Extract the entities array
                    if (fullResponse["entities"] != null)
                    {
                        json = (JArray)fullResponse["entities"];
                    }
                    else
                    {
                        // Fallback: try to parse as array directly
                        json = JArray.Parse(JsonString);
                    }

                    // Extract pagination info for backward compatibility
                    if (fullResponse["pageCount"] != null)
                    {
                        MaxPages = (int)fullResponse["pageCount"];
                        if (MaxPages > 30) // MaxPagesToRetrieve
                        {
                            MaxPages = 30;
                        }
                    }
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"JSON deserialization failed for wrap-up codes page {CurrentPage}: {jsonEx.Message}");
                    Console.WriteLine($"Response content: {JsonString}");
                    throw new Exception($"Failed to parse JSON response for wrap-up codes page {CurrentPage}", jsonEx);
                }

                DataTable TempWrapUps = ConvWrapUps(json);

                if (TempWrapUps.Rows.Count > 0)
                {
                    foreach (DataRow dr in TempWrapUps.Rows)
                    {
                        WrapUps.ImportRow(dr);
                    }
                    TempWrapUps = null;
                    json = null;
                }
                CurrentPage += 1;
            }
            Console.WriteLine("\nTotal WrapUps:{0} ", WrapUps.Rows.Count);

            return WrapUps;
        }

        private DataTable createWrapUpsTable()
        {
            DataTable DtTemp = new DataTable("WrapUps");

            DtTemp.Columns.Add("id", typeof(String));
            DtTemp.Columns.Add("name", typeof(String));
            DtTemp.Columns.Add("updated", typeof(String));
            return DtTemp;
        }

        private DataTable ConvWrapUps(dynamic json)
        {
            DataTable DtTemp = createWrapUpsTable();
            JsonActions.ConvJson(json, ref DtTemp);
            return DtTemp;
        }
    }
}
