CREATE
OR REPLACE VIEW vwadherenceexcdata AS
SELECT
    ad.userid,
    ad.startdate,
    ad.enddate,
    ad.startdateltc,
    ad.enddateltc,
    ad.durationsecs,
    ad.durationsecs / 86400.00 as durationsecsDay,
    ad.tolerance,
    ad.tolerance / 86400.00 as toleranceDay,
    ad.actualdurationsecs,
    ad.actualdurationsecs / 86400.00 as actualdurationsecsDay,
    ad.scheduledActivityCategory,
    ad.actualActivityCategory,
    ad.systemPresence,
    ad.routingStatus,
    ad.impact,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername
FROM
    adherenceexcData ad
    left outer join vwUserDetail ud on ud.id = ad.userid;