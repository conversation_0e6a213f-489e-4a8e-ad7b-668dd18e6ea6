DROP VIEW IF EXISTS vwuserqueuemappings CASCADE;
CREATE OR REPLACE VIEW vwuserqueuemappings AS
SELECT 
    qm.queueid AS queueid,
    qd.name AS queuename,
    qm.userid AS userid,
    ud.name AS name,
    ud.department AS department,
    ud.managername AS managername,
    qm.divisionid AS divisionid
FROM
    userQueueMappings qm 
    LEFT OUTER JOIN vwUserDetail ud ON qm.userid = ud.id
    LEFT OUTER JOIN vwQueueDetails qd ON qm.queueid = qd.id