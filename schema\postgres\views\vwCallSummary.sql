CREATE
OR REPLACE VIEW vwcallsummary AS
SELECT
    di.conversationid AS "Fact.ConversationId",
    qd.name AS "Fact.QueueName",
    di.conversationstartdate AS "Date.ConversationStartDate",
    di.conversationenddate AS "Date.ConversationEndDate",
    di.ani AS "Fact.ANI",
    di.dnis AS "Fact.DNIS",
    di.originaldirection AS "Fact.OriginalDirection",
    sum(di.ttalkcomplete) AS "Raw.Talk",
    sum(di.tacw) AS "Raw.ACW",
    sum(di.theldcomplete) AS "Raw.Hold",
    sum(di.tvoicemail) AS "Raw.Voicemail",
    sum(di.ntransferred) AS "Count.Transferred",
    sum(di.tabandon) AS "Raw.Abandon",
    sum(di.ttalkcomplete) / 86400.00 AS "Raw.TalkDay",
    sum(di.tacw) / 86400.00 AS "Raw.ACWDay",
    sum(di.theldcomplete) / 86400.00 AS "Raw.HoldDay",
    sum(di.tvoicemail) / 86400.00 AS "Raw.VoicemailDay",
    sum(di.ntransferred) :: numeric / 86400.00 AS "Count.TransferredDay",
    sum(di.tabandon) / 86400.00 AS "Raw.AbandonDay"
FROM
    detailedinteractiondata di
    LEFT JOIN queuedetails qd ON qd.id :: text = di.queueid :: text
GROUP BY
    di.conversationid,
    qd.name,
    di.conversationstartdate,
    di.conversationenddate,
    di.ani,
    di.dnis,
    di.originaldirection;

COMMENT ON COLUMN vwcallsummary."Fact.ConversationId" IS 'Fact: Conversation GUID';
COMMENT ON COLUMN vwcallsummary."Fact.QueueName" IS 'Fact: Queue Name';
COMMENT ON COLUMN vwcallsummary."Date.ConversationStartDate" IS 'Date: Conversation Start Date';
COMMENT ON COLUMN vwcallsummary."Date.ConversationEndDate" IS 'Date: Conversation End Date';
COMMENT ON COLUMN vwcallsummary."Fact.ANI" IS 'Fact: ANI';
COMMENT ON COLUMN vwcallsummary."Fact.DNIS" IS 'Fact: DNIS';
COMMENT ON COLUMN vwcallsummary."Fact.OriginalDirection" IS 'Fact: Original Direction';
COMMENT ON COLUMN vwcallsummary."Raw.Talk" IS 'Raw: Talk Time';
COMMENT ON COLUMN vwcallsummary."Raw.ACW" IS 'Raw: After Call Work Time';
COMMENT ON COLUMN vwcallsummary."Raw.Hold" IS 'Raw: Hold Time';
COMMENT ON COLUMN vwcallsummary."Raw.Voicemail" IS 'Raw: Voicemail Time';
COMMENT ON COLUMN vwcallsummary."Count.Transferred" IS 'Count: Transferred';
COMMENT ON COLUMN vwcallsummary."Raw.Abandon" IS 'Raw: Abandon Time';
COMMENT ON COLUMN vwcallsummary."Raw.TalkDay" IS 'Raw: Talk Time per Day';
COMMENT ON COLUMN vwcallsummary."Raw.ACWDay" IS 'Raw: After Call Work Time per Day';
COMMENT ON COLUMN vwcallsummary."Raw.HoldDay" IS 'Raw: Hold Time per Day';
COMMENT ON COLUMN vwcallsummary."Raw.VoicemailDay" IS 'Raw: Voicemail Time per Day';
COMMENT ON COLUMN vwcallsummary."Count.TransferredDay" IS 'Count: Transferred per Day';
COMMENT ON COLUMN vwcallsummary."Raw.AbandonDay" IS 'Raw: Abandon Time per Day';

COMMENT ON VIEW vwcallsummary IS 'See Callummary : View for call summary data with aggregated metrics';
