CREATE TABLE IF NOT EXISTS userpresencedata (
    keyid varchar(255) NOT NULL,
    id varchar(50),
    userid varchar(50),
    startdate timestamp without time zone NOT NULL,
    startdateltc timestamp without time zone,
    timetype varchar(20),
    systempresenceid varchar(50),
    presenceid varchar(50),
    presencetime numeric(20, 2),
    routingid varchar(50),
    routingtime numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedata_pkey PRIMARY KEY (keyid, startdate)
) PARTITION BY RANGE (startdate);

CREATE INDEX IF NOT EXISTS userpresencedatastart ON userpresencedata USING btree (startdate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedatastartltc ON userpresencedata USING btree (startdateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedatatype ON userpresencedata USING btree (
    timetype ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS userpresencedatauser ON userpresencedata USING btree (
    userid ASC NULLS LAST
);

ALTER TABLE IF EXISTS userpresdate_2018_12 RENAME TO userpresencedata_p2018_12;
ALTER TABLE IF EXISTS userpresdate_2019_01 RENAME TO userpresencedata_p2019_01;
ALTER TABLE IF EXISTS userpresdate_2019_02 RENAME TO userpresencedata_p2019_02;
ALTER TABLE IF EXISTS userpresdate_2019_03 RENAME TO userpresencedata_p2019_03;
ALTER TABLE IF EXISTS userpresdate_2019_04 RENAME TO userpresencedata_p2019_04;
ALTER TABLE IF EXISTS userpresdate_2019_05 RENAME TO userpresencedata_p2019_05;
ALTER TABLE IF EXISTS userpresdate_2019_06 RENAME TO userpresencedata_p2019_06;
ALTER TABLE IF EXISTS userpresdate_2019_07 RENAME TO userpresencedata_p2019_07;
ALTER TABLE IF EXISTS userpresdate_2019_08 RENAME TO userpresencedata_p2019_08;
ALTER TABLE IF EXISTS userpresdate_2019_09 RENAME TO userpresencedata_p2019_09;
ALTER TABLE IF EXISTS userpresdate_2019_10 RENAME TO userpresencedata_p2019_10;
ALTER TABLE IF EXISTS userpresdate_2019_11 RENAME TO userpresencedata_p2019_11;
ALTER TABLE IF EXISTS userpresdate_2019_12 RENAME TO userpresencedata_p2019_12;
ALTER TABLE IF EXISTS userpresdate_2020_01 RENAME TO userpresencedata_p2020_01;
ALTER TABLE IF EXISTS userpresdate_2020_02 RENAME TO userpresencedata_p2020_02;
ALTER TABLE IF EXISTS userpresdate_2020_03 RENAME TO userpresencedata_p2020_03;
ALTER TABLE IF EXISTS userpresdate_2020_04 RENAME TO userpresencedata_p2020_04;
ALTER TABLE IF EXISTS userpresdate_2020_05 RENAME TO userpresencedata_p2020_05;
ALTER TABLE IF EXISTS userpresdate_2020_06 RENAME TO userpresencedata_p2020_06;
ALTER TABLE IF EXISTS userpresdate_2020_07 RENAME TO userpresencedata_p2020_07;
ALTER TABLE IF EXISTS userpresdate_2020_08 RENAME TO userpresencedata_p2020_08;
ALTER TABLE IF EXISTS userpresdate_2020_09 RENAME TO userpresencedata_p2020_09;
ALTER TABLE IF EXISTS userpresdate_2020_10 RENAME TO userpresencedata_p2020_10;
ALTER TABLE IF EXISTS userpresdate_2020_11 RENAME TO userpresencedata_p2020_11;
ALTER TABLE IF EXISTS userpresdate_2020_12 RENAME TO userpresencedata_p2020_12;
ALTER TABLE IF EXISTS userpresdate_2021_01 RENAME TO userpresencedata_p2021_01;
ALTER TABLE IF EXISTS userpresdate_2021_02 RENAME TO userpresencedata_p2021_02;
ALTER TABLE IF EXISTS userpresdate_2021_03 RENAME TO userpresencedata_p2021_03;
ALTER TABLE IF EXISTS userpresdate_2021_04 RENAME TO userpresencedata_p2021_04;
ALTER TABLE IF EXISTS userpresdate_2021_05 RENAME TO userpresencedata_p2021_05;
ALTER TABLE IF EXISTS userpresdate_2021_06 RENAME TO userpresencedata_p2021_06;
ALTER TABLE IF EXISTS userpresdate_2021_07 RENAME TO userpresencedata_p2021_07;
ALTER TABLE IF EXISTS userpresdate_2021_08 RENAME TO userpresencedata_p2021_08;
ALTER TABLE IF EXISTS userpresdate_2021_09 RENAME TO userpresencedata_p2021_09;
ALTER TABLE IF EXISTS userpresdate_2021_10 RENAME TO userpresencedata_p2021_10;
ALTER TABLE IF EXISTS userpresdate_2021_11 RENAME TO userpresencedata_p2021_11;
ALTER TABLE IF EXISTS userpresdate_2021_12 RENAME TO userpresencedata_p2021_12;
ALTER TABLE IF EXISTS userpresdate_2022_01 RENAME TO userpresencedata_p2022_01;
ALTER TABLE IF EXISTS userpresdate_2022_02 RENAME TO userpresencedata_p2022_02;
ALTER TABLE IF EXISTS userpresdate_2022_03 RENAME TO userpresencedata_p2022_03;
ALTER TABLE IF EXISTS userpresdate_2022_04 RENAME TO userpresencedata_p2022_04;
ALTER TABLE IF EXISTS userpresdate_2022_05 RENAME TO userpresencedata_p2022_05;
ALTER TABLE IF EXISTS userpresdate_2022_06 RENAME TO userpresencedata_p2022_06;
ALTER TABLE IF EXISTS userpresdate_2022_07 RENAME TO userpresencedata_p2022_07;
ALTER TABLE IF EXISTS userpresdate_2022_08 RENAME TO userpresencedata_p2022_08;
ALTER TABLE IF EXISTS userpresdate_2022_09 RENAME TO userpresencedata_p2022_09;
ALTER TABLE IF EXISTS userpresdate_2022_10 RENAME TO userpresencedata_p2022_10;
ALTER TABLE IF EXISTS userpresdate_2022_11 RENAME TO userpresencedata_p2022_11;
ALTER TABLE IF EXISTS userpresdate_2022_12 RENAME TO userpresencedata_p2022_12;
