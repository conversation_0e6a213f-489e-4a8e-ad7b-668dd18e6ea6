CREATE OR REPLACE VIEW z_WFMScheduleData AS
SELECT
    sc.userid,
    sc.shiftstartdate,
    sc.shiftlengthtime,
    sc.activitystartdate,
    sc.activitystartdateltc,
    sc.activitylengthtime,
    sc.activitydescription,
    sc.activitycodeid,
    ad.category AS activitycategory,
    CAST(sc.activitypaid AS INTEGER) AS activitypaid,
	CAST(sc.shiftmanuallyeditted AS INTEGER) AS shiftmanuallyeditted,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername,
    ad.name AS activitycodedesc,
    bu.name AS businessunit,
    null as timeoffrequestid
FROM
    scheduleData sc
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = sc.userid
    LEFT OUTER JOIN activitycodeDetails ad ON ad.businessunitid = sc.buid
    AND ad.id = sc.activitycodeid
    LEFT OUTER JOIN buDetails AS bu ON bu.id = sc.buid
UNION
SELECT
    td.userid,
    td.businessunitdate AS shiftstartdate,
    CASE WHEN CAST(tdreq.isfulldayrequest AS INT) = 1 THEN 86400 ELSE td.length END as shiftlengthtime,
    td.businessunitdate AS activitystartdate,
    td.businessunitdate AS activitystartdateltc,
    CASE WHEN CAST(tdreq.isfulldayrequest AS INT) = 1 THEN 86400 ELSE td.length END as activitylengthtime,
    'Absent' as activitydescription,
    td.activitycode AS activitycodeid,
    'Absent' as activitycategory,
    0 as activitypaid,
    0 as shiftmanuallyeditted,
    userdet.name AS agentname,
    userdet.managerid AS managerid,
    userdet.managername,
    td.description AS activitycodedesc,
    businessunit.name AS businessunit,
    tdreq.id as timeoffrequestid
FROM
    timeoffData td
    LEFT OUTER JOIN vwUserDetail AS userdet ON userdet.id = td.userid
    LEFT OUTER JOIN activitycodeDetails ad ON ad.businessunitid = ad.businessunitid
    AND ad.id = td.activitycode
    LEFT OUTER JOIN buDetails AS businessunit ON businessunit.id = ad.businessunitid
    LEFT OUTER JOIN timeoffrequestData AS tdreq ON tdreq.id = td.timeoffrequestid;

COMMENT ON COLUMN z_WFMScheduleData.userid IS 'User GUID';
COMMENT ON COLUMN z_WFMScheduleData.shiftstartdate IS 'Shift Start Date';
COMMENT ON COLUMN z_WFMScheduleData.shiftlengthtime IS 'Shift Length Time in Seconds';
COMMENT ON COLUMN z_WFMScheduleData.activitystartdate IS 'Activity Start Date(UTC)';
COMMENT ON COLUMN z_WFMScheduleData.activitystartdateltc IS 'Activity Start Date (LTC)';
COMMENT ON COLUMN z_WFMScheduleData.activitylengthtime IS 'Activity Length Time in Seconds';
COMMENT ON COLUMN z_WFMScheduleData.activitydescription IS 'Activity Description';
COMMENT ON COLUMN z_WFMScheduleData.activitycodeid IS 'Activity Code GUID';
COMMENT ON COLUMN z_WFMScheduleData.activitycategory IS 'Activity Category';
COMMENT ON COLUMN z_WFMScheduleData.activitypaid IS 'Activity Paid or not';
COMMENT ON COLUMN z_WFMScheduleData.shiftmanuallyeditted IS 'Shift Manually Edited or not';
COMMENT ON COLUMN z_WFMScheduleData.agentname IS 'Agent Name';
COMMENT ON COLUMN z_WFMScheduleData.managerid IS 'Manager GUID';
COMMENT ON COLUMN z_WFMScheduleData.managername IS 'Manager Name';
COMMENT ON COLUMN z_WFMScheduleData.activitycodedesc IS 'Activity Code Description';
COMMENT ON COLUMN z_WFMScheduleData.businessunit IS 'Business Unit';
COMMENT ON COLUMN z_WFMScheduleData.timeoffrequestid IS 'Time Off Request GUID';

COMMENT ON VIEW z_WFMScheduleData IS ' See WFMScheduleData: Data combining schedule and time off information';
