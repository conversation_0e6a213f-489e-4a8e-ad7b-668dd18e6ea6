CREATE OR REPLACE VIEW vwadherencedaydata AS
	SELECT
    ad.userid,
    ad.startdate,
    ad.startdateltc,
    ad.dayStartOffsetSecs,
    ad.dayStartOffsetSecs / 86400.00 as dayStartOffsetSecsDay,
    ad.adherencePerc,
    ad.conformPerc,
    ad.impact,
    ad.adherenceScheduleSecs,
    ad.adherenceScheduleSecs / 86400.00 as adherenceScheduleSecsDay,
    ad.conformanceScheduleSecs,
    ad.conformanceScheduleSecs / 86400.00 as conformanceScheduleSecsDay,
    ad.conformanceActualSecs,
    ad.conformanceActualSecs / 86400.00 as conformanceActualSecsDay,
    ad.exceptionCount,
    ad.exceptionDurationSecs,
    ad.exceptionDurationSecs / 86400.00 as exceptionDurationSecsDay,
    ad.impactSeconds,
    ad.impactSeconds / 86400.00 as impactSecondsDay,
    ad.scheduleLengthSecs,
    ad.scheduleLengthSecs / 86400.00 as scheduleLengthSecsDay,
    ad.actualLengthSecs,
    ad.actualLengthSecs / 86400.00 as actualLengthSecsDay,
    ud.name as agentname,
    ud.managerid,
    ud.managername
FROM
    adherencedaydata ad
    LEFT JOIN vwuserdetail ud ON ud.id::text = ad.userid::text
;

COMMENT ON COLUMN vwadherencedayData.actualLengthSecs IS 'Total Actual Scheduled Time length'; 
COMMENT ON COLUMN vwadherencedayData.actualLengthSecsDay IS 'Total Actual Scheduled Time length in seconds'; 
COMMENT ON COLUMN vwadherencedayData.adherencePerc IS 'Adherence Percentage'; 
COMMENT ON COLUMN vwadherencedayData.adherenceScheduleSecs IS 'The Total Adherance Time that adherence is measured against'; 
COMMENT ON COLUMN vwadherencedayData.adherenceScheduleSecsDay IS 'The Total Adherance Time that adherence is measured against in seconds'; 
COMMENT ON COLUMN vwadherencedayData.agentname IS 'Agent Name'; 
COMMENT ON COLUMN vwadherencedayData.conformanceActualSecs IS 'Total Time Agent in Conformance'; 
COMMENT ON COLUMN vwadherencedayData.conformanceActualSecsDay IS 'Total Time Agent in Conformance'; 
COMMENT ON COLUMN vwadherencedayData.conformanceScheduleSecs IS 'The Total Conformance Time that adherence is measured against'; 
COMMENT ON COLUMN vwadherencedayData.conformanceScheduleSecsDay IS 'The Total Conformance Time that adherence is measured against in seconds'; 
COMMENT ON COLUMN vwadherencedayData.conformPerc IS 'Conformance Percentage'; 
COMMENT ON COLUMN vwadherencedayData.dayStartOffsetSecs IS 'Start of Day Offset'; 
COMMENT ON COLUMN vwadherencedayData.dayStartOffsetSecsDay IS 'Start of Day Offset in seconds'; 
COMMENT ON COLUMN vwadherencedayData.exceptionCount IS 'Total Exception Count'; 
COMMENT ON COLUMN vwadherencedayData.exceptionDurationSecs IS 'Impacted Seconds'; 
COMMENT ON COLUMN vwadherencedayData.impact IS 'Impact'; 
COMMENT ON COLUMN vwadherencedayData.impactSeconds IS 'Impacted Seconds'; 
COMMENT ON COLUMN vwadherencedayData.managerid IS 'Manager GUID'; 
COMMENT ON COLUMN vwadherencedayData.managername IS 'Manager Nam'; 
COMMENT ON COLUMN vwadherencedayData.scheduleLengthSecs IS 'Total Scheduled Time (Seconds)'; 
COMMENT ON COLUMN vwadherencedayData.scheduleLengthSecsDay IS 'Total Scheduled Time (Seconds)'; 
COMMENT ON COLUMN vwadherencedayData.startdate IS 'Start Date (UTC)'; 
COMMENT ON COLUMN vwadherencedayData.startdateltc IS 'Start Date (LTC)'; 
COMMENT ON COLUMN vwadherencedayData.userid IS 'User GUID'; 
COMMENT ON COLUMN vwadherencedayData.exceptionDurationSecsDay IS 'Exception Duration Seconds'; 
COMMENT ON VIEW vwadherencedayData IS 'Adherence Summarised Daily Data'; 