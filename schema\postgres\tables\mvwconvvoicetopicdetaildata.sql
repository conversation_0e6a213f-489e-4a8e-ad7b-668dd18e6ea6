CREATE TABLE IF NOT EXISTS mvwconvvoicetopicdetaildata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    starttime timestamp NOT NULL,
    starttimeltc timestamp NULL,
    participant varchar(50),
    duration numeric(20, 2),
    confidence numeric(20, 2),
    topicname varchar(200),
    topicid varchar(50),
    topicphrase varchar(200),
    transcriptphrase varchar(200),
    updated timestamp NULL,
    conversationstartdate timestamp NULL,
    conversationstartdateltc timestamp NULL,
    conversationenddate timestamp NULL,
    conversationenddateltc timestamp NULL,
    ttalkcomplete int4 NULL,
    ani varchar(400),
    dnis varchar(400),
    firstmediatype varchar(50),
    divisionid varchar(50),
    firstqueueid varchar(50),
    firstqueuename varchar(255),
    lastqueueid varchar(50),
    lastqueuename varchar(255),
    firstagentid varchar(50),
    firstagentname varchar(200),
    firstagentdept varchar(200),
    firstagentmanagerid varchar(50),
    firstagentmanagername varchar(200),
    lastagentid varchar(50),
    lastagentname varchar(200),
    lastagentdept varchar(200),
    lastagentmanagerid varchar(50),
    lastagentmanagername varchar(200),
    firstwrapupcode varchar(255),
    firstwrapupname varchar(255),
    lastwrapupcode varchar(255),
    lastwrapupname varchar(255),
    divisionname varchar(255),
    CONSTRAINT mvwconvvoicetopicdetaildata_pkey PRIMARY KEY (keyid, starttime)
);

ALTER TABLE mvwconvvoicetopicdetaildata 
ADD column IF NOT exists divisionname varchar(255);

CREATE INDEX IF NOT EXISTS idx_mvw_convstartdate ON mvwconvvoicetopicdetaildata (conversationstartdate);
