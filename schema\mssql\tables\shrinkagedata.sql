IF dbo.csg_table_exists('shrinkagedata') = 0
CREATE TABLE shrinkagedata (
    [keyid] [nvarchar](100) NOT NULL,
    [id] [nvarchar](50), 
    [startdate] [datetime],
    [enddate] [datetime],
    [activitycategory] [nvarchar](255),
    [scheduledShrinkageSeconds] [NUMERIC](20, 2),
    [scheduledShrinkagePercent] [NUMERIC](20, 2),
    [actualShrinkageSeconds] [NUMERIC](20, 2),
    [actualShrinkagePercent] [NUMERIC](20, 2),
    [paidShrinkageSeconds] [NUMERIC](20, 2),
    [unpaidShrinkageSeconds] [NUMERIC](20, 2),
    [plannedShrinkageSeconds] [NUMERIC](20, 2),
    [unplannedShrinkageSeconds] [NUMERIC](20, 2),
    [updated] [datetime],
    [businessUnitIds] [varchar](100),
    CONSTRAINT [PK_shrinkagedata] PRIMARY KEY ([keyid])
);
