CREATE
OR ALTER VIEW [z_vwCallAbandonedSummary] AS
SELECT
    det.conversationid,
    det.conversationstartdate,
    CONVERT(
        datetime,
        SWITCHOFFSET(det.conversationstartdate, '+10:00')
    ) as conversationstartdateLTC,
    det.conversationenddate,
    CONVERT(
        datetime,
        SWITCHOFFSET(det.conversationenddate, '+10:00')
    ) as conversationenddateLTC,
    det.segmentstartdate,
    CONVERT(
        datetime,
        SWITCHOFFSET(det.segmentstartdate, '+10:00')
    ) as segmentstartdateLTC,
    det.segmentenddate,
    CONVERT(
        datetime,
        SWITCHOFFSET(det.segmentenddate, '+10:00')
    ) as segmentenddateLTC,
    det.convtosegmentendtime as TotalCallTime,
    det.segmenttime as QueueTime,
    det.ani,
    det.dnis,
    det.queueid,
    que.name as queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    [detailedInteractionData] det
    left outer join queueDetails que ON det.queueid = que.id
WHERE
    det.segmenttype in ('delay', 'Interact', 'alert', 'ivr')
    and det.purpose in ('acd', 'ivr')
    and det.disconnectiontype = 'peer'
    and det.conversationenddate = segmentenddate