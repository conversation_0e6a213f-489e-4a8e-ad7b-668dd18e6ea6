CREATE TABLE IF NOT EXISTS flowoutcomedetails (
    id varchar(255) NOT NULL,
    name varchar(50) NOT NULL,
    divisionid varchar(50),
    description varchar(50),
    operationid varchar(50),
    operationcomplete bit(1),
    userid varchar(50),
    clientid varchar(50),
    errormessage varchar(255),
    errorcode varchar(50),
    actionname varchar(50),
    actionstatus varchar(50),
    updated timestamp without time zone,
    CONSTRAINT flowoutcomedetails_pkey PRIMARY KEY (id)
);

-- Add comments

COMMENT ON TABLE flowoutcomedetails IS 'Flow Outcome Details';
COMMENT ON COLUMN flowoutcomedetails.id IS 'Flow Outcome ID';
COMMENT ON COLUMN flowoutcomedetails.name IS 'Flow Outcome Name';
COMMENT ON COLUMN flowoutcomedetails.divisionid IS 'Division ID';
COMMENT ON COLUMN flowoutcomedetails.description IS 'Description';
COMMENT ON COLUMN flowoutcomedetails.operationid IS 'Operation ID';
COMMENT ON COLUMN flowoutcomedetails.operationcomplete IS 'Operation Complete';
COMMENT ON COLUMN flowoutcomedetails.userid IS 'User ID';
COMMENT ON COLUMN flowoutcomedetails.clientid IS 'Client ID';
COMMENT ON COLUMN flowoutcomedetails.errormessage IS 'Error Message';
COMMENT ON COLUMN flowoutcomedetails.errorcode IS 'Error Code';
COMMENT ON COLUMN flowoutcomedetails.actionname IS 'Action Name';
COMMENT ON COLUMN flowoutcomedetails.actionstatus IS 'Action Status';
COMMENT ON COLUMN flowoutcomedetails.updated IS 'Last Updated Timestamp';
