CREATE OR ALTER VIEW [vwUserPresenceData] AS
SELECT
    pd.keyid,
    pd.id,
    pd.userid,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername,
    ud.divisionid as divisionid,
    dd.name as divisionname,
    pd.startdate,
    pd.startdateltc,
    pd.systempresenceid,
    pd.presenceid,
    pd.presencetime,
    pd.presencetime / 86400.00 as presencetimeDay,
    pd.routingid,
    pd.routingtime,
    pd.routingtime / 86400.00 as routingtimeDay,
    pd.updated
FROM
    userPresenceData pd
    left outer join vwUserDetail ud on ud.id = pd.userid
    left outer join divisiondetails dd on dd.id = ud.divisionid;
