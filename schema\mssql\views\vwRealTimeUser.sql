CREATE
OR ALTER VIEW [vwRealTimeUser] AS
SELECT
    CASE
        WHEN rd.convstatus IS NOT NULL THEN rd.convstatus
        WHEN rd.systempresence = 'ON_QUEUE' THEN rd.routingstatus
        ELSE UPPER(REPLACE(rd.orgpresence, 'On Queue', 'ON_QUEUE'))
    END AS AgentStatus,
    CASE
        WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE' THEN rd.RoutStatTime
        ELSE rd.PresenceTime
    END AS AgentTime,
    (
        CASE
            WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
            WHEN rd.systempresence = 'ON_QUEUE' THEN rd.RoutStatTime
            ELSE rd.PresenceTime
        END
    ) / 86400.00 AS AgentTimeDay,
    managerid as managerid,
    managername,
    id as userid,
    name,
    systempresence,
    OrgPresence,
    routingstatus,
    routstarttime,
    RoutStatTime,
    RoutStatTimeDay,
    presenceid,
    presstarttime,
    PresenceTime,
    PresenceTimeDay,
    queuename,
    conversationid,
    media,
    direction,
    convstatus,
    convstatustime,
    adherencestate,
    adherencestarttime,
    impact,
    scheduledactivitycategory
FROM
    (
        SELECT
            rl.id,
            rl.name,
            ud.managerid,
            ud.managername,
            UPPER(
                REPLACE(rl.systempresence, 'On Queue', 'ON_QUEUE')
            ) AS systempresence,
            UPPER(REPLACE(pr.orgpresence, 'On Queue', 'ON_QUEUE')) AS OrgPresence,
            CASE
                WHEN (
                    rl.routingstatus = 'IDLE'
                    AND isnull(rl.cccallactive, 0) + isnull(rl.othcallactive, 0) + isnull(rl.cbcallactive, 0) + isnull(rl.cbothcallactive, 0) + isnull(rl.ccemailactive, 0) > 0
                ) THEN 'ALERT'
                ELSE rl.routingstatus
            END AS routingstatus,
            rl.routstarttime,
            DATEDIFF(s, rl.routstarttime, GETUTCDATE()) AS RoutStatTime,
            DATEDIFF(s, rl.routstarttime, GETUTCDATE()) / 86400.00 AS RoutStatTimeDay,
            rl.presenceid,
            rl.presstarttime,
            DATEDIFF(s, rl.presstarttime, GETUTCDATE()) AS PresenceTime,
            DATEDIFF(s, rl.presstarttime, GETUTCDATE()) / 86400.00 AS PresenceTimeDay,
            qd.name AS queuename,
            uc.conversationid,
            uc.media,
            uc.direction,
            uc.convstatus,
            uc.convstatustime,
            rl.adherencestate,
            rl.adherencestarttime,
            rl.impact,
            rl.scheduledactivitycategory
        FROM
            userRealTimeData AS rl
            LEFT OUTER JOIN presenceDetails AS pr ON pr.id = rl.presenceid
            LEFT OUTER JOIN vwRealTimeUserConv AS uc ON uc.userid = rl.id
            LEFT OUTER JOIN queueDetails AS qd ON qd.id = uc.queueid
            LEFT OUTER JOIN vwUserDetail ud ON ud.id = rl.id
    ) AS rd