CREATE
OR REPLACE VIEW vwchatdata AS
SELECT
    cd.conversationid,
    cd.conversationstart,
    cd.conversationstartltc,
    cd.userid,
    ud.name,
    ud.managerid as managerid,
    ud.managername,
    cd.chatinitiatedby,
    cd.agentchatcount,
    cd.agentchattotal,
    cd.agentchatmax,
    cd.agentchatmin,
    cd.custchatcount,
    cd.custchattotal,
    cd.custchatmax,
    cd.custchatmin
FROM
    chatData cd
    left outer join vwuserdetail ud ON ud.id = cd.userid;