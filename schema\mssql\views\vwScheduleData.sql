CREATE
OR ALTER VIEW [vwScheduleData] AS
SELECT
	sc.scheduleId,
	sc.userid,
	sc.shiftid,
	sc.shiftstartdate,
	sc.shiftstartdateltc,
	sc.shiftlengthtime,
	sc.activitystartdate,
	sc.activitystartdateltc,
	sc.activitylengthtime,
	sc.activitydescription,
	sc.activitycodeid,
	sc.activitypaid,
	sc.shiftmanuallyeditted,
	ud.name AS agentname,
	ud.managerid AS managerid,
	ud.managername,
	ad.name as activitycodedesc,
	COALESCE(NULLIF(ad.name, ''), ad.category) AS activitycodename
FROM
	scheduleData sc
	LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = sc.userid
	LEFT OUTER JOIN activitycodeDetails ad on ad.businessunitid = sc.buid
	and ad.id = sc.activitycodeid