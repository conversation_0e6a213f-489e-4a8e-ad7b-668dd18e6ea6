using System.ComponentModel;

namespace CSG.Adapter.Licensing
{
    /// <summary>
    /// Enum representing the different license types available
    /// </summary>
    public enum LicenseType
    {
        [Description("Basic license")]
        Basic = 1,
        
        [Description("Knowledge Quest license")]
        Knowledge_Quest = 2,
        
        [Description("Premium license")]
        Premium = 3
    }
}
