﻿using Microsoft.Extensions.Logging;

namespace StandardUtils;

public class ErrorMessages
{
    private readonly ILogger? _logger;

    [Obsolete("Use telemetry instead")]
    public ErrorMessages()
    {
    }

    [Obsolete("Use telemetry instead")]
    public ErrorMessages(ILogger? logger)
    {
        _logger = logger;
    }

    [Obsolete("Use telemetry instead")]
    public void SendErrorMessage(string CustomerId, string ErrorCode, String ErrorMessage)
    {
        if (_logger != null)
            _logger.LogCritical("SendErrorMessage: {0}, {1}", ErrorCode, ErrorMessage);
        else
            Console.WriteLine("SendErrorMessage: {0}, {1}", ErrorCode, ErrorMessage);
    }
}
