CREATE TABLE IF NOT EXISTS userpresencedetaileddata (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    starttime timestamp without time zone NOT NULL,
    starttimeltc timestamp without time zone,
    endtime timestamp without time zone,
    endtimeltc timestamp without time zone,
    systempresence varchar(50),
    orgpresence varchar(50),
    routingstatus varchar(50),
    timeinstate numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedetaileddata_pkey PRIMARY KEY (keyid, starttime)
) PARTITION BY RANGE (starttime);

CREATE INDEX IF NOT EXISTS userpresencedetend ON userpresencedetaileddata USING btree (endtime ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedetendltc ON userpresencedetaileddata USING btree (endtimeltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedetorgpres ON userpresencedetaileddata USING btree (
    orgpresence ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS userpresencedetrouting ON userpresencedetaileddata USING btree (
    routingstatus ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS userpresencedetstart ON userpresencedetaileddata USING btree (starttime ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedetstartltc ON userpresencedetaileddata USING btree (starttimeltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS userpresencedetsyspres ON userpresencedetaileddata USING btree (
    systempresence ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS userpresencedetuser ON userpresencedetaileddata USING btree (
    userid ASC NULLS LAST
);

ALTER TABLE IF EXISTS detprespartdate_2018_12 RENAME TO userpresencedetaileddata_p2018_12;
ALTER TABLE IF EXISTS detprespartdate_2019_01 RENAME TO userpresencedetaileddata_p2019_01;
ALTER TABLE IF EXISTS detprespartdate_2019_02 RENAME TO userpresencedetaileddata_p2019_02;
ALTER TABLE IF EXISTS detprespartdate_2019_03 RENAME TO userpresencedetaileddata_p2019_03;
ALTER TABLE IF EXISTS detprespartdate_2019_04 RENAME TO userpresencedetaileddata_p2019_04;
ALTER TABLE IF EXISTS detprespartdate_2019_05 RENAME TO userpresencedetaileddata_p2019_05;
ALTER TABLE IF EXISTS detprespartdate_2019_06 RENAME TO userpresencedetaileddata_p2019_06;
ALTER TABLE IF EXISTS detprespartdate_2019_07 RENAME TO userpresencedetaileddata_p2019_07;
ALTER TABLE IF EXISTS detprespartdate_2019_08 RENAME TO userpresencedetaileddata_p2019_08;
ALTER TABLE IF EXISTS detprespartdate_2019_09 RENAME TO userpresencedetaileddata_p2019_09;
ALTER TABLE IF EXISTS detprespartdate_2019_10 RENAME TO userpresencedetaileddata_p2019_10;
ALTER TABLE IF EXISTS detprespartdate_2019_11 RENAME TO userpresencedetaileddata_p2019_11;
ALTER TABLE IF EXISTS detprespartdate_2019_12 RENAME TO userpresencedetaileddata_p2019_12;
ALTER TABLE IF EXISTS detprespartdate_2020_01 RENAME TO userpresencedetaileddata_p2020_01;
ALTER TABLE IF EXISTS detprespartdate_2020_02 RENAME TO userpresencedetaileddata_p2020_02;
ALTER TABLE IF EXISTS detprespartdate_2020_03 RENAME TO userpresencedetaileddata_p2020_03;
ALTER TABLE IF EXISTS detprespartdate_2020_04 RENAME TO userpresencedetaileddata_p2020_04;
ALTER TABLE IF EXISTS detprespartdate_2020_05 RENAME TO userpresencedetaileddata_p2020_05;
ALTER TABLE IF EXISTS detprespartdate_2020_06 RENAME TO userpresencedetaileddata_p2020_06;
ALTER TABLE IF EXISTS detprespartdate_2020_07 RENAME TO userpresencedetaileddata_p2020_07;
ALTER TABLE IF EXISTS detprespartdate_2020_08 RENAME TO userpresencedetaileddata_p2020_08;
ALTER TABLE IF EXISTS detprespartdate_2020_09 RENAME TO userpresencedetaileddata_p2020_09;
ALTER TABLE IF EXISTS detprespartdate_2020_10 RENAME TO userpresencedetaileddata_p2020_10;
ALTER TABLE IF EXISTS detprespartdate_2020_11 RENAME TO userpresencedetaileddata_p2020_11;
ALTER TABLE IF EXISTS detprespartdate_2020_12 RENAME TO userpresencedetaileddata_p2020_12;
ALTER TABLE IF EXISTS detprespartdate_2021_01 RENAME TO userpresencedetaileddata_p2021_01;
ALTER TABLE IF EXISTS detprespartdate_2021_02 RENAME TO userpresencedetaileddata_p2021_02;
ALTER TABLE IF EXISTS detprespartdate_2021_03 RENAME TO userpresencedetaileddata_p2021_03;
ALTER TABLE IF EXISTS detprespartdate_2021_04 RENAME TO userpresencedetaileddata_p2021_04;
ALTER TABLE IF EXISTS detprespartdate_2021_05 RENAME TO userpresencedetaileddata_p2021_05;
ALTER TABLE IF EXISTS detprespartdate_2021_06 RENAME TO userpresencedetaileddata_p2021_06;
ALTER TABLE IF EXISTS detprespartdate_2021_07 RENAME TO userpresencedetaileddata_p2021_07;
ALTER TABLE IF EXISTS detprespartdate_2021_08 RENAME TO userpresencedetaileddata_p2021_08;
ALTER TABLE IF EXISTS detprespartdate_2021_09 RENAME TO userpresencedetaileddata_p2021_09;
ALTER TABLE IF EXISTS detprespartdate_2021_10 RENAME TO userpresencedetaileddata_p2021_10;
ALTER TABLE IF EXISTS detprespartdate_2021_11 RENAME TO userpresencedetaileddata_p2021_11;
ALTER TABLE IF EXISTS detprespartdate_2021_12 RENAME TO userpresencedetaileddata_p2021_12;
ALTER TABLE IF EXISTS detprespartdate_2022_01 RENAME TO userpresencedetaileddata_p2022_01;
ALTER TABLE IF EXISTS detprespartdate_2022_02 RENAME TO userpresencedetaileddata_p2022_02;
ALTER TABLE IF EXISTS detprespartdate_2022_03 RENAME TO userpresencedetaileddata_p2022_03;
ALTER TABLE IF EXISTS detprespartdate_2022_04 RENAME TO userpresencedetaileddata_p2022_04;
ALTER TABLE IF EXISTS detprespartdate_2022_05 RENAME TO userpresencedetaileddata_p2022_05;
ALTER TABLE IF EXISTS detprespartdate_2022_06 RENAME TO userpresencedetaileddata_p2022_06;
ALTER TABLE IF EXISTS detprespartdate_2022_07 RENAME TO userpresencedetaileddata_p2022_07;
ALTER TABLE IF EXISTS detprespartdate_2022_08 RENAME TO userpresencedetaileddata_p2022_08;
ALTER TABLE IF EXISTS detprespartdate_2022_09 RENAME TO userpresencedetaileddata_p2022_09;
ALTER TABLE IF EXISTS detprespartdate_2022_10 RENAME TO userpresencedetaileddata_p2022_10;
ALTER TABLE IF EXISTS detprespartdate_2022_11 RENAME TO userpresencedetaileddata_p2022_11;
ALTER TABLE IF EXISTS detprespartdate_2022_12 RENAME TO userpresencedetaileddata_p2022_12;

-- Add comments

COMMENT ON COLUMN userpresencedetaileddata.endtime IS 'End Time (UTC)'; 
COMMENT ON COLUMN userpresencedetaileddata.endtimeltc IS 'End Time (LTC)'; 
COMMENT ON COLUMN userpresencedetaileddata.keyid IS 'Primary Key'; 
COMMENT ON COLUMN userpresencedetaileddata.orgpresence IS 'Organisation Presence GUID'; 
COMMENT ON COLUMN userpresencedetaileddata.routingstatus IS 'Routing Status (GUID)'; 
COMMENT ON COLUMN userpresencedetaileddata.starttime IS 'Start Time (UTC)'; 
COMMENT ON COLUMN userpresencedetaileddata.starttimeltc IS 'Start Time (LTC)'; 
COMMENT ON COLUMN userpresencedetaileddata.systempresence IS 'System Presence GUID'; 
COMMENT ON COLUMN userpresencedetaileddata.timeinstate IS 'Time in State (Secs)'; 
COMMENT ON COLUMN userpresencedetaileddata.updated IS 'Date Row Updated (UTC)';
COMMENT ON COLUMN userpresencedetaileddata.userid IS 'Agent GUID'; 
COMMENT ON TABLE userpresencedetaileddata IS 'User Presence Detailed Data'; 
