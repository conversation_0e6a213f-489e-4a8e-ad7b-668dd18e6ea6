CREATE TABLE IF NOT EXISTS mvwevaluationgroupdata (
    keyid varchar(50) NOT NULL UNIQUE,
    evaluationid varchar(50),
    questiongroupid varchar(50),
    questiongroupname varchar(200),
    totalscore numeric(20, 2),
    maxtotalscore numeric(20, 2),
    markedna bit(1),
    totalcriticalscore numeric(20, 2),
    maxtotalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    maxtotalnoncriticalscore numeric(20, 2),
    totalscoreunweighted numeric(20, 2),
    maxtotalscoreunweighted numeric(20, 2),
    failedkillquestions bit(1),
    divisionid varchar(50),
    "comments" text NULL,
    conversationid varchar(50) NULL
);
CREATE INDEX IF NOT EXISTS mvevalgroupdatadivisionid ON mvwevaluationgroupdata USING btree (divisionid);
CREATE UNIQUE INDEX IF NOT EXISTS mvevalgroupdatakeyid ON mvwevaluationgroupdata USING btree (keyid);
CREATE INDEX IF NOT EXISTS mvwevalgroupdataevaluationid ON mvwevaluationgroupdata USING btree (evaluationid);
