DROP FUNCTION IF EXISTS csg_table_exists;
CREATE FUNCTION csg_table_exists(tablename varchar)
RETURNS integer AS $$
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = current_schema()
          AND lower(table_name) = lower(tablename)
    ) THEN 1 ELSE 0 END);
END;
$$ LANGUAGE plpgsql;
GO

DROP FUNCTION IF EXISTS csg_view_definition_contains_string;
CREATE FUNCTION csg_view_definition_contains_string(view_name varchar, expected_definition varchar)
RETURNS integer AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_schema = current_schema() AND lower(table_name) = lower(view_name)
    ) THEN
        -- View doesn't exist
        RETURN 2;
    END IF;

    IF position(expected_definition in pg_get_viewdef(view_name, true)) > 0 THEN
        -- Definition matches expectations.
        RETURN 1;
    END IF;

    -- Definition doesn't match expectations.
    RETURN 0;
END;
$$ LANGUAGE plpgsql;
GO

DROP FUNCTION IF EXISTS csg_column_exists;
CREATE OR REPLACE FUNCTION csg_column_exists(tablename varchar(255), columnname varchar(255))
RETURNS boolean AS $$
DECLARE
    column_count integer;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = %L AND LOWER(table_name) = LOWER(%L) AND LOWER(column_name) = LOWER(%L)',
                   current_schema(), tablename, columnname)
    INTO column_count;

    RETURN column_count > 0;
END;
$$ LANGUAGE plpgsql;
GO

CREATE OR REPLACE FUNCTION get_preferred_schema()
RETURNS TEXT AS $$
DECLARE
    schemas TEXT[];
    preferred_schema TEXT;
BEGIN
    SELECT array_agg(schema) INTO schemas
    FROM unnest(current_schemas(true)) AS s(schema)
    WHERE schema NOT LIKE 'pg_catalog'
    AND schema NOT LIKE 'pg_temp%'
    AND schema NOT LIKE 'pg_toast';

    IF schemas IS NOT NULL AND array_length(schemas, 1) > 0 THEN
        preferred_schema := schemas[1];
    ELSE
        preferred_schema := 'public';
    END IF;
    RETURN preferred_schema;
END;
$$ LANGUAGE plpgsql;
GO

-- Function to check if an index exists
DROP FUNCTION IF EXISTS csg_index_exists;
CREATE FUNCTION csg_index_exists(indexname varchar, tablename varchar)
RETURNS boolean AS $$
DECLARE
    index_count integer;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM pg_indexes WHERE indexname = %L AND tablename = %L',
                   lower(indexname), lower(tablename))
    INTO index_count;

    RETURN index_count > 0;
END;
$$ LANGUAGE plpgsql;
GO

-- Function to check if a constraint exists
DROP FUNCTION IF EXISTS csg_constraint_exists;
CREATE FUNCTION csg_constraint_exists(constraintname varchar, tablename varchar)
RETURNS boolean AS $$
DECLARE
    constraint_count integer;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM information_schema.table_constraints
                   WHERE constraint_name = %L AND table_name = %L',
                   lower(constraintname), lower(tablename))
    INTO constraint_count;

    RETURN constraint_count > 0;
END;
$$ LANGUAGE plpgsql;
GO

-- Function to check if a procedure exists
DROP FUNCTION IF EXISTS csg_procedure_exists;
CREATE FUNCTION csg_procedure_exists(procedurename varchar)
RETURNS boolean AS $$
DECLARE
    procedure_count integer;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM information_schema.routines
                   WHERE routine_name = %L AND routine_type = ''PROCEDURE''',
                   lower(procedurename))
    INTO procedure_count;

    RETURN procedure_count > 0;
END;
$$ LANGUAGE plpgsql;
GO

-- Function to safely recreate an index
DROP PROCEDURE IF EXISTS csg_recreate_index;
CREATE PROCEDURE csg_recreate_index(
    indexname varchar,
    tablename varchar,
    columnlist varchar,
    is_unique boolean DEFAULT false,
    include_columns varchar DEFAULT NULL
)
LANGUAGE plpgsql AS $$
BEGIN
    -- Drop the index if it exists
    EXECUTE format('DROP INDEX IF EXISTS %I', indexname);

    -- Create the index
    IF is_unique THEN
        IF include_columns IS NULL THEN
            EXECUTE format('CREATE UNIQUE INDEX %I ON %I (%s)',
                          indexname, tablename, columnlist);
        ELSE
            EXECUTE format('CREATE UNIQUE INDEX %I ON %I (%s) INCLUDE (%s)',
                          indexname, tablename, columnlist, include_columns);
        END IF;
    ELSE
        IF include_columns IS NULL THEN
            EXECUTE format('CREATE INDEX %I ON %I (%s)',
                          indexname, tablename, columnlist);
        ELSE
            EXECUTE format('CREATE INDEX %I ON %I (%s) INCLUDE (%s)',
                          indexname, tablename, columnlist, include_columns);
        END IF;
    END IF;
END;
$$;
GO

-- Function to simplify partitioning management
DROP PROCEDURE IF EXISTS csg_create_partition;
CREATE PROCEDURE csg_create_partition(
    tablename varchar,
    partition_column varchar,
    partition_type varchar, -- 'RANGE', 'LIST', or 'HASH'
    partition_key varchar,  -- For RANGE: 'date', 'integer', etc. For LIST: comma-separated values
    partition_interval varchar DEFAULT '1 month', -- For RANGE partitioning
    partition_count integer DEFAULT 12 -- Number of partitions to create ahead
)
LANGUAGE plpgsql AS $$
DECLARE
    partition_name varchar;
    start_date date;
    end_date date;
    i integer;
BEGIN
    -- Check if pg_partman extension is available
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_partman') THEN
        -- Use pg_partman if available
        IF partition_type = 'RANGE' AND partition_key = 'date' THEN
            EXECUTE format('SELECT partman.create_parent(
                %L,
                %L,
                ''partman'',
                %L
            )', tablename, partition_column, partition_interval);

            -- Run maintenance to create initial partitions
            PERFORM partman.run_maintenance(p_analyze := false);
        ELSE
            RAISE EXCEPTION 'Unsupported partition type or key for pg_partman';
        END IF;
    ELSE
        -- Manual partitioning if pg_partman is not available
        IF partition_type = 'RANGE' AND partition_key = 'date' THEN
            -- Create parent table if it doesn't exist
            EXECUTE format('CREATE TABLE IF NOT EXISTS %I (
                LIKE %I INCLUDING ALL
            ) PARTITION BY RANGE (%I)',
            tablename || '_parent', tablename, partition_column);

            -- Create partitions
            start_date := date_trunc('month', current_date);

            FOR i IN 0..partition_count LOOP
                start_date := start_date + (i * (partition_interval)::interval);
                end_date := start_date + (partition_interval)::interval;
                partition_name := tablename || '_p' || to_char(start_date, 'YYYYMM');

                EXECUTE format('CREATE TABLE IF NOT EXISTS %I
                    PARTITION OF %I
                    FOR VALUES FROM (%L) TO (%L)',
                    partition_name, tablename || '_parent', start_date, end_date);
            END LOOP;
        ELSE
            RAISE EXCEPTION 'Unsupported partition type or key for manual partitioning';
        END IF;
    END IF;
END;
$$;
GO