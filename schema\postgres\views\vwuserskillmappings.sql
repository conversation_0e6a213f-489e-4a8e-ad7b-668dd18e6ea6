DROP VIEW IF EXISTS vwuserskillmappings CASCADE;

CREATE OR REPLACE VIEW vwuserskillmappings AS
SELECT
    usm.keyid,
    usm.userid,
    ud.name AS username,
    ud.managerid,
    ud.managername,
    ud.divisionid,
    ud.divisionname,       -- Division name from vwuserdetail
    usm.skillid,
    sd.name AS skillname,
    usm.proficiency,
    usm.state AS mappingstate,
    usm.updated AS mappingupdated,
    sd.state AS skillstate,
    sd.updated AS skillupdated
FROM
    userskillmappings usm
    LEFT JOIN skilldetails sd ON usm.skillid = sd.id
    LEFT JOIN vwuserdetail ud ON usm.userid = ud.id;

-- Optional: Add comments to document the view and its columns
COMMENT ON COLUMN vwuserskillmappings.keyid IS 'Unique key identifier for the user-skill mapping';
COMMENT ON COLUMN vwuserskillmappings.userid IS 'Identifier for the user';
COMMENT ON COLUMN vwuserskillmappings.username IS 'Name of the user (from vwuserdetail)';
COMMENT ON COLUMN vwuserskillmappings.managerid IS 'Identifier for the user''s manager (from vwuserdetail)';
COMMENT ON COLUMN vwuserskillmappings.managername IS 'Name of the user''s manager (from vwuserdetail)';
COMMENT ON COLUMN vwuserskillmappings.divisionid IS 'Division identifier for the user (from vwuserdetail)';
COMMENT ON COLUMN vwuserskillmappings.divisionname IS 'Name of the division (from vwuserdetail)';
COMMENT ON COLUMN vwuserskillmappings.skillid IS 'Identifier for the skill';
COMMENT ON COLUMN vwuserskillmappings.skillname IS 'Name of the skill (from skilldetails)';
COMMENT ON COLUMN vwuserskillmappings.proficiency IS 'Proficiency level of the user for the skill';
COMMENT ON COLUMN vwuserskillmappings.mappingstate IS 'State of the mapping';
COMMENT ON COLUMN vwuserskillmappings.mappingupdated IS 'Timestamp when the mapping was last updated';
COMMENT ON COLUMN vwuserskillmappings.skillstate IS 'State of the skill (from skilldetails)';
COMMENT ON COLUMN vwuserskillmappings.skillupdated IS 'Timestamp when the skill record was last updated';
COMMENT ON VIEW vwuserskillmappings IS 'View combining user-skill mappings with detailed user information (including manager, division, and division name) and corresponding skill details';