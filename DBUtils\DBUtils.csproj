﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <NoWarn>CS8632</NoWarn>
    <!--
    CS8632  The annotation for nullable reference types should only be used in code within a '#nullable' annotations context
    -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MySql.Data" Version="8.0.30" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.6" />
    <PackageReference Include="Snowflake.Data" Version="2.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />
    <PackageReference Include="Z.EntityFramework.Extensions.EFCore" Version="6.14.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GCACommon\GCACommon.csproj" />
    <ProjectReference Include="..\StandardUtils\StandardUtils.csproj" />
  </ItemGroup>

</Project>
<!-- spell-checker: ignore: Npgsql -->