{"$schema": "http://json-schema.org/draft-04/schema#", "title": "Build Schema", "$ref": "#/definitions/build", "definitions": {"build": {"type": "object", "properties": {"Configuration": {"type": "string", "description": "Configuration to build - Default is 'Debug' (non-release) or 'Release' (release)", "enum": ["Debug", "Release"]}, "Continue": {"type": "boolean", "description": "Indicates to continue a previously failed build attempt"}, "DockerPassword": {"type": "string", "description": "Password for docker registry", "default": "Secrets must be entered via 'nuke :secrets [profile]'"}, "DockerRegistryName": {"type": "string", "description": "Name of the Docker registry"}, "DockerRegistryUrl": {"type": "string", "description": "Path to the docker registry"}, "DockerRuntime": {"type": "string", "description": "Runtime to use in Docker image"}, "DockerTagPrefix": {"type": "string", "description": "Prefix for the Docker image tag"}, "DockerUsername": {"type": "string", "description": "Username for docker registry"}, "Help": {"type": "boolean", "description": "Shows the help text for this build assembly"}, "Host": {"type": "string", "description": "Host for execution. <PERSON><PERSON><PERSON> is 'automatic'", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "AzurePipelines", "Bamboo", "Bitbucket", "Bitrise", "GitHubActions", "GitLab", "<PERSON>", "Rider", "SpaceAutomation", "TeamCity", "Terminal", "TravisCI", "VisualStudio", "VSCode"]}, "MinCoverage": {"type": "integer", "description": "Minimum code coverage percentage"}, "NoLogo": {"type": "boolean", "description": "Disables displaying the NUKE logo"}, "Partition": {"type": "string", "description": "Partition to use on CI"}, "Plan": {"type": "boolean", "description": "Shows the execution plan (HTML)"}, "Profile": {"type": "array", "description": "Defines the profiles to load", "items": {"type": "string"}}, "PublishRuntimes": {"type": "array", "description": "Runtimes to publish", "items": {"type": "string"}}, "Root": {"type": "string", "description": "Root directory during build execution"}, "Skip": {"type": "array", "description": "List of targets to be skipped. Empty list skips all dependencies", "items": {"type": "string", "enum": ["Clean", "Compile", "Coverage", "<PERSON><PERSON><PERSON>", "DockerAll", "DockerBuild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Publish", "Rest<PERSON>", "Test"]}}, "Solution": {"type": "string", "description": "Path to a solution file that is automatically loaded"}, "Target": {"type": "array", "description": "List of targets to be invoked. Default is '{default_target}'", "items": {"type": "string", "enum": ["Clean", "Compile", "Coverage", "<PERSON><PERSON><PERSON>", "DockerAll", "DockerBuild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Publish", "Rest<PERSON>", "Test"]}}, "Verbosity": {"type": "string", "description": "Logging verbosity during build execution. Default is 'Normal'", "enum": ["Minimal", "Normal", "Quiet", "Verbose"]}}}}}