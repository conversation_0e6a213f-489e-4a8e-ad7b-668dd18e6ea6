IF dbo.csg_table_exists('mvwconvvoicetopicdetaildata') = 0
CREATE TABLE mvwconvvoicetopicdetaildata (
    [keyid] varchar(100) NOT NULL,
    [conversationid] varchar(50),
    [starttime] datetime NOT NULL,
    [starttimeltc] datetime NULL,
    [participant] varchar(50),
    [duration] numeric(20, 2),
    [confidence] numeric(20, 2),
    [topicname] varchar(200),
    [topicid] varchar(50),
    [topicphrase] varchar(200),
    [transcriptphrase] varchar(200),
    [updated] datetime NULL,
    [conversationstartdate] datetime NULL,
    [conversationstartdateltc] datetime NULL,
    [conversationenddate] datetime NULL,
    [conversationenddateltc] datetime NULL,
    [ttalkcomplete] int NULL,
    [ani] varchar(400),
    [dnis] varchar(400),
    [firstmediatype] varchar(50),
    [divisionid] varchar(50),
    [firstqueueid] varchar(50),
    [firstqueuename] varchar(255),
    [lastqueueid] varchar(50),
    [lastqueuename] varchar(255),
    [firstagentid] varchar(50),
    [firstagentname] varchar(200),
    [firstagentdept] varchar(200),
    [firstagentmanagerid] varchar(50),
    [firstagentmanagername] varchar(200),
    [lastagentid] varchar(50),
    [lastagentname] varchar(200),
    [lastagentdept] varchar(200),
    [lastagentmanagerid] varchar(50),
    [lastagentmanagername] varchar(200),
    [firstwrapupcode] varchar(255),
    [firstwrapupname] varchar(255),
    [lastwrapupcode] varchar(255),
    [lastwrapupname] varchar(255),
    CONSTRAINT [mvwconvvoicetopicdetaildata_pkey] PRIMARY KEY ([keyid], [starttime])
);
