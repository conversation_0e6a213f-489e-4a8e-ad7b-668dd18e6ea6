# Schema Function Analyzer
# This script analyzes schema files across different database types to identify function calls
# and verify they exist in the corresponding function definition files.

# Configuration
$schemaRoot = "schema"
$dbTypes = @("mssql", "postgres", "snowflake")
$outputFile = "documentation\database\schema\SchemaFunctionAnalysisReport.md"

# Initialize results
$results = @()
$missingFunctions = @()
$unusedFunctions = @()
$inconsistentUsage = @()

# Function to extract function definitions from a file
function Extract-FunctionDefinitions {
    param (
        [string]$filePath,
        [string]$dbType
    )

    $content = Get-Content -Path $filePath -Raw
    $functions = @()

    switch ($dbType) {
        "mssql" {
            # Extract CREATE FUNCTION statements for MSSQL
            $pattern = "CREATE\s+(?:OR\s+ALTER\s+)?FUNCTION\s+(?:\[?dbo\]?\.)?\[?(\w+)\]?"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }

            # Extract CREATE PROCEDURE statements for MSSQL
            $pattern = "CREATE\s+(?:OR\s+ALTER\s+)?PROCEDURE\s+(?:\[?dbo\]?\.)?\[?(\w+)\]?"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }
        }
        "postgres" {
            # Extract CREATE FUNCTION statements for PostgreSQL
            $pattern = "CREATE\s+(?:OR\s+REPLACE\s+)?FUNCTION\s+(\w+)"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }

            # Extract CREATE PROCEDURE statements for PostgreSQL
            $pattern = "CREATE\s+(?:OR\s+REPLACE\s+)?PROCEDURE\s+(\w+)"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }
        }
        "snowflake" {
            # Extract CREATE FUNCTION statements for Snowflake
            $pattern = "create\s+(?:or\s+replace\s+)?function\s+(\w+)"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }

            # Extract CREATE PROCEDURE statements for Snowflake
            $pattern = "CREATE\s+(?:OR\s+REPLACE\s+)?PROCEDURE\s+(\w+)"
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functions += $match.Groups[1].Value
            }
        }
    }

    return $functions
}

# Function to extract function calls from a file
function Extract-FunctionCalls {
    param (
        [string]$filePath,
        [string]$dbType
    )

    $content = Get-Content -Path $filePath -Raw
    $functionCalls = @()

    switch ($dbType) {
        "mssql" {
            # Extract function calls like dbo.csg_table_exists, dbo.csg_column_exists, etc.
            $pattern = "(?:dbo\.)?(\w+)\s*\("
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functionName = $match.Groups[1].Value
                if ($functionName -notmatch "^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|EXEC|IF|WHILE|BEGIN|END)$") {
                    $functionCalls += $functionName
                }
            }
        }
        "postgres" {
            # Extract function calls for PostgreSQL
            $pattern = "(\w+)\s*\("
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functionName = $match.Groups[1].Value
                if ($functionName -notmatch "^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|EXECUTE|IF|WHILE|BEGIN|END)$") {
                    $functionCalls += $functionName
                }
            }
        }
        "snowflake" {
            # Extract function calls for Snowflake
            $pattern = "(\w+)\s*\("
            $matches = [regex]::Matches($content, $pattern)
            foreach ($match in $matches) {
                $functionName = $match.Groups[1].Value
                if ($functionName -notmatch "^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|EXECUTE|IF|WHILE|BEGIN|END)$") {
                    $functionCalls += $functionName
                }
            }
        }
    }

    return $functionCalls | Select-Object -Unique
}

# Main analysis loop
foreach ($dbType in $dbTypes) {
    Write-Host "Analyzing $dbType schema files..."
    $dbTypeDir = Join-Path -Path $schemaRoot -ChildPath $dbType

    # Skip if directory doesn't exist
    if (-not (Test-Path $dbTypeDir)) {
        Write-Host "Directory not found: $dbTypeDir" -ForegroundColor Yellow
        continue
    }

    # Get all function definition files
    $functionDir = Join-Path -Path $dbTypeDir -ChildPath "functions"
    if (Test-Path $functionDir) {
        $functionFiles = Get-ChildItem -Path $functionDir -Filter "*.sql" -Recurse
    } else {
        $functionFiles = @()
    }

    # Extract all defined functions
    $definedFunctions = @()
    foreach ($file in $functionFiles) {
        $functions = Extract-FunctionDefinitions -filePath $file.FullName -dbType $dbType
        $definedFunctions += $functions
    }
    $definedFunctions = $definedFunctions | Select-Object -Unique

    # Get all schema files (tables, views, etc.)
    $schemaFiles = @()
    $tablesDir = Join-Path -Path $dbTypeDir -ChildPath "tables"
    if (Test-Path $tablesDir) {
        $schemaFiles += Get-ChildItem -Path $tablesDir -Filter "*.sql" -Recurse
    }

    $viewsDir = Join-Path -Path $dbTypeDir -ChildPath "views"
    if (Test-Path $viewsDir) {
        $schemaFiles += Get-ChildItem -Path $viewsDir -Filter "*.sql" -Recurse
    }

    # Track all function calls across all schema files
    $allFunctionCalls = @()

    # Check each schema file for function calls
    foreach ($file in $schemaFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
        $functionCalls = Extract-FunctionCalls -filePath $file.FullName -dbType $dbType

        foreach ($functionCall in $functionCalls) {
            $allFunctionCalls += $functionCall

            # Check if the function call exists in defined functions
            if ($definedFunctions -notcontains $functionCall) {
                # Special case for common SQL functions and keywords
                if ($functionCall -notmatch "^(COUNT|SUM|AVG|MIN|MAX|DATEADD|DATEDIFF|GETDATE|CURRENT_DATE|CURRENT_TIMESTAMP|TO_DATE|TO_TIMESTAMP|CAST|CONVERT)$") {
                    $missingFunctions += [PSCustomObject]@{
                        DBType = $dbType
                        File = $relativePath
                        Function = $functionCall
                    }
                }
            }

            # Add to results
            $results += [PSCustomObject]@{
                DBType = $dbType
                File = $relativePath
                Function = $functionCall
                IsDefined = ($definedFunctions -contains $functionCall)
            }
        }
    }

    # Find unused functions
    foreach ($function in $definedFunctions) {
        if ($allFunctionCalls -notcontains $function) {
            $unusedFunctions += [PSCustomObject]@{
                DBType = $dbType
                Function = $function
            }
        }
    }

    # Check for inconsistent usage patterns
    if ($dbType -eq "mssql") {
        # Check if all table creations use csg_table_exists
        $tableCreationFiles = $schemaFiles | Where-Object { (Get-Content $_.FullName) -match "CREATE\s+TABLE" }
        foreach ($file in $tableCreationFiles) {
            $content = Get-Content -Path $file.FullName -Raw
            $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

            if ($content -match "CREATE\s+TABLE" -and $content -notmatch "csg_table_exists") {
                $inconsistentUsage += [PSCustomObject]@{
                    DBType = $dbType
                    File = $relativePath
                    Issue = "Table creation without csg_table_exists check"
                }
            }
        }

        # Check if all index creations use csg_index_exists
        $indexCreationFiles = $schemaFiles | Where-Object { (Get-Content $_.FullName) -match "CREATE\s+INDEX" }
        foreach ($file in $indexCreationFiles) {
            $content = Get-Content -Path $file.FullName -Raw
            $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

            if ($content -match "CREATE\s+INDEX" -and $content -notmatch "csg_index_exists") {
                $inconsistentUsage += [PSCustomObject]@{
                    DBType = $dbType
                    File = $relativePath
                    Issue = "Index creation without csg_index_exists check"
                }
            }
        }
    }

    if ($dbType -eq "postgres") {
        # Check if all table creations use appropriate existence checks
        $tableCreationFiles = $schemaFiles | Where-Object { (Get-Content $_.FullName) -match "CREATE\s+TABLE" }
        foreach ($file in $tableCreationFiles) {
            $content = Get-Content -Path $file.FullName -Raw
            $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

            if ($content -match "CREATE\s+TABLE" -and $content -notmatch "IF\s+NOT\s+EXISTS" -and $content -notmatch "csg_table_exists") {
                $inconsistentUsage += [PSCustomObject]@{
                    DBType = $dbType
                    File = $relativePath
                    Issue = "Table creation without existence check"
                }
            }
        }
    }

    if ($dbType -eq "snowflake") {
        # Check if all table creations use IF NOT EXISTS
        $tableCreationFiles = $schemaFiles | Where-Object { (Get-Content $_.FullName) -match "CREATE\s+TABLE" }
        foreach ($file in $tableCreationFiles) {
            $content = Get-Content -Path $file.FullName -Raw
            $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

            if ($content -match "CREATE\s+TABLE" -and $content -notmatch "IF\s+NOT\s+EXISTS") {
                $inconsistentUsage += [PSCustomObject]@{
                    DBType = $dbType
                    File = $relativePath
                    Issue = "Table creation without IF NOT EXISTS"
                }
            }
        }

        # Check for explicit index creation (not recommended in Snowflake)
        $indexCreationFiles = $schemaFiles | Where-Object { (Get-Content $_.FullName) -match "CREATE\s+INDEX" }
        foreach ($file in $indexCreationFiles) {
            $relativePath = $file.FullName.Replace((Get-Location).Path + "\", "")

            $inconsistentUsage += [PSCustomObject]@{
                DBType = $dbType
                File = $relativePath
                Issue = "Explicit index creation in Snowflake (not recommended)"
            }
        }
    }
}

# Generate report
$reportContent = @"
# Schema Function Analysis Report

This report analyzes the usage of functions across schema files for different database types.

## Missing Functions

These are function calls in schema files that don't have corresponding function definitions:

| Database Type | File | Function |
|---------------|------|----------|
"@

foreach ($item in $missingFunctions) {
    $reportContent += "`n| $($item.DBType) | $($item.File) | $($item.Function) |"
}

$reportContent += @"

## Unused Functions

These are defined functions that aren't called in any schema files:

| Database Type | Function |
|---------------|----------|
"@

foreach ($item in $unusedFunctions) {
    $reportContent += "`n| $($item.DBType) | $($item.Function) |"
}

$reportContent += @"

## Inconsistent Usage Patterns

These are files with inconsistent usage patterns:

| Database Type | File | Issue |
|---------------|------|-------|
"@

foreach ($item in $inconsistentUsage) {
    $reportContent += "`n| $($item.DBType) | $($item.File) | $($item.Issue) |"
}

$reportContent += @"

## Function Usage Summary

| Database Type | Function | Used In Files |
|---------------|----------|---------------|
"@

$functionUsage = $results | Group-Object -Property DBType, Function | ForEach-Object {
    $dbType, $function = $_.Name -split ", "
    $files = ($_.Group | Select-Object -ExpandProperty File) -join ", "

    [PSCustomObject]@{
        DBType = $dbType
        Function = $function
        Files = $files
        Count = $_.Count
    }
}

foreach ($item in $functionUsage) {
    $reportContent += "`n| $($item.DBType) | $($item.Function) | $($item.Count) files |"
}

# Save report
$reportContent | Out-File -FilePath $outputFile
Write-Host "Analysis complete. Report saved to $outputFile" -ForegroundColor Green
