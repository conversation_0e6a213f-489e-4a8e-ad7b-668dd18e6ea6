CREATE
OR ALTER VIEW vwheadcountforecast AS
select
	hcf.businessunitid,
	bu.name as businessunitname,
	hcf.planninggroup as planninggroup_id,
	pg.[name] as planninggroup_name,
	pgq.[name] as planninggroup_queuename,
	hcf.scheduleid,
	sd.shorttermforecastid,
	sd.description as scheduledesc,
	sd.published as sched_published,
	hcf.weekdate,
	hcf.startdate,
	hcf.startdateltc,
	hcf.requiredperinterval,
	hcf.requiredwithoutshrinkageperinterval,
	hcf.updated
from 
	headcountforecastdata hcf
	
LEFT JOIN budetails bu  ON hcf.businessunitid = bu.id
LEFT JOIN scheduledetails sd ON hcf.scheduleid= sd.scheduleid
LEFT JOIN planninggroupdetails pg  ON hcf.planninggroup = pg.id
LEFT JOIN queuedetails pgq ON pg.queueid = pgq.id;