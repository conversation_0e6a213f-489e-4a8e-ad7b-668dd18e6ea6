
CREATE TABLE IF NOT EXISTS evaldata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    calibrationid varchar(50),
    evaluationformid varchar(50),
    evaluatorid varchar(50),
    userid varchar(50),
    status varchar(20),
    totalscore numeric(20, 2),
    averagescore numeric(20, 2),
    lowscore numeric(20, 2),
    highscore numeric(20, 2),
    totalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    agenthasread number,
    releasedate timestamp without time zone,
    releasedateltc timestamp without time zone,
    assigneddate timestamp without time zone,
    assigneddateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT evaldata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE evaldata ALTER COLUMN calibrationid DROP NOT NULL;
