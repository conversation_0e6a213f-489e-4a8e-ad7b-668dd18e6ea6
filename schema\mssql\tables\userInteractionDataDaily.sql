IF dbo.csg_table_exists('userInteractionDataDaily') = 0
CREATE TABLE [userInteractionDataDaily](
    [keyid] [nvarchar](255) NOT NULL,
    [userid] [nvarchar](50),
    [direction] [nvarchar](50),
    [queueid] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [wrapupcode] [nvarchar](255),
    [startdate] [datetime],
    [talertcount] [int],
    [talerttimesum] [decimal](20, 2),
    [talerttimemax] [decimal](20, 2),
    [talerttimemin] [decimal](20, 2),
    [tansweredcount] [int],
    [tansweredtimesum] [decimal](20, 2),
    [tansweredtimemax] [decimal](20, 2),
    [tansweredtimemin] [decimal](20, 2),
    [ttalkcount] [int],
    [ttalktimesum] [decimal](20, 2),
    [ttalktimemax] [decimal](20, 2),
    [ttalktimemin] [decimal](20, 2),
    [ttalkcompletecount] [int],
    [ttalkcompletetimesum] [decimal](20, 2),
    [ttalkcompletetimemax] [decimal](20, 2),
    [ttalkcompletetimemin] [decimal](20, 2),
    [tnotrespondingcount] [int],
    [tnotrespondingtimesum] [decimal](20, 2),
    [tnotrespondingtimemax] [decimal](20, 2),
    [tnotrespondingtimemin] [decimal](20, 2),
    [theldcount] [int],
    [theldtimesum] [decimal](20, 2),
    [theldtimemax] [decimal](20, 2),
    [theldtimemin] [decimal](20, 2),
    [theldcompletecount] [int],
    [theldcompletetimesum] [decimal](20, 2),
    [theldcompletetimemax] [decimal](20, 2),
    [theldcompletetimemin] [decimal](20, 2),
    [thandlecount] [int],
    [thandletimesum] [decimal](20, 2),
    [thandletimemax] [decimal](20, 2),
    [thandletimemin] [decimal](20, 2),
    [tacwcount] [int],
    [tacwtimesum] [decimal](20, 2),
    [tacwtimemax] [decimal](20, 2),
    [tacwtimemin] [decimal](20, 2),
    [nconsult] [int],
    [nconsulttransferred] [int],
    [noutbound] [int],
    [nerror] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nconnected] [int],
    [tdialingcount] [int],
    [tdialingtimesum] [decimal](20, 2),
    [tdialingtimemax] [decimal](20, 2),
    [tdialingtimemin] [decimal](20, 2),
    [tcontactingcount] [int],
    [tcontactingtimesum] [decimal](20, 2),
    [tcontactingtimemax] [decimal](20, 2),
    [tcontactingtimemin] [decimal](20, 2),
    [tvoicemailcount] [int],
    [tvoicemailtimesum] [decimal](20, 2),
    [tvoicemailtimemax] [decimal](20, 2),
    [tvoicemailtimemin] [decimal](20, 2),
    [tuserresponsetimecount] [int],
    [tuserresponsetimetimesum] [NUMERIC](20, 2),
    [tuserresponsetimetimemax] [NUMERIC](20, 2),
    [tuserresponsetimetimemin] [NUMERIC](20, 2),
    [tagentresponsetimecount] [int],
    [tagentresponsetimetimesum] [NUMERIC](20, 2),
    [tagentresponsetimetimemax] [NUMERIC](20, 2),
    [tagentresponsetimetimemin] [NUMERIC](20, 2),
    [av1count] [INT],
    [av1timesum] [NUMERIC](20, 2),
    [av1timemax] [NUMERIC](20, 2),
    [av1timemin] [NUMERIC](20, 2),
    [av2count] [INT],
    [av2timesum] [NUMERIC](20, 2),
    [av2timemax] [NUMERIC](20, 2),
    [av2timemin] [NUMERIC](20, 2),
    [av3count] [INT],
    [av3timesum] [NUMERIC](20, 2),
    [av3timemax] [NUMERIC](20, 2),
    [av3timemin] [NUMERIC](20, 2),
    [av4count] [INT],
    [av4timesum] [NUMERIC](20, 2),
    [av4timemax] [NUMERIC](20, 2),
    [av4timemin] [NUMERIC](20, 2),
    [av5count] [INT],
    [av5timesum] [NUMERIC](20, 2),
    [av5timemax] [NUMERIC](20, 2),
    [av5timemin] [NUMERIC](20, 2),
    [av6count] [INT],
    [av6timesum] [NUMERIC](20, 2),
    [av6timemax] [NUMERIC](20, 2),
    [av6timemin] [NUMERIC](20, 2),
    [av7count] [INT],
    [av7timesum] [NUMERIC](20, 2),
    [av7timemax] [NUMERIC](20, 2),
    [av7timemin] [NUMERIC](20, 2),
    [av8count] [INT],
    [av8timesum] [NUMERIC](20, 2),
    [av8timemax] [NUMERIC](20, 2),
    [av8timemin] [NUMERIC](20, 2),
    [av9count] [INT],
    [av9timesum] [NUMERIC](20, 2),
    [av9timemax] [NUMERIC](20, 2),
    [av9timemin] [NUMERIC](20, 2),
    [av10count] [INT],
    [av10timesum] [NUMERIC](20, 2),
    [av10timemax] [NUMERIC](20, 2),
    [av10timemin] [NUMERIC](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userInteractionDataDaily] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('userInteractionDataDaily_Date', 'userInteractionDataDaily') = 0
CREATE INDEX [userInteractionDataDaily_Date] ON [userInteractionDataDaily] ([startdate]);
IF dbo.csg_index_exists('userInteractionDataDaily_Media', 'userInteractionDataDaily') = 0
CREATE INDEX [userInteractionDataDaily_Media] ON [userInteractionDataDaily] ([mediatype]);
IF dbo.csg_index_exists('userInteractionDataDaily_User', 'userInteractionDataDaily') = 0
CREATE INDEX [userInteractionDataDaily_User] ON [userInteractionDataDaily] ([userid]);
IF dbo.csg_index_exists('userInteractionDataDaily_WrapUp', 'userInteractionDataDaily') = 0
CREATE INDEX [userInteractionDataDaily_WrapUp] ON [userInteractionDataDaily] ([wrapupcode]);
IF dbo.csg_index_exists('userInteractionDataDaily_Queue', 'userInteractionDataDaily') = 0
CREATE INDEX [userInteractionDataDaily_Queue] ON [userInteractionDataDaily] ([queueid]);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av1count') = 0
    ALTER TABLE userInteractionDataDaily ADD av1count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av1count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av1timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av1timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av1timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av1timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av1timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av1timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av1timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av1timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av1timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av2count') = 0
    ALTER TABLE userInteractionDataDaily ADD av2count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av2count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av2timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av2timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av2timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av2timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av2timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av2timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av2timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av2timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av2timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av3count') = 0
    ALTER TABLE userInteractionDataDaily ADD av3count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av3count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av3timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av3timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av3timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av3timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av3timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av3timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av3timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av3timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av3timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av4count') = 0
    ALTER TABLE userInteractionDataDaily ADD av4count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av4count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av4timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av4timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av4timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av4timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av4timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av4timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av4timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av4timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av4timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av5count') = 0
    ALTER TABLE userInteractionDataDaily ADD av5count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av5count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av5timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av5timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av5timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av5timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av5timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av5timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av5timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av5timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av5timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av6count') = 0
    ALTER TABLE userInteractionDataDaily ADD av6count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av6count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av6timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av6timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av6timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av6timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av6timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av6timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av6timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av6timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av6timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av7count') = 0
    ALTER TABLE userInteractionDataDaily ADD av7count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av7count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av7timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av7timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av7timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av7timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av7timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av7timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av7timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av7timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av7timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av8count') = 0
    ALTER TABLE userInteractionDataDaily ADD av8count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av8count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av8timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av8timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av8timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av8timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av8timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av8timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av8timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av8timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av8timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av9count') = 0
    ALTER TABLE userInteractionDataDaily ADD av9count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av9count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av9timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av9timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av9timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av9timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av9timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av9timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av9timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av9timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av9timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av10count') = 0
    ALTER TABLE userInteractionDataDaily ADD av10count INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av10count INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'av10timesum') = 0
    ALTER TABLE userInteractionDataDaily ADD av10timesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av10timesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av10timemax') = 0
    ALTER TABLE userInteractionDataDaily ADD av10timemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av10timemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'av10timemin') = 0
    ALTER TABLE userInteractionDataDaily ADD av10timemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN av10timemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tuserresponsetimecount') = 0
    ALTER TABLE userInteractionDataDaily ADD tuserresponsetimecount INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tuserresponsetimecount INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'tuserresponsetimetimesum') = 0
    ALTER TABLE userInteractionDataDaily ADD tuserresponsetimetimesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tuserresponsetimetimesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tuserresponsetimetimemax') = 0
    ALTER TABLE userInteractionDataDaily ADD tuserresponsetimetimemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tuserresponsetimetimemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tuserresponsetimetimemin') = 0
    ALTER TABLE userInteractionDataDaily ADD tuserresponsetimetimemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tuserresponsetimetimemin NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tagentresponsetimecount') = 0
    ALTER TABLE userInteractionDataDaily ADD tagentresponsetimecount INT;
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tagentresponsetimecount INT;

IF dbo.csg_column_exists('userInteractionDataDaily', 'tagentresponsetimetimesum') = 0
    ALTER TABLE userInteractionDataDaily ADD tagentresponsetimetimesum NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tagentresponsetimetimesum NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tagentresponsetimetimemax') = 0
    ALTER TABLE userInteractionDataDaily ADD tagentresponsetimetimemax NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tagentresponsetimetimemax NUMERIC(20, 2);

IF dbo.csg_column_exists('userInteractionDataDaily', 'tagentresponsetimetimemin') = 0
    ALTER TABLE userInteractionDataDaily ADD tagentresponsetimetimemin NUMERIC(20, 2);
ELSE
    ALTER TABLE userInteractionDataDaily ALTER COLUMN tagentresponsetimetimemin NUMERIC(20, 2);