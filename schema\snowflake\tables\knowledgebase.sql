CREATE TABLE IF NOT EXISTS knowledgebase (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(255),
    coreLanguage VARCHAR(50),
    dateCreated timestamp without time zone,
    dateModified timestamp without time zone,
    dateDocumentLastModified timestamp without time zone,
    faqCount integer,
    articleCount integer,
    published boolean,
    updated timestamp without time zone,
    CONSTRAINT knowledgebase_pkey PRIMARY KEY (id)
);
