DROP PROCEDURE IF EXISTS update_mvwconvvoiceoverviewdata(date);

DROP PROCEDURE IF EXISTS update_mvwconvvoiceoverviewdata();

CREATE OR REPLACE PROCEDURE update_mvwconvvoiceoverviewdata()
LANGUAGE plpgsql
AS $$
DECLARE
    record_count bigint;
    effective_from_date date;
BEGIN
    -- Check if the mvwconvvoiceoverviewdata table is empty
    SELECT COUNT(*) INTO record_count FROM mvwconvvoiceoverviewdata;

    -- Determine the effective from_date dynamically
    IF record_count = 0 THEN
        -- If the table is empty, update from '2000-01-01'
        effective_from_date := '2000-01-01';
    ELSE
        -- If the table is not empty, update from 3 months ago
        effective_from_date := now() - interval '30 days';
    END IF;

    -- Insert or update data based on the determined effective_from_date
    INSERT INTO mvwconvvoiceoverviewdata
    (
        SELECT
            cv.conversationid,
            cv.sentimentscore,
            cv.sentimenttrend,
            cv.agentdurationpercentage,
            cv.customerdurationpercentage,
            cv.silencedurationpercentage,
            cv.overtalkdurationpercentage,
            cv.overtalkcount,
            cv.ivrdurationpercentage,
            cv.acddurationpercentage,
            cv.otherdurationpercentage,
            cs.conversationstartdate,
            cs.conversationstartdateltc,
            cs.conversationenddate,
            cs.conversationenddateltc,
            cs.ttalkcomplete,
            cs.ani,
            cs.dnis,
            cs.firstmediatype,
            cs.divisionid,
            cs.firstqueueid,
            qd1.name AS firstqueuename,
            cs.lastqueueid,
            qd2.name AS lastqueuename,
            cs.firstagentid,
            ud1.name AS firstagentname,
            ud1.department AS firstagentdept,
            ud1.managerid AS firstagentmanagerid,
            ud1.managername AS firstagentmanagername,
            cs.lastagentid,
            ud2.name AS lastagentname,
            ud2.department AS lastagentdept,
            ud2.managerid AS lastagentmanagerid,
            ud2.managername AS lastagentmanagername,
            cs.firstwrapupcode,
            wd1.name AS firstwrapupname,
            cs.lastwrapupcode,
            wd2.name AS lastwrapupname,
            dd.name as divisionname
        FROM convvoiceoverviewdata cv
        LEFT JOIN convsummarydata cs ON cs.conversationid::text = cv.conversationid::text
        LEFT JOIN divisiondetails dd ON cs.divisionid::text = dd.id::text
        LEFT JOIN queuedetails qd1 ON qd1.id::text = cs.firstqueueid::text
        LEFT JOIN queuedetails qd2 ON qd2.id::text = cs.lastqueueid::text
        LEFT JOIN wrapupdetails wd1 ON wd1.id::text = cs.firstwrapupcode::text
        LEFT JOIN wrapupdetails wd2 ON wd2.id::text = cs.lastwrapupcode::text
        LEFT JOIN vwuserdetail ud1 ON ud1.id::text = cs.firstagentid::text
        LEFT JOIN vwuserdetail ud2 ON ud2.id::text = cs.lastagentid::text
        WHERE cs.conversationstartdate >= effective_from_date
    )
    ON CONFLICT ON CONSTRAINT mvwconvvoiceoverviewdata_conversationid_key
    DO UPDATE SET
        sentimentscore = EXCLUDED.sentimentscore,
        sentimenttrend = EXCLUDED.sentimenttrend,
        agentdurationpercentage = EXCLUDED.agentdurationpercentage,
        customerdurationpercentage = EXCLUDED.customerdurationpercentage,
        silencedurationpercentage = EXCLUDED.silencedurationpercentage,
        overtalkdurationpercentage = EXCLUDED.overtalkdurationpercentage,
        overtalkcount = EXCLUDED.overtalkcount,
        ivrdurationpercentage = EXCLUDED.ivrdurationpercentage,
        acddurationpercentage = EXCLUDED.acddurationpercentage,
        otherdurationpercentage = EXCLUDED.otherdurationpercentage,
        conversationstartdate = EXCLUDED.conversationstartdate,
        conversationstartdateltc = EXCLUDED.conversationstartdateltc,
        conversationenddate = EXCLUDED.conversationenddate,
        conversationenddateltc = EXCLUDED.conversationenddateltc,
        ttalkcomplete = EXCLUDED.ttalkcomplete,
        ani = EXCLUDED.ani,
        dnis = EXCLUDED.dnis,
        firstmediatype = EXCLUDED.firstmediatype,
        divisionid = EXCLUDED.divisionid,
        firstqueueid = EXCLUDED.firstqueueid,
        firstqueuename = EXCLUDED.firstqueuename,
        lastqueueid = EXCLUDED.lastqueueid,
        lastqueuename = EXCLUDED.lastqueuename,
        firstagentid = EXCLUDED.firstagentid,
        firstagentname = EXCLUDED.firstagentname,
        firstagentdept = EXCLUDED.firstagentdept,
        firstagentmanagerid = EXCLUDED.firstagentmanagerid,
        firstagentmanagername = EXCLUDED.firstagentmanagername,
        lastagentid = EXCLUDED.lastagentid,
        lastagentname = EXCLUDED.lastagentname,
        lastagentdept = EXCLUDED.lastagentdept,
        lastagentmanagerid = EXCLUDED.lastagentmanagerid,
        lastagentmanagername = EXCLUDED.lastagentmanagername,
        firstwrapupcode = EXCLUDED.firstwrapupcode,
        firstwrapupname = EXCLUDED.firstwrapupname,
        lastwrapupcode = EXCLUDED.lastwrapupcode,
        lastwrapupname = EXCLUDED.lastwrapupname,
        divisionname = EXCLUDED.divisionname;
END;
$$;

-- Example call
CALL update_mvwconvvoiceoverviewdata();
