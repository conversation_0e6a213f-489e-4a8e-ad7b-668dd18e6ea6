CREATE TABLE IF NOT EXISTS scheduledata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    buid varchar(50),
    scheduleid varchar(50),
    shiftid integer,
    shiftstartdate timestamp without time zone,
    shiftstartdateltc timestamp without time zone,
    shiftlengthtime integer,
    activitystartdate timestamp without time zone,
    activitystartdateltc timestamp without time zone,
    activitylengthtime integer,
    activitydescription varchar(200),
    activitycodeid varchar(50),
    activitypaid bit(1),
    shiftmanuallyeditted bit(1),
    updated timestamp without time zone,
    CONSTRAINT scheduledata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

ALTER TABLE scheduledata
ADD COLUMN IF NOT EXISTS shiftstartdateltc TIMESTAMP WITHOUT TIME ZONE;

COMMENT ON COLUMN scheduleData.userid IS 'Agent GUID';
COMMENT ON COLUMN scheduleData.buid	IS 'Business Unit GUID';
COMMENT ON COLUMN scheduleData.scheduleid IS 'Schedule GUID';
COMMENT ON COLUMN scheduleData.shiftid IS 'Shift GUID';
COMMENT ON COLUMN scheduleData.shiftstartdate IS 'Shift Start Date (UTC)';
COMMENT ON COLUMN scheduleData.shiftlengthtime IS 'Shift Length (Mins)';
COMMENT ON COLUMN scheduleData.activitystartdate IS 'Activity Start Date (UTC)';
COMMENT ON COLUMN scheduleData.activitystartdateltc IS 'Activity Start Date (LTC)';
COMMENT ON COLUMN scheduleData.activitylengthtime IS 'Activity Length (Mins)';
COMMENT ON COLUMN scheduleData.activitydescription IS 'Activity Description';
COMMENT ON COLUMN scheduleData.activitycodeid IS 'Activity GUID';
COMMENT ON COLUMN scheduleData.activitypaid	IS 'Activity is Paid Work? (True/False)';
COMMENT ON COLUMN scheduleData.shiftmanuallyeditted IS 'Shift Manually Editted (True/False)';
COMMENT ON COLUMN scheduleData.updated IS 'Date Row Updated (UTC)';