# Database Documentation

This directory contains documentation for the database components of the Genesys Adapter.

## Schema Documentation

The `schema` directory contains documentation for the database schema, including:

- [Database Function Documentation](schema/DatabaseFunctionDocumentation.md): Comprehensive documentation for all database functions used across different database types.
- [Schema Review Checklist](schema/SchemaReviewChecklist.md): A checklist for reviewing schema files across different database types.
- [Implementation Plan](schema/ImplementationPlan.md): A plan for implementing and standardizing functions across all schema files.
- [Schema Templates](schema/SchemaTemplates.md): Standardized templates for creating schema files for different database types.
- [Schema Function Analysis Report](schema/SchemaFunctionAnalysisReport.md): Analysis of function usage across schema files.
- [Schema Function Analyzer](schema/SchemaFunctionAnalyzer.ps1): PowerShell script to analyze function usage in schema files.

## Supported Database Types

The Genesys Adapter supports the following database types:

- Microsoft SQL Server (MSSQL)
- PostgreSQL
- Snowflake

Each database type has its own set of schema files and functions to handle the specific features and syntax of that database.

## Database Functions

The Genesys Adapter uses a set of utility functions to manage database objects consistently across different database types. These functions are defined in the `installfunctions.sql` file for each database type:

- `schema\mssql\functions\installfunctions.sql`
- `schema\postgres\functions\installfunctions.sql`
- `schema\snowflake\functions\installfunctions.sql`

See the [Database Function Documentation](schema/DatabaseFunctionDocumentation.md) for details on each function.

## Schema Templates

To ensure consistency when creating new schema files, use the templates provided in the [Schema Templates](schema/SchemaTemplates.md) document. These templates include examples for creating tables, adding columns, creating indexes, and adding foreign keys for each database type.

## Best Practices

When working with the database schema, follow these best practices:

1. **Use existence checks**: Always check if objects exist before creating or modifying them.
2. **Consistent naming**: Use consistent naming conventions for tables, columns, indexes, and constraints.
3. **Documentation**: Include comments explaining the purpose of tables, columns, and complex logic.
4. **Error handling**: Implement proper error handling in procedures and functions.
5. **Database-specific features**: Leverage the specific features of each database type (e.g., partitioning in PostgreSQL, automatic indexing in Snowflake).
6. **Cross-database consistency**: Ensure that table and column definitions are consistent across database types.

See the [Schema Review Checklist](schema/SchemaReviewChecklist.md) for a comprehensive list of best practices for each database type.
