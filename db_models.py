from __future__ import annotations
import uuid
from typing import List, Optional
from decimal import Decimal

from sqlalchemy import String, Boolean, Numeric, Text, ForeignKey
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy.orm import validates

class Base(DeclarativeBase):
    pass

def gen_uuid() -> str:
    return str(uuid.uuid4())

class _DecimalMixin:
    @validates("satisfaction_sentiment")
    def _cast_decimal(self, key, value):
        return Decimal(value) if value is not None and not isinstance(value, Decimal) else value
    
class KQAnalysis(Base, _DecimalMixin):
    __tablename__ = "kq_analysis"

    id: Mapped[str]                  = mapped_column(
        "kq_analysisid", String(36), primary_key=True, default=gen_uuid
    )
    conversationid: Mapped[str]      = mapped_column(String(36), nullable=False)
    communicationid: Mapped[str]     = mapped_column(String(36), nullable=False)
    call_summary: Mapped[Optional[str]]       = mapped_column(Text, nullable=True)
    call_resolution: Mapped[Optional[str]]    = mapped_column(String(50), nullable=True)
    callback_required: Mapped[Optional[bool]] = mapped_column(<PERSON>olean, nullable=True)
    selfservice_attempted: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    selfservice_problems: Mapped[Optional[bool]]  = mapped_column(Boolean, nullable=True)
    satisfaction_sentiment: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))

    taxonomies: Mapped[list["KQAnalysisTaxonomy"]] = relationship(
        back_populates="analysis",
        cascade="all, delete-orphan",
        lazy="noload",
    )
    questions: Mapped[list["KQAnalysisQuestion"]] = relationship(
        back_populates="analysis",
        cascade="all, delete-orphan",
        lazy="noload",
    )

    def __repr__(self) -> str:
        return f"<KQAnalysis id={self.id!r} conversationid={self.conversationid!r}>"

class KQAnalysisTaxonomy(Base):
    __tablename__ = "kq_analysis_taxonomy"

    id: Mapped[str]                 = mapped_column(
        "kq_analysistaxonomyid", String(36), primary_key=True, default=gen_uuid
    )
    kq_analysisid: Mapped[str]      = mapped_column(
        String(36), ForeignKey("kq_analysis.kq_analysisid"), nullable=False
    )
    taxonomy: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    analysis: Mapped[KQAnalysis] = relationship(back_populates="taxonomies")

    def __repr__(self) -> str:
        return f"<KQAnalysisTaxonomy id={self.id!r} taxonomy={self.taxonomy!r}>"

class KQAnalysisQuestion(Base):
    __tablename__ = "kq_analysis_question"

    id: Mapped[str]                      = mapped_column(
        "kq_analysisquestionid", String(36), primary_key=True, default=gen_uuid
    )
    kq_analysisid: Mapped[str]           = mapped_column(
        String(36), ForeignKey("kq_analysis.kq_analysisid"), nullable=False
    )
    question: Mapped[Optional[str]]      = mapped_column(Text, nullable=True)
    answer: Mapped[Optional[str]]        = mapped_column(Text, nullable=True)
    taxonomy: Mapped[Optional[str]]      = mapped_column(String(200), nullable=True)
    knowledgeid: Mapped[Optional[str]]   = mapped_column(String(36), nullable=True)
    knowledge_confidence: Mapped[Optional[float]] = mapped_column(Numeric(5,2), nullable=True)

    analysis: Mapped[KQAnalysis] = relationship(back_populates="questions")

    def __repr__(self) -> str:
        return f"<KQAnalysisQuestion id={self.id!r} question={self.question!r}>"