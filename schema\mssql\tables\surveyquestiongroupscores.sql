IF dbo.csg_table_exists('surveyQuestionGroupScores') = 0
CREATE TABLE [surveyQuestionGroupScores] (
    [surveyId]                      [nvarchar](50) NOT NULL,
    [conversationId]                [nvarchar](50) NOT NULL,
    [surveyFormId]                  [nvarchar](50),
    [surveyName]                    [nvarchar](200),
    [agentId]                       [nvarchar](50),
    [agentTeamId]                   [nvarchar](50),
    [queueId]                       [nvarchar](50),
    [questionGroupId]               [nvarchar](50) NOT NULL,
    [questionGroupName]             [nvarchar](200),
    [questionGroupTotalScore]       [decimal](20, 2),
    [questionGroupMaxTotalScore]    [decimal](20, 2),
    [questionGroupMarkedNa]         [bit],
    [updated]                       [datetime],
    CONSTRAINT [PK_surveyquestiongroupscores] PRIMARY KEY ([surveyId], [questionGroupId])
);
