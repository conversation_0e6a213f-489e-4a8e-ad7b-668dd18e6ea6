CREATE TABLE IF NOT EXISTS chatdata (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50),
    conversationstart timestamp without time zone,
    conversationstartltc timestamp without time zone,
    userid varchar(50),
    chatinitiatedby varchar(10),
    agentchatcount integer,
    agentchattotal numeric(20, 0),
    agentchatmax numeric(20, 0),
    agentchatmin numeric(20, 0),
    agenthasread numeric(20, 2),
    custchatcount integer,
    custchattotal numeric(20, 0),
    custchatmax numeric(20, 0),
    custchatmin numeric(20, 0),
    updated timestamp without time zone,
    mediatype varchar(10),
    CONSTRAINT chatdata_pkey PRIMARY KEY (keyid)
);

-- Add columns if they don't exist (for existing installations)
ALTER TABLE chatdata 
ADD COLUMN IF NOT EXISTS agenthasread numeric(20, 2);

ALTER TABLE chatdata 
ADD COLUMN IF NOT EXISTS mediatype varchar(10);
