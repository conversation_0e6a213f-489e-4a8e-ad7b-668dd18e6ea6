CREATE TABLE IF NOT EXISTS chatdata (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50),
    conversationstart timestamp without time zone,
    conversationstartltc timestamp without time zone,
    userid varchar(50),
    chatinitiatedby varchar(10),
    agentchatcount integer,
    agentchattotal numeric(20, 0),
    agentchatmax numeric(20, 0),
    agentchatmin numeric(20, 0),
    agenthasread numeric(20, 2),
    custchatcount integer,
    custchattotal numeric(20, 0),
    custchatmax numeric(20, 0),
    custchatmin numeric(20, 0),
    updated timestamp without time zone,
    CONSTRAINT chatdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE chatdata 
ADD column IF NOT exists agenthasread numeric(20, 2);


COMMENT ON COLUMN chatData.agentchatcount IS 'Agent Chat Count'; 
COMMENT ON COLUMN chatData.agentchatmax IS 'Agent Chat Response Max Time (Seconds)'; 
COMMENT ON COLUMN chatData.agentchatmin IS 'Agent Chat Response Min Time (Seconds)'; 
COMMENT ON COLUMN chatData.agentchattotal IS 'Agent Chat Response Total Time (Seconds)'; 
--COMMENT ON COLUMN chatData.agenthasread IS 'Agent Read Chat'; 
COMMENT ON COLUMN chatData.chatinitiatedby IS 'Chat Started By (Agent/Remote)'; 
COMMENT ON COLUMN chatData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN chatData.conversationstart IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN chatData.conversationstartltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON COLUMN chatData.custchatcount IS 'Customer Chat Count'; 
COMMENT ON COLUMN chatData.custchatmax IS 'Customer Chat Response Max Time (Seconds)'; 
COMMENT ON COLUMN chatData.custchatmin IS 'Customer Chat Response Min Time (Seconds)'; 
COMMENT ON COLUMN chatData.custchattotal IS 'Customer Chat Response Total Time (Seconds)'; 
COMMENT ON COLUMN chatData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN chatData.userid IS 'User GUID	'; 
COMMENT ON TABLE chatData IS 'Chat Data'; 
