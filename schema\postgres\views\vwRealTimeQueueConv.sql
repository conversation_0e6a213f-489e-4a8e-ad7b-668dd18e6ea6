DROP VIEW IF EXISTS vwRealTimeQueueConv;

CREATE
OR REPLACE VIEW vwRealTimeQueueConv AS
SELECT
    qc.conversationid,
    qc.media,
    qc.actingas,
    (
        DATEDIFF(
            'second',
            qc.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        )
    ) as statusSecs,
    (
        DATEDIFF(
            'second',
            qc.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        )
    ) / 86400.00 as statusDays,
    qc.skill1,
    sk1.name as SkillName1,
    qc.skill2,
    sk2.name as SkillName2,
    qc.skill3,
    sk3.name as SkillName3,
    qc.initialpriority,
    qc.participantname,
    qc.queueid,
    qd.name as queuename,
    qd.divisionid as division,
    qc.userid,
    ud.name as agentname,
    ud.department,
    ud.managername,
    qc.direction,
    qc.ani,
    qc.dnis,
    qc.requestedrout1,
    qc.requestedrout2,
    qc.usedrout
FROM
    queuerealtimeconvData qc
    left outer join skillDetails as sk1 on sk1.id = qc.skill1
    left outer join skillDetails as sk2 on sk2.id = qc.skill2
    left outer join skillDetails as sk3 on sk3.id = qc.skill3
    left outer join queuedetails as qd on qd.id = qc.queueid
    left outer join vwUserDetail as ud on ud.id = qc.userid;

COMMENT ON COLUMN vwRealTimeQueueConv.conversationid IS 'ID of the conversation';
COMMENT ON COLUMN vwRealTimeQueueConv.media IS 'Type of media';
COMMENT ON COLUMN vwRealTimeQueueConv.actingas IS 'Role of the participant';
COMMENT ON COLUMN vwRealTimeQueueConv.statusSecs IS 'Duration of status (in seconds)';
COMMENT ON COLUMN vwRealTimeQueueConv.statusDays IS 'Duration of status (in days)';
COMMENT ON COLUMN vwRealTimeQueueConv.skill1 IS 'First associated skill ID';
COMMENT ON COLUMN vwRealTimeQueueConv.SkillName1 IS 'Name of the first associated skill';
COMMENT ON COLUMN vwRealTimeQueueConv.skill2 IS 'Second associated skill ID';
COMMENT ON COLUMN vwRealTimeQueueConv.SkillName2 IS 'Name of the second associated skill';
COMMENT ON COLUMN vwRealTimeQueueConv.skill3 IS 'Third associated skill ID';
COMMENT ON COLUMN vwRealTimeQueueConv.SkillName3 IS 'Name of the third associated skill';
COMMENT ON COLUMN vwRealTimeQueueConv.initialpriority IS 'Initial priority of the conversation';
COMMENT ON COLUMN vwRealTimeQueueConv.participantname IS 'Name of the participant';
COMMENT ON COLUMN vwRealTimeQueueConv.queueid IS 'ID of the associated queue';
COMMENT ON COLUMN vwRealTimeQueueConv.queuename IS 'Name of the associated queue';
COMMENT ON COLUMN vwRealTimeQueueConv.division IS 'ID of the associated division';
COMMENT ON COLUMN vwRealTimeQueueConv.userid IS 'ID of the associated user';
COMMENT ON COLUMN vwRealTimeQueueConv.agentname IS 'Name of the agent';
COMMENT ON COLUMN vwRealTimeQueueConv.department IS 'Department of the handling agent';
COMMENT ON COLUMN vwRealTimeQueueConv.managername IS 'Agent manager Name';
COMMENT ON COLUMN vwRealTimeQueueConv.direction IS 'Direction of the conversation';
COMMENT ON COLUMN vwRealTimeQueueConv.ani IS 'Automatic Number Identification';
COMMENT ON COLUMN vwRealTimeQueueConv.dnis IS 'Dialed Number Identification Service';
COMMENT ON COLUMN vwRealTimeQueueConv.requestedrout1 IS 'First requested route';
COMMENT ON COLUMN vwRealTimeQueueConv.requestedrout2 IS 'Second requested route';
COMMENT ON COLUMN vwRealTimeQueueConv.usedrout IS 'Used route';
COMMENT ON VIEW vwRealTimeQueueConv IS 'Real-time queue conversation data';
