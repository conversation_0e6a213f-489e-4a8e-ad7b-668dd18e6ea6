CREATE TABLE IF NOT EXISTS adherenceexcdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    enddate timestamp without time zone,
    enddateltc timestamp without time zone,
    durationsecs integer,
    tolerance integer,
    actualdurationsecs integer,
    scheduledactivitycategory varchar(50),
    actualactivitycategory varchar(50),
    systempresence varchar(50),
    routingstatus varchar(50),
    impact varchar(50),
    updated timestamp without time zone,
    CONSTRAINT adherenceexcdata_pkey PRIMARY KEY (keyid)
);

DO $$
DECLARE
    KeyidPrefix VARCHAR(255) := 'v1_';
    deleted_count INTEGER;
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM adherenceexcdata
        WHERE SUBSTR(keyid, 1, LENGTH(KeyidPrefix)) <> KeyidPrefix
    ) THEN
        DELETE FROM adherenceexcdata
        WHERE LEFT(keyid, LENGTH(KeyidPrefix)) <> KeyidPrefix;
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        
        UPDATE tabledefinitions
        SET datekeyfield = CURRENT_DATE - INTERVAL '12 months'
        WHERE tablename = 'adherenceexcdata';
        
        RAISE NOTICE 'Records deleted: % and tabledefinitions updated.', deleted_count;
    ELSE
        RAISE NOTICE 'No records found with old keyid in use.';
    END IF;
END $$;

COMMENT ON COLUMN adherenceexcData.actualActivityCategory IS 'The Actual Activity Category'; 
COMMENT ON COLUMN adherenceexcData.actualdurationsecs IS 'The actual time of the exception'; 
COMMENT ON COLUMN adherenceexcData.durationsecs IS 'Total Duration of Exception in sec(s) - Tolerance Time in sec(s)'; 
COMMENT ON COLUMN adherenceexcData.enddate IS 'End Time (UTC)'; 
COMMENT ON COLUMN adherenceexcData.enddateltc IS 'End Time (LTC)'; 
COMMENT ON COLUMN adherenceexcData.impact IS 'Impact of the Exception'; 
COMMENT ON COLUMN adherenceexcData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN adherenceexcData.routingStatus IS 'Routing Status GUID'; 
COMMENT ON COLUMN adherenceexcData.scheduledactivitycategory IS 'The Activity Category';
COMMENT ON COLUMN adherenceexcData.startdate IS 'Start Time (UTC)'; 
COMMENT ON COLUMN adherenceexcData.startdateltc IS 'Start Time (LTC)'; 
COMMENT ON COLUMN adherenceexcData.systemPresence IS 'Presense GUID'; 
COMMENT ON COLUMN adherenceexcData.tolerance IS 'The tolerance before exception starts'; 
COMMENT ON COLUMN adherenceexcData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN adherenceexcData.userid IS 'User GUID'; 
COMMENT ON TABLE adherenceexcData IS 'Adherence Detailed Exception Data'; 