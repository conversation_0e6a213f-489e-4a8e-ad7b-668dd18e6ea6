CREATE TABLE IF NOT EXISTS wfmauditdata(
    keyid varchar(150) NOT NULL,
    modifiedby varchar(50),
    scheduleid varchar(50),
    addorremove varchar(10),
    datemodified timestamp without time zone,
    datemodifiedLTC timestamp without time zone,
    agentid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT wfmauditdata_key PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN wfmauditData.addorremove IS 'Schedule Change Type'; 
COMMENT ON COLUMN wfmauditData.agentid IS 'Schedule Change User GUID'; 
COMMENT ON COLUMN wfmauditData.datemodified IS 'Schedule Date Changed (UTC)'; 
COMMENT ON COLUMN wfmauditData.datemodifiedLTC IS 'Schedule Date Changed (LTC)'; 
COMMENT ON COLUMN wfmauditData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN wfmauditData.modifiedby IS 'Schedule GUID'; 
COMMENT ON COLUMN wfmauditData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE wfmauditData IS 'Audit Table for WFM Schedule Change Data'; 
