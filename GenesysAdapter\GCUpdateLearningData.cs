using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateLearningData
    {
        private readonly ILogger? _logger;

        public GCUpdateLearningData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateLearningData()
        {
            Boolean Successful = false;
            string SyncType = "learningassignmentresults";

            try
            {
                _logger?.LogInformation("Starting Learning Assignment Results data update");

                GCGetData GCData = new GCGetData(_logger);
                DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();

                DBAdapter.Initialize();
                GCData.Initialize(SyncType);

                // Retrieve learning assignment results from Genesys Cloud
                DataTable LearningAssignmentResults = GCData.LearningAssignmentResultsData();

                if (LearningAssignmentResults == null)
                {
                    _logger?.LogWarning("No learning assignment results data returned from Genesys Cloud");
                    return false;
                }

                _logger?.LogInformation("Retrieved {Count} learning assignment results from Genesys Cloud",
                    LearningAssignmentResults.Rows.Count);

                if (LearningAssignmentResults.Rows.Count > 0)
                {
                    _logger?.LogInformation("Writing learning assignment results to database");
                    Successful = DBAdapter.WriteSQLData(LearningAssignmentResults, "learningassignmentresults");

                    if (Successful)
                    {
                        _logger?.LogInformation("Successfully wrote {Count} learning assignment results to database",
                            LearningAssignmentResults.Rows.Count);
                    }
                    else
                    {
                        _logger?.LogError("Failed to write learning assignment results to database");
                    }
                }
                else
                {
                    _logger?.LogInformation("No learning assignment results to write to database");
                    // Consider this successful since there was no error, just no data
                    Successful = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in UpdateLearningData: {ExceptionType}: {Message}",
                    ex.GetType().Name, ex.Message);
                Successful = false;
            }

            return Successful;
        }

    }
}
