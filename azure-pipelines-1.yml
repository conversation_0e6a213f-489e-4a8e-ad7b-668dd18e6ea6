# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- master

pool:
  vmImage: ubuntu-22.04

steps:
- script: echo SonarQube Manual Run
  displayName: 'SonarQube'

- task: SonarQubePrepare@5
  inputs:
    SonarQube: 'SonarQube - Technology'
    scannerMode: 'MSBuild'
    projectKey: 'technology_genesys-adapter_860bf6a8-e93a-45bc-ba4e-dd3c3326a555'
    projectName: 'genesys-adapter'
- task: SonarQubeAnalyze@5
  inputs:
    jdkversion: 'JAVA_HOME_17_X64'
- task: SonarQubePublish@5
  inputs:
    pollingTimeoutSec: '300'