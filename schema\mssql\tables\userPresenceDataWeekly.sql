IF dbo.csg_table_exists('userPresenceDataWeekly') = 0
CREATE TABLE [userPresenceDataWeekly](
    [keyid] [nvarchar](255) NOT NULL,
    [id] [nvarchar](50),
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [timetype] [nvarchar](50),
    [systempresenceid] [nvarchar](50),
    [presenceid] [nvarchar](50),
    [presencetime] [decimal](20, 2),
    [routingid] [nvarchar](50),
    [routingtime] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userPresenceDataWeekly] PRIMARY KEY ([keyid])
);
IF dbo.csg_column_exists('userPresenceDataWeekly', 'timetype') = 0
    ALTER TABLE userPresenceDataWeekly ADD timetype [nvarchar](50);