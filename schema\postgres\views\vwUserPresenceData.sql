DROP VIEW IF EXISTS vwUserPresenceData CASCADE;
CREATE OR REPLACE VIEW vwUserPresenceData AS
SELECT
    pd.id,
    pd.userid,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername,
    ud.divisionid as divisionid,
    dd.name as divisionname,
    pd.startdate,
    pd.startdateltc,
    (
        pd.startdate + (
            select
                diff
            from
                timezonecalcs('Australia/Sydney')
        ) * interval '1 second'
    ) :: timestamp AS startdateusrtz,
    pd.timetype AS timetype,
    pd.systempresenceid,
    pd.presenceid,
    case
        pd.timetype
        when 'Presence' then pd.presencetime
        else 0
    end AS presencetime,
    pd.presencetime / 86400.00 as presencetimeDay,
    pd.routingid,
    case
        pd.timetype
        when 'Routing' then pd.presencetime
        else 0
    end AS routingtime,
    pd.routingtime / 86400.00 as routingtimeDay,
    pd.updated
FROM
    userPresenceData pd
    left outer join vwUserDetail ud on ud.id = pd.userid
    left outer join divisiondetails dd on dd.id = ud.divisionid;

-- Add comments

COMMENT ON COLUMN vwUserPresenceData.agentname IS 'Agent Name'; 
COMMENT ON COLUMN vwUserPresenceData.id IS 'Primary Key'; 
COMMENT ON COLUMN vwUserPresenceData.managerid IS 'Manager GUID'; 
COMMENT ON COLUMN vwUserPresenceData.divisionname IS 'Division Name'; 
COMMENT ON COLUMN vwUserPresenceData.userid IS 'User GUID'; 
COMMENT ON COLUMN vwUserPresenceData.presenceid IS 'Presence GUID'; 
COMMENT ON COLUMN vwUserPresenceData.presencetime IS 'Presence Time'; 
COMMENT ON COLUMN vwUserPresenceData.presencetimeDay IS 'Presence Time (Days)'; 
COMMENT ON COLUMN vwUserPresenceData.routingid IS 'Routing GUID'; 
COMMENT ON COLUMN vwUserPresenceData.routingtime IS 'Routing Time'; 
COMMENT ON COLUMN vwUserPresenceData.routingtimeDay IS 'Routing Time (Days)'; 
COMMENT ON COLUMN vwUserPresenceData.startdate IS 'Start Date(UTC)'; 
COMMENT ON COLUMN vwUserPresenceData.startDateLTC IS 'Start Date (LTC)'; 
COMMENT ON COLUMN vwUserPresenceData.systempresenceid IS 'System Presence GUID'; 
COMMENT ON COLUMN vwUserPresenceData.updated IS 'Last Updated Time';
COMMENT ON COLUMN vwUserPresenceData.userid IS 'User GUID'; 

COMMENT ON VIEW vwUserPresenceData IS 'See UserPresenceData - Expands all the GUIDs with their lookups';
