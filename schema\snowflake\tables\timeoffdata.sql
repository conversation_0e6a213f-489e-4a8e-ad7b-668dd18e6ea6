CREATE TABLE IF NOT EXISTS timeoffdata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    businessunitdate timestamp without time zone,
    length integer,
    description varchar(200),
    activitycode varchar(50),
    paid number,
    timeoffrequestid varchar(50),
    isfulldayrequest number,
    updated timestamp without time zone,
    CONSTRAINT timeoffdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE timeoffdata
ADD column IF NOT exists isfulldayrequest number;