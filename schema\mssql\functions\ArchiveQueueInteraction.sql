CREATE OR ALTER PROCEDURE [dbo].[ArchiveQueueInteraction]  
    @AggOffSet int,   
    @AggType nvarchar(1)   
AS   

DECLARE @CurrentTimeUTC datetime
DECLARE @CurrentTimeLTC datetime
DECLARE @SystemTime datetime
DECLARE @Offset int
DECLARE @CurrentDOW int
DECLARE @StartDate datetime
DECLARE @EndDate datetime
DECLARE @TableName nvarchar(50)
DECLARE @DelsqlCommand varchar(1000)
DECLARE @InssqlCommand nvarchar(max)

SET ANSI_WARNINGS OFF

SET @SystemTime = GETDATE()
SET @CurrentTimeUTC = GETUTCDATE()
SET @CurrentTimeLTC = CONVERT(datetime, SWITCHOFFSET(GETUTCDATE(), DATEPART(TZOFFSET, GETUTCDATE() AT TIME ZONE 'AUS Eastern Standard Time'))) 
SET @Offset = DATEDIFF(MINUTE,@CurrentTimeUTC,@CurrentTimeLTC )
SET @CurrentDOW = 1 + ((5 + DATEPART(dw, @CurrentTimeLTC) + @@DATEFIRST) % 7)

SET @StartDate = CASE @AggType
		WHEN 'M' THEN Dateadd(MONTH,@AggOffSet * -1,@CurrentTimeLTC)
		WHEN 'W' THEN Dateadd(WEEK,@AggOffSet * -1,DATEADD(DAY,(@CurrentDOW -1) * -1,@CurrentTimeLTC))
		WHEN 'D' THEN Dateadd(DAY,@AggOffSet * -1,@CurrentTimeLTC)
END

SET @StartDate = CASE @AggType
		WHEN 'M' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + CAST(MONTH(@StartDate) AS NVARCHAR(2)) + '-01 00:00:00'
		WHEN 'W' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + CAST(MONTH(@StartDate) AS NVARCHAR(2)) + '-' + CAST(DAY(@StartDate) AS NVARCHAR(2)) + ' 00:00:00'
		WHEN 'D' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + CAST(MONTH(@StartDate) AS NVARCHAR(2)) + '-' + CAST(DAY(@StartDate) AS NVARCHAR(2)) + ' 00:00:00'
END

SET @EndDate = CASE @AggType
		WHEN 'M' THEN DateAdd(MINUTE,-1,Dateadd(MONTH,1,@StartDate))
		WHEN 'W' THEN DateAdd(MINUTE,-1,Dateadd(WEEK,1,@StartDate))
		WHEN 'D' THEN DateAdd(MINUTE,-1,Dateadd(DAY,1,@StartDate))
END

SET @TableName = CASE @AggType
		WHEN 'M' THEN 'queueInteractionDataMonthly'
		WHEN 'W' THEN 'queueInteractionDataWeekly'
		WHEN 'D' THEN 'queueInteractionDataDaily'
END

SET @StartDate = Dateadd(MINUTE,@OffSet * -1,@StartDate)
SET @EndDate = Dateadd(MINUTE,@OffSet * -1,@EndDate)

SET @DelsqlCommand = 'DELETE FROM ' +  @TableName + ' WHERE startdate = ''' + CAST(Dateadd(MINUTE,@OffSet,@StartDate)AS NVARCHAR(25)) + ''' '

Print 'Delete Old Rows' 

EXEC (@DelsqlCommand)

SET @InssqlCommand = N'INSERT INTO ' +  @TableName + ' SELECT direction + ''|'' + isnull(queueid,''NOQUEUE'') + ''|'' + mediatype + ''|'' + isnull(wrapupcode,''NOWRAP'') + ''|'' + ''' + CAST(Dateadd(MINUTE,@OffSet,@StartDate)AS NVARCHAR(25)) + ''' as keyid
      ,direction
      ,queueid
      ,mediatype
      ,wrapupcode
	  ,''' + CAST(Dateadd(MINUTE,@OffSet,@StartDate)AS NVARCHAR(25)) + ''' as startDate
      ,SUM(talertcount) as talertcount
      ,SUM(talerttimesum) as talerttimesum
      ,MAX(talerttimemax) as talerttimemax
      ,MIN(talerttimemin) as talerttimemin
      ,SUM(tansweredcount) as tansweredcount
      ,SUM(tansweredtimesum) as tansweredtimesum
      ,MAX(tansweredtimemax) as tansweredtimemax
      ,MIN(tansweredtimemin) as tansweredtimemin
      ,SUM(ttalkcount) as ttalkcount
      ,SUM(ttalktimesum) as ttalktimesum
      ,MAX(ttalktimemax) as ttalktimemax
      ,MIN(ttalktimemin) as ttalktimemin
      ,SUM(ttalkcompletecount) as ttalkcompletecount
      ,SUM(ttalkcompletetimesum) as ttalkcompletetimesum
      ,MAX(ttalkcompletetimemax) as ttalkcompletetimemax
      ,MIN(ttalkcompletetimemin) as ttalkcompletetimemin
      ,SUM(tnotrespondingcount) as tnotrespondingcount
      ,SUM(tnotrespondingtimesum) as tnotrespondingtimesum
      ,MAX(tnotrespondingtimemax) as tnotrespondingtimemax
      ,MIN(tnotrespondingtimemin) as tnotrespondingtimemin
      ,SUM(theldcount) as theldcount
      ,SUM(theldtimesum) as theldtimesum
      ,MAX(theldtimemax) as theldtimemax
      ,MIN(theldtimemin) as theldtimemin
      ,SUM(theldcompletecount) as theldcompletecount
      ,SUM(theldcompletetimesum) as theldcompletetimesum
      ,MAX(theldcompletetimemax) as theldcompletetimemax
      ,MIN(theldcompletetimemin) as theldcompletetimemin
      ,SUM(thandlecount) as thandlecount
      ,SUM(thandletimesum) as thandletimesum
      ,MAX(thandletimemax) as thandletimemax
      ,MIN(thandletimemin) as thandletimemin
      ,SUM(tacwcount) as tacwcount
      ,SUM(tacwtimesum) as tacwtimesum
      ,MAX(tacwtimemax) as tacwtimemax
      ,MIN(tacwtimemin) as tacwtimemin
      ,SUM(nconsult) as nconsult
      ,SUM(nconsulttransferred) as nconsulttransferred
      ,SUM(noutbound) as noutbound
      ,SUM(nerror) as nerror
      ,SUM(ntransferred) as ntransferred
      ,SUM(nblindtransferred) as nblindtransferred
      ,SUM(nconnected) as nconnected
	  ,SUM(noffered) as nofferred
	  ,SUM(noversla) as noversla
  	  ,SUM(tacdcount) as tacdcount
      ,SUM(tacdtimesum) as tacdtimesum
      ,MAX(tacdtimemax) as tacdtimemax
      ,MIN(tacdtimemin) as tacdtimemin
	  ,SUM(tdialingcount) as tdialingcount
      ,SUM(tdialingtimesum) as tdialingtimesum
      ,MAX(tdialingtimemax) as tdialingtimemax
      ,MIN(tdialingtimemin) as tdialingtimemin
      ,SUM(tcontactingcount) as tcontactingcount
      ,SUM(tcontactingtimesum) as tcontactingtimesum
      ,MAX(tcontactingtimemax) as tcontactingtimemax
      ,MIN(tcontactingtimemin) as tcontactingtimemin
      ,SUM(tvoicemailcount) as tvoicemailcount
      ,SUM(tvoicemailtimesum) as tvoicemailtimesum
      ,MAX(tvoicemailtimemax) as tvoicemailtimemax
      ,MIN(tvoicemailtimemin) as tvoicemailtimemin
      ,SUM(tflowoutcount) as tflowoutcount
      ,SUM(tflowouttimesum) as tflowouttimesum
      ,MAX(tflowouttimemax) as tflowouttimemax
      ,MIN(tflowouttimemin) as tflowouttimemin
      ,SUM(twaitcount) as twaitcount
      ,SUM(twaittimesum) as twaittimesum
      ,MAX(twaittimemax) as twaittimemax
      ,MIN(twaittimemin) as twaittimemin
      ,SUM(tabandoncount) as tabandoncount
      ,SUM(tabandontimesum) as tabandontimesum
      ,MAX(tabandontimemax) as tabandontimemax
      ,MIN(tabandontimemin) as tabandontimemin'

SET @InssqlCommand = @InssqlCommand + N'
      ,SUM(servicelevelnumerator) as servicelevelnumerator
      ,SUM(serviceleveldenominator) as serviceleveldenominator
	  ,SUM(av1count) as av1count
      ,SUM(av1timesum) as av1timesum
      ,MAX(av1timemax) as av1timemax
      ,MIN(av1timemin) as av1timemin
	  ,SUM(av2count) as av2count
      ,SUM(av2timesum) as av2timesum
      ,MAX(av2timemax) as av2timemax
      ,MIN(av2timemin) as av2timemin
	  ,SUM(av3count) as av3count
      ,SUM(av3timesum) as av3timesum
      ,MAX(av3timemax) as av3timemax
      ,MIN(av3timemin) as av3timemin
	  ,SUM(av4count) as av4count
      ,SUM(av4timesum) as av4timesum
      ,MAX(av4timemax) as av4timemax
      ,MIN(av4timemin) as av4timemin
	  ,SUM(av5count) as av5count
      ,SUM(av5timesum) as av5timesum
      ,MAX(av5timemax) as av5timemax
      ,MIN(av5timemin) as av5timemin
	  ,SUM(av6count) as av6count
      ,SUM(av6timesum) as av6timesum
      ,MAX(av6timemax) as av6timemax
      ,MIN(av6timemin) as av6timemin
	  ,SUM(av7count) as av7count
      ,SUM(av7timesum) as av7timesum
      ,MAX(av7timemax) as av7timemax
      ,MIN(av7timemin) as av7timemin
	  ,SUM(av8count) as av8count
      ,SUM(av8timesum) as av8timesum
      ,MAX(av8timemax) as av8timemax
      ,MIN(av8timemin) as av8timemin
	  ,SUM(av9count) as av9count
      ,SUM(av9timesum) as av9timesum
      ,MAX(av9timemax) as av9timemax
      ,MIN(av9timemin) as av9timemin
	  ,SUM(av10count) as av10count
      ,SUM(av10timesum) as av10timesum
      ,MAX(av10timemax) as av10timemax
      ,MIN(av10timemin) as av10timemin
	  ,GETUTCDATE() as updated
FROM queueInteractionData
WHERE startdate between ''' + CAST(@StartDate AS NVARCHAR(25)) + ''' and ''' + CAST(@EndDate AS NVARCHAR(25)) + '''
GROUP BY direction + ''|'' + isnull(queueid,''NOQUEUE'') + ''|'' + mediatype + ''|'' + isnull(wrapupcode,''NOWRAP'') + ''|'' + ''' + CAST(Dateadd(MINUTE,@OffSet,@StartDate)AS NVARCHAR(25)) + '''
      	,direction
      	,queueid
      	,mediatype
      	,wrapupcode
'

Print 'Insert New Rows'

EXEC (@InssqlCommand)

