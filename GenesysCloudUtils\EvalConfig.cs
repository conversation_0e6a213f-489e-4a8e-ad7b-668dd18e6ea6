﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using evalForms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class EvalConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
            GCApiKey = GCUtilities.GCApiKey;


            DBUtil.Initialize();
        }

        public DataTable GetEvalConfigFromGC()
        {
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            JsonUtils JsonActions = new JsonUtils();

            int CurrentPage = 1;

            DataTable EvalFormList = CreateEvalIdTempTable();

            #region Eval Forms List
            try
            {
                Console.WriteLine("Retrieving Eval Forms");

                CurrentPage = 1;
                JsonActions.MaxPages = 1;

                while (CurrentPage <= JsonActions.MaxPages)
                {
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/quality/publishedforms/evaluations?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                    EvalForm EvalFormsData = new EvalForm();

                    EvalFormsData = JsonConvert.DeserializeObject<EvalForm>(JsonString,
                           new JsonSerializerSettings
                           {
                               NullValueHandling = NullValueHandling.Ignore
                           });

                    JsonActions.MaxPages = EvalFormsData.pageCount;

                    foreach (evalForms.Entity JSON in EvalFormsData.entities)
                    {
                        Console.Write("F");
                        DataRow checkRow = EvalFormList.Select("id='" + JSON.id + "'").FirstOrDefault();

                        if (checkRow == null)
                        {
                            DataRow EvalRow = EvalFormList.NewRow();
                            EvalRow["id"] = JSON.id;
                            EvalFormList.Rows.Add(EvalRow);
                        }
                        else
                        {
                            Console.Write("D");
                        }
                    }
                    CurrentPage += 1;
                }

                Console.WriteLine("\nTotal Evaluation Forms Found:{0} ", EvalFormList.Rows.Count);
            }
            catch (Exception ex)
            {
                EvalFormList = null;
                Console.WriteLine("Exception caught in Evaluation Details.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                return null;
            }

            EvalFormList.AcceptChanges();
            #endregion

            //Now Return the Evaluation Group/Question Details.

            DataTable EvaluationQuestions = DBUtil.CreateInMemTable("evalDetails");
            //CreateEvaluationQuestionsTable();

            #region Eval Details

            try
            {
                foreach (DataRow EvalForm in EvalFormList.Rows)
                {
                    //Console.WriteLine("\nEvaluation Form ID :{0}\n", EvalForm["id"]);
                    Console.Write("F");

                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/quality/publishedforms/evaluations/" + EvalForm["id"].ToString(), GCApiKey);

                    EvaluationDetails EvalQuestionData = new EvaluationDetails();

                    EvalQuestionData = JsonConvert.DeserializeObject<EvaluationDetails>(JsonString,
                           new JsonSerializerSettings
                           {
                               NullValueHandling = NullValueHandling.Ignore
                           });

                    foreach (Questiongroup EvalFormQuestionGroup in EvalQuestionData.questionGroups)
                    {

                        Console.Write("G");

                        foreach (Question EvalFormQuestion in EvalFormQuestionGroup.questions)
                        {
                            int AnswerCount = 0;
                            Console.Write("Q");

                            foreach (Answeroption EvalFormAnswer in EvalFormQuestion.answerOptions)
                            {
                                AnswerCount++;

                                Console.Write("A");

                                string KeyId = EvalForm["id"].ToString() +
                                               "|" + EvalQuestionData.id +
                                               "|" + EvalFormQuestionGroup.id +
                                               "|" + EvalFormQuestion.id +
                                               "|" + EvalFormAnswer.id +
                                               "|" + AnswerCount.ToString();

                                DataRow CheckRow = EvaluationQuestions.Select("id='" + KeyId + "'").FirstOrDefault();

                                //Console.WriteLine("\nKeyid:{0}\n", KeyId);
                                if (CheckRow == null)
                                {
                                    DataRow EvalQuestion = EvaluationQuestions.NewRow();

                                    // Note: evaluationid column actually stores the form ID for historical reasons
                                    // This has caused confusion with customers as it's not actually the evaluation ID
                                    EvalQuestion["evaluationid"] = EvalForm["id"].ToString();
                                    EvalQuestion["evaluationformid"] = EvalQuestionData.id;
                                    EvalQuestion["evaluationname"] = EvalQuestionData.name;
                                    EvalQuestion["questiongroupid"] = EvalFormQuestionGroup.id;
                                    EvalQuestion["questiongroupname"] = EvalFormQuestionGroup.name;
                                    EvalQuestion["questiongroupToHighest"] = EvalFormQuestionGroup.defaultAnswersToHighest;
                                    EvalQuestion["questiongroupToNA"] = EvalFormQuestionGroup.defaultAnswersToNA;
                                    EvalQuestion["questiongroupwieght"] = EvalFormQuestionGroup.weight;
                                    EvalQuestion["questiongroupmanwieght"] = EvalFormQuestionGroup.manualWeight;
                                    EvalQuestion["questionid"] = EvalFormQuestion.id;

                                    if (EvalFormQuestion.text != null && EvalFormQuestion.text.Length > 349)
                                        EvalFormQuestion.text = EvalFormQuestion.text.Substring(0, 350);

                                    EvalQuestion["questiontext"] = EvalFormQuestion.text;

                                    if (EvalFormQuestion.helpText != null && EvalFormQuestion.helpText.Length > 999)
                                        EvalFormQuestion.helpText = EvalFormQuestion.helpText.Substring(0, 1000);

                                    EvalQuestion["questionhelptext"] = EvalFormQuestion.text;

                                    EvalQuestion["quesiontype"] = EvalFormQuestion.type;
                                    EvalQuestion["questionnaenabled"] = EvalFormQuestion.naEnabled;
                                    EvalQuestion["questioncommentsreq"] = EvalFormQuestion.commentsRequired;
                                    EvalQuestion["questioniskill"] = EvalFormQuestion.isKill;
                                    EvalQuestion["questioniscritical"] = EvalFormQuestion.isCritical;
                                    EvalQuestion["questionanwserid"] = EvalFormAnswer.id;


                                    if (EvalFormAnswer.text != null && EvalFormAnswer.text.Length > 198)
                                        EvalFormAnswer.text = EvalFormAnswer.text.Substring(0, 199);

                                    EvalQuestion["questionanswertext"] = EvalFormAnswer.text;
                                    EvalQuestion["questionanswervalue"] = EvalFormAnswer.value;
                                    EvalQuestion["id"] = KeyId;

                                    EvaluationQuestions.Rows.Add(EvalQuestion);
                                }
                                else
                                {
                                    Console.Write("DF");
                                }

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                EvaluationQuestions = null;
                Console.WriteLine("\nError Retrieving list of Published Evaluations");
                Console.WriteLine(ex.ToString());
            }

            #endregion

            Console.Write("\n");
            return EvaluationQuestions;
        }

        private DataTable CreateEvalIdTempTable()
        {
            DataTable DTTemp = new DataTable("Eval");

            DTTemp.Columns.Add("id", typeof(String));

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["id"] };
            return DTTemp;
        }

        private DataTable CreateEvaluationQuestionsTable()
        {

            DataTable DTTemp = new DataTable("evalDetails");

            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("evaluationid", typeof(String));
            DTTemp.Columns.Add("evaluationformid", typeof(String));
            DTTemp.Columns.Add("evaluationname", typeof(String));
            DTTemp.Columns.Add("questiongroupid", typeof(String));
            DTTemp.Columns.Add("questiongroupname", typeof(String));
            DTTemp.Columns.Add("questiongroupToHighest", typeof(bool));
            DTTemp.Columns.Add("questiongroupToNA", typeof(bool));
            DTTemp.Columns.Add("questiongroupwieght", typeof(decimal));
            DTTemp.Columns.Add("questiongroupmanwieght", typeof(bool));
            DTTemp.Columns.Add("questionid", typeof(String));
            DTTemp.Columns.Add("questiontext", typeof(String));
            DTTemp.Columns.Add("questionhelptext", typeof(String));
            DTTemp.Columns.Add("quesiontype", typeof(String));
            DTTemp.Columns.Add("questionnaenabled", typeof(bool));
            DTTemp.Columns.Add("questioncommentsreq", typeof(bool));
            DTTemp.Columns.Add("questioniskill", typeof(bool));
            DTTemp.Columns.Add("questioniscritical", typeof(bool));
            DTTemp.Columns.Add("questionanwserid", typeof(String));
            DTTemp.Columns.Add("questionanswertext", typeof(String));
            DTTemp.Columns.Add("questionanswervalue", typeof(decimal));
            DTTemp.Columns.Add("updated", typeof(DateTime));

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["id"] };

            return DTTemp;
        }
    }

    public class EvaluationDetails
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime modifiedDate { get; set; }
        public bool published { get; set; }
        public string contextId { get; set; }
        public Questiongroup[] questionGroups { get; set; }
        public string selfUri { get; set; }
    }

    public class Questiongroup
    {
        public string id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public bool defaultAnswersToHighest { get; set; }
        public bool defaultAnswersToNA { get; set; }
        public bool naEnabled { get; set; }
        public decimal weight { get; set; }
        public bool manualWeight { get; set; }
        public Question[] questions { get; set; }
    }

    public class Question
    {
        public string id { get; set; }
        public string text { get; set; }
        public string helpText { get; set; }
        public string type { get; set; }
        public bool naEnabled { get; set; }
        public bool commentsRequired { get; set; }
        public Answeroption[] answerOptions { get; set; }
        public bool isCritical { get; set; }
        public bool isKill { get; set; }
    }

    public class Answeroption
    {
        public string id { get; set; }
        public string text { get; set; }
        public decimal value { get; set; }
    }

    public class EvalsAvailable
    {
        public EvalForms[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class EvalForms
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime modifiedDate { get; set; }
        public bool published { get; set; }
        public string contextId { get; set; }
        public object[] questionGroups { get; set; }
        public string selfUri { get; set; }
    }

}

namespace evalForms
{


    public class EvalForm
    {
        public Entity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string name { get; set; }
        public DateTime modifiedDate { get; set; }
        public bool published { get; set; }
        public string contextId { get; set; }
        public object[] questionGroups { get; set; }
        public string weightMode { get; set; }
        public string selfUri { get; set; }
    }

}
// spell-checker: ignore: questiongroupid questionid questionnaenabled questioniscritical
