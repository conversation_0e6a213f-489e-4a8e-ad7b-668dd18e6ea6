CREATE TABLE IF NOT EXISTS userinteractionpresencedetaileddata (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    starttime timestamp without time zone NOT NULL,
    starttimeltc timestamp without time zone,
    endtime timestamp without time zone,
    endtimeltc timestamp without time zone,
    systempresence varchar(50),
    orgpresence varchar(50),
    routingstatus varchar(50),
    conversationid varchar(50),
    mediatype varchar(50),
    timeinstate numeric(20, 2),
    updated timestamp without time zone,
    PRIMARY KEY (keyid, starttime)
) PARTITION BY RANGE (starttime);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_starttime ON userinteractionpresencedetaileddata
(starttime);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_starttimeltc ON userinteractionpresencedetaileddata
(starttimeltc);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_endtime ON userinteractionpresencedetaileddata
(endtime);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_endtimeltc ON userinteractionpresencedetaileddata
(endtimeltc);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_orgpresence ON userinteractionpresencedetaileddata
(orgpresence);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_routingstatus ON userinteractionpresencedetaileddata
(routingstatus);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_systempresence ON userinteractionpresencedetaileddata
(systempresence);

CREATE INDEX IF NOT EXISTS userinteractionpresencedetaileddata_userid ON userinteractionpresencedetaileddata
(userid);
