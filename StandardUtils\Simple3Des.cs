﻿using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace StandardUtils;

#nullable enable
public class Secret
{
    private const string _prefixV2 = "enc:v2:";
    private const string _suffix = "8rF3";
    private string _key = "";
    private string _value;

    public Secret(string key, string value)
    {
        // NOTE: This is not intended to be irreversible, if you have access to the code it will be easy to break.
        // The intention is to provide some level of protection against exposed plain-text configuration files.
        // This will not help if the attacker has access to the binaries and can reverse engineer the algorithm or
        // extract the secret from the running binaries memory. The key to decrypt the secret has to be in the
        // configuration for the script to work, so there is no secure way to fully protect the secret.
        _key = key;
        _value = value;
    }

    public int EncryptionVersion
    {
        get
        {
            if (_value.StartsWith("enc:v2:"))
                return 2;

            try
            {
                new Simple3Des(_key).DecryptData(_value);
                return 1;
            }
            catch
            {
                return 0;
            }
        }
    }

    public bool IsEncrypted => EncryptionVersion > 0;

    public string Encrypted => IsEncrypted ? _value : EncryptUsingAesV2();
    public string PlainText
    {
        get
        {
            switch (EncryptionVersion)
            {
                case 0:
                    // Not currently encrypted.
                    return _value;
                case 1:
                    return new Simple3Des(_key).DecryptData(_value);
                case 2:
                    return DecryptUsingAesV2();
                default:
                    throw new NotImplementedException();
            }
        }
    }

    public override string ToString() => IsEncrypted ? _value : "** REDACTED **";

    private string EncryptUsingAesV2()
    {
        using (Aes aesAlg = Aes.Create())
        {
            byte[] salt = GenerateRandomBytes(16); // Generate a random salt
            byte[] derivedKey = DeriveKey(_key + _suffix, salt); // Derive a key using the provided key and salt

            aesAlg.Key = derivedKey;
            aesAlg.Mode = CipherMode.CBC;
            aesAlg.GenerateIV();

            ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

            byte[] plaintextBytes = Encoding.UTF8.GetBytes(_value);

            byte[] encryptedBytes = encryptor.TransformFinalBlock(plaintextBytes, 0, plaintextBytes.Length);

            byte[] ciphertextBytes = new byte[aesAlg.IV.Length + encryptedBytes.Length];
            Array.Copy(aesAlg.IV, 0, ciphertextBytes, 0, aesAlg.IV.Length);
            Array.Copy(encryptedBytes, 0, ciphertextBytes, aesAlg.IV.Length, encryptedBytes.Length);

            byte[] saltAndCiphertextBytes = new byte[salt.Length + ciphertextBytes.Length];
            Array.Copy(salt, 0, saltAndCiphertextBytes, 0, salt.Length);
            Array.Copy(ciphertextBytes, 0, saltAndCiphertextBytes, salt.Length, ciphertextBytes.Length);

            string encryptedText = Convert.ToBase64String(saltAndCiphertextBytes);
            return _prefixV2 + encryptedText;
        }
    }

    private string DecryptUsingAesV2()
    {
        if (!_value.StartsWith(_prefixV2))
            throw new ArgumentException("Invalid encrypted text format");

        var ciphertext = _value.Substring(_prefixV2.Length);

        byte[] saltAndCiphertextBytes;
        try
        {
            saltAndCiphertextBytes = Convert.FromBase64String(ciphertext);
        }
        catch (FormatException ex)
        {
            #if DEBUG
                var message = string.Format("Failed to decrypt credential for {0}, ciphertext {1}",
                    _key,
                    _value);
            #else
                var message = string.Format("Failed to decrypt credential for {0}", _key);
            #endif
            throw new FormatException(message, ex);
        }

        byte[] salt = new byte[16];
        Array.Copy(saltAndCiphertextBytes, 0, salt, 0, salt.Length);
        byte[] ciphertextBytes = new byte[saltAndCiphertextBytes.Length - salt.Length];
        Array.Copy(saltAndCiphertextBytes, salt.Length, ciphertextBytes, 0, ciphertextBytes.Length);

        byte[] derivedKey = DeriveKey(_key + _suffix, salt);

        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = derivedKey;
            aesAlg.Mode = CipherMode.CBC;

            byte[] iv = new byte[aesAlg.IV.Length];
            Array.Copy(ciphertextBytes, 0, iv, 0, aesAlg.IV.Length);
            aesAlg.IV = iv;

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

            byte[] encryptedBytes = new byte[ciphertextBytes.Length - aesAlg.IV.Length];
            Array.Copy(ciphertextBytes, aesAlg.IV.Length, encryptedBytes, 0, encryptedBytes.Length);

            byte[] decryptedBytes;
            try
            {
                decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
            }
            catch (CryptographicException ex)
            {
                #if DEBUG
                    var message = string.Format("Failed to decrypt credential for {0}, ciphertext {1}",
                        _key,
                        _value);
                #else
                    var message = string.Format("Failed to decrypt credential for {0}", _key);
                #endif
                throw new CryptographicException(message, ex);
            }

            return Encoding.UTF8.GetString(decryptedBytes);
        }
    }

    private byte[] DeriveKey(string key, byte[] salt)
    {
        using (Rfc2898DeriveBytes deriveBytes = new Rfc2898DeriveBytes(key, salt, 10000))
        {
            return deriveBytes.GetBytes(32); // 256 bits
        }
    }

    private byte[] GenerateRandomBytes(int length)
    {
        byte[] randomBytes = new byte[length];
        using (RandomNumberGenerator rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomBytes);
        }
        return randomBytes;
    }
}

public sealed class Simple3Des
{
    private TripleDES TripleDes = TripleDES.Create();
    private string _key;

    public Simple3Des(string key, string IV = "")
    {
        // Initialize the crypto provider.
        _key = key;
        TripleDes.Key = TruncateHash(key, TripleDes.KeySize / 8);
        TripleDes.IV = TruncateHash(IV, TripleDes.BlockSize / 8);
    }

    private byte[] TruncateHash(string key, int length)
    {
        SHA1 sha1 = SHA1.Create();

        // Hash the key.
        byte[] keyBytes = System.Text.Encoding.Unicode.GetBytes(key);
        byte[] hash = sha1.ComputeHash(keyBytes);
        var oldHash = hash;
        hash = new byte[length - 1 + 1];
        // Truncate or pad the hash.
        if (oldHash != null)
            Array.Copy(oldHash, hash, Math.Min(length - 1 + 1, oldHash.Length));
        return hash;
    }

    public string EncryptData(string plaintext)
    {
        // Convert the plaintext string to a byte array.
        byte[] plaintextBytes = System.Text.Encoding.Unicode.GetBytes(plaintext);

        // Create the stream.
        System.IO.MemoryStream ms = new System.IO.MemoryStream();
        // Create the encoder to write to the stream.
        CryptoStream encStream = new CryptoStream(ms, TripleDes.CreateEncryptor(), System.Security.Cryptography.CryptoStreamMode.Write);

        // Use the crypto stream to write the byte array to the stream.
        encStream.Write(plaintextBytes, 0, plaintextBytes.Length);
        encStream.FlushFinalBlock();

        // Convert the encrypted stream to a printable string.
        return Convert.ToBase64String(ms.ToArray());
    }

    public string DecryptData(string encryptedtext)
    {
        // Convert the encrypted text string to a byte array.
        byte[] encryptedBytes;
        try
        {
            encryptedBytes = Convert.FromBase64String(encryptedtext);
        }
        catch (FormatException ex)
        {
            #if DEBUG
                var message = string.Format("Failed to decrypt credential for {0}, ciphertext {1}",
                    _key,
                    encryptedtext);
            #else
                var message = string.Format("Failed to decrypt credential for {0}", _key);
            #endif
            throw new FormatException(message, ex);
        }

        // Create the stream.
        System.IO.MemoryStream ms = new System.IO.MemoryStream();
        // Create the decoder to write to the stream.
        CryptoStream decStream = new CryptoStream(ms, TripleDes.CreateDecryptor(), System.Security.Cryptography.CryptoStreamMode.Write);

        // Use the crypto stream to write the byte array to the stream.
        decStream.Write(encryptedBytes, 0, encryptedBytes.Length);
        try
        {
            decStream.FlushFinalBlock();
        }
        catch (CryptographicException ex)
        {
            #if DEBUG
                var message = string.Format("Failed to decrypt credential for {0}, ciphertext {1}",
                    _key,
                    encryptedtext);
            #else
                var message = string.Format("Failed to decrypt credential for {0}", _key);
            #endif
            throw new CryptographicException(message, ex);
        }

        // Convert the plaintext stream to a string.
        return System.Text.Encoding.Unicode.GetString(ms.ToArray());
    }

    public string ToAlphaNumericOnly(string input)
    {
        Regex rgx = new Regex("[^a-zA-Z0-9]");
        return rgx.Replace(input, "");
    }
}
#nullable restore
// spell-checker: ignore: encryptor, decryptor
