CREATE TABLE IF NOT EXISTS knowledgebase (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(255),
    coreLanguage VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
    dateDocumentLastModified TIMESTAMP WITHOUT TIME ZONE,
    faqCount INTEGER,
    articleCount INTEGER,
    published BOOLEAN,
    updated timestamp without time zone,
    CONSTRAINT knowledgebase_pkey PRIMARY KEY (id)
);