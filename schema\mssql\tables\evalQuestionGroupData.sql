IF dbo.csg_table_exists('evalQuestionGroupData') = 0
CREATE TABLE [evalQuestionGroupData](
    [keyid] [nvarchar](50) NOT NULL,
    [evaluationid] [nvarchar](50) NOT NULL,
    [evaluationformid] [nvarchar](50),
    [questiongroupid] [nvarchar](50),
    [totalscore] [decimal](20, 2),
    [maxtotalscore] [decimal](20, 2),
    [markedna] [bit],
    [totalcriticalscore] [decimal](20, 2),
    [maxtotalcriticalscore] [decimal](20, 2),
    [totalnoncriticalscore] [decimal](20, 2),
    [maxtotalnoncriticalscore] [decimal](20, 2),
    [totalscoreunweighted] [decimal](20, 2),
    [maxtotalscoreunweighted] [decimal](20, 2),
    [failedkillquestions] [bit],
    [comments] [nvarchar](max),
    [updated] [datetime],
    CONSTRAINT [PK__evalQues__607AFDE049C5BC33] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('evalquestiongroupdata_eval', 'evalQuestionGroupData') = 0
CREATE INDEX [evalquestiongroupdata_eval] ON [evalQuestionGroupData] ([evaluationid]);