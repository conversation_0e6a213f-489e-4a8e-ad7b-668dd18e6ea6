CREATE
OR REPLACE VIEW vwCallDetail AS
SELECT
    di.conversationid,
    di.originaldirection,
    di.mediatype,
    di.ani,
    di.dnis,
    di.purpose,
    di.sessiondirection,
    di.conversationstartdate,
    di.userid,
    di.queueid,
    qd.name AS queueName,
    di.conversationenddate,
    DATEDIFF(
        'second',
        di.conversationstartdate :: timestamp,
        di.conversationenddate :: timestamp
    ) AS CallDuration,
    SUM(
        DATEDIFF(
            'second',
            di.segmentstartdate :: timestamp,
            di.segmentenddate :: timestamp
        )
    ) AS TalkDuration,
    DATEDIFF(
        'second',
        di.conversationstartdate :: timestamp,
        di.conversationenddate :: timestamp
    ) / 86400.00 AS CallDurationDay,
    SUM(
        DATEDIFF(
            'second',
            di.segmentstartdate :: timestamp,
            di.segmentenddate :: timestamp
        )
    ) / 86400.00 AS TalkDurationDay,
    ud.name,
    di.segmenttype
FROM
    detailedInteractionData AS di
    LEFT OUTER JOIN userDetails AS ud ON ud.id = di.userid
    LEFT OUTER JOIN queueDetails AS qd ON qd.id = di.queueid
WHERE
    (NOT (di.userid IS NULL))
    AND (di.purpose IN ('agent', 'user', 'voicemail'))
    AND (
        di.segmenttype IN ('Interact', 'contacting', 'dialing')
    )
GROUP BY
    di.conversationid,
    di.originaldirection,
    di.mediatype,
    di.ani,
    di.dnis,
    di.purpose,
    di.sessiondirection,
    di.conversationstartdate,
    di.userid,
    di.queueid,
    qd.name,
    di.conversationenddate,
    ud.name,
    di.segmenttype;

COMMENT ON COLUMN vwCallDetail.ani IS 'ANI'; 
COMMENT ON COLUMN vwCallDetail.CallDuration IS 'Call Duration'; 
COMMENT ON COLUMN vwCallDetail.CallDurationDay IS 'Call Duration per Day'; 
COMMENT ON COLUMN vwCallDetail.conversationenddate IS 'Conversation End Date'; 
COMMENT ON COLUMN vwCallDetail.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN vwCallDetail.conversationstartdate IS 'Conversation Start Date'; 
COMMENT ON COLUMN vwCallDetail.dnis IS 'DNIS'; 
COMMENT ON COLUMN vwCallDetail.mediatype IS 'Media Type'; 
COMMENT ON COLUMN vwCallDetail.name IS 'Name'; 
COMMENT ON COLUMN vwCallDetail.originaldirection IS 'Original Direction'; 
COMMENT ON COLUMN vwCallDetail.purpose IS 'Purpose'; 
COMMENT ON COLUMN vwCallDetail.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN vwCallDetail.queueName IS 'Queue Name'; 
COMMENT ON COLUMN vwCallDetail.segmenttype IS 'Segment Type'; 
COMMENT ON COLUMN vwCallDetail.sessiondirection IS 'Session Direction'; 
COMMENT ON COLUMN vwCallDetail.TalkDuration IS 'Talk Duration'; 
COMMENT ON COLUMN vwCallDetail.TalkDurationDay IS 'Talk Duration per Day'; 
COMMENT ON COLUMN vwCallDetail.userid IS 'User GUID';  

COMMENT ON VIEW vwCallDetail IS 'See DetailedInteractionData - Expands all the GUIDs with their lookups';