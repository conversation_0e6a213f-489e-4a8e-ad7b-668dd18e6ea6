-- Drop existing procedure if it exists
DROP PROCEDURE IF EXISTS update_mvwevaluationgroupdata(date);
DROP PROCEDURE IF EXISTS update_mvwevaluationgroupdata();

-- Create the procedure
CREATE OR REPLACE PROCEDURE update_mvwevaluationgroupdata(
    IN from_date date DEFAULT now() - interval '2 day'
)
LANGUAGE plpgsql
AS $procedure$
BEGIN
    -- Check if the required table exists before attempting to use it
    IF csg_table_exists('mvwevaluationgroupdata') = 1 THEN
        -- Check if the required tables for the join exist
        IF csg_table_exists('evalquestiongroupdata') = 1
           AND csg_table_exists('evaldata') = 1
           AND csg_table_exists('userdetails') = 1 THEN

            INSERT INTO mvwevaluationgroupdata
            (
                SELECT
                    egd.keyid,
                    egd.evaluationid,
                    egd.questiongroupid,
                    (SELECT ed.questiongroupname
                        FROM evaldetails ed
                        WHERE ed.questiongroupid::text = egd.questiongroupid::text
                        LIMIT 1) AS questiongroupname,
                    egd.totalscore,
                    egd.maxtotalscore,
                    egd.markedna,
                    egd.totalcriticalscore,
                    egd.maxtotalcriticalscore,
                    egd.totalnoncriticalscore,
                    egd.maxtotalnoncriticalscore,
                    egd.totalscoreunweighted,
                    egd.maxtotalscoreunweighted,
                    egd.failedkillquestions,
                    ud.divisionid,
                    egd.comments,
                    eda.conversationid
                FROM evalquestiongroupdata egd
                LEFT JOIN evaldata eda ON eda.evaluationid::text = egd.evaluationid::text
                LEFT JOIN userdetails ud ON ud.id::text = eda.userid::text
                WHERE eda.releasedate >= from_date OR eda.updated >= from_date
            )
            ON CONFLICT ON CONSTRAINT mvwevaluationgroupdata_keyid_key
            DO UPDATE SET
                keyid = EXCLUDED.keyid,
                evaluationid = EXCLUDED.evaluationid,
                questiongroupid = EXCLUDED.questiongroupid,
                questiongroupname = EXCLUDED.questiongroupname,
                totalscore = EXCLUDED.totalscore,
                maxtotalscore = EXCLUDED.maxtotalscore,
                markedna = EXCLUDED.markedna,
                totalcriticalscore = EXCLUDED.totalcriticalscore,
                maxtotalcriticalscore = EXCLUDED.maxtotalcriticalscore,
                totalnoncriticalscore = EXCLUDED.totalnoncriticalscore,
                maxtotalnoncriticalscore = EXCLUDED.maxtotalnoncriticalscore,
                totalscoreunweighted = EXCLUDED.totalscoreunweighted,
                maxtotalscoreunweighted = EXCLUDED.maxtotalscoreunweighted,
                failedkillquestions = EXCLUDED.failedkillquestions,
                divisionid = EXCLUDED.divisionid,
                comments = EXCLUDED.comments,
                conversationid = EXCLUDED.conversationid;
        ELSE
            RAISE NOTICE 'One or more required tables for the join do not exist. Skipping update.';
        END IF;
    ELSE
        RAISE NOTICE 'Table mvwevaluationgroupdata does not exist. Skipping update.';
    END IF;
END;
$procedure$;

-- Only call the procedure if it exists and if the table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'update_mvwevaluationgroupdata'
        AND pg_function_is_visible(oid)
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = current_schema()
        AND table_name = 'mvwevaluationgroupdata'
    ) THEN
        CALL update_mvwevaluationgroupdata('2000-01-01');
    ELSE
        RAISE NOTICE 'Skipping call to update_mvwevaluationgroupdata as either the procedure or table does not exist';
    END IF;
END $$;