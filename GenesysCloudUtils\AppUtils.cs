﻿using StandardUtils;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    public class AppUtils

    {

        private Utils UCAUtils = new Utils();
        public string CustomerKeyID { get; set; }
        private Simple3Des UCAEncryption;
        private readonly ILogger? _logger;

        public AppUtils(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            _logger?.LogInformation("Initializing GC Config");
            UCAUtils = new StandardUtils.Utils();

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        }

    }
}
