﻿using StandardUtils;

namespace GenesysAdapter
{
    public class AppUtils

    {

        private Utils UCAUtils = new Utils();
        public string CustomerKeyID { get; set; }
        private Simple3Des UCAEncryption;

        public void Initialize()
        {
            Console.WriteLine("Initialization of GC Config");
            UCAUtils = new StandardUtils.Utils();

            CustomerKeyID = UCAUtils.ReadSetting("CSG_CUSTOMERKEYID");
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        }

    }
}
