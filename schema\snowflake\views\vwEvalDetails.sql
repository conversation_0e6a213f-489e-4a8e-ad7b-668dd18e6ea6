CREATE
OR REPLACE VIEW vwevaldetails AS
SELECT
    id,
    evaluationid,
    evaluationformid,
    evaluationname,
    questiongroupid,
    questiongroupname,
    questiongroupToHighest,
    questiongroupToNA,
    questiongroupwieght,
    questiongroupmanwieght,
    questionid,
    questiontext,
    questionhelptext,
    quesiontype,
    questionnaenabled,
    questioncommentsreq,
    questioniskill,
    questioniscritical,
    questionanwserid,
    questionanswertext,
    questionanswervalue
FROM
    evalDetails;