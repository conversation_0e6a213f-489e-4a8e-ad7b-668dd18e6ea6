CREATE
OR ALTER VIEW [vwUserPresenceDetailedData] AS
SELECT
    upd.keyid,
    upd.userid,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.divisionid as divisionid,
    ud.managername,
    upd.starttime,
    upd.starttimeltc,
    upd.endtime,
    upd.endtimeltc,
    upd.systempresence,
    upd.orgpresence,
    upd.routingstatus,
    upd.timeinstate,
    upd.timeinstate / 86400.00 AS timeinstateDay,
    upd.updated
FROM
    userPresenceDetailedData AS upd
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = upd.userid;