DROP VIEW IF EXISTS vwheadcountforecast;

CREATE
OR REPLACE VIEW vwheadcountforecast AS
select
	hcf.businessunitid,
	bu.name as businessunitname,
	hcf.planninggroup as planninggroup_id,
	pg.name as planninggroup_name,
	pgq.name as planninggroup_queuename,
	hcf.scheduleid,
	sd.shorttermforecastid,
	sd.description as scheduledesc,
	sd.published as sched_published,
	hcf.weekdate,
	hcf.startdate,
	hcf.startdateltc,
	hcf.requiredperinterval,
	hcf.requiredwithoutshrinkageperinterval,
	hcf.updated
from 
	headcountforecastdata hcf
LEFT JOIN budetails bu  ON hcf.businessunitid ::text = bu.id::text
LEFT JOIN scheduledetails sd ON hcf.scheduleid ::text = sd.scheduleid ::text
LEFT JOIN planninggroupdetails pg  ON hcf.planninggroup ::text = pg.id::text
LEFT JOIN queuedetails pgq ON pg.queueid ::text = pgq.id ::text;