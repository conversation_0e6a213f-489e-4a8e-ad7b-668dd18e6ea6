CREATE TABLE IF NOT EXISTS scheduledata (
    keyid varchar(100) NOT NULL,
    userid varchar(50),
    buid varchar(50),
    scheduleid varchar(50),
    shiftid integer,
    shiftstartdate timestamp without time zone,
    shiftstartdateltc timestamp without time zone,
    shiftlengthtime integer,
    activitystartdate timestamp without time zone,
    activitystartdateltc timestamp without time zone,
    activitylengthtime integer,
    activitydescription varchar(200),
    activitycodeid varchar(50),
    activitypaid number,
    shiftmanuallyeditted number,
    updated timestamp without time zone,
    CONSTRAINT scheduledata_pkey PRIMARY KEY (keyid)
) ;

ALTER TABLE scheduledata
ADD COLUMN IF NOT EXISTS shiftstartdateltc TIMESTAMP WITHOUT TIME ZONE;
