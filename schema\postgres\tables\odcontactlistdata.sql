CREATE TABLE IF NOT EXISTS odcontactlistdata (
    keyid varchar(100),
    contactlistid varchar(50),
    "inin-outbound-id" varchar(50),
    updated timestamp without time zone,
    PRIMARY KEY (keyid)
);

COMMENT ON COLUMN odcontactlistdata.contactlistid IS 'Contact List GUID'; 
COMMENT ON COLUMN odcontactlistdata."inin-outbound-id" IS 'inin-outbound-id GUID'; 
COMMENT ON COLUMN odcontactlistdata.keyid IS 'Primary Key'; 
COMMENT ON COLUMN odcontactlistdata.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE odcontactlistdata IS 'Outbound Dialling Contact List Data - Any new attribs are added automatically by the adapter.'; 