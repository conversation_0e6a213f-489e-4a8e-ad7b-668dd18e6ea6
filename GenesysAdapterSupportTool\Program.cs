﻿using Microsoft.Extensions.Configuration;
using StandardUtils;

class SupportTool
{
#if DEBUG
    static private void Help()
    {
        Console.WriteLine("");
        Console.WriteLine("Usage:");
        Console.WriteLine("");
        Console.WriteLine("GenesysAdapterSupportTool Mode=<encrypt|encryptV2|encryptV1|decrypt> Key=<CustomerKeyId|Username> Value=<plaintext|ciphertext>");
        Console.WriteLine("");
        Console.WriteLine("  Encrypt or decrypt a secret.");
        Console.WriteLine("");
        Console.WriteLine("  Encryption versions:");
        Console.WriteLine("    Version 1: Uses the CustomerKeyId as the key, the ciphertext is a base64 string. (Mode=encryptV1)");
        Console.WriteLine("    Version 2: Uses the credential username as the key, the ciphertext is prefixed with 'enc:v2:'. (Mode=encrypt|encryptV2)");
        Console.WriteLine("");
    }
#endif

    static void Main(string[] args)
    {
#if DEBUG
        Console.WriteLine("=========================================================================");
        Console.WriteLine("Genesys Cloud Data Adapter Support Tools v{0}", ApplicationVersion.MajorMinorPatch);
        Console.WriteLine("=========================================================================");
        Console.WriteLine("Version {0}", ApplicationVersion.InformationalVersion);
        Console.WriteLine("");

        var environmentVariablePrefix = "CSG_";
        IConfigurationRoot configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(path: "appsettings.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables(environmentVariablePrefix)
            .AddCommandLine(args)
            .Build();

        bool usingArgs =
            (args.Length == 3 && (
                args[0].StartsWith("encrypt", System.StringComparison.InvariantCultureIgnoreCase)
                || args[0].StartsWith("decrypt", System.StringComparison.InvariantCultureIgnoreCase)
            ));

        var mode = "";
        if (string.IsNullOrEmpty(mode) && usingArgs)
            mode = args[0];
        if (string.IsNullOrEmpty(mode))
            mode = configuration.GetValue<string>("Mode", "");
        if (string.IsNullOrEmpty(mode) || !(
            mode.Equals("encrypt", System.StringComparison.InvariantCultureIgnoreCase)
            || mode.Equals("encryptV1", System.StringComparison.InvariantCultureIgnoreCase)
            || mode.Equals("encryptV2", System.StringComparison.InvariantCultureIgnoreCase)
            || mode.Equals("decrypt", System.StringComparison.InvariantCultureIgnoreCase)
        ))
        {
            Help();
            if (string.IsNullOrEmpty(mode))
                Console.WriteLine("Must pass the 'Mode' parameter");
            else
                Console.WriteLine("Must pass a valid 'Mode' parameter, value passed '{0}'.", mode);

            return;
        }

        var key = "";
        if (string.IsNullOrEmpty(key) && usingArgs)
            key = args[1];
        if (string.IsNullOrEmpty(key))
            key = configuration.GetValue<string>("Key", "");
        if (string.IsNullOrEmpty(key))
            key = configuration.GetValue<string>("CustomerKeyId", "");
        if (string.IsNullOrEmpty(key))
            key = configuration.GetValue<string>("GenesysApi:ClientId", "");
        if (string.IsNullOrEmpty(key))
            key = configuration.GetValue<string>("GenesysApi:ClientId", "");
        if (string.IsNullOrEmpty(key))
        {
            Help();
            Console.WriteLine("Must pass either the 'CustomerKeyId' or 'GenesysApi:ClientId' parameter");
            return;
        }

        var value = "";
        if (mode.StartsWith("encrypt", System.StringComparison.InvariantCultureIgnoreCase)
            || mode.StartsWith("decrypt", System.StringComparison.InvariantCultureIgnoreCase))
        {

            if (string.IsNullOrEmpty(value) && usingArgs)
                value = args[2];
            if (string.IsNullOrEmpty(value))
                value = configuration.GetValue<string>("Value", "");
            if (string.IsNullOrEmpty(value))
            {
                Help();
                Console.WriteLine("Must pass the 'Value' parameter");
                return;
            }
        }

        switch(mode)
        {
            case string m when m.StartsWith("encrypt", StringComparison.InvariantCultureIgnoreCase):
                Console.WriteLine("Attempting to encrypt...");
                Console.WriteLine("Key  : " + key);
                Console.WriteLine("Value: " + value);
                Console.WriteLine();
                Console.WriteLine();
                if (mode.Equals("encryptV1", System.StringComparison.InvariantCultureIgnoreCase))
                {
                    Console.WriteLine(new Simple3Des(key).EncryptData(value));
                }
                else
                {
                    Console.WriteLine(new Secret(key, value).Encrypted);
                }
                Console.WriteLine();
                break;
            case string m when m.Equals("decrypt", StringComparison.InvariantCultureIgnoreCase):
                Console.WriteLine("Attempting to decrypt...");
                Console.WriteLine("Key  : " + key);
                Console.WriteLine("Value: " + value);
                Console.WriteLine();
                Console.WriteLine();
                Console.WriteLine(new Secret(key, value).PlainText);
                Console.WriteLine();
                break;
            default:
                Help();
                return;
        }
#else
        Console.WriteLine("Not available in release builds. Contact CSG support if you need assistance.");
#endif
    }
}
