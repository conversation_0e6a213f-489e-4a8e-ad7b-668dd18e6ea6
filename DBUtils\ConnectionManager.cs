using System;
using System.Data;
using System.Data.Common;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using Npgsql;
using Snowflake.Data.Client;

namespace DBUtils
{
    /// <summary>
    /// Simple class to create database connections
    /// </summary>
    public static class ConnectionManager
    {
        private static readonly object _lock = new object();
        private static string _connectionString;
        private static CSG.Adapter.Configuration.DatabaseType _dbType;
        private static ILogger _logger;
        private static bool _initialized = false;

        /// <summary>
        /// Initialize the connection manager with database settings
        /// </summary>
        /// <param name="connectionString">The database connection string</param>
        /// <param name="dbType">The database type</param>
        /// <param name="logger">Optional logger for logging connection events</param>
        public static void Initialize(string connectionString, CSG.Adapter.Configuration.DatabaseType dbType, ILogger logger = null)
        {
            lock (_lock)
            {
                _connectionString = connectionString;
                _dbType = dbType;
                _logger = logger;
                _initialized = true;

                _logger?.LogInformation("ConnectionManager initialized for {DbType}", dbType);
            }
        }

        /// <summary>
        /// Create a new database connection
        /// </summary>
        /// <returns>A new database connection (not opened)</returns>
        public static DbConnection GetConnection()
        {
            if (!_initialized)
            {
                throw new InvalidOperationException("ConnectionManager has not been initialized. Call Initialize() first.");
            }

            DbConnection connection = null;

            try
            {
                switch (_dbType)
                {
                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                        connection = new SqlConnection(_connectionString);
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                        connection = new MySqlConnection(_connectionString);
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                        connection = new NpgsqlConnection(_connectionString);
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                        connection = new SnowflakeDbConnection(_connectionString);
                        break;
                    default:
                        throw new NotImplementedException($"Unsupported database type: {_dbType}");
                }

                return connection;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to create database connection");
                connection?.Dispose();
                throw;
            }
        }

        /// <summary>
        /// Close and dispose a connection
        /// </summary>
        /// <param name="connection">The connection to close and dispose</param>
        public static void ReturnConnection(DbConnection connection)
        {
            if (connection == null)
                return;

            try
            {
                if (connection.State == ConnectionState.Open)
                {
                    _logger?.LogDebug("Closing database connection");
                    connection.Close();
                }
                connection.Dispose();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error closing connection");
            }
        }

        /// <summary>
        /// Log database connection information
        /// </summary>
        public static void LogPoolStatus()
        {
            if (!_initialized)
            {
                return;
            }

            _logger?.LogInformation("Database connection information for {DbType}", _dbType);
        }

        /// <summary>
        /// Clear all connection pools (maintained for compatibility)
        /// </summary>
        public static void ClearAllPools()
        {
            if (!_initialized)
            {
                return;
            }

            try
            {
                switch (_dbType)
                {
                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                        SqlConnection.ClearAllPools();
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                        MySqlConnection.ClearAllPools();
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                        NpgsqlConnection.ClearAllPools();
                        break;
                    // Snowflake doesn't have a ClearAllPools method
                }
                _logger?.LogInformation("Cleared all connection pools for {DbType}", _dbType);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to clear connection pools");
            }
        }
    }
}
