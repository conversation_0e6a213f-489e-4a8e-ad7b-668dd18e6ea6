CREATE TABLE IF NOT EXISTS knowledgebasecategorydata (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(255),
    externalId VARCHAR(100),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified <PERSON>IM<PERSON><PERSON>MP WITHOUT TIME ZONE,
    documentCount INTEGER,
    knowledgeBaseId VARCHAR(100),
    parentCategoryId VARCHAR(100),
    parentCategoryName VARCHAR(100),
    updated timestamp without time zone,
    CONSTRAINT knowledgebasecategorydata_pkey PRIMARY KEY (id)
);
