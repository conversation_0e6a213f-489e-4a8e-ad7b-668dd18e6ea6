# Message Job Implementation - Complete

## Overview
Successfully created a new "Message" job similar to the existing "Chat" job but for "message" mediatype instead of "chat" mediatype. This implementation provides complete parity with the chat functionality while targeting message-based interactions in Genesys Cloud.

## Files Created/Modified

### 1. Job Configuration
**File:** `GCACommon/GenesysAdapterOptions.cs`
- **Change:** Added `Message` job enum value with description "The aggregated message data"
- **Location:** Line 203 (after InteractionPresence, before KnowledgeBaseDetails)

### 2. Core Message Data Processing
**File:** `GenesysCloudUtils/MessageData.cs` (NEW)
- **Purpose:** Complete message data processing logic parallel to ChatData.cs
- **Key Features:**
  - `GetMessageDataFromGC()` - Retrieves message transcript data from conversations
  - `CalculateMessageData()` - Processes segments with MediaType='message' filter
  - Creates message response time analytics (min, max, total, count)
  - Tracks message initiation (Agent vs Customer)
  - Supports SQL output formatting
  - Reuses existing `ChatObject` and `Transcript` classes

### 3. Message Job Controller
**File:** `GenesysAdapter/GCUpdateMessageData.cs` (NEW)
- **Purpose:** Job controller parallel to GCUpdateChatData.cs
- **Features:**
  - Manages message data synchronization cycle
  - Handles database writing with "messagedata" sync type
  - Updates last successful sync timestamps
  - Proper error handling and logging

### 4. Data Access Layer Integration
**File:** `GCData/GCData.cs`
- **Addition:** New `DetailMessageLastUpdate` property (Line 28)
- **Addition:** New `MessageData()` method that:
  - Creates MessageData instance
  - Configures time zones and date ranges
  - Calls CalculateMessageData with SQL formatting
  - Updates tracking timestamps

### 5. Main Program Integration
**File:** `GenesysAdapter/Program.cs`
- **Addition:** New case for `CSG.Adapter.Configuration.Job.Message` (after Chat case)
- **Calls:** `new GCUpdateMessageData(_logger).UpdateGCMessageData(options.Preferences.RenameParticipantAttributeNames)`

### 6. Database Schema

#### PostgreSQL Schema
**File:** `schema/postgres/tables/messagedata.sql` (NEW)
- Table: `messagedata` with all necessary columns
- Primary key: `keyid`
- Columns: conversationid, conversationstart, conversationstartltc, userid, messageinitiatedby
- Analytics columns: agentmessagecount, agentmessagetotal, agentmessagemax, agentmessagemin
- Customer analytics: custmessagecount, custmessagetotal, custmessagemax, custmessagemin
- Metadata: agenthasread, updated
- Complete column comments for documentation

#### MSSQL Schema
**File:** `schema/mssql/tables/messageData.sql` (NEW)
- Equivalent MSSQL table definition
- Uses `csg_table_exists()` and `csg_column_exists()` functions
- Proper MSSQL data types (nvarchar, decimal, datetime)

### 7. Database Views

#### PostgreSQL View
**File:** `schema/postgres/views/vwmessagedata.sql` (NEW)
- View: `vwmessagedata`
- Joins messageData with vwuserdetail for user information expansion
- Includes manager details and user names
- Complete column comments

#### MSSQL View
**File:** `schema/mssql/views/vwMessageData.sql` (NEW)
- Equivalent MSSQL view using `CREATE OR ALTER VIEW`
- Same functionality as PostgreSQL version

### 8. Job Configuration Updates
**Files:**
- `schema/mssql/tables/jobminimumdefinition.sql`
- `schema/postgres/tables/jobminimumdefinition.sql`
- **Addition:** Message job entry with standard timing: MaxSyncSpan='1.00:00', LookBackSpan='0.08:00'

## Key Architecture Decisions

1. **Code Reuse:** MessageData class reuses existing `ChatObject` and `Transcript` classes from ChatData.cs instead of creating duplicates, avoiding namespace conflicts.

2. **Media Type Filtering:** Uses `MediaType='message'` filter in segment processing, parallel to chat's `MediaType='chat'` filter.

3. **Database Consistency:** Message table schema mirrors chat table structure with appropriate name changes (messageinitiatedby vs chatinitiatedby, etc.).

4. **Time Zone Handling:** Implements the same time zone conversion logic as chat for consistent LTC (Local Time Conversion) handling.

5. **Sync Management:** Uses "messagedata" as the sync type identifier for independent tracking from chat data synchronization.

## Usage

The Message job can now be executed using:
```bash
GenesysAdapter.exe Message
```

Or via configuration file with:
```json
{
  "Job": "Message"
}
```

## Data Flow

1. **Program.cs** → Routes Message job to `GCUpdateMessageData`
2. **GCUpdateMessageData** → Calls `GCGetData.MessageData()`
3. **GCGetData.MessageData()** → Creates `MessageData` instance and calls `CalculateMessageData()`
4. **MessageData.CalculateMessageData()** → 
   - Queries segments with `MediaType='message'`
   - For each conversation, calls `GetMessageDataFromGC()`
   - Processes message transcripts for timing analytics
   - Returns formatted data for database insertion
5. **GCUpdateMessageData** → Writes results to `messagedata` table via `DBAdapter.WriteSQLData()`

## Testing Status

- **Build Status:** ✅ Successfully compiles with no errors
- **Warnings:** Only standard code quality warnings (nullability, obsolete APIs)
- **Architecture:** Complete parallel implementation to existing Chat job

## Database Objects Summary

**Tables:** messagedata
**Views:** vwmessagedata, vwMessageData  
**Configuration:** jobminimumdefinition entries for Message job

The implementation provides complete feature parity with the Chat job while maintaining code consistency and following established patterns in the codebase.
