CREATE
OR ALTER VIEW [vwCallSummary] AS
SELECT
    di.conversationid AS [Fact.ConversationId],
    qd.name AS [Fact.QueueName],
    di.conversationstartdate AS [Date.ConversationStartDate],
    di.conversationenddate AS [Date.ConversationEndDate],
    di.ani AS [Fact.ANI],
    di.dnis AS [Fact.DNIS],
    di.originaldirection AS [Fact.OriginalDirection],
    <PERSON>UM(di.ttalkcomplete) AS [Raw.Talk],
    <PERSON>UM(di.tacw) AS [Raw.ACW],
    <PERSON>UM(di.theldcomplete) AS [Raw.Hold],
    <PERSON>UM(di.tvoicemail) AS [Raw.Voicemail],
    <PERSON><PERSON>(di.ntransferred) AS [Count.Transferred],
    <PERSON><PERSON>(di.tabandon) AS [Raw.Abandon],
    <PERSON><PERSON>(di.ttalkcomplete) / 86400.00 AS [Raw.TalkDay],
    SUM(di.tacw) / 86400.00 AS [Raw.ACWDay],
    <PERSON><PERSON>(di.theldcomplete) / 86400.00 AS [Raw.HoldDay],
    <PERSON><PERSON>(di.tvoicemail) / 86400.00 AS [Raw.VoicemailDay],
    <PERSON><PERSON>(di.ntransferred) / 86400.00 AS [Count.TransferredDay],
    <PERSON><PERSON>(di.tabandon) / 86400.00 AS [Raw.AbandonDay]
FROM
    detailedInteractionData AS di
    LEFT OUTER JOIN queueDetails AS qd ON qd.id = di.queueid
GROUP BY
    di.conversationid,
    qd.name,
    di.conversationstartdate,
    di.conversationenddate,
    di.ani,
    di.dnis,
    di.originaldirection