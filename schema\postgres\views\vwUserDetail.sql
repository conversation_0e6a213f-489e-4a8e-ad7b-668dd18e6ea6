-- First, check if the required tables exist
DO $$
DECLARE
    tables_exist BOOLEAN;
BEGIN
    -- Check if the required tables exist
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = current_schema()
        AND table_name = 'userdetails'
    ) INTO tables_exist;

    IF NOT tables_exist THEN
        RAISE NOTICE 'Table userdetails does not exist. Skipping view creation.';
        RETURN;
    END IF;

    -- Check if the divisiondetails table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = current_schema()
        AND table_name = 'divisiondetails'
    ) INTO tables_exist;

    IF NOT tables_exist THEN
        RAISE NOTICE 'Table divisiondetails does not exist. Skipping view creation.';
        RETURN;
    END IF;

    -- Check if the function exists before trying to use it
    IF EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'csg_view_definition_contains_string'
        AND pg_function_is_visible(oid)
    ) THEN
        -- Only execute this block if the function exists
        IF csg_view_definition_contains_string('vwuserdetail', 'lower(ud.email::text) AS email,') = 0 THEN
            DROP MATERIALIZED VIEW IF EXISTS mvwevaluationoverview;
            DROP VIEW IF EXISTS vwdetailedinteractiondata;
            DROP VIEW IF EXISTS vwscheduledata;
            DROP VIEW IF EXISTS vwuserinteractionpresencedetaileddata;
            DROP VIEW IF EXISTS vwuserpresencedata;
            DROP VIEW IF EXISTS vwadherenceactdata;
            DROP VIEW IF EXISTS vwadherencedaydata;
            DROP VIEW IF EXISTS vwadherenceexcdata;
            DROP VIEW IF EXISTS vwchatdata;
            DROP VIEW IF EXISTS vwtimeoffdata;
            DROP VIEW IF EXISTS vwtimeoffrequestdata;
            DROP VIEW IF EXISTS vwuserpresencedatadaily;
            DROP VIEW IF EXISTS vwrealtimeuser;
            DROP VIEW IF EXISTS vwqueueconvrealtime;
            DROP VIEW IF EXISTS vwuserinteractiondata;
            DROP VIEW IF EXISTS vwqueueauditdata;
            DROP VIEW IF EXISTS vwrealtimeuser_groups;
            DROP VIEW IF EXISTS vwuserqueuemappings;
            DROP VIEW IF EXISTS vwuserdetail;
        END IF;
    ELSE
        -- If the function doesn't exist, just drop the view to recreate it
        DROP VIEW IF EXISTS vwuserdetail CASCADE;
    END IF;
END $$;

DROP VIEW IF EXISTS vwuserdetail cascade;

-- Only create the view if the required tables exist
DO $$
DECLARE
    tables_exist BOOLEAN;
BEGIN
    -- Check if the required tables exist again (in case they were created between checks)
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = current_schema()
        AND table_name = 'userdetails'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = current_schema()
        AND table_name = 'divisiondetails'
    ) INTO tables_exist;

    IF tables_exist THEN
        EXECUTE '
        CREATE OR REPLACE VIEW vwuserdetail AS
SELECT
    ud.id,
    ud.name,
    ud.jabberid,
    ud.state,
    ud.title,
    lower(ud.email::text) AS email,
    ud.username,
    ud.department,
    ud.manager as managerid,
    md.name as managername,
    ud.username as agentname,
    ud.divisionid,
    ud.employeeid,
    ud.dateHire,
    ud.updated,
    dd.name as divisionname,
    dd.homedivision
FROM
    userdetails AS ud
    LEFT JOIN userdetails AS md ON md.id = ud.manager
    LEFT JOIN divisiondetails AS dd ON ud.divisionid = dd.id;

-- Add comments

COMMENT ON COLUMN vwuserdetail.id IS ''User GUID'';
COMMENT ON COLUMN vwuserdetail.name IS ''User Name'';
COMMENT ON COLUMN vwuserdetail.jabberid IS ''Jabber GUID'';
COMMENT ON COLUMN vwuserdetail.state IS ''User State'';
COMMENT ON COLUMN vwuserdetail.title IS ''User Title'';
COMMENT ON COLUMN vwuserdetail.email IS ''Email Address'';
COMMENT ON COLUMN vwuserdetail.username IS ''Username'';
COMMENT ON COLUMN vwuserdetail.department IS ''User Department'';
COMMENT ON COLUMN vwuserdetail.managerid IS ''Manager GUID'';
COMMENT ON COLUMN vwuserdetail.managername IS ''Manager Name'';
COMMENT ON COLUMN vwuserdetail.agentname IS ''Agent Name'';
COMMENT ON COLUMN vwuserdetail.divisionid IS ''Division GUID'';
COMMENT ON COLUMN vwuserdetail.employeeid IS ''Employee GUID'';
COMMENT ON COLUMN vwuserdetail.dateHire IS ''Employee Hire Date'';
COMMENT ON COLUMN vwuserdetail.updated IS ''Last Updated'';
COMMENT ON COLUMN vwuserdetail.divisionname IS ''Division Name'';
COMMENT ON COLUMN vwuserdetail.homedivision IS ''Home Division'';

COMMENT ON VIEW vwuserdetail IS ''See UserDetail: User Description in detail'';';
    ELSE
        RAISE NOTICE 'Required tables for vwuserdetail do not exist. Skipping view creation.';
    END IF;
END $$;