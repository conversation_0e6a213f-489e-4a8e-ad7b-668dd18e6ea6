<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace></RootNamespace>
    <NoWarn>CS0649;CS0169</NoWarn>
    <NukeRootDirectory>..</NukeRootDirectory>
    <NukeScriptDirectory>..</NukeScriptDirectory>
    <NukeTelemetryVersion>1</NukeTelemetryVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Nuke.Common" Version="6.2.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageDownload Include="coverlet.console" Version="[3.2.0]" />
    <PackageDownload Include="GitVersion.Tool" Version="[5.10.3]" />
    <PackageDownload Include="ReportGenerator" Version="[5.1.10]" />
  </ItemGroup>

</Project>
