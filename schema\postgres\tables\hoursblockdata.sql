CREATE TABLE IF NOT EXISTS hoursblockdata (
    keyid varchar(200) NOT NULL,
    userid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    enddate timestamp without time zone,
    enddateltc timestamp without time zone,
    totalhrs numeric(20, 2),
    breakhrs numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT hoursblockdata_pkey PRIMARY KEY (keyid)
);

CREATE INDEX IF NOT EXISTS hoursblockdataenddate ON hoursblockdata USING btree (enddate ASC NULLS LAST) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS hoursblockdatauserid ON hoursblockdata USING btree (
    userid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS hoursblockedate ON hoursblockdata USING btree (startdate ASC NULLS LAST) TABLESPACE pg_default;