DROP VIEW IF EXISTS vwGroupDetails;

CREATE
OR REPLACE VIEW vwGroupDetails AS
SELECT
    id,
    name,
    description,
    membercount,
    state,
    type,
    selfUri
FROM
    groupDetails;

COMMENT ON COLUMN vwGroupDetails.description IS 'Agent Group Description'; 
COMMENT ON COLUMN vwGroupDetails.membercount IS 'Agent Group Number of Members (Includes all members and owners if flag set in GC)';
COMMENT ON COLUMN vwGroupDetails.id IS 'Primary Key / Agent Group GUID';  
COMMENT ON COLUMN vwGroupDetails.name IS 'Agent Group Name'; 
COMMENT ON COLUMN vwGroupDetails.state IS 'Agent Group State'; 
COMMENT ON COLUMN vwGroupDetails.type IS 'Agent Group Type'; 