﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Auditing = GenesysCloudDefWFMAuditing;
using Newtonsoft.Json;
using StandardUtils;
using System.Web;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace GenesysCloudUtils
{

    public class WFMAuditData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private readonly GCUtils GCUtilities = new GCUtils();
        private readonly JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        public string OAuthUser { get; set; }
        public DateTime WFMAuditLastUpdate { get; set; }
        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;
        private readonly ILogger? _logger;

        public WFMAuditData(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            Console.WriteLine("Obtaining Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }

        public DataTable GetWFMAuditData(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueAuditData = DBUtil.CreateInMemTable("WFMAuditData");

            string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\",\"serviceName\": \"WorkforceManagement\"}";

            _logger?.LogDebug("WFM Audit JSON: {RequestBody}", RequestBody);
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query", GCApiKey, RequestBody);

            // Check if the response contains an error related to exceeding the maximum retention range
            if (JsonString != null &&
                (JsonString.Contains("exceeds the maximum retention range") ||
                 JsonString.Contains("\"code\":\"bad.request\"")))
            {
                _logger?.LogError("Error response received from Genesys Cloud API: {Response}", JsonString);
                return QueueAuditData; // Return empty table
            }

            // Check for 202 Accepted response (async job started)
            if (JsonString != null && (JsonString.Contains("\"statusCode\":\"Accepted\"") ||
                (JsonString.Contains("\"error\": true") && JsonString.Contains("\"message\": \"Accepted:"))))
            {
                _logger?.LogDebug("Received 202 Accepted response, extracting job information");
                // Use regex to extract the job ID from the malformed JSON message
                var jobIdPattern = @"""id"":""([a-f0-9\-]{36})""";
                var match = Regex.Match(JsonString, jobIdPattern);
                if (match.Success)
                {
                    var jobId = match.Groups[1].Value;
                    var currentTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                    JsonString = $"{{\"id\":\"{jobId}\",\"state\":\"Running\",\"startDate\":\"{currentTime}\"}}";
                    _logger?.LogDebug("Extracted job ID {JobId} from malformed 202 Accepted response", jobId);
                }
                else
                {
                    _logger?.LogError("Failed to extract job ID from 202 Accepted response: {Response}", JsonString);
                    return QueueAuditData; // Return empty table
                }
            }

            // Check if we have a valid JSON response to process
            if (string.IsNullOrEmpty(JsonString) || JsonString.Length <= 30)
            {
                _logger?.LogWarning("WFM audit job response too short or invalid: {Response}", JsonString);
                return QueueAuditData;
            }

            Auditing.AuditJob AuditJobInfo;

            // Process the job information
            {
                try
                {
                    AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                    // Verify that we have a valid job ID before proceeding
                    if (AuditJobInfo == null || string.IsNullOrEmpty(AuditJobInfo.id))
                    {
                        _logger?.LogError("Failed to get a valid WFM audit job ID from response: {Response}", JsonString);
                        return QueueAuditData; // Return empty table
                    }

                    _logger?.LogInformation("WFM audit job {JobId} started, polling for completion", AuditJobInfo.id);

                    // Use the shared job manager for polling
                    var jobManager = new GenesysJobManager(JsonActions, GCApiKey, URI);
                    var jobConfig = new JobConfig
                    {
                        JobId = AuditJobInfo.id,
                        JobType = "WFMAudit",
                        ContextInfo = $"WFM audit data from {StartDate} to {EndDate}",
                        StatusEndpoint = URI + "/api/v2/audits/query/" + AuditJobInfo.id,
                        TimeoutMinutes = 30,
                        PollIntervalSeconds = 10,
                        CompletedStatuses = new List<string> { "succeeded" },
                        FailedStatuses = new List<string> { "failed", "cancelled" },
                        StatusParser = (json) =>
                        {
                            try
                            {
                                var auditJobData = JsonConvert.DeserializeObject<Auditing.AuditJob>(json,
                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                                return new JobStatusInfo
                                {
                                    Status = auditJobData?.state ?? "unknown",
                                    JobData = auditJobData
                                };
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogWarning(ex, "Failed to parse WFM audit job status from JSON: {Json}", json);
                                return null;
                            }
                        }
                    };

                    var jobResult = jobManager.PollJobToCompletion(jobConfig);

                    if (!jobResult.Success)
                    {
                        _logger?.LogError("WFM audit job {JobId} failed: {ErrorMessage}", AuditJobInfo.id, jobResult.ErrorMessage);
                        return QueueAuditData; // Return empty table
                    }

                    _logger?.LogInformation("WFM audit job {JobId} completed successfully", AuditJobInfo.id);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing WFM audit job response: {Response}", JsonString);
                    return QueueAuditData; // Return empty table
                }

                // Job completed successfully, now retrieve the results
                string CursorString = String.Empty;
                string LastCursor = String.Empty;
                bool FirstTime = true;
                bool RepeatDownload = true;
                int NoChangeCounter = 0;

                try
                {
                    while (RepeatDownload && NoChangeCounter < 2)
                    {
                        if (FirstTime)
                        {
                            CursorString = "?pageSize=100";
                            FirstTime = false;
                        }
                        else
                        {
                            CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor) + "&pageSize=100";
                        }

                        string resultsUrl = URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString;
                        _logger?.LogDebug("Requesting WFM audit results: {Url}", resultsUrl);

                        JsonString = JsonActions.JsonReturnString(resultsUrl, GCApiKey);

                        // Check for errors in the results response
                        if (JsonString == null ||
                            JsonString.Contains("\"error\":") ||
                            JsonString.Contains("\"status\":4"))
                        {
                            _logger?.LogError("Error retrieving WFM audit results: {Response}", JsonString);
                            break;
                        }

                        Auditing.WFMAudit AuditChangeData;

                        try
                        {
                            AuditChangeData = JsonConvert.DeserializeObject<Auditing.WFMAudit>(JsonString,
                                              new JsonSerializerSettings
                                              {
                                                  NullValueHandling = NullValueHandling.Ignore
                                              });
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Failed to deserialize WFM audit results: {Response}", JsonString);
                            break;
                        }

                        _logger?.LogDebug("WFM audit results page retrieved, processing entities");

                        if (AuditChangeData.entities != null && AuditChangeData.entities.Length > 0)
                        {
                            if (AuditChangeData.cursor != null)
                            {
                                LastCursor = AuditChangeData.cursor;
                                RepeatDownload = true;
                            }
                            else
                            {
                                RepeatDownload = false;
                            }

                            NoChangeCounter = 0; // Reset counter when we get new data
                            _logger?.LogDebug("Processing {EntityCount} WFM audit entities, cursor: {Cursor}, repeat: {Repeat}",
                                AuditChangeData.entities.Length, AuditChangeData.cursor, RepeatDownload);

                            int addedCount = 0;
                            int duplicateCount = 0;

                            foreach (Auditing.Entity AuditEntry in AuditChangeData.entities)
                            {
                                if (AuditEntry.entityType == "Schedule")
                                {
                                    foreach (Auditing.Propertychange AgentId in AuditEntry.propertyChanges)
                                    {
                                        if (AgentId.newValues != null)
                                        {
                                            foreach (string Agent in AgentId.newValues)
                                            {
                                                try
                                                {
                                                    DataRow DRAuditRow = QueueAuditData.NewRow();

                                                    string UserId = Agent;
                                                    string MajorAction = AuditEntry.action.ToLower();

                                                    DRAuditRow["keyid"] = AuditEntry.entity.id + "|" + AuditEntry.action.ToLower() + "|" + AuditEntry.eventDate.ToString("yyyyMMddhhmmss") + "|" + AuditEntry.entity.id + "|" + UserId;
                                                    DRAuditRow["scheduleid"] = AuditEntry.entity.id;
                                                    DRAuditRow["addorremove"] = MajorAction;
                                                    DRAuditRow["datemodified"] = AuditEntry.eventDate;
                                                    DRAuditRow["datemodifiedLTC"] = TimeZoneInfo.ConvertTimeFromUtc(AuditEntry.eventDate, AppTimeZone);
                                                    DRAuditRow["modifiedby"] = AuditEntry.user.id;
                                                    DRAuditRow["agentid"] = UserId;
                                                    QueueAuditData.Rows.Add(DRAuditRow);
                                                    addedCount++;
                                                }
                                                catch (System.Data.ConstraintException ex)
                                                {
                                                    _logger?.LogWarning(ex, "Duplicate WFM audit row detected");
                                                    duplicateCount++;
                                                }
                                                catch (Exception ex)
                                                {
                                                    _logger?.LogError(ex, "Error processing WFM audit entry for agent {Agent}", Agent);
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            _logger?.LogDebug("Processed WFM audit page: {AddedCount} added, {DuplicateCount} duplicates", addedCount, duplicateCount);
                        }
                        else
                        {
                            _logger?.LogDebug("No WFM audit data returned in this page");
                            RepeatDownload = false;
                            NoChangeCounter++;
                        }

                    }





                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error retrieving WFM audit results");
                }

            _logger?.LogInformation("WFM audit data retrieval completed. Total rows: {RowCount}", QueueAuditData.Rows.Count);
            return QueueAuditData;
            }
        }
    }

    public class WFMSchedule
    {
        public string status { get; set; }
        public string operationId { get; set; }
        public Result result { get; set; }
    }

    public class Result
    {
        public Agentschedule[] agentSchedules { get; set; }
        public string businessUnitTimeZone { get; set; }
        public Publishedschedule[] publishedSchedules { get; set; }
    }

    public class Agentschedule
    {
        public User user { get; set; }
        public Shift[] shifts { get; set; }
        public Fulldaytimeoffmarker[] fullDayTimeOffMarkers { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Shift
    {
        public string id { get; set; }
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public Activity[] activities { get; set; }
        public bool manuallyEdited { get; set; }
        public Schedule schedule { get; set; }
    }

    public class Schedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public string selfUri { get; set; }
    }

    public class Activity
    {
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
    }

    public class Fulldaytimeoffmarker
    {
        public string businessUnitDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
        public string timeOffRequestId { get; set; }
    }

    public class Publishedschedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public int weekCount { get; set; }
        public string selfUri { get; set; }
    }
}
