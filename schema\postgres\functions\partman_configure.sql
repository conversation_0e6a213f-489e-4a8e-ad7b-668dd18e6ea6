-- pg_partman extension configuration

SET
    search_path TO partman;

CREATE OR REPLACE FUNCTION partman.create_partman_monthly(p_table text, p_column text)
RETURNS integer
LANGUAGE plpgsql
AS
$func$
DECLARE
	v_full_table_name text;
	v_parent_table_exists text;
BEGIN
-- Determine full table name with schema
IF position('.' in p_table) = 0 THEN
	v_full_table_name := get_preferred_schema() || '.' || p_table;
ELSE
	v_full_table_name := p_table;
END IF;

-- Check if the parent table is already configured in partman
SELECT parent_table INTO v_parent_table_exists
FROM partman.part_config
WHERE parent_table = v_full_table_name;

-- If not configured, create the parent table with partman
IF v_parent_table_exists IS NULL THEN
	PERFORM partman.create_parent(
		p_parent_table => v_full_table_name,
		p_control => p_column,
		p_type => 'native',
		p_interval=> 'monthly',
		p_start_partition := '2019-01-01',
		p_premake => 4);

	UPDATE partman.part_config
	SET infinite_time_partitions = true
	WHERE parent_table = v_full_table_name;

	RETURN 1;
END IF;
RETURN 0;
END
$func$;

