-- Step 1: Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS jobminimumdefinition (
    JobID SERIAL PRIMARY KEY,
    JobName VARCHAR(255),
    MaxSyncSpan VARCHAR(50),
    LookBackSpan VARCHAR(50)
);

-- Step 2: Clean up any existing duplicate records
DO $$
BEGIN
    DELETE FROM jobminimumdefinition
    WHERE ctid NOT IN (
        SELECT min(ctid)
        FROM jobminimumdefinition
        GROUP BY JobName
    );
END $$;

-- Step 3: Check if the unique constraint on <PERSON>Name exists, and add it if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'jobname_unique' 
        AND conrelid = 'jobminimumdefinition'::regclass
    ) THEN
        ALTER TABLE jobminimumdefinition
        ADD CONSTRAINT jobname_unique UNIQUE (JobName);
    END IF;
END $$;

-- Step 4: Insert data, ignoring conflicts on the unique constraint
INSERT INTO jobminimumdefinition (<PERSON><PERSON><PERSON>, MaxSyncSpan, LookBackSpan)
VALUES 
    ('Adherence', '7.00:00', '14.00:00'),
    ('Aggregation', '1.00:00', '0.08:00'),
    ('Chat', '1.00:00', '0.08:00'),
    ('Evaluation', '7.00:00', '90.00:00'),
    ('EvaluationCatchup', '1.00:00', '0.08:00'),
    ('FactData', '1.00:00', '0.08:00'),
    ('HeadCountForecast', '1.00:00', '0.08:00'),
    ('HoursBlockData', '1.00:00', '0.08:00'),
    ('Information', '1.00:00', '0.08:00'),
    ('Install', '1.00:00', '0.08:00'),
    ('Interaction', '1.00:00', '0.08:00'),
    ('InteractionPresence', '1.00:00', '0.08:00'),
    ('Knowledge', '1.00:00', '0.08:00'),
    ('OAuthUsage', '1.00:00', '0.08:00'),
    ('ODContactLists', '1.00:00', '0.08:00'),
    ('ODDetails', '1.00:00', '0.08:00'),
    ('OfferedForecast', '1.00:00', '0.08:00'),
    ('PresenceDetail', '1.00:00', '0.08:00'),
    ('QueueMembership', '1.00:00', '0.08:00'),
    ('Realtime', '1.00:00', '0.08:00'),
    ('ScheduleDetails', '1.00:00', '0.08:00'),
    ('Shrinkage', '1.00:00', '0.08:00'),
    ('Subscription', '1.00:00', '0.08:00'),
    ('SubsUsers', '1.00:00', '0.08:00'),
    ('Survey', '1.00:00', '0.08:00'),
    ('SysConvUsage', '1.00:00', '0.08:00'),
    ('TimeOffReq', '1.00:00', '0.08:00'),
    ('UserQueueAudit', '1.00:00', '0.08:00'),
    ('UserQueueMapping', '1.00:00', '0.08:00'),
    ('VoiceAnalysis', '1.00:00', '0.08:00'),
    ('WFMAudit', '1.00:00', '0.08:00'),
    ('WFMSchedule', '30.00:00', '30.00:00'),
    ('Learning', '1.00:00', '2.00:00')
ON CONFLICT (JobName) DO NOTHING;


UPDATE JobMinimumDefinition
SET LookBackSpan = CASE
    WHEN TO_CHAR(CURRENT_DATE, 'YY-MM-DD') = '24-12-19'
         AND REGEXP_REPLACE(LookBackSpan::text, '\.', ':')::interval < interval '4 days'
    THEN '4.00:00'
    ELSE '0.08:00'
END
WHERE JobName = 'Interaction';


UPDATE JobMinimumDefinition
SET LookBackSpan = '90.00:00'
WHERE JobName = 'Evaluation'
  AND REGEXP_REPLACE(LookBackSpan::text, '\.', ':')::interval < interval '90 days';
