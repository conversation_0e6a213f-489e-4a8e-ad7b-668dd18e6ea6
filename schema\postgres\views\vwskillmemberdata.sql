CREATE
OR REPLACE VIEW vwskillmemberdata AS
SELECT
	sm.USERID,
	ud.NAME AS user_name,
	sd.NAME AS skill_name,
	sm.PROFICIENCY,
	sm.STATE 
from 
	USERSKILLMAPPINGS sm
	LEFT JOIN VWUSERDETAIL ud ON sm.USERID = ud.ID
	LEFT JOIN SKILLDETAILS sd ON sm.SKILLID = sd.ID
ORDER BY 3,2;

COMMENT ON COLUMN vwskillmemberdata.USERID IS 'User ID';
COMMENT ON COLUMN vwskillmemberdata.user_name IS 'User Name';
COMMENT ON COLUMN vwskillmemberdata.skill_name IS 'Skill Name';
COMMENT ON COLUMN vwskillmemberdata.PROFICIENCY IS 'Indicates the proficiency level';
COMMENT ON COLUMN vwskillmemberdata.STATE IS 'Represents the current state';
COMMENT ON VIEW vwskillmemberdata IS 'Contains data on skill mappings for users';
