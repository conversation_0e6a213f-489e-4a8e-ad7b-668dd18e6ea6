CREATE OR REPLACE VIEW z_WFMScheduleData AS
SELECT
    sc.userid,
    sc.shiftstartdate,
    sc.shiftlengthtime,
    sc.activitystartdate,
    sc.activitystartdateltc,
    sc.activitylengthtime,
    sc.activitydescription,
    sc.activitycodeid,
    ad.category AS activitycategory,
    sc.activitypaid,
    sc.shiftmanuallyeditted,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername,
    ad.name AS activitycodedesc,
    bu.name AS businessunit,
    null as timeoffrequestid
FROM
    scheduleData sc
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = sc.userid
    LEFT OUTER JOIN activitycodeDetails ad ON ad.businessunitid = sc.buid
    AND ad.id = sc.activitycodeid
    LEFT OUTER JOIN buDetails AS bu ON bu.id = sc.buid
UNION
SELECT
    td.userid,
    td.businessunitdate AS shiftstartdate,
    CASE WHEN tdreq.isfulldayrequest = 1 THEN 86400 ELSE td.length END as shiftlengthtime,
    td.businessunitdate AS activitystartdate,
    td.businessunitdate AS activitystartdateltc,
    CASE WHEN tdreq.isfulldayrequest = 1 THEN 86400 ELSE td.length END as activitylengthtime,
    'Absent' as activitydescription,
    td.activitycode AS activitycodeid,
    'Absent' as activitycategory,
    0 as activitypaid,
    0 as shiftmanuallyeditted,
    userdet.name AS agentname,
    userdet.managerid AS managerid,
    userdet.managername,
    td.description AS activitycodedesc,
    businessunit.name AS businessunit,
    tdreq.id as timeoffrequestid
FROM
    timeoffData td
    LEFT OUTER JOIN vwUserDetail AS userdet ON userdet.id = td.userid
    LEFT OUTER JOIN activitycodeDetails ad ON ad.businessunitid = ad.businessunitid
    AND ad.id = td.activitycode
    LEFT OUTER JOIN buDetails AS businessunit ON businessunit.id = ad.businessunitid
    LEFT OUTER JOIN timeoffrequestData AS tdreq ON tdreq.id = td.timeoffrequestid;
