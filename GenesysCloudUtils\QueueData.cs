﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Auditing = GenesysCloudDefQueueAuditing;
using Interactions = GenesysCloudDefInteractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class QueueData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;
        public string AggInterval { get; set; }
        public string QueueAggViews { get; set; }
        private ILogger _logger;

        // Property to track the last error message for retry logic
        public string LastErrorMessage { get; private set; }
        public QueueData(ILogger logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            _logger?.LogDebug("Initializing QueueData");
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            _logger?.LogDebug("QueueData initialization completed");
        }

        /// <summary>
        /// Gets queue interaction data from Genesys Cloud for the specified date range.
        /// </summary>
        /// <param name="StartDate">The start date in ISO 8601 format.</param>
        /// <param name="EndDate">The end date in ISO 8601 format.</param>
        /// <param name="AggViews">The aggregation views configuration.</param>
        /// <param name="granularity">Optional granularity in ISO-8601 duration format (e.g., PT15M, PT30M, PT1H). If not provided, uses the default AggInterval from configuration.</param>
        /// <returns>A DataTable containing queue interaction data.</returns>
        public DataTable GetQueueInteractionDataFromGC(String StartDate, String EndDate, String AggViews, string granularity = null)
        {
            // Use provided granularity or fall back to the default AggInterval
            string intervalValue = string.IsNullOrEmpty(granularity) ? AggInterval : granularity;

            // Ensure granularity is in proper ISO-8601 format for Genesys Cloud API
            if (!string.IsNullOrEmpty(intervalValue))
            {
                // If intervalValue is just a number (legacy format), convert to ISO-8601
                if (int.TryParse(intervalValue, out int minutes))
                {
                    intervalValue = $"PT{minutes}M";
                    _logger?.LogDebug("Converted legacy granularity format '{LegacyFormat}' to ISO-8601 format '{Iso8601Format}'",
                        granularity ?? AggInterval, intervalValue);
                }
                // If it doesn't start with PT, assume it's minutes and format correctly
                else if (!intervalValue.StartsWith("PT"))
                {
                    // Try to extract number from string like "30M" or "30"
                    string numericPart = new string(intervalValue.Where(char.IsDigit).ToArray());
                    if (int.TryParse(numericPart, out int extractedMinutes))
                    {
                        intervalValue = $"PT{extractedMinutes}M";
                        _logger?.LogDebug("Formatted granularity '{OriginalFormat}' to ISO-8601 format '{Iso8601Format}'",
                            granularity ?? AggInterval, intervalValue);
                    }
                    else
                    {
                        // Default to 30 minutes if we can't parse
                        intervalValue = "PT30M";
                        _logger?.LogWarning("Unable to parse granularity '{InvalidFormat}', defaulting to PT30M",
                            granularity ?? AggInterval);
                    }
                }

                // Validate minimum granularity (1 minute)
                try
                {
                    TimeSpan duration = System.Xml.XmlConvert.ToTimeSpan(intervalValue);
                    if (duration.TotalMinutes < 1)
                    {
                        intervalValue = "PT1M";
                        _logger?.LogWarning("Granularity was less than 1 minute, adjusted to PT1M");
                    }
                }
                catch (Exception ex)
                {
                    intervalValue = "PT30M";
                    _logger?.LogWarning(ex, "Invalid granularity format, defaulting to PT30M");
                }
            }
            else
            {
                intervalValue = "PT30M"; // Default fallback
                _logger?.LogDebug("No granularity specified, using default PT30M");
            }

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueInteraction = DBUtil.CreateInMemTable("queueInteractionData");

            _logger?.LogInformation("Retrieving Queue Interaction Data from {0} with granularity {1}", StartDate, intervalValue);

            DateTime TempUserInteractionLastUpdate = QueueInteractionLastUpdate;

            // First try with the full date range
            bool useChunking = false;

            StringBuilder AViews = new StringBuilder();
            foreach (string AggregationViews in AggViews.Split(';'))
            {
                string[] Views = AggregationViews.Split(',');
                AViews.Append("{\"target\": \"" + Views[0] + "\",  \"name\": \"" + Views[3] + "\", \"function\": \"rangeBound\",  \"range\": { \"gte\": " + Views[1] + ",    \"lt\": " + Views[2] + "}},");
            }

            string RequestBody = "{ " +
                                 "  \"interval\": \"" + StartDate + "/" + EndDate + "\"," +
                                 "  \"granularity\": \"" + intervalValue + "\"," +
                                 "  \"groupBy\": [" +
                                 "    \"queueId\"," +
                                 "    \"mediaType\"," +
                                 "    \"direction\"," +
                                 "    \"wrapUpCode\"" +
                                 "  ]," +
                                 "    \"metrics\": [" +
                                 "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\",\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\",\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\",\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\",\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\",\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\",\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\",\"tVoicemail\",\"tWait\"" +
                                 " ]";

            if (AViews.Length > 0)
            {
                AViews.Length = AViews.Length - 1;
                RequestBody = RequestBody + ", \"views\": [" +
                        AViews.ToString() + "]}";
            }
            else
            {
                RequestBody += "}";
            }

            // Logging of request body is disabled
            // _logger?.LogDebug("Queue interaction data request body: {RequestBody}", RequestBody);

            string JsonString = string.Empty;
            try
            {
                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "API call failed in GetQueueInteractionDataFromGC");

                // Check if the error is related to result set size
                if (ex.Message.Contains("Result set is larger than result limit") ||
                    ex.Message.Contains("too many results") ||
                    ex.Message.Contains("result limit"))
                {
                    _logger?.LogWarning("Detected result size limit error. Switching to chunked processing.");
                    useChunking = true;
                }
                else
                {
                    // For other errors, just return the empty table
                    return QueueInteraction;
                }
            }

            // Check if the response is an error response related to result size
            if (!useChunking && JsonString != null &&
                (JsonString.Contains("Result set is larger than result limit") ||
                 JsonString.Contains("too many results") ||
                 JsonString.Contains("result limit")))
            {
                _logger?.LogWarning("Detected result size limit error in response. Switching to chunked processing.");
                useChunking = true;
            }

            // If we need to use chunking, parse the dates and process in chunks
            if (useChunking)
            {
                _logger?.LogInformation("Using chunked processing to handle large result set.");

                // Parse start and end dates
                if (!DateTime.TryParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime startDateTime))
                {
                    _logger?.LogError("Invalid StartDate format '{StartDate}' in GetQueueInteractionDataFromGC.", StartDate);
                    return QueueInteraction;
                }

                if (!DateTime.TryParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime endDateTime))
                {
                    _logger?.LogError("Invalid EndDate format '{EndDate}' in GetQueueInteractionDataFromGC.", EndDate);
                    return QueueInteraction;
                }

                // Calculate the maximum chunk size (6 hours)
                TimeSpan chunkSize = TimeSpan.FromHours(6);

                // Process data in chunks
                for (DateTime chunkStart = startDateTime; chunkStart < endDateTime; chunkStart = chunkStart.Add(chunkSize))
                {
                    // Calculate chunk end (either 6 hours later or the original end date, whichever is earlier)
                    DateTime chunkEnd = chunkStart.Add(chunkSize);
                    if (chunkEnd > endDateTime)
                    {
                        chunkEnd = endDateTime;
                    }

                    string chunkStartStr = chunkStart.ToString("yyyy-MM-ddTHH:00:00.000Z");
                    string chunkEndStr = chunkEnd.ToString("yyyy-MM-ddTHH:00:00.000Z");

                    _logger?.LogInformation("Processing chunk from {ChunkStart} to {ChunkEnd}", chunkStartStr, chunkEndStr);

                    StringBuilder chunkAViews = new StringBuilder();
                    foreach (string AggregationViews in AggViews.Split(';'))
                    {
                        string[] Views = AggregationViews.Split(',');
                        if (Views.Length >= 4)
                        {
                            chunkAViews.Append("{\"target\": \"" + Views[0] + "\",  \"name\": \"" + Views[3] + "\", \"function\": \"rangeBound\",  \"range\": { \"gte\": " + Views[1] + ",    \"lt\": " + Views[2] + "}},");
                        }
                    }

                    // Use the same groupBy fields as the original query
                    string chunkRequestBody = "{ " +
                                     $"\"interval\": \"{chunkStartStr}/{chunkEndStr}\"," +
                                     $"\"granularity\": \"{intervalValue}\"," +
                                     "\"groupBy\": [" +
                                     "\"queueId\"," +
                                     "\"mediaType\"," +
                                     "\"direction\"," +
                                     "\"wrapUpCode\"" +
                                     "]," +
                                     "\"metrics\": [" +
                                     "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\",\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\",\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\",\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\",\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\",\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\",\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\",\"tVoicemail\",\"tWait\"" +
                                     "]";

                    if (chunkAViews.Length > 0)
                    {
                        chunkAViews.Length--;
                        chunkRequestBody += $", \"views\": [{chunkAViews}]}}";
                    }
                    else
                    {
                        chunkRequestBody += "}";
                    }

                    // Log the actual request body being sent to the API if debug logging is enabled
                    if (_logger != null && _logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug("QUEUE INTERACTION DATA CHUNK REQUEST BODY: {RequestBody}", chunkRequestBody);
                    }

                    string chunkJsonString = string.Empty;
                    try
                    {
                        chunkJsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/aggregates/query", GCApiKey, chunkRequestBody);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "API call failed for chunk {ChunkStart} to {ChunkEnd} in GetQueueInteractionDataFromGC", chunkStartStr, chunkEndStr);

                        // Check if this is a rate limiting related exception
                        if (ex.Message.Contains("429") || ex.Message.Contains("TooManyRequests") || ex.Message.Contains("rate limit"))
                        {
                            int retryAttempts = 0;
                            const int maxRetryAttempts = 3;
                            bool chunkProcessed = false;

                            while (!chunkProcessed && retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                _logger?.LogWarning("Rate limit related error for chunk {ChunkStart} to {ChunkEnd} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                    chunkStartStr, chunkEndStr, retryAttempts, maxRetryAttempts);

                                // Get a new API key for rate limiting issues
                                try
                                {
                                    GCUtilities.GetGCAPIKey();
                                    GCApiKey = GCUtilities.GCApiKey;
                                }
                                catch (Exception keyEx)
                                {
                                    _logger?.LogError(keyEx, "Failed to refresh API key for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, keyEx.Message);
                                }

                                // Implement exponential backoff with jitter
                                int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));

                                // Retry the API call
                                try
                                {
                                    chunkJsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/queues/details/query", GCApiKey, chunkRequestBody);
                                    chunkProcessed = true;
                                    break;
                                }
                                catch (Exception retryEx)
                                {
                                    if (retryAttempts >= maxRetryAttempts)
                                    {
                                        _logger?.LogError("Rate limiting exceeded retry limit for chunk {ChunkStart} to {ChunkEnd} after {Attempts} attempts - halting processing",
                                            chunkStartStr, chunkEndStr, maxRetryAttempts);
                                        throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts due to rate limiting");
                                    }
                                }
                            }

                            if (!chunkProcessed)
                            {
                                throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts");
                            }
                        }
                        else
                        {
                            // For other non-retryable errors, halt processing to ensure data integrity
                            throw;
                        }
                    }

                    // Check if the response is an error response and handle appropriately
                    if (chunkJsonString != null && (chunkJsonString.Contains("\"error\":") || chunkJsonString.Contains("\"status\":") || chunkJsonString.Contains("\"message\":")))
                    {
                        // Check if this is a rate limiting error that should be retried
                        if (chunkJsonString.Contains("429") || chunkJsonString.Contains("TooManyRequests") || chunkJsonString.Contains("rate limit"))
                        {
                            int retryAttempts = 0;
                            const int maxRetryAttempts = 3;
                            bool chunkProcessed = false;

                            while (!chunkProcessed && retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                _logger?.LogWarning("Rate limit error in response for chunk {ChunkStart} to {ChunkEnd} (attempt {Attempt}/{MaxAttempts}) - implementing retry with backoff",
                                    chunkStartStr, chunkEndStr, retryAttempts, maxRetryAttempts);

                                // Get a new API key for rate limiting issues
                                try
                                {
                                    GCUtilities.GetGCAPIKey();
                                    GCApiKey = GCUtilities.GCApiKey;
                                }
                                catch (Exception keyEx)
                                {
                                    _logger?.LogError(keyEx, "Failed to refresh API key for chunk {ChunkStart} to {ChunkEnd}: {Message}", chunkStartStr, chunkEndStr, keyEx.Message);
                                }

                                // Implement exponential backoff with jitter
                                int waitSeconds = (int)Math.Pow(2, retryAttempts) + new Random().Next(1, 5);
                                Thread.Sleep(TimeSpan.FromSeconds(waitSeconds));

                                // Retry the API call
                                try
                                {
                                    chunkJsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/queues/details/query", GCApiKey, chunkRequestBody);

                                    // Check if the retry was successful (no error in response)
                                    if (chunkJsonString != null && !(chunkJsonString.Contains("\"error\":") || chunkJsonString.Contains("\"status\":") || chunkJsonString.Contains("\"message\":")))
                                    {
                                        chunkProcessed = true;
                                        break;
                                    }
                                }
                                catch (Exception retryEx)
                                {
                                    if (retryAttempts >= maxRetryAttempts)
                                    {
                                        _logger?.LogError("Rate limiting exceeded retry limit for chunk {ChunkStart} to {ChunkEnd} after {Attempts} attempts - halting processing",
                                            chunkStartStr, chunkEndStr, maxRetryAttempts);
                                        throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts due to rate limiting");
                                    }
                                }
                            }

                            if (!chunkProcessed)
                            {
                                throw new Exception($"Failed to process chunk {chunkStartStr} to {chunkEndStr} after {maxRetryAttempts} retry attempts");
                            }
                        }
                        else if (chunkJsonString.Contains("400") || chunkJsonString.Contains("Bad Request"))
                        {
                            // For HTTP 400 errors, skip chunk but continue processing to preserve sync date progression
                            _logger?.LogError("HTTP 400 Bad Request error in response for chunk {ChunkStart} to {ChunkEnd} - skipping chunk: {Response}",
                                chunkStartStr, chunkEndStr, chunkJsonString);
                            Console.WriteLine($"FAILED CHUNK: {chunkStartStr} to {chunkEndStr} - HTTP 400 Bad Request error. Skipping to prevent sync date stagnation.");
                            continue; // Skip this chunk and continue to next chunk
                        }
                        else
                        {
                            // For other errors, skip chunk but continue processing to preserve sync date progression
                            _logger?.LogError("Error response received for chunk {ChunkStart} to {ChunkEnd} - skipping chunk: {Response}",
                                chunkStartStr, chunkEndStr, chunkJsonString);
                            Console.WriteLine($"FAILED CHUNK: {chunkStartStr} to {chunkEndStr} - Error response received. Skipping to prevent sync date stagnation.");
                            continue; // Skip this chunk and continue to next chunk
                        }
                    }

                    // Process the chunk data
                    ProcessChunkData(chunkJsonString, QueueInteraction, AppTimeZone);
                }

                _logger?.LogInformation("Completed processing all chunks. Total rows: {RowCount}", QueueInteraction.Rows.Count);
                return QueueInteraction;
            }

            // Process the full date range response
            if (JsonString != null && JsonString.Length > 10)
            {
                Interactions.InteractionDataStruct QueueData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                if (QueueData != null && QueueData.results != null)
                {
                    foreach (Interactions.Result Results in QueueData.results)
                    {
                        foreach (Interactions.Datum ResultsData in Results.data)
                        {
                            string TimeInterval = ResultsData.interval.Split('/')[0];
                            // ParseExact with 'Z' format already indicates UTC, use ToUtcSafe to avoid double conversion
                            DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUtcSafe();

                            if (MaxUpdateDateTest > QueueInteractionLastUpdate)
                                QueueInteractionLastUpdate = MaxUpdateDateTest;

                            if (Results.group.queueId != null)
                            {
                                DataRow DRNewRow = QueueInteraction.NewRow();

                                DRNewRow["queueId"] = Results.group.queueId;
                                DRNewRow["mediaType"] = Results.group.mediaType;
                                //Make Sure Default Code is set.

                                string RowWrapUp = Results.group.wrapUpCode;

                                if (RowWrapUp != null && RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                    RowWrapUp = "00000000-0000-0000-0000-0000000000000";

                                if (Results.group.wrapUpCode == null)
                                    RowWrapUp = "";

                                DRNewRow["wrapUpCode"] = RowWrapUp;
                                DRNewRow["direction"] = Results.group.direction;

                                // ParseExact with 'Z' format already indicates UTC, use ToUtcSafe to avoid double conversion
                                DateTime IntervalStart = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUtcSafe();



                            IntervalStart = new DateTime(
                                     IntervalStart.Ticks - (IntervalStart.Ticks % TimeSpan.TicksPerSecond),
                                     IntervalStart.Kind
                                 );


                            string TempKeyid = Results.group.queueId + "|" + Results.group.mediaType + "|" +
                                             RowWrapUp + "|" + Results.group.direction + "|" + TimeInterval;

                            DRNewRow["keyId"] = Results.group.queueId + "|" + UCAUtils.GetStableHashCode(TempKeyid);


                            DRNewRow["startdate"] = IntervalStart;
                            DRNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(IntervalStart, AppTimeZone);

                            foreach (DataColumn DCTemp in QueueInteraction.Columns)
                            {
                                switch (DCTemp.DataType.ToString())
                                {
                                    case "System.Int32":
                                    case "System.Single":
                                        DRNewRow[DCTemp.ColumnName] = 0;
                                        break;
                                }
                            }

                            foreach (Interactions.Metric ResultMetrics in ResultsData.metrics)
                            {

                                string MetricName = ResultMetrics.metric.ToString();

                                switch (ResultMetrics.metric)
                                {
                                    case "tAlert":
                                    case "tAnswered":
                                    case "tTalk":
                                    case "tNotResponding":
                                    case "tHeld":
                                    case "tHeldComplete":
                                    case "tAcw":
                                    case "tContacting":
                                    case "tDialing":
                                    case "tHandle":
                                    case "tTalkComplete":
                                    case "tVoicemail":
                                    case "tAcd":
                                    case "tFlowOut":
                                    case "tAbandon":
                                    case "tWait":
                                        DRNewRow[MetricName + "Count"] = ResultMetrics.stats.count;

                                        //if (Math.Round(ResultMetrics.stats.sum / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2)))
                                        //    ResultMetrics.stats.sum = ResultMetrics.stats.sum + 111;

                                        //if (Math.Round(ResultMetrics.stats.max / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.max / 1000.00F, 2)))
                                        //    ResultMetrics.stats.max = ResultMetrics.stats.max + 111;

                                        //if (Math.Round(ResultMetrics.stats.min / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.min / 1000.00F, 2)))
                                        //    ResultMetrics.stats.min = ResultMetrics.stats.min + 111;
                                        DRNewRow[MetricName + "TimeSum"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2)) + 0.11;
                                        DRNewRow[MetricName + "TimeMax"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.max / 1000.00F, 2)) + 0.11;
                                        DRNewRow[MetricName + "TimeMin"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.min / 1000.00F, 2)) + 0.11;
                                        break;

                                    case "nConsult":
                                    case "nConsultTransferred":
                                    case "nError":
                                    case "nTransferred":
                                    case "nBlindTransferred":
                                    case "nOutbound":
                                    case "nConnected":
                                    case "nOffered":
                                    case "nOverSla":
                                        DRNewRow[MetricName] = ResultMetrics.stats.count;
                                        break;

                                    case "oServiceLevel":
                                        DRNewRow["servicelevelnumerator"] = Math.Round(ResultMetrics.stats.numerator);
                                        DRNewRow["serviceleveldenominator"] = Math.Round(ResultMetrics.stats.denominator);
                                        break;

                                    default:
                                        //Console.WriteLine("Missing Current Metric : {0}", ResultMetrics.metric);
                                        break;

                                }


                            }

                            if (ResultsData.views != null)
                            {
                                foreach (Interactions.View ResultsView in ResultsData.views)

                                {
                                    string MetricName = ResultsView.name;

                                    //if (Math.Round(ResultsView.stats.sum / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2)))
                                    //    ResultsView.stats.sum = ResultsView.stats.sum + 111;

                                    //if (Math.Round(ResultsView.stats.max / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2)))
                                    //    ResultsView.stats.max = ResultsView.stats.max + 111;

                                    //if (Math.Round(ResultsView.stats.min / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2)))
                                    //    ResultsView.stats.min = ResultsView.stats.min + 111;

                                    DRNewRow[MetricName + "Count"] = ResultsView.stats.count;
                                    DRNewRow[MetricName + "TimeSum"] = Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2)) + 0.11;
                                    DRNewRow[MetricName + "TimeMax"] = Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2)) + 0.11;
                                    DRNewRow[MetricName + "TimeMin"] = Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2)) + 0.11;
                                }
                            }
                            try
                            {
                                QueueInteraction.Rows.Add(DRNewRow);
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "Error inserting data: {KeyId}", TempKeyid);
                            }

                        }
                    }

                    }
                }
            }

            // Log a summary of the processed data
            _logger?.LogInformation("Queue interaction data processing completed. Total rows processed: {RowCount}", QueueInteraction.Rows.Count);
            return QueueInteraction;
        }

        public async Task<DataTable> GetQueueAuditData(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueAuditData = DBUtil.CreateInMemTable("queueAuditData");

            string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\",\"serviceName\": \"ContactCenter\"}";

            Console.WriteLine("Audit JSON:{0}", RequestBody);

            // Use JsonReturnHttpResponseAsync for proper rate limit handling
            var response = await JsonActions.JsonReturnHttpResponseAsync(URI + "/api/v2/audits/query", GCApiKey, RequestBody);

            // Handle different HTTP status codes appropriately
            if (!response.IsSuccess && !response.IsAccepted)
            {
                if (response.StatusCode == 429)
                {
                    _logger?.LogWarning("Rate limit encountered for audit query. Response: {Response}", response.Content);
                    throw new Exception("Rate limiting exceeded retry limit for audit query");
                }
                else
                {
                    _logger?.LogError("API call failed with status {StatusCode}: {Content}", response.StatusCode, response.Content);
                    throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                }
            }

            string JsonString = response.Content;

            // Handle asynchronous job responses (HTTP 202 Accepted)
            if (response.IsAccepted)
            {
                _logger?.LogInformation("Queue audit job created, polling for completion");

                // For HTTP 202 responses, the response content contains the job information directly
                Auditing.AuditJob jobData = new Auditing.AuditJob();
                try
                {
                    jobData = JsonConvert.DeserializeObject<Auditing.AuditJob>(response.Content,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

                    if (jobData == null || string.IsNullOrEmpty(jobData.id))
                    {
                        _logger?.LogError("Failed to get valid audit job ID from HTTP 202 response: {Response}", response.Content);
                        return QueueAuditData; // Return empty table
                    }

                    // Use the shared job manager for polling
                    var jobManager = new GenesysJobManager(JsonActions, GCApiKey, URI);
                    var jobConfig = new JobConfig
                    {
                        JobId = jobData.id,
                        JobType = "QueueAudit",
                        ContextInfo = $"Queue audit data from {StartDate} to {EndDate}",
                        StatusEndpoint = URI + "/api/v2/audits/query/" + jobData.id,
                        TimeoutMinutes = 30,
                        PollIntervalSeconds = 10,
                        CompletedStatuses = new List<string> { "succeeded" },
                        FailedStatuses = new List<string> { "failed", "cancelled" },
                        StatusParser = (json) =>
                        {
                            try
                            {
                                var auditJobData = JsonConvert.DeserializeObject<Auditing.AuditJob>(json,
                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                                return new JobStatusInfo
                                {
                                    Status = auditJobData?.state ?? "unknown",
                                    DownloadUrls = new List<string>() // Audit jobs don't use download URLs
                                };
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogWarning(ex, "Failed to parse audit job status: {Json}", json);
                                return null;
                            }
                        }
                    };

                    var jobResult = await jobManager.PollJobToCompletionAsync(jobConfig);
                    if (!jobResult.Success)
                    {
                        _logger?.LogError("Queue audit job failed: {ErrorMessage}", jobResult.ErrorMessage);
                        return QueueAuditData; // Return empty table
                    }

                    _logger?.LogInformation("Queue audit job completed successfully, processing results");

                    // Update JsonString to indicate successful job completion for the rest of the method
                    JsonString = $"{{\"id\":\"{jobData.id}\",\"state\":\"Succeeded\"}}";
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error handling HTTP 202 audit job response: {Response}", response.Content);
                    return QueueAuditData; // Return empty table
                }
            }

            Console.WriteLine("Json Returned: {0}", JsonString);

            // Check if the response contains an error
            if (JsonString == null ||
                JsonString.Contains("\"error\":") ||
                JsonString.Contains("\"status\":400") ||
                JsonString.Contains("\"code\":\"bad.request\"") ||
                JsonString.Contains("exceeds the maximum retention range"))
            {
                // Store the error message for retry logic
                LastErrorMessage = JsonString ?? "Empty response";
                _logger?.LogError("Error response received from Genesys Cloud API: {Response}", JsonString);
                return QueueAuditData; // Return empty table
            }

            // Clear any previous error message
            LastErrorMessage = null;

            // Initialize AuditJobInfo
            Auditing.AuditJob AuditJobInfo = new Auditing.AuditJob();

            // Only proceed if we have a valid response with sufficient length
            if (JsonString.Length > 30)
            {
                try
                {
                    AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                    // Verify that we have a valid job ID before proceeding
                    if (AuditJobInfo == null || string.IsNullOrEmpty(AuditJobInfo.id))
                    {
                        _logger?.LogError("Failed to get a valid audit job ID from response: {Response}", JsonString);
                        return QueueAuditData; // Return empty table
                    }

                    //Need to make sure that the job is completed.
                    bool DataAvailable = false;
                    int Counter = 0;

                    while (!DataAvailable)
                    {
                        Counter++;
                        Thread.Sleep(3000);

                        string jobStatusUrl = URI + "/api/v2/audits/query/" + AuditJobInfo.id;
                        JsonString = JsonActions.JsonReturnString(jobStatusUrl, GCApiKey);

                        // Check for errors in the job status response
                        if (JsonString == null || JsonString.Contains("\"error\":") || JsonString.Contains("\"status\":4"))
                        {
                            _logger?.LogError("Error checking job status: {Response}", JsonString);
                            break;
                        }

                        try
                        {
                            AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });

                            // Verify we have a valid job state
                            if (AuditJobInfo == null)
                            {
                                _logger?.LogError("Failed to deserialize job status response: {Response}", JsonString);
                                break;
                            }

                            if (AuditJobInfo.state == "Succeeded")
                            {
                                DataAvailable = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error deserializing job status response: {Response}", JsonString);
                            break;
                        }

                        if (Counter > 6)
                        {
                            _logger?.LogWarning("Maximum job status check attempts reached without success");
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error deserializing audit job response: {Response}", JsonString);
                    return QueueAuditData; // Return empty table
                }
            }
            else
            {
                _logger?.LogWarning("Insufficient data in API response: {Response}", JsonString);
                return QueueAuditData; // Return empty table
            }

            // Only process data if the job completed successfully
            bool jobSucceeded = false;
            if (AuditJobInfo != null && AuditJobInfo.state == "Succeeded")
            {
                jobSucceeded = true;
            }

            if (jobSucceeded)
            {
                string CursorString = String.Empty;
                string LastCursor = String.Empty;
                bool FirstTime = true;
                bool RepeatDownload = true;
                int TempRowCount = 0;
                int NoChangeCounter = 0;

                try
                {
                    while (RepeatDownload && NoChangeCounter < 2)
                    {
                        if (FirstTime)
                        {
                            CursorString = "?pageSize=100";
                            FirstTime = false;
                        }
                        else
                        {
                            CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor) + "&pageSize=100";
                        }

                        string resultsUrl = URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString;
                        _logger?.LogDebug("Requesting audit results: {Url}", resultsUrl);

                        JsonString = JsonActions.JsonReturnString(resultsUrl, GCApiKey);

                        // Check for errors in the results response
                        if (JsonString == null ||
                            JsonString.Contains("\"error\":") ||
                            JsonString.Contains("\"status\":4"))
                        {
                            _logger?.LogError("Error retrieving audit results: {Response}", JsonString);
                            break;
                        }

                        Auditing.AuditChange AuditChangeData;

                        try
                        {
                            AuditChangeData = JsonConvert.DeserializeObject<Auditing.AuditChange>(JsonString,
                                              new JsonSerializerSettings
                                              {
                                                  NullValueHandling = NullValueHandling.Ignore
                                              });

                            if (AuditChangeData == null)
                            {
                                _logger?.LogError("Failed to deserialize audit results: {Response}", JsonString);
                                break;
                            }

                            _logger?.LogDebug("Retrieved audit data with cursor: {Cursor}", AuditChangeData.cursor);

                            if (AuditChangeData.entities != null && AuditChangeData.entities.Length > 0)
                            {
                                if (AuditChangeData.cursor != null)
                                {
                                    LastCursor = AuditChangeData.cursor;
                                    RepeatDownload = true;
                                }
                                else
                                {
                                    RepeatDownload = false;
                                }

                                _logger?.LogDebug("Processing {Count} audit entries, cursor={Cursor}, continue={Continue}",
                                    AuditChangeData.entities.Length, AuditChangeData.cursor, RepeatDownload);

                                foreach (Auditing.Entity AuditEntry in AuditChangeData.entities)
                                {
                                    // Skip entries that aren't related to queue membership changes
                                    if (AuditEntry == null ||
                                        string.IsNullOrEmpty(AuditEntry.action) ||
                                        AuditEntry.entityType != "Queue")
                                    {
                                        continue;
                                    }

                                    string actionLower = AuditEntry.action.ToLower();
                                    if (actionLower != "memberremove" &&
                                        actionLower != "memberadd" &&
                                        actionLower != "memberupdate")
                                    {
                                        continue;
                                    }

                                    // Skip entries with no property changes
                                    if (AuditEntry.propertyChanges == null || AuditEntry.propertyChanges.Length == 0)
                                    {
                                        continue;
                                    }

                                    foreach (Auditing.Propertychange userChanged in AuditEntry.propertyChanges)
                                    {
                                        try
                                        {
                                            // Skip entries with no property or invalid format
                                            if (userChanged == null || string.IsNullOrEmpty(userChanged.property))
                                            {
                                                continue;
                                            }

                                            string[] propertyParts = userChanged.property.Split(':');
                                            if (propertyParts.Length < 2)
                                            {
                                                _logger?.LogWarning("Invalid property format: {Property}", userChanged.property);
                                                continue;
                                            }

                                            string UserIdRaw = userChanged.property;
                                            string UserId = propertyParts[1];
                                            string MajorAction = actionLower.Replace("member", "");
                                            string MinorAction = string.Empty;

                                            switch (MajorAction)
                                            {
                                                case "add":
                                                    MinorAction = "active";
                                                    break;
                                                case "remove":
                                                    MinorAction = "inactive";
                                                    break;
                                                case "update":
                                                    if (AuditEntry.propertyChanges[0]?.oldValues != null &&
                                                        AuditEntry.propertyChanges[0].oldValues.Length > 0 &&
                                                        AuditEntry.propertyChanges[0].oldValues[0] == "false")
                                                    {
                                                        MinorAction = "active";
                                                    }
                                                    else
                                                    {
                                                        MinorAction = "inactive";
                                                    }
                                                    break;
                                                default:
                                                    MinorAction = "unknown";
                                                    break;
                                            }

                                            string keyId = AuditEntry.entity.id + "|" +
                                                          MajorAction + "|" +
                                                          AuditEntry.eventDate.ToString("yyyyMMddhhmmss") + "|" +
                                                          UserId;

                                            DataRow[] QueueAuditRows = QueueAuditData.Select("keyid='" + keyId + "'");

                                            if (!QueueAuditRows.Any())
                                            {
                                                DataRow DRAuditRow = QueueAuditData.NewRow();

                                                DRAuditRow["keyid"] = keyId;
                                                DRAuditRow["queueid"] = AuditEntry.entity.id;
                                                DRAuditRow["userid"] = UserId;
                                                DRAuditRow["addorremove"] = MajorAction;
                                                DRAuditRow["activeorinactive"] = MinorAction;
                                                DRAuditRow["datemodified"] = AuditEntry.eventDate;
                                                DRAuditRow["datemodifiedLTC"] = TimeZoneInfo.ConvertTimeFromUtc(AuditEntry.eventDate, AppTimeZone);
                                                DRAuditRow["modifiedby"] = AuditEntry.user.id;

                                                QueueAuditData.Rows.Add(DRAuditRow);
                                            }
                                            else
                                            {
                                                DataRow DRAuditRow = QueueAuditRows.FirstOrDefault();
                                                if (DRAuditRow != null)
                                                {
                                                    DRAuditRow["userid"] = UserId;
                                                    DRAuditRow["activeorinactive"] = MinorAction;
                                                    DRAuditRow["modifiedby"] = AuditEntry.user.id;
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            // Log the error but continue processing other entries
                                            _logger?.LogError(ex, "Error processing audit entry: {EntityId}",
                                                AuditEntry.entity?.id ?? "unknown");
                                        }
                                    }
                                }

                                // Log progress after each page
                                _logger?.LogInformation("Processed page of audit data. Current row count: {RowCount}",
                                    QueueAuditData.Rows.Count);
                            }
                            else
                            {
                                _logger?.LogInformation("No audit data entities returned - ending pagination");
                                RepeatDownload = false;
                            }

                            // Check if we're making progress
                            if (TempRowCount == QueueAuditData.Rows.Count)
                            {
                                NoChangeCounter++;
                                _logger?.LogDebug("No new rows added in this iteration. NoChangeCounter: {Counter}", NoChangeCounter);
                            }
                            else
                            {
                                TempRowCount = QueueAuditData.Rows.Count;
                                NoChangeCounter = 0;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error processing audit results: {Response}", JsonString);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error in audit data processing loop");
                }
            }
            else if (!jobSucceeded)
            {
                _logger?.LogWarning("Audit job did not complete successfully. No data will be processed.");
            }
            else
            {
                _logger?.LogWarning("Invalid or insufficient response from audit API. No data will be processed.");
            }

            // Log a summary of the processed audit data
            _logger?.LogInformation("Queue audit data processing completed. Total rows processed: {RowCount}", QueueAuditData.Rows.Count);
            return QueueAuditData;
        }

        private void ProcessChunkData(string jsonString, DataTable queueInteraction, TimeZoneInfo appTimeZone)
        {
            int rowsProcessed = 0;
            if (jsonString != null && jsonString.Length > 30)
            {
                Interactions.InteractionDataStruct queueData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(jsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                if (queueData != null && queueData.results != null)
                {
                    foreach (Interactions.Result results in queueData.results)
                    {
                        foreach (Interactions.Datum resultsData in results.data)
                        {
                            string timeInterval = resultsData.interval.Split('/')[0];
                            // ParseExact with 'Z' format already indicates UTC, use ToUtcSafe to avoid double conversion
                            DateTime maxUpdateDateTest = DateTime.ParseExact(timeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUtcSafe();

                            if (maxUpdateDateTest > QueueInteractionLastUpdate)
                                QueueInteractionLastUpdate = maxUpdateDateTest;

                            if (results.group.queueId != null)
                            {
                                DataRow drNewRow = queueInteraction.NewRow();

                                drNewRow["queueId"] = results.group.queueId;
                                drNewRow["mediaType"] = results.group.mediaType;

                                // Make Sure Default Code is set
                                string rowWrapUp = results.group.wrapUpCode;

                                if (rowWrapUp != null && rowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                    rowWrapUp = "00000000-0000-0000-0000-0000000000000";

                                if (results.group.wrapUpCode == null)
                                    rowWrapUp = "";

                                drNewRow["wrapUpCode"] = rowWrapUp;
                                drNewRow["direction"] = results.group.direction;

                                // ParseExact with 'Z' format already indicates UTC, use ToUtcSafe to avoid double conversion
                                DateTime intervalStart = DateTime.ParseExact(timeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUtcSafe();

                                intervalStart = new DateTime(
                                         intervalStart.Ticks - (intervalStart.Ticks % TimeSpan.TicksPerSecond),
                                         intervalStart.Kind
                                     );

                                string tempKeyid = results.group.queueId + "|" + results.group.mediaType + "|" +
                                                 rowWrapUp + "|" + results.group.direction + "|" + timeInterval;

                                drNewRow["keyId"] = results.group.queueId + "|" + UCAUtils.GetStableHashCode(tempKeyid);

                                drNewRow["startdate"] = intervalStart;
                                drNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(intervalStart, appTimeZone);

                                foreach (DataColumn dcTemp in queueInteraction.Columns)
                                {
                                    switch (dcTemp.DataType.ToString())
                                    {
                                        case "System.Int32":
                                        case "System.Single":
                                            drNewRow[dcTemp.ColumnName] = 0;
                                            break;
                                    }
                                }

                                foreach (Interactions.Metric resultMetrics in resultsData.metrics)
                                {
                                    string metricName = resultMetrics.metric.ToString();

                                    switch (resultMetrics.metric)
                                    {
                                        case "tAlert":
                                        case "tAnswered":
                                        case "tTalk":
                                        case "tNotResponding":
                                        case "tHeld":
                                        case "tHeldComplete":
                                        case "tAcw":
                                        case "tContacting":
                                        case "tDialing":
                                        case "tHandle":
                                        case "tTalkComplete":
                                        case "tVoicemail":
                                        case "tAcd":
                                        case "tFlowOut":
                                        case "tAbandon":
                                        case "tWait":
                                            drNewRow[metricName + "Count"] = resultMetrics.stats.count;
                                            drNewRow[metricName + "TimeSum"] = Convert.ToInt64(Math.Round(resultMetrics.stats.sum / 1000.00F, 2)) + 0.11;
                                            drNewRow[metricName + "TimeMax"] = Convert.ToInt64(Math.Round(resultMetrics.stats.max / 1000.00F, 2)) + 0.11;
                                            drNewRow[metricName + "TimeMin"] = Convert.ToInt64(Math.Round(resultMetrics.stats.min / 1000.00F, 2)) + 0.11;
                                            break;

                                        case "nConsult":
                                        case "nConsultTransferred":
                                        case "nError":
                                        case "nTransferred":
                                        case "nBlindTransferred":
                                        case "nOutbound":
                                        case "nConnected":
                                        case "nOffered":
                                        case "nOverSla":
                                            drNewRow[metricName] = resultMetrics.stats.count;
                                            break;

                                        case "oServiceLevel":
                                            drNewRow["servicelevelnumerator"] = Math.Round(resultMetrics.stats.numerator);
                                            drNewRow["serviceleveldenominator"] = Math.Round(resultMetrics.stats.denominator);
                                            break;
                                    }
                                }

                                if (resultsData.views != null)
                                {
                                    foreach (Interactions.View resultsView in resultsData.views)
                                    {
                                        string metricName = resultsView.name;

                                        drNewRow[metricName + "Count"] = resultsView.stats.count;
                                        drNewRow[metricName + "TimeSum"] = Convert.ToInt32(Math.Round(resultsView.stats.sum / 1000.00F, 2)) + 0.11;
                                        drNewRow[metricName + "TimeMax"] = Convert.ToInt32(Math.Round(resultsView.stats.max / 1000.00F, 2)) + 0.11;
                                        drNewRow[metricName + "TimeMin"] = Convert.ToInt32(Math.Round(resultsView.stats.min / 1000.00F, 2)) + 0.11;
                                    }
                                }
                                try
                                {
                                    queueInteraction.Rows.Add(drNewRow);
                                    rowsProcessed++;

                                    // Log a summary every 50 rows
                                    if (rowsProcessed % 50 == 0)
                                    {
                                        _logger?.LogDebug("Queue interaction chunk processing: {RowCount} rows processed so far", rowsProcessed);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogError(ex, "Error inserting data for key: {KeyId}", tempKeyid);
                                }

                            }
                        }
                    }
                }
            }

            // Log final summary
            _logger?.LogInformation("Queue interaction chunk processing completed. Total rows processed: {RowCount}", rowsProcessed);
        }
    }
}
