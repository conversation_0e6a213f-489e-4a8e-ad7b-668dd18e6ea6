CREATE TABLE IF NOT EXISTS queueauditdata (
    keyid varchar(500) NOT NULL,
    queueid varchar(50),
    userid varchar(50),
    addorremove varchar(10),
    datemodified timestamp without time zone,
    datemodifiedltc timestamp without time zone,
    modifiedby varchar(50),
    updated timestamp without time zone,
    activeorinactive varchar(20),
    CONSTRAINT queueauditdata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN queueAuditData.activeorinactive IS 'Action to make active or inactive'; 
COMMENT ON COLUMN queueAuditData.addorremove IS 'Action to Add or Remove'; 
COMMENT ON COLUMN queueAuditData.datemodified IS 'Date Modified (UTC)'; 
COMMENT ON COLUMN queueAuditData.datemodifiedLTC IS 'Date Modified (LTC)'; 
COMMENT ON COLUMN queueAuditData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN queueAuditData.modifiedby IS 'Modified By GUID'; 
COMMENT ON COLUMN queueAuditData.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN queueAuditData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN queueAuditData.userid IS 'User GUID'; 
COMMENT ON TABLE queueAuditData IS 'Audit Table for Queue Membership changes'; 