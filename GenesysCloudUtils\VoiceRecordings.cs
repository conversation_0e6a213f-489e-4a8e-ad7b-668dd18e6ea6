﻿using System;
using StandardUtils;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net;
using System.Data;
using VoiceRec = GenesysCloudDefVoiceRecMetadata;
using System.Text;

namespace GenesysCloudUtils
{
    public class VoiceRecordings
    {

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }

        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public bool RequestRecording(string ConversationId)
        {
            bool Successful = false;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string JsonString = string.Empty;

            JsonString = JsonActions.JsonReturnString(URI + "/api/v2/conversations/" + ConversationId + "/recordingmetadata", GCApiKey);

            VoiceRec.VoiceRecMetadata VoiceRecProp = new VoiceRec.VoiceRecMetadata();

            if (JsonString.Length > 10)
            {
                JsonString = JsonString.Substring(1, JsonString.Length - 2);

                VoiceRecProp = JsonConvert.DeserializeObject<VoiceRec.VoiceRecMetadata>(JsonString,
                                           new JsonSerializerSettings
                                           {
                                               NullValueHandling = NullValueHandling.Ignore
                                           });

                Console.WriteLine("Recording: {0}  Conversation ID: {1} FileState **{2}** ", VoiceRecProp.id, VoiceRecProp.conversationId, VoiceRecProp.fileState);

                if (VoiceRecProp.fileState == "AVAILABLE")
                {
                    String URLString = URI + "/api/v2/conversations/" + VoiceRecProp.conversationId + "/recordings/" + VoiceRecProp.id + "?formatId=MP3&download=true";


                    VoiceRec.VoiceRecDetails RecDetails = new VoiceRec.VoiceRecDetails();

                    JsonString = JsonActions.JsonReturnString(URLString, GCApiKey);

                    RecDetails = JsonConvert.DeserializeObject<VoiceRec.VoiceRecDetails>(JsonString,
                                           new JsonSerializerSettings
                                           {
                                               NullValueHandling = NullValueHandling.Ignore
                                           });

                    if (RecDetails != null)
                    {
                        Console.WriteLine("Recording can be found at : {0}", RecDetails.mediaUris.S.mediaUri);
                        DownloadRecording(RecDetails.mediaUris.S.mediaUri, VoiceRecProp.conversationId);
                    }

                }
            }

            return Successful;

        }

        public bool DownloadRecording(string URL, string ConversationId)
        {
            Boolean Successful = false;
            string Directory = @"c:\temp\";
            try
            {
                using (var DownloadClient = new WebClient())
                {
                    DownloadClient.DownloadFile(URL, Directory + ConversationId + ".mp3");
                }
                Successful = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                Successful = false;
            }
            return Successful;
        }

        public bool UploadRecording(string ConversationId, string APIKey)
        {
            bool Successful = false;

            WebClient myWebClient = new WebClient();

            string FileName = ConversationId;
            string Directory = @"c:\temp\";

            myWebClient.Headers.Add("apikey", APIKey);
            myWebClient.Headers.Add("Content-Type", "audio/mpeg");
            myWebClient.Headers.Add("tags", "conversationid=" + ConversationId + ";recordingid=" + ConversationId + ";jm_version=2.3.3-11;audio_properties=adpcm (a-law), cbr, 128 kb/s, 8000 hz, 8 bits, 2 ch;userId=" + ConversationId + ";");

            Console.WriteLine("Starting the Upload {0}", FileName + ".mp3");

            byte[] responseArray = myWebClient.UploadFile("https://api.prod.au.tmacomms.com/api/audiofiles?filename=" + FileName + ".mp3&customerkey=" + FileName + "&processmode-1&deleteafterprocessing=true"
                , "POST"
                , Directory + FileName + ".mp3");

            Console.WriteLine("\nResponse Received. The contents of the file uploaded are:\n{0}",
            Encoding.ASCII.GetString(responseArray));

            Console.WriteLine("Finished The Upload {0}", FileName + ".mp3");

            return Successful;
        }
    }
}
// spell-checker: ignore: recordingid, adpcm
