CREATE TABLE IF NOT EXISTS queuedetails (
    id varchar(50) NOT NULL,
    name varchar(255),
    description varchar(255),
    divisionid varchar(50),
    updated timestamp without time zone,
    enabletranscription boolean,
    isactive boolean,
    callslatargetperc float,
    callslatargetduration int,
    callbackslatargetperc float,
    callbackslatargetduration int,
    chatslatargetperc float,
    chatslatargetduration int,
    emailslatargetperc float,
    emailslatargetduration int,
    messageslatargetperc float,
    messageslatargetduration int,
    CONSTRAINT queuedetails_pkey PRIMARY KEY (id)
) ;

ALTER TABLE queuedetails 
ADD column IF NOT exists description varchar(255);

ALTER TABLE queuedetails 
ADD column IF NOT exists isactive boolean;

ALTER TABLE queuedetails 
ADD column IF NOT exists callslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists callslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists callbackslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists callbackslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists chatslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists chatslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists emailslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists emailslatargetduration int;
ALTER TABLE queuedetails 
ADD column IF NOT exists messageslatargetperc float;
ALTER TABLE queuedetails 
ADD column IF NOT exists messageslatargetduration int;