-- Template for cron
/*
SELECT
    cron.schedule(
        'Description',
        'Cron schedule (see: crontab.guru',
        'Command to run',
        'Database to process against'
    );
*/

SET
    search_path TO cron;

SELECT
    cron.schedule(
        'mvwconvvoiceoverviewdata_customername',
        '20 */1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.update_mvwconvvoiceoverviewdata()'
    );

SELECT
    cron.schedule(
        'mvwconvvoicetopicdetaildata_customername',
        '25 */1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.update_mvwconvvoicetopicdetaildata()'
    );

SELECT
    cron.schedule(
        'mvwconvvoicesentimentdetaildata_customername',
        '30 */1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.update_mvwconvvoicesentimentdetaildata()'
    );

SELECT
    cron.schedule(
        'mvwevaluationoverview_customername',
        '40 */1 * * *',
        'SET search_path TO contactcentredb; REFRESH MATERIALIZED VIEW CONCURRENTLY mvwevaluationoverview'
    );

SELECT
    cron.schedule(
        'mvwevaluationgroupdata_customername',
        '35 */1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.update_mvwevaluationgroupdata()'
    );

SELECT
    cron.schedule(
        'evaluationquestiondata_customername',
        '45 */1 * * *',
        'SET search_path TO contactcentredb; REFRESH MATERIALIZED VIEW CONCURRENTLY mvwevaluationquestiondata'
    );
SELECT
    cron.schedule(
        'archivequeueinteraction_customername',
        '0 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivequeueinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserinteraction_customername',
        '15 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserpresence_customername',
        '30 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserpresence(0,''D'');'
    );

SELECT
    cron.schedule(
        'archivebacklog_customername',
        '0 1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivebacklog()'
    );
