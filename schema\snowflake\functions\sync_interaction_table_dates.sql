-- Snowflake: Synchronize maxdate values for interaction job tables
-- This script ensures all interaction-related tables have consistent synchronization dates
-- to prevent data gaps after the implementation of individual table sync date tracking
-- Only runs for version 3.47.4 and excludes backfill tables
-- This script is automatically executed during installation/upgrade

CREATE OR REPLACE PROCEDURE sync_interaction_table_dates()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  // Get the sync date from detailedinteractiondata as the authoritative source
  var getReferenceDateQuery = `
    SELECT
        CASE
            WHEN datekeyfield IS NULL THEN '2000-01-01 00:00:00'
            ELSE datekeyfield
        END as reference_date
    FROM tabledefinitions
    WHERE tablename = 'detailedinteractiondata'
        AND version LIKE '3.48.%'
  `;

  var resultSet = snowflake.execute({ sqlText: getReferenceDateQuery });

  if (!resultSet.next()) {
    return 'No reference date found for detailedinteractiondata';
  }

  var referenceDate = resultSet.getColumnValue(1);

  // Update interaction tables (excluding backfill) to use the detailedinteractiondata sync date
  // Only update if the target table's current maxdate is older than detailedinteractiondata
  // NOTE: detailedinteractiondata is excluded as it is the authoritative source
  var updateQuery = `
    UPDATE tabledefinitions
    SET datekeyfield = '` + referenceDate + `'
    WHERE tablename IN (
        'convsummarydata',
        'participantattributesdynamic',
        'participantsummarydata',
        'flowoutcomedata'
    )
    AND version LIKE '3.48.%'
    AND (
        datekeyfield IS NULL
        OR TO_TIMESTAMP(datekeyfield, 'YYYY-MM-DD HH24:MI:SS') < TO_TIMESTAMP('` + referenceDate + `', 'YYYY-MM-DD HH24:MI:SS')
    )
  `;

  var updateResult = snowflake.execute({ sqlText: updateQuery });

  return 'Interaction table sync dates synchronized to: ' + referenceDate;
$$;

-- Execute the procedure
CALL sync_interaction_table_dates();

-- Display the reference date from detailedinteractiondata
SELECT
    'detailedinteractiondata' as reference_table,
    datekeyfield as reference_date,
    'This date will be used for all interaction tables' as description
FROM tabledefinitions
WHERE tablename = 'detailedinteractiondata';

-- Display the synchronized dates for verification (only tables that were updated)
SELECT
    tablename,
    datekeyfield as synchronized_date,
    version,
    'Interaction table sync dates synchronized to detailedinteractiondata' as status
FROM tabledefinitions
WHERE tablename IN (
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version LIKE '3.48.%'
ORDER BY tablename;

-- Clean up the procedure
DROP PROCEDURE IF EXISTS sync_interaction_table_dates();

-- Optional: Set a specific sync date if needed (uncomment and modify as required)
/*
UPDATE tabledefinitions
SET datekeyfield = '2024-01-01 00:00:00'
WHERE tablename IN (
    'detailedinteractiondata',
    'detailedinteractiondata_backfill',
    'convsummarydata',
    'convsummarydata_backfill',
    'participantattributesdynamic',
    'participantattributesdynamic_backfill',
    'participantsummarydata',
    'participantsummarydata_backfill',
    'flowoutcomedata',
    'flowoutcomedata_backfill'
);

SELECT 'All interaction table sync dates set to specific date: 2024-01-01 00:00:00' as result;
*/
