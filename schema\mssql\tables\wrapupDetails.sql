IF dbo.csg_table_exists('wrapupDetails') = 0
CREATE TABLE [wrapupDetails] (
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](255),
    [updated] [datetime],
    PRIMARY KEY ([id])
);

MERGE INTO wrapupDetails AS target
USING (VALUES
    ('00000000-0000-0000-0000-0000000000000','ININ-WRAP-UP-TIMEOUT')
) AS source (id, name)
ON target.id = source.id
WHEN NOT MATCHED THEN
    INSERT (id, name)
    VALUES (source.id, source.name);
