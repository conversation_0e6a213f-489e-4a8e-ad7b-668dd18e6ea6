CREATE
OR REPLACE VIEW vwUserPresenceDetailedData AS
SELECT
    upd.keyid,
    upd.userid,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.divisionid as divisionid,
    ud.managername,
    upd.starttime,
    upd.starttimeltc,
    upd.endtime,
    upd.endtimeltc,
    upd.systempresence,
    upd.orgpresence,
    upd.routingstatus,
    upd.timeinstate,
    upd.timeinstate / 86400.00 AS timeinstateDay,
    upd.updated
FROM
    userPresenceDetailedData AS upd
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = upd.userid;

COMMENT ON COLUMN vwUserPresenceDetailedData.keyid IS 'Primary Key';
COMMENT ON COLUMN vwUserPresenceDetailedData.userid IS 'User GUID';
COMMENT ON COLUMN vwUserPresenceDetailedData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwUserPresenceDetailedData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwUserPresenceDetailedData.divisionid IS 'Division GUID';
COMMENT ON COLUMN vwUserPresenceDetailedData.managername IS 'Manager Name';
COMMENT ON COLUMN vwUserPresenceDetailedData.starttime IS 'Start Time(UTC)';
COMMENT ON COLUMN vwUserPresenceDetailedData.starttimeltc IS 'Start Time in LTC';
COMMENT ON COLUMN vwUserPresenceDetailedData.endtime IS 'End Time(UTC)';
COMMENT ON COLUMN vwUserPresenceDetailedData.endtimeltc IS 'End Time in LTC';
COMMENT ON COLUMN vwUserPresenceDetailedData.systempresence IS 'System Presence';
COMMENT ON COLUMN vwUserPresenceDetailedData.orgpresence IS 'Organization Presence';
COMMENT ON COLUMN vwUserPresenceDetailedData.routingstatus IS 'Routing Status';
COMMENT ON COLUMN vwUserPresenceDetailedData.timeinstate IS 'Time in State';
COMMENT ON COLUMN vwUserPresenceDetailedData.timeinstateDay IS 'Time in State (Days)';
COMMENT ON COLUMN vwUserPresenceDetailedData.updated IS 'Last Updated Time';
COMMENT ON VIEW vwUserPresenceDetailedData IS 'See UserPresenceDetailedData - Expands all the GUIDs with their lookups(Detailed)';
