CREATE TABLE IF NOT EXISTS timeoffrequestdata (
    keyid varchar(100) NOT NULL,
    id varchar(50),
    userid varchar(50),
    isfulldayrequest bit(1),
    status varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    notes varchar(400),
    timeoffduration integer,
    submittedbyid varchar(50),
    submittedate timestamp without time zone,
    reviewedbyid varchar(50),
    revieweddate timestamp without time zone,
    modifiedbyid varchar(50),
    modifieddate timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT timeoffrequestdata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN timeoffrequestData.id IS 'Time Off Request GUID'; 
COMMENT ON COLUMN timeoffrequestData.isfulldayrequest IS 'If this a Full Day off request (True/False)'; 
COMMENT ON COLUMN timeoffrequestData.keyid IS 'Primary Keyid'; 
COMMENT ON COLUMN timeoffrequestData.modifiedbyid IS 'Request Modified By GUID'; 
COMMENT ON COLUMN timeoffrequestData.modifieddate IS 'Request Modified by Date (UTC)'; 
COMMENT ON COLUMN timeoffrequestData.notes IS 'Request Notes'; 
COMMENT ON COLUMN timeoffrequestData.reviewedbyid IS 'Request Reviewed By GUID'; 
COMMENT ON COLUMN timeoffrequestData.revieweddate IS 'Request Reviewed by Date (UTC)'; 
COMMENT ON COLUMN timeoffrequestData.startdate IS 'Request Start Date (UTC)'; 
COMMENT ON COLUMN timeoffrequestData.startdateltc IS 'Request Start Date (LTC)'; 
COMMENT ON COLUMN timeoffrequestData.status IS 'Request Status'; 
COMMENT ON COLUMN timeoffrequestData.submittedate IS 'Request Submitted Date (UTC)'; 
COMMENT ON COLUMN timeoffrequestData.submittedbyid IS 'Request Submitted By GUID'; 
COMMENT ON COLUMN timeoffrequestData.timeoffduration IS 'Request Duration'; 
COMMENT ON COLUMN timeoffrequestData.userid IS 'Agent GUID'; 
COMMENT ON TABLE timeoffrequestData IS 'WFM Time off requests entered by the Agents / or by proxys on their behalf'; 