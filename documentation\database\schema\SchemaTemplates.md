# Schema Templates

This document provides standardized templates for creating schema files for different database types.

## MSSQL Table Template

```sql
-- MSSQL Table Template
-- Use this template when creating new tables in MSSQL

-- Check if table exists before creating
IF dbo.csg_table_exists('table_name') = 0
CREATE TABLE [table_name](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](100),
    [description] [nvarchar](max),
    [created_date] [datetime],
    [updated_date] [datetime],
    CONSTRAINT [PK_table_name] PRIMARY KEY ([id])
);

-- Add columns if they don't exist
IF dbo.csg_column_exists('table_name', 'new_column') = 0
    ALTER TABLE table_name ADD new_column [nvarchar](100);

-- Create indexes if they don't exist
IF dbo.csg_index_exists('IX_table_name_column', 'table_name') = 0
CREATE INDEX [IX_table_name_column] ON [table_name]([column]);

-- Example of adding a foreign key
IF NOT EXISTS (
    SELECT * FROM sys.foreign_keys 
    WHERE object_id = OBJECT_ID('FK_table_name_reference_table')
    AND parent_object_id = OBJECT_ID('table_name')
)
ALTER TABLE [table_name]
ADD CONSTRAINT [FK_table_name_reference_table] FOREIGN KEY ([reference_id])
REFERENCES [reference_table] ([id]);
```

## PostgreSQL Table Template

```sql
-- PostgreSQL Table Template
-- Use this template when creating new tables in PostgreSQL

-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS table_name (
    id varchar(50) NOT NULL,
    name varchar(100),
    description text,
    created_date timestamp without time zone,
    updated_date timestamp without time zone,
    CONSTRAINT table_name_pkey PRIMARY KEY (id)
);

-- Add columns if they don't exist
ALTER TABLE table_name
ADD COLUMN IF NOT EXISTS new_column varchar(100);

-- Create indexes if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'table_name'
        AND indexname = 'ix_table_name_column'
    ) THEN
        CREATE INDEX ix_table_name_column ON table_name(column);
        RAISE NOTICE 'Created index ix_table_name_column on table_name.column';
    END IF;
END $$;

-- Example of adding a foreign key
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_table_name_reference_table'
        AND table_name = 'table_name'
    ) THEN
        ALTER TABLE table_name
        ADD CONSTRAINT fk_table_name_reference_table
        FOREIGN KEY (reference_id) REFERENCES reference_table(id);
    END IF;
END $$;
```

## Snowflake Table Template

```sql
-- Snowflake Table Template
-- Use this template when creating new tables in Snowflake

-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS table_name (
    id varchar(50) NOT NULL,
    name varchar(100),
    description text,
    created_date timestamp without time zone,
    updated_date timestamp without time zone,
    CONSTRAINT table_name_pkey PRIMARY KEY (id)
);

-- Add columns if they don't exist
ALTER TABLE table_name 
ADD COLUMN IF NOT EXISTS new_column varchar(100);

-- Note: Snowflake doesn't support explicit index creation as it uses automatic indexing
-- This is just a comment to document which columns would benefit from clustering
-- CLUSTER BY (id, created_date)

-- Example of adding a foreign key
ALTER TABLE table_name
ADD CONSTRAINT IF NOT EXISTS fk_table_name_reference_table
FOREIGN KEY (reference_id) REFERENCES reference_table(id);

-- Example of a JavaScript procedure to check and update data
CREATE OR REPLACE PROCEDURE check_and_update_table_name()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  // Query to check for specific conditions
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) AS count FROM table_name WHERE condition = true`
  });
  
  resultSet.next();
  var count = resultSet.getColumnValue(1);
  
  if (count > 0) {
    // Perform update if condition is met
    snowflake.execute({ 
      sqlText: `UPDATE table_name SET column = value WHERE condition = true`
    });
    return 'Updated ' + count + ' records';
  } else {
    return 'No records needed updating';
  }
$$;
```

## Best Practices

### General Best Practices

1. **Use existence checks**: Always check if objects exist before creating or modifying them.
2. **Consistent naming**: Use consistent naming conventions for tables, columns, indexes, and constraints.
3. **Documentation**: Include comments explaining the purpose of tables, columns, and complex logic.
4. **Error handling**: Implement proper error handling in procedures and functions.

### MSSQL Best Practices

1. **Use schema prefix**: Always use the schema prefix (e.g., `dbo.`) when referencing objects.
2. **Use square brackets**: Use square brackets around object names to avoid issues with reserved words.
3. **GO statements**: Use GO statements to separate batches of SQL statements.
4. **Use csg_* functions**: Use the csg_* functions for existence checks.

### PostgreSQL Best Practices

1. **Use IF NOT EXISTS**: Use IF NOT EXISTS clauses for creating objects.
2. **Use DO blocks**: Use DO blocks for complex logic that requires procedural code.
3. **Use RAISE NOTICE**: Use RAISE NOTICE for logging important actions.
4. **Partitioning**: Consider partitioning for large tables.

### Snowflake Best Practices

1. **No explicit indexes**: Don't create explicit indexes; use clustering instead.
2. **JavaScript procedures**: Use JavaScript stored procedures for complex logic.
3. **Time travel**: Leverage time travel for data recovery.
4. **Zero-copy cloning**: Use zero-copy cloning for efficient data copying.
