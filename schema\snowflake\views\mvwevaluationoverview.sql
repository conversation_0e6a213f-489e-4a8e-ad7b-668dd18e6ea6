DROP  VIEW IF EXISTS mvwevaluationoverview;
CREATE VIEW IF NOT EXISTS mvwevaluationoverview AS
SELECT
  concat(
    evaldata.conversationid,
    '|',
    evaldata.evaluationid,
    '|',
    evaldata.evaluationformid
  ) AS keyid,
  evaldata.conversationid,
  CONCAT ('Conversation ID: ', evaldata.conversationid) AS Title_Conv,
  evaldata.evaluationid,
  CONCAT ('Eval ID: ', evaluationid) AS Title_Eval,
  case when evaldata.calibrationid is null then FALSE else TRUE end AS iscalibration,
  evaldata.evaluationformid,
  evaldata.evaluatorid,
  CONCAT ('Evaluator: ', vwuserdetail.name) AS Title_Evaluator,
  evaldata.userid,
  evaldata.status,
  evaldata.totalscore,
  evaldata.totalcriticalscore,
  evaldata.totalnoncriticalscore,
  evaldata.agenthasread :: varchar :: boolean AS agenthasread,
  evaldata.releasedate,
  evaldata.releasedateltc,
  evaldata.assigneddate,
  evaldata.assigneddateltc,
  userdetails.name AS Agent_Name,
  userdetails.department AS Agent_Dept,
  userdetails.managerid AS Agent_Manager_Id,
  userdetails.managername AS Agent_Manager_Name,
  userdetails.divisionid AS Agent_Div,
  vwuserdetail.name AS Evaluator_Name,
  vwuserdetail.managerid AS Evaluator_Manager_Id,
  vwuserdetail.managername AS Evaluator_Manager_Name,
  convsumm.conversationstartdate AS convstartdate,
  convsumm.conversationenddate AS convenddate,
  CAST(
    SUM(evaldata.totalscore) / Count(evaldata.conversationid) AS INTEGER
  ) as average_score,
  case
    when evaldata.totalscore > 80 then 1
    else 0
  end as Over80,
  convsumm.firstqueuename AS queuename,
  convsumm.firstmediatype AS mediatype
FROM
  evaldata evaldata
  LEFT JOIN vwuserdetail userdetails ON evaldata.userid :: text = userdetails.id :: text
  LEFT JOIN vwuserdetail vwuserdetail ON evaldata.evaluatorid :: text = vwuserdetail.id :: text
  LEFT JOIN vwconvsummarydata convsumm ON convsumm.conversationid :: text = evaldata.conversationid :: text
WHERE
  evaldata.status :: text = 'FINISHED' :: text
group by
  convsumm.conversationenddate,
  convsumm.conversationstartdate,
  convsumm.firstmediatype,
  convsumm.firstqueuename,
  evaldata.agenthasread,
  evaldata.assigneddate,
  evaldata.assigneddateltc,
  evaldata.conversationid,
  evaldata.evaluationformid,
  evaldata.evaluationid,
  evaldata.calibrationid,
  evaldata.evaluatorid,
  evaldata.releasedate,
  evaldata.releasedate,
  evaldata.releasedateltc,
  evaldata.releasedateltc,
  evaldata.status,
  evaldata.totalcriticalscore,
  evaldata.totalnoncriticalscore,
  evaldata.totalscore,
  evaldata.userid,
  userdetails.department,
  userdetails.divisionid,
  userdetails.managerid,
  userdetails.managername,
  userdetails.name,
  vwuserdetail.managerid,
  vwuserdetail.managername,
  vwuserdetail.name;