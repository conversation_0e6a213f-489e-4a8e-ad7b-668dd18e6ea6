DROP VIEW IF EXISTS vwScheduleData;
CREATE
OR REPLACE VIEW vwScheduleData AS
SELECT
    sc.scheduleId,
    sc.userid,
    sc.shiftid,
    sc.shiftstartdate,
    sc.shiftstartdateltc,
    sc.shiftlengthtime,
    sc.activitystartdate,
    sc.activitystartdateltc,
    sc.activitylengthtime,
    sc.activitydescription,
    sc.activitycodeid,
    sc.activitypaid,
    sc.shiftmanuallyeditted,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername,
    ad.name as activitycodedesc,
    COALESCE(NULLIF(ad.name, ''), ad.category) AS activitycodename
FROM
    scheduleData sc
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = sc.userid
    LEFT OUTER JOIN activitycodeDetails ad on ad.businessunitid = sc.buid
    and ad.id = sc.activitycodeid;

COMMENT ON COLUMN vwScheduleData.scheduleId IS 'Schedule GUID';
COMMENT ON COLUMN vwScheduleData.userid IS 'User GUID';
COMMENT ON COLUMN vwScheduleData.shiftid IS 'Shift GUID';
COMMENT ON COLUMN vwScheduleData.shiftstartdate IS 'Start date of the shift';
COMMENT ON COLUMN vwScheduleData.shiftlengthtime IS 'Length of the shift';
COMMENT ON COLUMN vwScheduleData.activitystartdate IS 'Start date of the activity';
COMMENT ON COLUMN vwScheduleData.activitystartdateltc IS 'Start date of the activity in local time';
COMMENT ON COLUMN vwScheduleData.activitylengthtime IS 'Length of the activity';
COMMENT ON COLUMN vwScheduleData.activitydescription IS 'Activity Description';
COMMENT ON COLUMN vwScheduleData.activitycodeid IS 'Activity code GUID';
COMMENT ON COLUMN vwScheduleData.activitypaid IS 'Activity paid or not';
COMMENT ON COLUMN vwScheduleData.shiftmanuallyeditted IS 'Shift was manually edited or not';
COMMENT ON COLUMN vwScheduleData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwScheduleData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwScheduleData.managername IS 'Name of the manager';
COMMENT ON COLUMN vwScheduleData.activitycodedesc IS 'Description of the activity code';
COMMENT ON COLUMN vwScheduleData.activitycodename IS 'Name of the activity code';

COMMENT ON VIEW vwScheduleData  IS 'See ScheduleData - Expands all the GUIDs with their lookups'; 