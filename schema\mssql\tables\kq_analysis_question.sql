IF dbo.csg_table_exists('kq_analysis_question') = 0
CREATE TABLE [kq_analysis_question] (
    [kq_analysisquestionid] [uniqueidentifier] NOT NULL,
    [kq_analysisid] [uniqueidentifier],
    [question] [nvarchar](max),
    [answer] [nvarchar](max),
    [taxonomy] [nvarchar](200),
    [knowledgeid] [uniqueidentifier],
    [knowledge_confidence] [decimal](5,2),
    CONSTRAINT [PK_kq_analysis_question] PRIMARY KEY ([kq_analysisquestionid])
);



-- Create indexes on foreign key columns for performance
IF dbo.csg_index_exists('IX_kq_analysis_question_kq_analysisid', 'kq_analysis_question') = 0
CREATE INDEX [IX_kq_analysis_question_kq_analysisid] ON [kq_analysis_question]([kq_analysisid]);
