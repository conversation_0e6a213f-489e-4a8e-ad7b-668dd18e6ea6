CREATE OR ALTER VIEW [vwAdherenceActData] AS
SELECT
    ad.userid,
    ad.startdate,
    ad.enddate,
    ad.startdateltc,
    ad.enddateltc,
    ad.durationsecs,
    ad.durationsecs / 86400.00 AS durationsecsday,
    ad.actualActivityCategory,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername
FROM
    adherenceactData AS ad
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = ad.userid;
