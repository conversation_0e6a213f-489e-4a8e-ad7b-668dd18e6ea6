SELECT partman.create_partman_monthly('convsummarydata', 'conversationstartdate');
SELECT partman.create_partman_monthly('convvoicetopicdetaildata', 'starttime');
SELECT partman.create_partman_monthly('detailedinteractiondata', 'conversationstartdate');
SELECT partman.create_partman_monthly('participantattributesdynamic', 'conversationstartdate');
SELECT partman.create_partman_monthly('flowoutcomedata', 'conversationstartdate');
SELECT partman.create_partman_monthly('queueinteractiondata', 'startdate');
SELECT partman.create_partman_monthly('userinteractiondata', 'startdate');
SELECT partman.create_partman_monthly('userpresencedata', 'startdate');
SELECT partman.create_partman_monthly('userpresencedetaileddata', 'starttime');
SELECT partman.create_partman_monthly('userinteractionpresencedetaileddata', 'starttime');

-- Script for table: convsummarydata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.convsummarydata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.convsummarydata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: convvoicetopicdetaildata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.convvoicetopicdetaildata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.convvoicetopicdetaildata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: detailedinteractiondata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.detailedinteractiondata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.detailedinteractiondata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: participantattributesdynamic
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.participantattributesdynamic', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.participantattributesdynamic', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: flowoutcomedata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.flowoutcomedata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.flowoutcomedata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: queueinteractiondata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.queueinteractiondata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.queueinteractiondata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: userinteractiondata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.userinteractiondata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.userinteractiondata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: userpresencedata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.userpresencedata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.userpresencedata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: userpresencedetaileddata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.userpresencedetaileddata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.userpresencedetaileddata', get_preferred_schema()), p_batch := 1000);
END $$;

-- Script for table: userinteractionpresencedetaileddata
DO $$
BEGIN
    RAISE NOTICE 'Processing table: %', format('%I.userinteractionpresencedetaileddata', get_preferred_schema());
    CALL partman.partition_data_proc(format('%I.userinteractionpresencedetaileddata', get_preferred_schema()), p_batch := 1000);
END $$;

select partman.run_maintenance(p_analyze := true);

--drop function create_partman_monthly