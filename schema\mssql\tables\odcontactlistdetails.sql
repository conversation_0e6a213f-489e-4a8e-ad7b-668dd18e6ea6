IF dbo.csg_table_exists('odcontactlistdetails') = 0
CREATE TABLE [odcontactlistdetails](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](200),
    [datecreated] [datetime],
    [datecreatedltc] [datetime],
    [datemodified] [datetime],
    [version] [int],
    [automatictimezonemapping] [bit],
    [division] [nvarchar](50),
    [divisionname] [nvarchar](200),
    [updated] [datetime],
    CONSTRAINT [PK_odcontactlistdetails] PRIMARY KEY ([id])
);

-- Add datemodified column if it doesn't exist (for existing tables)
IF dbo.csg_column_exists('odcontactlistdetails', 'datemodified') = 0
    ALTER TABLE dbo.odcontactlistdetails ADD datemodified [datetime];
