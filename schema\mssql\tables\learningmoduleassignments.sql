-- Learning Module Assignments Table
-- Stores assignment data for learning modules assigned to users
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user is assigned the learning module
-- - moduleid: Links to learningmodules.id to identify which learning module is assigned
-- - Correlates with learningassignmentresults table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningassignmentresults on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

IF dbo.csg_table_exists('learningmoduleassignments') = 0
CREATE TABLE [learningmoduleassignments] (
    [id] VARCHAR(50) NOT NULL,
    [userid] VARCHAR(50), -- User ID from Genesys Cloud - links assignments to specific users
    [moduleid] VARCHAR(50), -- Learning Module ID - links assignments to specific learning modules
    [assessmentId] VARCHAR(50),
    [isOverdue] BIT,
    [version] VARCHAR(255),
    [percentageScore] DECIMAL(20, 2),
    [assessmentPercentageScore] DECIMAL(20, 2),
    [isRule] BIT,
    [isManual] BIT,
    [isPassed] BIT,
    [isLatest] BIT,
    [assessmentCompletionPercentage] DECIMAL(20, 2),
    [completionPercentage] DECIMAL(20, 2),
    [dateRecommendedForCompletion] DATETIME,
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [dateSubmitted] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [dateAssigned] DATETIME, -- Date when assignment was assigned to user
    [dateDue] DATETIME, -- Due date for assignment completion
    [state] VARCHAR(50), -- Current state of assignment (e.g., Assigned, InProgress, Completed)
    [updated] DATETIME,
    CONSTRAINT [learningmoduleassignments_pkey] PRIMARY KEY ([id])
);

-- Add missing columns if they don't exist (for existing installations)
IF dbo.csg_column_exists('learningmoduleassignments', 'userid') = 0
    ALTER TABLE learningmoduleassignments ADD userid VARCHAR(50) NULL;

IF dbo.csg_column_exists('learningmoduleassignments', 'moduleid') = 0
    ALTER TABLE learningmoduleassignments ADD moduleid VARCHAR(50) NULL;

-- Add missing assignment-specific columns if they don't exist (for existing installations)
IF dbo.csg_column_exists('learningmoduleassignments', 'dateAssigned') = 0
    ALTER TABLE learningmoduleassignments ADD dateAssigned DATETIME NULL;

IF dbo.csg_column_exists('learningmoduleassignments', 'dateDue') = 0
    ALTER TABLE learningmoduleassignments ADD dateDue DATETIME NULL;

IF dbo.csg_column_exists('learningmoduleassignments', 'state') = 0
    ALTER TABLE learningmoduleassignments ADD state VARCHAR(50) NULL;
