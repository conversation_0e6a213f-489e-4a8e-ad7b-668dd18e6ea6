CREATE
OR REPLACE VIEW vwCallNotRespondingDetails AS
SELECT
    det.conversationid,
    det.userid,
    ud.name as agentname,
    md.id as managerid,
    md.name as managername,
    det.mediatype,
    det.conversationstartdate,
    det.conversationstartdateLTC,
    det.conversationenddate,
    det.conversationenddateLTC,
    det.segmentstartdate,
    det.segmentstartdateLTC,
    det.segmentenddate,
    det.segmentenddateLTC,
    det.convtosegmentendtime AS TotalCallTime,
    det.segmenttime AS QueueTime,
    (det.convtosegmentendtime / 86400.00) AS TotalCallTimeDay,
    (det.segmenttime / 86400.00) AS QueueTimeDay,
    det.ani,
    det.dnis,
    det.queueid,
    que.name AS queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    detailedInteractionData det
    LEFT OUTER JOIN queueDetails que ON que.id = det.queueid
    LEFT OUTER JOIN userDetails ud ON ud.id = det.userid
    LEFT OUTER JOIN userDetails md ON md.id = ud.manager
WHERE
    det.segmenttype IN ('alert')
    AND det.purpose = 'agent'
    AND det.disconnectiontype in ('client', 'endpoint', 'noAnswerTransfer');

COMMENT ON COLUMN vwCallNotRespondingDetails.agentname IS 'Agent Name'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.ani IS 'ANI'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.conversationenddate IS 'Conversation End Date(UTC)'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.conversationenddateLTC IS 'Conversation End Date in LTC'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.conversationstartdate IS 'Conversation Start Date(UTC)'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.conversationstartdateLTC IS 'Conversation Start Date in LTC'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.disconnectiontype IS 'Disconnection Type'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.dnis IS 'DNIS'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.managerid IS 'Manager GUID'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.managername IS 'Manager Name'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.mediatype IS 'Media Type'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.purpose IS 'Purpose'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.queueName IS 'Queue Name'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.QueueTime IS 'Queue Time'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.QueueTimeDay IS 'Queue Time per Day'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.segmentenddate IS 'Segment End Date(UTC)'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.segmentenddateLTC IS 'Segment End Date in LTC'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.segmentstartdate IS 'Segment Start Date(UTC)'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.segmentstartdateLTC IS 'Segment Start Date in LTC'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.segmenttype IS 'Segment Type'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.TotalCallTime IS 'Total Call Time'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.TotalCallTimeDay IS 'Total Call Time per Day'; 
COMMENT ON COLUMN vwCallNotRespondingDetails.userid IS 'User GUID';  

COMMENT ON VIEW vwCallNotRespondingDetails IS 'Shows the details of agents missing calls (Not Responding) from the DetailedInteractionData table - Expands all the GUIDs with their lookups'; 