CREATE
OR REPLACE VIEW vwqueueauditdata AS
SELECT 
        qa.queueid AS queueid,
        qa.userid AS userid,
        ud.name AS name,
        ud.department AS department,
        ud.managername AS managername,
        qa.addorremove AS addorremove,
		qa.activeorinactive AS activeorinactive,
        qa.datemodified AS datemodified,
        qa.modifiedby AS modifiedby,
        ud1.name AS modbyname,
        ud1.department AS modbydept,
        ud1.managername AS modbymanager,
        qd.name AS queuename,
		qd.divisionid as divisionid
    FROM queueauditdata qa
        LEFT OUTER JOIN vwUserDetail ud ON qa.userid = ud.id
        LEFT OUTER JOIN vwUserDetail ud1 ON qa.modifiedby = ud1.id
        LEFT OUTER JOIN vwQueueDetails qd ON qa.queueid = qd.id;