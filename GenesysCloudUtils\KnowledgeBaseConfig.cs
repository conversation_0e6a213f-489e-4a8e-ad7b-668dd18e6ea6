using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;
using KnowledgeBase = GenesysCloudDefKnowledgeBase;
using KnowledgeBaseCategory = GenesysCloudDefKnowledgeBaseCategory;
using KnowledgeBaseDocument = GenesysCloudDefKnowledgeBaseDocument;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;


namespace GenesysCloudUtils
{
    public class KnowledgeBaseConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
         private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public KnowledgeBaseConfig(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Knowledge Base Config ");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetKnowledgeBaseDataFromGC()
        {
            Console.WriteLine("Get Knowledge Base Data");

            DataTable KnowledgeBases = DBUtil.CreateInMemTable("knowledgebase");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            _logger?.LogDebug("Retrieving knowledge base configuration");
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/knowledge/knowledgebases?pageSize=100", GCApiKey);
            if (JsonString != null && JsonString.Length > 30)
            {
                var KnowledgeBaseList = JsonConvert.DeserializeObject<KnowledgeBase.KnowledgeBase>(JsonString,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });
                foreach (KnowledgeBase.Entity Base in KnowledgeBaseList.entities)
                {
                    DataRow DrList = KnowledgeBases.NewRow();

                    DrList["id"] = Base.id;
                    DrList["name"] = Base.name;
                    DrList["description"] = Base.name;
                    DrList["coreLanguage"] = Base.name;
                    DrList["datecreated"] = Base.dateCreated;
                    DrList["datemodified"] = Base.dateModified;
                    DrList["faqCount"] = Base.faqCount;
                    DrList["dateDocumentLastModified"] = Base.dateDocumentLastModified;
                    DrList["articleCount"] = Base.articleCount;
                    DrList["published"] = Base.published;

                    KnowledgeBases.Rows.Add(DrList);

                }

            }
            return   KnowledgeBases;
        }
        public DataTable GetKnowledgeBaseCategoryDataFromGC(DataTable KnowledgeBase)
        {
            Console.WriteLine("Get Knowledge Base Data");

            DataTable KnowledgeBaseCategories = DBUtil.CreateInMemTable("knowledgebasecategorydata");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");
            foreach (DataRow row in KnowledgeBase.Rows)
            {
                string knowledgeBaseId = row["id"].ToString();
                string RequestString = URI + "/api/v2/knowledge/knowledgebases/"+ knowledgeBaseId+"/categories";
                                                                                    
                // Use JsonReturnHttpResponseGet for proper rate limit handling
                var response = JsonActions.JsonReturnHttpResponseGet(RequestString, GCApiKey);

                // Check HTTP status code before processing JSON
                if (response.StatusCode != 200)
                {
                    if (response.StatusCode == 429)
                    {
                        Console.WriteLine($"Rate limit encountered for knowledge base categories. Response: {response.Content}");
                        throw new Exception("Rate limiting exceeded retry limit for knowledge base categories");
                    }
                    else
                    {
                        throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                    }
                }

                string JsonString = response.Content;
                if (JsonString != null && JsonString.Length > 30)
                {
                    var KnowledgeBaseCategoryList = JsonConvert.DeserializeObject<KnowledgeBaseCategory.KnowledgeBaseCategoryData>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
                    foreach (KnowledgeBaseCategory.Entity Base in KnowledgeBaseCategoryList.entities)
                    {
                        DataRow DrList = KnowledgeBaseCategories.NewRow();

                        DrList["id"] = Base.id;
                        DrList["name"] = Base.name;
                        DrList["description"] = Base.description;
                        DrList["externalId"] = Base.externalId;
                        DrList["datecreated"] = Base.dateCreated;
                        DrList["datemodified"] = Base.dateModified;
                        if (Base.documentCount == null)
                        {
                            DrList["documentCount"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["documentCount"] = Base.documentCount;
                        }
                        if(Base.parentCategory!=null)
                        {
                            DrList["parentCategoryName"] = Base.parentCategory.name;
                            DrList["parentCategoryId"] = Base.parentCategory.id;
                        }
                        DrList["knowledgeBaseId"] = Base.knowledgeBase.id;

                        KnowledgeBaseCategories.Rows.Add(DrList);

                    }
                }

            }
            return   KnowledgeBaseCategories;
        }
        public DataTable GetKnowledgeBaseDocumentDataFromGC(DataTable KnowledgeBaseDetails)
        {
            Console.WriteLine("Get Knowledge Base Data");

            DataTable KnowledgeBaseDocuments = DBUtil.CreateInMemTable("knowledgebasedocument");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");
            foreach (DataRow row in KnowledgeBaseDetails.Rows)
            {
                string knowledgeBaseId = row["id"].ToString();
                string RequestString = URI + "/api/v2/knowledge/knowledgebases/"+ knowledgeBaseId+"/documents";
                                                                                    
                string JsonString = JsonActions.JsonReturnString(RequestString, GCApiKey);
                if (JsonString != null && JsonString.Length > 30)
                {
                    var KnowledgeBaseDocumentList = JsonConvert.DeserializeObject<KnowledgeBaseDocument.KnowledgeBaseDocument>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
                    foreach (KnowledgeBaseDocument.Entity Base in KnowledgeBaseDocumentList.entities)
                    {
                        DataRow DrList = KnowledgeBaseDocuments.NewRow();

                        DrList["id"] = Base.id;
                        DrList["title"] = Base.title;
                        DrList["visible"] = Base.visible;
                        DrList["state"] = Base.state;
                        DrList["datecreated"] = Base.dateCreated;
                        DrList["datemodified"] = Base.dateModified;
                        DrList["dateImported"] = Base.dateImported;
                        DrList["datePublished"] = Base.datePublished;
                        if (Base.externalId == null)
                        {
                            DrList["externalId"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["externalId"] = Base.externalId;
                        }
                        if (Base.lastPublishedVersionNumber == null)
                        {
                            DrList["lastPublishedVersionNumber"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["lastPublishedVersionNumber"] = Base.lastPublishedVersionNumber;
                        }
                        DrList["documentVersion"] = Base.documentVersion;
                        if (Base.category != null)
                        {
                            DrList["categoryId"] = Base.category.id;
                        }
                        DrList["knowledgeBaseId"] = Base.knowledgeBase.id;

                        KnowledgeBaseDocuments.Rows.Add(DrList);

                    }
                }

            }
            return   KnowledgeBaseDocuments;
        }
    }
}
