CREATE TABLE IF NOT EXISTS userpresencedataweekly (
    keyid varchar(255) NOT NULL,
    id varchar(50),
    userid varchar(50),
    startdate timestamp without time zone,
    timetype varchar(50),
    systempresenceid varchar(50),
    presenceid varchar(50),
    presencetime numeric(20, 2),
    routingid varchar(50),
    routingtime numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userpresencedataweekly_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN userPresenceDataWeekly.id IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.keyid IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.presenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.presencetime IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.routingid IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.routingtime IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.startdate IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.systempresenceid IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.timetype IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.updated IS ' '; 
COMMENT ON COLUMN userPresenceDataWeekly.userid IS ' '; 
COMMENT ON TABLE userPresenceDataWeekly IS 'User Presence Data Weekly Data - LTC - See UserPresenceData for Descriptions'; 