CREATE
OR REPLACE VIEW vwQueueInteractionData AS
SELECT
    queueinteractiondata.keyid,
    queueinteractiondata.startdate,
    queueinteractiondata.startDateltc,
    queueinteractiondata.direction,
    queueinteractiondata.queueid,
    queuedetails.name as queuename,
    queuedetails.divisionid AS queue_divisionid,
    divisiondetails.name as queue_divisionname,
    queueinteractiondata.mediatype,
    queueinteractiondata.wrapupcode,
    wrapupdetails.name as wrapupdesc,
    queueinteractiondata.talertcount,
    queueinteractiondata.talerttimesum,
    queueinteractiondata.talerttimesum / 86400.00 as talerttimesumDay,
    queueinteractiondata.talerttimemax,
    queueinteractiondata.talerttimemax / 86400.00 as talerttimemaxDay,
    queueinteractiondata.talerttimemin,
    queueinteractiondata.talerttimemin / 86400.00 as talerttimeminDay,
    queueinteractiondata.tansweredcount,
    queueinteractiondata.tansweredtimesum,
    queueinteractiondata.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    queueinteractiondata.tansweredtimemax,
    queueinteractiondata.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    queueinteractiondata.tansweredtimemin,
    queueinteractiondata.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    queueinteractiondata.ttalkcount,
    queueinteractiondata.ttalktimesum,
    queueinteractiondata.ttalktimesum / 86400.00 as ttalktimesumDay,
    queueinteractiondata.ttalktimemax,
    queueinteractiondata.ttalktimemax / 86400.00 as ttalktimemaxDay,
    queueinteractiondata.ttalktimemin,
    queueinteractiondata.ttalktimemin / 86400.00 as ttalktimeminDay,
    queueinteractiondata.ttalkcompletecount,
    queueinteractiondata.ttalkcompletetimesum,
    queueinteractiondata.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    queueinteractiondata.ttalkcompletetimemax,
    queueinteractiondata.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    queueinteractiondata.ttalkcompletetimemin,
    queueinteractiondata.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    SUM(ttalkcompletetimesum) / SUM(ttalkcompletecount) as ttalktimeavg,
    queueinteractiondata.tnotrespondingcount,
    queueinteractiondata.tnotrespondingtimesum,
    queueinteractiondata.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    queueinteractiondata.tnotrespondingtimemax,
    queueinteractiondata.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    queueinteractiondata.tnotrespondingtimemin,
    queueinteractiondata.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    queueinteractiondata.theldcount,
    queueinteractiondata.theldtimesum,
    queueinteractiondata.theldtimesum / 86400.00 as theldtimesumDay,
    queueinteractiondata.theldtimemax,
    queueinteractiondata.theldtimemax / 86400.00 as theldtimemaxDay,
    queueinteractiondata.theldtimemin,
    queueinteractiondata.theldtimemin / 86400.00 as theldtimeminDay,
    queueinteractiondata.theldcompletecount,
    queueinteractiondata.theldcompletetimesum,
    queueinteractiondata.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    queueinteractiondata.theldcompletetimemax,
    queueinteractiondata.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    queueinteractiondata.theldcompletetimemin,
    queueinteractiondata.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    queueinteractiondata.thandlecount,
    queueinteractiondata.thandletimesum,
    queueinteractiondata.thandletimesum / 86400.00 as thandletimesumDay,
    queueinteractiondata.thandletimemax,
    queueinteractiondata.thandletimemax / 86400.00 as thandletimemaxDay,
    queueinteractiondata.thandletimemin,
    queueinteractiondata.thandletimemin / 86400.00 as thandletimeminDay,
    SUM(thandletimesum) / SUM(thandlecount) as thandletimeavg,
    queueinteractiondata.tacwcount,
    queueinteractiondata.tacwtimesum,
    queueinteractiondata.tacwtimesum / 86400.00 as tacwtimesumDay,
    queueinteractiondata.tacwtimemax,
    queueinteractiondata.tacwtimemax / 86400.00 as tacwtimemaxDay,
    queueinteractiondata.tacwtimemin,
    queueinteractiondata.tacwtimemin / 86400.00 as tacwtimeminDay,
    queueinteractiondata.nconsult,
    queueinteractiondata.nconsulttransferred,
    queueinteractiondata.noutbound,
    queueinteractiondata.nerror,
    queueinteractiondata.ntransferred,
    queueinteractiondata.nblindtransferred,
    queueinteractiondata.nconnected,
    queueinteractiondata.noffered,
    queueinteractiondata.noversla,
    queueinteractiondata.tacdcount,
    queueinteractiondata.tacdtimesum,
    queueinteractiondata.tacdtimesum / 86400.00 as tacdtimesumDay,
    queueinteractiondata.tacdtimemax,
    queueinteractiondata.tacdtimemax / 86400.00 as tacdtimemaxDay,
    queueinteractiondata.tacdtimemin,
    queueinteractiondata.tacdtimemin / 86400.00 as tacdtimeminDay,
    queueinteractiondata.tdialingcount,
    queueinteractiondata.tdialingtimesum,
    queueinteractiondata.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    queueinteractiondata.tdialingtimemax,
    queueinteractiondata.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    queueinteractiondata.tdialingtimemin,
    queueinteractiondata.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    queueinteractiondata.tcontactingcount,
    queueinteractiondata.tcontactingtimesum,
    queueinteractiondata.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    queueinteractiondata.tcontactingtimemax,
    queueinteractiondata.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    queueinteractiondata.tcontactingtimemin,
    queueinteractiondata.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    queueinteractiondata.tvoicemailcount,
    queueinteractiondata.tvoicemailtimesum,
    queueinteractiondata.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    queueinteractiondata.tvoicemailtimemax,
    queueinteractiondata.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    queueinteractiondata.tvoicemailtimemin,
    queueinteractiondata.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    queueinteractiondata.tflowoutcount,
    queueinteractiondata.tflowouttimesum,
    queueinteractiondata.tflowouttimesum / 86400.00 as tflowouttimesumDay,
    queueinteractiondata.tflowouttimemax,
    queueinteractiondata.tflowouttimemax / 86400.00 as tflowouttimemaxDay,
    queueinteractiondata.tflowouttimemin,
    queueinteractiondata.tflowouttimemin / 86400.00 as tflowouttimeminDay,
    queueinteractiondata.twaitcount,
    queueinteractiondata.twaittimesum,
    queueinteractiondata.twaittimesum / 86400.00 as twaittimesumDay,
    queueinteractiondata.twaittimemax,
    queueinteractiondata.twaittimemax / 86400.00 as twaittimemaxDay,
    queueinteractiondata.twaittimemin,
    queueinteractiondata.twaittimemin / 86400.00 as twaittimeminDay,
    SUM(twaittimesum) / SUM(twaitcount) AS twaittimeavg,
    queueinteractiondata.tabandoncount,
    queueinteractiondata.tabandontimesum,
    queueinteractiondata.tabandontimesum / 86400.00 as tabandontimesumDay,
    queueinteractiondata.tabandontimemax,
    queueinteractiondata.tabandontimemax / 86400.00 as tabandontimemaxDay,
    queueinteractiondata.tabandontimemin,
    queueinteractiondata.tabandontimemin / 86400.00 as tabandontimeminDay,
    SUM(tabandontimesum) / SUM(tabandoncount) AS tabandontimeavg,
    (SUM(tabandontimesum) / SUM(tabandoncount)) / 86400.00 as tabandontimeavgDay,
    SUM(tabandoncount) / NULLIF(SUM(noffered), 0) as tabandoncountavg,
    queueinteractiondata.servicelevelnumerator,
    queueinteractiondata.serviceleveldenominator,
    SUM(servicelevelnumerator) / SUM(serviceleveldenominator) as servicelevelcalc,
    queueinteractiondata.av1count,
    queueinteractiondata.av1timesum,
    queueinteractiondata.av1timesum / 86400.00 as av1timesumDay,
    queueinteractiondata.av1timemax,
    queueinteractiondata.av1timemax / 86400.00 as av1timemaxDay,
    queueinteractiondata.av1timemin,
    queueinteractiondata.av1timemin / 86400.00 as av1timeminDay,
    queueinteractiondata.av2count,
    queueinteractiondata.av2timesum,
    queueinteractiondata.av2timesum / 86400.00 as av2timesumDay,
    queueinteractiondata.av2timemax,
    queueinteractiondata.av2timemax / 86400.00 as av2timemaxDay,
    queueinteractiondata.av2timemin,
    queueinteractiondata.av2timemin / 86400.00 as av2timeminDay,
    queueinteractiondata.av3count,
    queueinteractiondata.av3timesum,
    queueinteractiondata.av3timesum / 86400.00 as av3timesumDay,
    queueinteractiondata.av3timemax,
    queueinteractiondata.av3timemax / 86400.00 as av3timemaxDay,
    queueinteractiondata.av3timemin,
    queueinteractiondata.av3timemin / 86400.00 as av3timeminDay,
    queueinteractiondata.av4count,
    queueinteractiondata.av4timesum,
    queueinteractiondata.av4timesum / 86400.00 as av4timesumDay,
    queueinteractiondata.av4timemax,
    queueinteractiondata.av4timemax / 86400.00 as av4timemaxDay,
    queueinteractiondata.av4timemin,
    queueinteractiondata.av4timemin / 86400.00 as av4timeminDay,
    queueinteractiondata.av5count,
    queueinteractiondata.av5timesum,
    queueinteractiondata.av5timesum / 86400.00 as av5timesumDay,
    queueinteractiondata.av5timemax,
    queueinteractiondata.av5timemax / 86400.00 as av5timemaxDay,
    queueinteractiondata.av5timemin,
    queueinteractiondata.av5timemin / 86400.00 as av5timeminDay,
    queueinteractiondata.av6count,
    queueinteractiondata.av6timesum,
    queueinteractiondata.av6timesum / 86400.00 as av6timesumDay,
    queueinteractiondata.av6timemax,
    queueinteractiondata.av6timemax / 86400.00 as av6timemaxDay,
    queueinteractiondata.av6timemin,
    queueinteractiondata.av6timemin / 86400.00 as av6timeminDay,
    queueinteractiondata.av7count,
    queueinteractiondata.av7timesum,
    queueinteractiondata.av7timesum / 86400.00 as av7timesumDay,
    queueinteractiondata.av7timemax,
    queueinteractiondata.av7timemax / 86400.00 as av7timemaxDay,
    queueinteractiondata.av7timemin,
    queueinteractiondata.av7timemin / 86400.00 as av7timeminDay,
    queueinteractiondata.av8count,
    queueinteractiondata.av8timesum,
    queueinteractiondata.av8timesum / 86400.00 as av8timesumDay,
    queueinteractiondata.av8timemax,
    queueinteractiondata.av8timemax / 86400.00 as av8timemaxDay,
    queueinteractiondata.av8timemin,
    queueinteractiondata.av8timemin / 86400.00 as av8timeminDay,
    queueinteractiondata.av9count,
    queueinteractiondata.av9timesum,
    queueinteractiondata.av9timesum / 86400.00 as av9timesumDay,
    queueinteractiondata.av9timemax,
    queueinteractiondata.av9timemax / 86400.00 as av9timemaxDay,
    queueinteractiondata.av9timemin,
    queueinteractiondata.av9timemin / 86400.00 as av9timeminDay,
    queueinteractiondata.av10count,
    queueinteractiondata.av10timesum,
    queueinteractiondata.av10timesum / 86400.00 as av10timesumDay,
    queueinteractiondata.av10timemax,
    queueinteractiondata.av10timemax / 86400.00 as av10timemaxDay,
    queueinteractiondata.av10timemin,
    queueinteractiondata.av10timemin / 86400.00 as av10timeminDay,
    queueinteractiondata.updated
FROM
    queueInteractiondata as queueinteractiondata
    left outer join queuedetails as queuedetails on queuedetails.id = queueinteractiondata.queueid
    left outer join wrapupdetails as wrapupdetails on wrapupdetails.id = queueinteractiondata.wrapupcode
    left outer join divisiondetails as divisiondetails on queuedetails.divisionid = divisiondetails.id
GROUP BY
    queueinteractiondata.keyid,
    queueinteractiondata.startdate,
    queueinteractiondata.startdateltc,
    queueinteractiondata.direction,
    queueinteractiondata.queueid,
    queuedetails.name,
    queuedetails.divisionid,
    divisiondetails.name,
    queueinteractiondata.mediatype,
    queueinteractiondata.wrapupcode,
    wrapupdetails.name,
    queueinteractiondata.talertcount,
    queueinteractiondata.talerttimesum,
    queueinteractiondata.talerttimemax,
    queueinteractiondata.talerttimemin,
    queueinteractiondata.tansweredcount,
    queueinteractiondata.tansweredtimesum,
    queueinteractiondata.tansweredtimemax,
    queueinteractiondata.tansweredtimemin,
    queueinteractiondata.ttalkcount,
    queueinteractiondata.ttalktimesum,
    queueinteractiondata.ttalktimemax,
    queueinteractiondata.ttalktimemin,
    queueinteractiondata.ttalkcompletecount,
    queueinteractiondata.ttalkcompletetimesum,
    queueinteractiondata.ttalkcompletetimemax,
    queueinteractiondata.ttalkcompletetimemin,
    queueinteractiondata.tnotrespondingcount,
    queueinteractiondata.tnotrespondingtimesum,
    queueinteractiondata.tnotrespondingtimemax,
    queueinteractiondata.tnotrespondingtimemin,
    queueinteractiondata.theldcount,
    queueinteractiondata.theldtimesum,
    queueinteractiondata.theldtimemax,
    queueinteractiondata.theldtimemin,
    queueinteractiondata.theldcompletecount,
    queueinteractiondata.theldcompletetimesum,
    queueinteractiondata.theldcompletetimemax,
    queueinteractiondata.theldcompletetimemin,
    queueinteractiondata.thandlecount,
    queueinteractiondata.thandletimesum,
    queueinteractiondata.thandletimemax,
    queueinteractiondata.thandletimemin,
    queueinteractiondata.tacwcount,
    queueinteractiondata.tacwtimesum,
    queueinteractiondata.tacwtimemax,
    queueinteractiondata.tacwtimemin,
    queueinteractiondata.nconsult,
    queueinteractiondata.nconsulttransferred,
    queueinteractiondata.noutbound,
    queueinteractiondata.nerror,
    queueinteractiondata.ntransferred,
    queueinteractiondata.nblindtransferred,
    queueinteractiondata.nconnected,
    queueinteractiondata.noffered,
    queueinteractiondata.noversla,
    queueinteractiondata.tacdcount,
    queueinteractiondata.tacdtimesum,
    queueinteractiondata.tacdtimemax,
    queueinteractiondata.tacdtimemin,
    queueinteractiondata.tdialingcount,
    queueinteractiondata.tdialingtimesum,
    queueinteractiondata.tdialingtimemax,
    queueinteractiondata.tdialingtimemin,
    queueinteractiondata.tcontactingcount,
    queueinteractiondata.tcontactingtimesum,
    queueinteractiondata.tcontactingtimemax,
    queueinteractiondata.tcontactingtimemin,
    queueinteractiondata.tvoicemailcount,
    queueinteractiondata.tvoicemailtimesum,
    queueinteractiondata.tvoicemailtimemax,
    queueinteractiondata.tvoicemailtimemin,
    queueinteractiondata.tflowoutcount,
    queueinteractiondata.tflowouttimesum,
    queueinteractiondata.tflowouttimemax,
    queueinteractiondata.tflowouttimemin,
    queueinteractiondata.twaitcount,
    queueinteractiondata.twaittimesum,
    queueinteractiondata.twaittimemax,
    queueinteractiondata.twaittimemin,
    queueinteractiondata.tabandoncount,
    queueinteractiondata.tabandontimesum,
    queueinteractiondata.tabandontimemax,
    queueinteractiondata.tabandontimemin,
    queueinteractiondata.servicelevelnumerator,
    queueinteractiondata.serviceleveldenominator,
    queueinteractiondata.av1count,
    queueinteractiondata.av1timesum,
    queueinteractiondata.av1timemax,
    queueinteractiondata.av1timemin,
    queueinteractiondata.av2count,
    queueinteractiondata.av2timesum,
    queueinteractiondata.av2timemax,
    queueinteractiondata.av2timemin,
    queueinteractiondata.av3count,
    queueinteractiondata.av3timesum,
    queueinteractiondata.av3timemax,
    queueinteractiondata.av3timemin,
    queueinteractiondata.av4count,
    queueinteractiondata.av4timesum,
    queueinteractiondata.av4timemax,
    queueinteractiondata.av4timemin,
    queueinteractiondata.av5count,
    queueinteractiondata.av5timesum,
    queueinteractiondata.av5timemax,
    queueinteractiondata.av5timemin,
    queueinteractiondata.av6count,
    queueinteractiondata.av6timesum,
    queueinteractiondata.av6timemax,
    queueinteractiondata.av6timemin,
    queueinteractiondata.av7count,
    queueinteractiondata.av7timesum,
    queueinteractiondata.av7timemax,
    queueinteractiondata.av7timemin,
    queueinteractiondata.av8count,
    queueinteractiondata.av8timesum,
    queueinteractiondata.av8timemax,
    queueinteractiondata.av8timemin,
    queueinteractiondata.av9count,
    queueinteractiondata.av9timesum,
    queueinteractiondata.av9timemax,
    queueinteractiondata.av9timemin,
    queueinteractiondata.av10count,
    queueinteractiondata.av10timesum,
    queueinteractiondata.av10timemax,
    queueinteractiondata.av10timemin,
    queueinteractiondata.updated