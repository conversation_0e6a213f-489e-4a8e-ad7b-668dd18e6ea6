CREATE TABLE IF NOT EXISTS convvoicetopicdetaildata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    starttime timestamp without time zone NOT NULL,
    starttimeltc timestamp without time zone,
    participant varchar(50),
    duration numeric(20, 2),
    confidence numeric(20, 2),
    topicname varchar(200),
    topicid varchar(50),
    topicphrase varchar(200),
    transcriptphrase varchar(200),
    updated timestamp without time zone,
    transcriptnumber varchar(50),
	communicationid varchar(50),
	ani varchar(200),
	dnis varchar(200),
	queueid varchar(50),
	userid varchar(50),
    CONSTRAINT convvoicetopicdetaildata_new_pkey PRIMARY KEY (keyid, starttime)
) PARTITION BY RANGE (starttime);

CREATE INDEX IF NOT EXISTS convvoicetopic__new_topicid ON convvoicetopicdetaildata USING btree (
    topicid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS convvoicetopic_new_conv ON convvoicetopicdetaildata USING btree (
    conversationid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS convvoicetopic_new_start ON convvoicetopicdetaildata USING btree (starttime ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS convvoicetopic_new_startltc ON convvoicetopicdetaildata USING btree (starttimeltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS convvoicetopic_participant ON convvoicetopicdetaildata USING btree (
    participant ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS convvoicetopic_topicphrase ON convvoicetopicdetaildata USING btree (
    topicphrase ASC NULLS LAST
);

ALTER TABLE IF EXISTS convvoicetopic_2021_02 RENAME TO convvoicetopicdetaildata_p2021_02;
ALTER TABLE IF EXISTS convvoicetopic_2021_03 RENAME TO convvoicetopicdetaildata_p2021_03;
ALTER TABLE IF EXISTS convvoicetopic_2021_04 RENAME TO convvoicetopicdetaildata_p2021_04;
ALTER TABLE IF EXISTS convvoicetopic_2021_05 RENAME TO convvoicetopicdetaildata_p2021_05;
ALTER TABLE IF EXISTS convvoicetopic_2021_06 RENAME TO convvoicetopicdetaildata_p2021_06;
ALTER TABLE IF EXISTS convvoicetopic_2021_07 RENAME TO convvoicetopicdetaildata_p2021_07;
ALTER TABLE IF EXISTS convvoicetopic_2021_08 RENAME TO convvoicetopicdetaildata_p2021_08;
ALTER TABLE IF EXISTS convvoicetopic_2021_09 RENAME TO convvoicetopicdetaildata_p2021_09;
ALTER TABLE IF EXISTS convvoicetopic_2021_10 RENAME TO convvoicetopicdetaildata_p2021_10;
ALTER TABLE IF EXISTS convvoicetopic_2021_11 RENAME TO convvoicetopicdetaildata_p2021_11;
ALTER TABLE IF EXISTS convvoicetopic_2021_12 RENAME TO convvoicetopicdetaildata_p2021_12;
ALTER TABLE IF EXISTS convvoicetopic_2022_01 RENAME TO convvoicetopicdetaildata_p2022_01;
ALTER TABLE IF EXISTS convvoicetopic_2022_02 RENAME TO convvoicetopicdetaildata_p2022_02;
ALTER TABLE IF EXISTS convvoicetopic_2022_03 RENAME TO convvoicetopicdetaildata_p2022_03;
ALTER TABLE IF EXISTS convvoicetopic_2022_04 RENAME TO convvoicetopicdetaildata_p2022_04;
ALTER TABLE IF EXISTS convvoicetopic_2022_05 RENAME TO convvoicetopicdetaildata_p2022_05;
ALTER TABLE IF EXISTS convvoicetopic_2022_06 RENAME TO convvoicetopicdetaildata_p2022_06;
ALTER TABLE IF EXISTS convvoicetopic_2022_07 RENAME TO convvoicetopicdetaildata_p2022_07;
ALTER TABLE IF EXISTS convvoicetopic_2022_08 RENAME TO convvoicetopicdetaildata_p2022_08;
ALTER TABLE IF EXISTS convvoicetopic_2022_09 RENAME TO convvoicetopicdetaildata_p2022_09;
ALTER TABLE IF EXISTS convvoicetopic_2022_10 RENAME TO convvoicetopicdetaildata_p2022_10;
ALTER TABLE IF EXISTS convvoicetopic_2022_11 RENAME TO convvoicetopicdetaildata_p2022_11;
ALTER TABLE IF EXISTS convvoicetopic_2022_12 RENAME TO convvoicetopicdetaildata_p2022_12;
