# ------------------------------------------------------------------------------
# <auto-generated>
#
#     This code was generated.
#o
#     - To turn off auto-generation set:
#
#         [AzurePipelines (AutoGenerate = false)]
#
#     - To trigger manual generation invoke:
#
#         nuke --generate-configuration AzurePipelines --host AzurePipelines
#
# </auto-generated>
# ------------------------------------------------------------------------------

trigger:
  branches:
    include:
      - "*"
resources:
  repositories:
    - repository: technology-scripts
      type: git
      name: "technology/technology-scripts"
      ref: "main"
      endpoint: CI_Pipeline_Integration
stages:
  - stage: ubuntu_2204
    displayName: "ubuntu-22.04"
    pool:
      vmImage: "ubuntu-22.04"
    jobs:
      - job: Test
        displayName: "Test"
        steps:
          - checkout: self
            submodules: false
            fetchDepth: 0
          - task: Cache@2
            displayName: Cache (nuget-packages)
            inputs:
              key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuget-packages 
              path: $(HOME)/.nuget/packages
          - task: Cache@2
            displayName: Cache (nuke-temp)
            inputs:
              key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuke-temp
              path: .nuke/temp
          - task: SonarQubePrepare@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
            inputs:
              SonarQube: "SonarQube"
              scannerMode: "dotnet"
              projectKey: "technology_genesys-adapter_929befe3-2e89-4d20-a577-53e600680bc5"
              projectName: "genesys-adapter"
          - task: CmdLine@2
            inputs:
              script: "./build.cmd Test --skip"
            env:
              DockerPassword: $(DockerPassword)
          - task: PublishBuildArtifacts@1
            condition: always()
            inputs:
              artifactName: results
              pathtoPublish: "tests/results"
          - task: SonarQubeAnalyze@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
          - task: SonarQubePublish@7
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
            inputs:
              pollingTimeoutSec: "300"
      - job: Publish
        displayName: "Publish"
        dependsOn: Test
        condition: succeeded()
        steps:
          - checkout: self
            persistCredentials: tru
            clean: true
            submodules: false
            fetchDepth: 0
          - task: Cache@2
            displayName: Cache (nuget-packages)
            inputs:
              key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuget-packages
              path: $(HOME)/.nuget/packages
          - task: Cache@2
            displayName: Cache (nuke-temp)
            inputs:
              key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuke-temp
              path: .nuke/temp
          - task: CmdLine@2
            inputs:
              script: "./build.cmd Publish --skip"
            env:
              DockerPassword: $(DockerPassword)
          - task: PublishBuildArtifacts@1
            condition: always()
            inputs:
              artifactName: artifacts
              pathtoPublish: "artifacts"

      - job: DeployInstance_MSSQL
        displayName: "Deploy GA (MSSQL)"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull mcr.microsoft.com/mssql/server:2019-latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              docker run \
              --detach \
              --name database_mssql \
              -e ACCEPT_EULA=Y \
              -e MSSQL_PID=Developer \
              -e MSSQL_SA_PASSWORD="System(!)" \
              -p 1433:1433 \
              mcr.microsoft.com/mssql/server:2019-latest
            displayName: "Deploy Database - MSSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #MSSQL sync jobs
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Install"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "FactData"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Aggregation"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "PresenceDetail"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Interaction"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Evaluation"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "VoiceAnalysis"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
          #Run Install a second time to test against existing database
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Install"
              databaseName: "master"
              databaseType: "MSSQL"
              databaseUser: "sa"
      - job: DeployInstance_SF
        displayName: "Deploy GA (Snowflake)"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker network create ga_tbls
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull mcr.microsoft.com/mssql/server:2019-latest
              fi
            displayName: "Prepare Docker Environment"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Snowflake sync jobs
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              databaseAddress: "mzejbvj-yj74104.snowflakecomputing.com"
              databaseName: "CI_Testing"
              databaseType: "Snowflake"
              databaseUser: "CI_Testing"
      - job: DeployInstance_PG_CC1
        displayName: "Deploy GA (PSQL) - Customer Centric 1"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure CI Pipeline"
              git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull customerscience/genesys_adapter:postgres15-bullseye
                docker pull mcr.microsoft.com/mssql/server:2019-latest
                docker pull ghcr.io/k1low/tbls:latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Postgres sync jobs

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Install"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "FactData"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Aggregation"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Realtime"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "UserQueueMapping"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              jobOption: "Install"
              genesysapiclientId: "fe5a3ec3-b353-45c7-9a88-a24fbbd0b957"
              genesysapiclientSecret: "enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA="
      - job: DeployInstance_PG_CC2
        displayName: "Deploy GA (PSQL) - Customer Centric 2"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure CI Pipeline"
              git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull customerscience/genesys_adapter:postgres15-bullseye
                docker pull mcr.microsoft.com/mssql/server:2019-latest
                docker pull ghcr.io/k1low/tbls:latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Postgres sync jobs
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "Install"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "FactData"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "Adherence"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "HeadCountForecast"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "OfferedForecast"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "ScheduleDetails"

          # - template: azure-pipelines-genesys_adapter-template.yaml
          #   parameters:
          #     genesysapiclientId: '5bf00927-fef8-4b16-9743-99992fac3f72'
          #     genesysapiclientSecret: 'enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8='
          #     jobOption: 'Shrinkage'

          # - template: azure-pipelines-genesys_adapter-template.yaml
          #   parameters:
          #     genesysapiclientId: '5bf00927-fef8-4b16-9743-99992fac3f72'
          #     genesysapiclientSecret: 'enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8='
          #     jobOption: 'TimeOffReq'

          # - template: azure-pipelines-genesys_adapter-template.yaml
          #   parameters:
          #     genesysapiclientId: '5bf00927-fef8-4b16-9743-99992fac3f72'
          #     genesysapiclientSecret: 'enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8='
          #     jobOption: 'WFMAudit'

          # - template: azure-pipelines-genesys_adapter-template.yaml
          #   parameters:
          #     genesysapiclientId: '5bf00927-fef8-4b16-9743-99992fac3f72'
          #     genesysapiclientSecret: 'enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8='
          #     jobOption: 'WFMSchedule'

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "5bf00927-fef8-4b16-9743-99992fac3f72"
              genesysapiclientSecret: "enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8="
              jobOption: "Install"
      - job: DeployInstance_PG_CC3
        displayName: "Deploy GA (PSQL) - Customer Centric 3"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure CI Pipeline"
              git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull customerscience/genesys_adapter:postgres15-bullseye
                docker pull mcr.microsoft.com/mssql/server:2019-latest
                docker pull ghcr.io/k1low/tbls:latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Postgres sync jobs
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "Install"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "FactData"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "Evaluation"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "EvaluationCatchup"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "HoursBlockData"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d0b4dcc8-e437-4c7c-bf30-274c7f20cafc"
              genesysapiclientSecret: "enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ="
              jobOption: "Install"
      - job: DeployInstance_PG_CC4
        displayName: "Deploy GA (PSQL) - Customer Centric 4"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure CI Pipeline"
              git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull customerscience/genesys_adapter:postgres15-bullseye
                docker pull mcr.microsoft.com/mssql/server:2019-latest
                docker pull ghcr.io/k1low/tbls:latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Postgres sync jobs

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "Information"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "Install"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "FactData"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "Interaction"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "InteractionPresence"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "QueueMembership"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "Survey"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "UserQueueAudit"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "VoiceAnalysis"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "d7260378-2509-4fbc-ae5b-82ccb33e0ef0"
              genesysapiclientSecret: "enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4="
              jobOption: "Install"
      - job: DeployInstance_PG_CC5
        displayName: "Deploy GA (PSQL) - Customer Centric 5"
        dependsOn: Publish
        condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
        pool:
          vmImage: "ubuntu-22.04"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure CI Pipeline"
              git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"
          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"
          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              docker network create ga_tbls
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull customerscience/genesys_adapter:postgres15-bullseye
                docker pull mcr.microsoft.com/mssql/server:2019-latest
                docker pull ghcr.io/k1low/tbls:latest
              fi
            displayName: "Prepare Docker Environment"
          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"
          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"
          #Postgres sync jobs
          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Install"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Aggregation"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Interaction"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Knowledge"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Learning"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "OAuthUsage"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Subscription"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "SubsUsers"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "VoiceAnalysis"

          - template: azure-pipelines-genesys_adapter-template.yaml
            parameters:
              genesysapiclientId: "f1482484-31f1-4ca6-a4a0-9e6ccdb026e6"
              genesysapiclientSecret: "enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8="
              jobOption: "Install"

      - job: Deploy_TBLS
        displayName: "Deploy TBLS - Create Data Dictionary"
        dependsOn:
          - Publish
        condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
        pool:
          vmImage: "ubuntu-22.04"
        variables:
          - group: sync_kba_config
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - checkout: technology-scripts
            persistCredentials: true
            clean: true
            submodules: false
            fetchDepth: 0
          - script: |
              docker login --username customerscience --password $(DockerPassword)
              docker network create ga_tbls
              ls -lah $(Build.SourcesDirectory)
              ls -lah $(Build.SourcesDirectory)/technology-scripts
              ls -lah $(Build.SourcesDirectory)/technology-scripts/python
          - script: |
              if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
                dockerImageTag="latest"
              else
                dockerImageTag="$(Build.BuildNumber)"
              fi
              echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
            displayName: "Set Docker Image Tag"

          - script: |
              mkdir -p $(Build.SourcesDirectory)/docker-cache
            displayName: "Create Docker Cache Directory"

          - task: Cache@2
            inputs:
              key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
              path: $(Build.SourcesDirectory)
              cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"

          - script: |
              echo "Docker image tag: $(dockerImageTag)"
              echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
              docker images
              if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
                echo "Using cached Docker images"
              else
                docker pull ghcr.io/k1low/tbls:latest
                docker pull customerscience/genesys_adapter:postgres15-bullseye
              fi
            displayName: "Prepare Docker Environment"

          - script: |
              sudo apt-get update -qq
              sudo apt-get install -yy pandoc
              python -m pip install --upgrade pip
              pip install pypandoc

          - script: |
              if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
                  if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                      # Cleanup if the container exists but stopped
                      docker rm database_postgres
                  fi
                  # Run your container since it does not exist
                  docker run \
                  --detach \
                  --name database_postgres \
                  --network ga_tbls \
                  --quiet \
                  -e POSTGRES_DB=contactcentredb \
                  -e POSTGRES_PASSWORD=system \
                  -e POSTGRES_USER=system \
                  -p 5432:5432 \
                  customerscience/genesys_adapter:postgres15-bullseye
              else
                  echo "Container database_postgres already running."
              fi
            displayName: "Deploy Database - PostgreSQL"

          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: "current"
              downloadType: "single"
              artifactName: "artifacts"
              downloadPath: "$(System.ArtifactsDirectory)"

          - script: |
              mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
              unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
            displayName: "Unzip Linux Artifacts"

          #Postgres Install job
          - template: azure-pipelines-genesys_adapter-template.yaml

          - script: |
              mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/
              mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/md
              mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/html
              cd $(Build.SourcesDirectory)/genesys-adapter/
              source <(curl https://raw.githubusercontent.com/k1LoW/tbls/main/use)
              tbls -c .tbls/.tbls.yml doc --rm-dist
              tbls -c .tbls/.tbls.yml lint
              tbls -c .tbls/.tbls.yml coverage
            displayName: "Generate markdown from database"

          - task: PythonScript@0
            inputs:
              scriptSource: "filePath"
              scriptPath: $(Build.SourcesDirectory)/technology-scripts/python/sync_kba.py
            displayName: "Sync Documentation"
            # condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/dev'))
            env:
              SOURCE_DIR: $(Build.SourcesDirectory)/genesys-adapter/doc/md
              OUTPUT_DIR: $(Build.SourcesDirectory)/genesys-adapter/doc/html
              ACCESS_TOKEN: $(access_token)
              CLIENT_ID: $(client_id)
              CLIENT_SECRET: $(client_secret)
              REFRESH_TOKEN: $(refresh_token)

          - task: PublishBuildArtifacts@1
            condition: always()
            inputs:
              artifactName: "documentation"
              pathtoPublish: $(Build.SourcesDirectory)/genesys-adapter/doc/
      - job: DockerAll
        displayName: "DockerAll"
        dependsOn:
          - Deploy_TBLS
          - Test
          - Publish
        condition: |
          and(
            or(
              succeeded('Deploy_TBLS'),
              failed('Deploy_TBLS'),
              eq(dependencies.Deploy_TBLS.result, 'Skipped')
            ),
            succeeded('Test'),
            succeeded('Publish'),
            ne(variables['Build.Reason'], 'PullRequest')
          )
        steps:
          - checkout: self
            submodules: false
            fetchDepth: 0
          - task: Cache@2
            displayName: Cache (nuget-packages)
            inputs:
              key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuget-packages
              path: $(HOME)/.nuget/packages
          - task: Cache@2
            displayName: Cache (nuke-temp)
            inputs:
              key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
              restoreKeys: $(Agent.OS) | nuke-temp
              path: .nuke/temp
          - task: CmdLine@2
            inputs:
              script: "./build.cmd DockerAll --skip"
            env:
              DockerPassword: $(DockerPassword)

    # DeployInstance_PG_CC1
    # CWH
    # DeployInstance_PG_CC2
    # LNE
    # DeployInstance_PG_CC3
    # USG
    # DeployInstance_PG_CC4
    # TBRP
    # DeployInstance_PG_CC5
    # DKN
