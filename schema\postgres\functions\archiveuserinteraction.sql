CREATE
OR R<PERSON>LACE PROCEDURE archiveuserinteraction(IN aggoffset integer, IN aggtype character) LANGUAGE 'plpgsql' AS $BODY$ DECLARE CurrentTimeUTC timestamp without time zone;

CurrentTimeLTC timestamp without time zone;

SystemTime timestamp without time zone;

OffsetTime integer;

CurrentDOW integer;

StartDate timestamp without time zone;

EndDate timestamp without time zone;

TableName varchar(50);

DelsqlCommand varchar(1000);

InssqlCommand text;

begin
select
    utctime into CurrentTimeUTC
from
    timezonecalcs('Australia/Sydney');

select
    ltctime into CurrentTimeLTC
from
    timezonecalcs('Australia/Sydney');

select
    OffSetT into OffsetTime
from
    (
        select
            datediff('minute', CurrentTimeUTC, CurrentTimeLTC) as OffsetT
    ) rawdata;

select
    currDow into CurrentDOW
from
    (
        select
            extract(
                isodow
                from
                    date(CurrentTimeLTC)
            ) - 1 as currDow
    ) rawdata;

RAISE NOTICE 'CurrentTimeUTC is : %',
CurrentTimeUTC;

RAISE NOTICE 'CurrentTimeLTC is : %',
CurrentTimeLTC;

RAISE NOTICE 'CurrDOW is : %',
CurrentDOW;

RAISE NOTICE 'OffsetTime is : %',
OffsetTime;

StartDate = CASE
    AggType
    WHEN 'M' THEN CAST(
        CurrentTimeLTC + ((AggOffSet * -1) * interval '1 month') AS DATE
    )
    WHEN 'W' THEN CAST(
        CurrentTimeLTC + ((CurrentDOW * -1) * interval '1 day') + ((AggOffSet * -1) * interval '1 week') AS DATE
    )
    WHEN 'D' THEN CAST(
        CurrentTimeLTC + ((AggOffSet * -1) * interval '1 day') AS DATE
    )
END;

RAISE NOTICE 'Month= %',
EXTRACT(
    MONTH
    FROM
        StartDate :: timestamp
);

RAISE NOTICE 'StartDate First Pass  is : %',
StartDate;

StartDate = CASE
    AggType
    WHEN 'M' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-01 00:00:00.000'
    )
    WHEN 'W' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-',
        CAST(
            EXTRACT(
                DAY
                FROM
                    StartDate :: timestamp
            ) AS CHAR(2)
        ),
        ' 00:00:00.000'
    )
    WHEN 'D' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-',
        CAST(
            EXTRACT(
                DAY
                FROM
                    StartDate :: timestamp
            ) AS CHAR(2)
        ),
        ' 00:00:00.000'
    )
END;

RAISE NOTICE 'StartDate Second Pass is : %',
StartDate;

EndDate = CASE
    AggType
    WHEN 'M' THEN StartDate + (1 * interval '1 month') - (1 * interval '1 minute')
    WHEN 'W' THEN StartDate + (1 * interval '1 week') - (1 * interval '1 minute')
    WHEN 'D' THEN StartDate + (1 * interval '1 day') - (1 * interval '1 minute')
END;

RAISE NOTICE 'End Date First Pass is : %',
EndDate;

TableName = CASE
    AggType
    WHEN 'M' THEN 'userInteractionDataMonthly'
    WHEN 'W' THEN 'userInteractionDataWeekly'
    WHEN 'D' THEN 'userInteractionDataDaily'
END;

RAISE NOTICE 'TableName is : %',
TableName;

DelsqlCommand = CONCAT(
    'DELETE FROM ',
    TableName,
    ' Where StartDate = ''',
    StartDate,
    ''''
);

IF DelsqlCommand <> '' THEN EXECUTE DelsqlCommand;

END IF;

RAISE NOTICE 'Delete Completed:';

InssqlCommand = CONCAT(
    'INSERT INTO ',
    TableName,
    ' ',
    'SELECT CONCAT (userid ,''|'' , direction , ''|'' , 
		coalesce(queueid, ''NOQUEUE'') ,''|'' , mediatype , ''|'' , coalesce(wrapupcode, ''NOWRAP'') , ''|'' ,''',
    StartDate,
    ''') as keyid
        ,userid   					
		,direction   					
        ,queueid   					
        ,mediatype   					
        ,wrapupcode   					
        ,''',
    StartDate,
    ''' as startDate
        ,SUM(talertcount) as talertcount   					
        ,SUM(talerttimesum) as talerttimesum   					
        ,MAX(talerttimemax) as talerttimemax   					
        ,MIN(talerttimemin) as talerttimemin   					
        ,SUM(tansweredcount) as tansweredcount   					
        ,SUM(tansweredtimesum) as tansweredtimesum   					
        ,MAX(tansweredtimemax) as tansweredtimemax   					
        ,MIN(tansweredtimemin) as tansweredtimemin   					
        ,SUM(ttalkcount) as ttalkcount   					
        ,SUM(ttalktimesum) as ttalktimesum   					
        ,MAX(ttalktimemax) as ttalktimemax   					
        ,MIN(ttalktimemin) as ttalktimemin   					
        ,SUM(ttalkcompletecount) as ttalkcompletecount   					
        ,SUM(ttalkcompletetimesum) as ttalkcompletetimesum   					
        ,MAX(ttalkcompletetimemax) as ttalkcompletetimemax   					
        ,MIN(ttalkcompletetimemin) as ttalkcompletetimemin   					
        ,SUM(tnotrespondingcount) as tnotrespondingcount   					
        ,SUM(tnotrespondingtimesum) as tnotrespondingtimesum   					
        ,MAX(tnotrespondingtimemax) as tnotrespondingtimemax   					
        ,MIN(tnotrespondingtimemin) as tnotrespondingtimemin   					
        ,SUM(theldcount) as theldcount   					
        ,SUM(theldtimesum) as theldtimesum   					
        ,MAX(theldtimemax) as theldtimemax   					
        ,MIN(theldtimemin) as theldtimemin   					
        ,SUM(theldcompletecount) as theldcompletecount   					
        ,SUM(theldcompletetimesum) as theldcompletetimesum   					
        ,MAX(theldcompletetimemax) as theldcompletetimemax   					
        ,MIN(theldcompletetimemin) as theldcompletetimemin   					
        ,SUM(thandlecount) as thandlecount   					
        ,SUM(thandletimesum) as thandletimesum   					
        ,MAX(thandletimemax) as thandletimemax   					
        ,MIN(thandletimemin) as thandletimemin   					
        ,SUM(tacwcount) as tacwcount   					
        ,SUM(tacwtimesum) as tacwtimesum   					
        ,MAX(tacwtimemax) as tacwtimemax   					
        ,MIN(tacwtimemin) as tacwtimemin   					
        ,SUM(nconsult) as nconsult   					
        ,SUM(nconsulttransferred) as nconsulttransferred   					
        ,SUM(noutbound) as noutbound   					
        ,SUM(nerror) as nerror   					
        ,SUM(ntransferred) as ntransferred   					
        ,SUM(nblindtransferred) as nblindtransferred   					
        ,SUM(nconnected) as nconnected   					
        ,SUM(tdialingcount) as tdialingcount   					
        ,SUM(tdialingtimesum) as tdialingtimesum   					
        ,MAX(tdialingtimemax) as tdialingtimemax   					
        ,MIN(tdialingtimemin) as tdialingtimemin   					
        ,SUM(tcontactingcount) as tcontactingcount   					
        ,SUM(tcontactingtimesum) as tcontactingtimesum   					
        ,MAX(tcontactingtimemax) as tcontactingtimemax   					
        ,MIN(tcontactingtimemin) as tcontactingtimemin   					
        ,SUM(tvoicemailcount) as tvoicemailcount   				
        ,SUM(tvoicemailtimesum) as tvoicemailtimesum   					
        ,MAX(tvoicemailtimemax) as tvoicemailtimemax   					
        ,MIN(tvoicemailtimemin) as tvoicemailtimemin 
        ,SUM(tuserresponsetimecount) as tuserresponsetimecount  				
        ,SUM(tuserresponsetimetimesum) as tuserresponsetimetimesum  					
        ,MAX(tuserresponsetimetimemax) as tuserresponsetimetimemax  					
        ,MIN(tuserresponsetimetimemin) as tuserresponsetimetimemin
        ,SUM(tagentresponsetimecount) as tagentresponsetimecount  				
        ,SUM(tagentresponsetimetimesum) as tagentresponsetimetimesum					
        ,MAX(tagentresponsetimetimemax) as tagentresponsetimetimemax 					
        ,MIN(tagentresponsetimetimemin) as tagentresponsetimetimemin
        ,SUM(av1count) as av1count         
        ,SUM(av1timesum) as av1timesum         
        ,MAX(av1timemax) as av1timemax         
        ,MIN(av1timemin) as av1timemin   	  
        ,SUM(av2count) as av2count         
        ,SUM(av2timesum) as av2timesum         
        ,MAX(av2timemax) as av2timemax         
        ,MIN(av2timemin) as av2timemin   	  
        ,SUM(av3count) as av3count         
        ,SUM(av3timesum) as av3timesum         
        ,MAX(av3timemax) as av3timemax         
        ,MIN(av3timemin) as av3timemin   	  
        ,SUM(av4count) as av4count         
        ,SUM(av4timesum) as av4timesum         
        ,MAX(av4timemax) as av4timemax         
        ,MIN(av4timemin) as av4timemin   	  
        ,SUM(av5count) as av5count         
        ,SUM(av5timesum) as av5timesum         
        ,MAX(av5timemax) as av5timemax         
        ,MIN(av5timemin) as av5timemin   	  
        ,SUM(av6count) as av6count         
        ,SUM(av6timesum) as av6timesum         
        ,MAX(av6timemax) as av6timemax         
        ,MIN(av6timemin) as av6timemin   	  
        ,SUM(av7count) as av7count         
        ,SUM(av7timesum) as av7timesum         
        ,MAX(av7timemax) as av7timemax         
        ,MIN(av7timemin) as av7timemin   	  
        ,SUM(av8count) as av8count         
        ,SUM(av8timesum) as av8timesum         
        ,MAX(av8timemax) as av8timemax         
        ,MIN(av8timemin) as av8timemin   	  
        ,SUM(av9count) as av9count         
        ,SUM(av9timesum) as av9timesum         
        ,MAX(av9timemax) as av9timemax         
        ,MIN(av9timemin) as av9timemin   	  
        ,SUM(av10count) as av10count         
        ,SUM(av10timesum) as av10timesum         
        ,MAX(av10timemax) as av10timemax         
        ,MIN(av10timemin) as av10timemin   	  
        ,timezone(''UTC'', now()) as updated  
        FROM userInteractionData   					
        WHERE startdateltc between ''',
    StartDate,
    ''' and ''',
    EndDate,
    '''',
    ' GROUP BY  CONCAT(userid , ''|'' , direction , ''|'' , coalesce(queueid, ''NOQUEUE'') , ''|'' , mediatype , ''|'' , 
		coalesce(wrapupcode, ''NOWRAP'') , ''|'' , ''',
    StartDate,
    ''') 
	    ,userid   						
        ,direction   						
        ,queueid   						
        ,mediatype   						
        ,wrapupcode;'
);

IF InssqlCommand <> '' THEN EXECUTE InssqlCommand;

END IF;

RAISE NOTICE 'Insert Completed:';

end;

$BODY$;