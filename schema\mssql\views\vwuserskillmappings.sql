IF OBJECT_ID('dbo.vwuserskillmappings', 'V') IS NOT NULL
    DROP VIEW dbo.vwuserskillmappings;
GO

CREATE VIEW dbo.vwuserskillmappings
AS
SELECT
    usm.keyid,
    usm.userid,
    ud.name AS username,
    ud.managerid,
    ud.managername,
    ud.divisionid,
    ud.divisionname,       -- Division name from vwuserdetail
    usm.skillid,
    sd.name AS skillname,
    usm.proficiency,
    usm.state AS mappingstate,
    usm.updated AS mappingupdated,
    sd.state AS skillstate,
    sd.updated AS skillupdated
FROM
    dbo.userskillmappings usm
    LEFT JOIN dbo.skilldetails sd ON usm.skillid = sd.id
    LEFT JOIN dbo.vwuserdetail ud ON usm.userid = ud.id;
GO