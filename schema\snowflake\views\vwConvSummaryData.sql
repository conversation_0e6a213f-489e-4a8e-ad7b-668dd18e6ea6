DROP VIEW IF EXISTS vwconvsummarydata CASCADE;

CREATE
OR REPLACE VIEW vwconvsummarydata AS
SELECT
    cs.conversationid,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate AS convstartdateusrtz,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.conversationenddate AS convenddateusrtz,
    cs.originaldirection,
    cs.firstmediatype,
    cs.lastmediatype,
    cs.ani,
    cs.dnis,
    cs.firstagentid,
    ud1.name AS firstagentname,
    ud1.department AS firstagentdepartment,
    ud3.name AS firstagentmanager,
    cs.lastagentid,
    ud2.name AS lastagentname,
    ud2.department AS lastagentdepartment,
    ud4.name AS lastagentmanager,
    cs.firstqueueid,
    qd1.name AS firstqueuename,
    cs.lastqueueid,
    qd2.name AS lastqueuename,
    cs.firstwrapupcode,
    wd1.name AS firstwrapname,
    cs.lastwrapupcode,
    wd2.name AS lastwrapname,
    cs.ttalkcomplete,
    cs.ttalkcomplete :: numeric / 86400.00 AS ttalkcompleteday,
    1 as "callans",
    case
        when cs.ttalkcomplete between 0
        and 10 then 1
        else 0
    end as "000-010",
    case
        when cs.ttalkcomplete between 10.01
        and 20 then 1
        else 0
    end as "010-020",
    case
        when cs.ttalkcomplete between 20.01
        and 30 then 1
        else 0
    end as "020-030",
    case
        when cs.ttalkcomplete between 30.01
        and 60 then 1
        else 0
    end as "030-060",
    case
        when cs.ttalkcomplete between 60.01
        and 120 then 1
        else 0
    end as "060-120",
    case
        when cs.ttalkcomplete between 120.01
        and 360 then 1
        else 0
    end as "120-360",
    case
        when cs.ttalkcomplete > 360.01 then 1
        else 0
    end as "360plus",
    case
        when wd1.name is null then 'System Default'
        else wd2.name
    end as wrapname,
    case
        when (
            (
                cs.originaldirection = 'inbound'
                and cs.lastdisconnect = 'peer'
            )
            or (
                cs.originaldirection = 'outbound'
                and cs.lastdisconnect = 'peer'
            )
        ) then 'Cust Disc'
        else 'Agt Disc'
    end as disccause,
    cs.divisionid,
    cs.tqueuetime,
    cs.tqueuetime :: numeric / 86400.00 AS tqueuetimeday,
    cs.tacw,
    cs.tacw / 86400.00 AS tacwday,
    cs.tansweredcount,
    cs.tanswered,
    cs.tanswered / 86400.00 AS tansweredday,
    cs.tabandonedcount,
    cs.tabandonedcount :: integer / 86400.00 AS tabandonedcountday,
    cs.tresponsecount,
    cs.tresponse,
    cs.tresponse / 86400.00 AS tresponseday,
    cs.thandlecount,
    cs.thandle,
    cs.thandle / 86400.00 AS thandleday,
    cs.theldcompletecount,
    cs.theldcomplete,
    cs.theldcomplete / 86400.00 AS theldcompleteday,
    cs.nconsulttransferred,
    cs.nblindtransferred,
    cs.lastdisconnect,
    cs.lastpurpose,
    cs.lastsegmenttime,
    cs.lastsegmenttime :: numeric / 86400.00 AS lastsegmenttimeday,
    cs.updated
FROM
    convsummarydata cs
    LEFT JOIN userdetails ud1 ON ud1.id :: text = cs.firstagentid :: text
    LEFT JOIN userdetails ud2 ON ud2.id :: text = cs.lastagentid :: text
    LEFT JOIN userdetails ud3 ON ud3.id :: text = ud1.manager :: text
    LEFT JOIN userdetails ud4 ON ud4.id :: text = ud2.manager :: text
    LEFT JOIN queuedetails qd1 ON qd1.id :: text = cs.firstqueueid :: text
    LEFT JOIN queuedetails qd2 ON qd2.id :: text = cs.lastqueueid :: text
    LEFT JOIN wrapupdetails wd1 ON wd1.id :: text = cs.firstwrapupcode :: text
    LEFT JOIN wrapupdetails wd2 ON wd2.id :: text = cs.lastwrapupcode :: text;