﻿using System;
using System.Data;
using System.Net;
using ManUnits = GenesysCloudDefManagementUnit;
using Newtonsoft.Json;
using StandardUtils;
using System.Web;
using System.IO.Compression;
using System.Security.Cryptography;

namespace GenesysCloudUtils
{

    public class ShrinkageData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        public DateTime ShrinkageLastUpdate { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            Console.WriteLine("Obtaining Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }

        public DataTable GetShrinkageData(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable ShrinkageDataTable = DBUtil.CreateInMemTable("shrinkagedata");

            string RequestBody = "{ \"startDate\": \"" + StartDate + "\", \"endDate\": \"" + EndDate + "\",\"granularity\": \"Daily\"}";

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits", GCApiKey);

            ManUnits.ManagementUnits MUnits = new ManUnits.ManagementUnits();

            MUnits = JsonConvert.DeserializeObject<ManUnits.ManagementUnits>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            foreach (ManUnits.Entity JSON in MUnits.entities)
            {
                Console.Write("MU");


                Console.WriteLine("Shrinkage JSON:{0}", RequestBody);
                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + JSON.id + "/shrinkage/jobs", GCApiKey, RequestBody);
                Console.WriteLine("Json Returned for Shrinkage Job. Continuing to next cursor.");

                ShrinkageJob shrinkageJob = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                    new JsonSerializerSettings
                                                    {
                                                        NullValueHandling = NullValueHandling.Ignore
                                                    });

                ShrinkageJob shrinkageJobDetail = null;

                if (shrinkageJob != null && shrinkageJob.operationId != null)
                {
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/shrinkage/jobs/" + shrinkageJob.operationId, GCApiKey);

                    shrinkageJobDetail = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });
                }
                else
                {
                    Console.WriteLine($"No Job ID returned. Skipping Entity");
                    continue;
                }

                bool DataAvailable = shrinkageJobDetail.state == "Complete";
                int Counter = 0;
                while (!DataAvailable)
                {
                    Counter++;
                    Console.WriteLine($"Job id : {shrinkageJob.operationId} retuned the statecode : {shrinkageJobDetail.state}, COUNTER : {Counter} ");
                    Thread.Sleep(3000);
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/shrinkage/jobs/" + shrinkageJob.operationId, GCApiKey);

                    shrinkageJobDetail = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });


                    DataAvailable = shrinkageJobDetail.state == "Complete";
                    if (Counter > 6) break;
                }

                if (DataAvailable)
                {


                    WebClient Client = new WebClient();
                    Client.Headers[HttpRequestHeader.AcceptEncoding] = "gzip";
                    foreach (string url in shrinkageJobDetail.downloadUrls)
                    {

                        GZipStream ResponseStream = new GZipStream(Client.OpenRead(url), CompressionMode.Decompress);
                        StreamReader WebReader = new StreamReader(ResponseStream);
                        String JsonStringData = WebReader.ReadToEnd();
                        ResponseStream.Dispose();
                        WebReader.Dispose();

                        ShrinkageDataResult shrinkageDataResult = JsonConvert.DeserializeObject<ShrinkageDataResult>(JsonStringData,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });

                        foreach (var entity in shrinkageDataResult.entities)
                        {
                            foreach (var activity in entity.shrinkageForActivityCategories)
                            {

                                foreach (var code in activity.shrinkageForActivityCodes)
                                {
                                    DataRow dr = ShrinkageDataTable.NewRow();
                                    dr["startdate"] = entity.startDate;
                                    dr["enddate"] = entity.endDate;
                                    dr["activitycategory"] = activity.activityCategory;

                                    var keyId = UCAUtils.GetUInt64Hash(SHA256.Create(), JSON.id + "|" + code.activityCodeId + "|" + entity.startDate.ToString("yyyyMMddhhmmsstt") + "|" + string.Join("|", entity.businessUnitIds));
                                    dr["keyid"] = keyId;
                                    dr["scheduledShrinkageSeconds"] = code.shrinkageForActivityCode.scheduledShrinkageSeconds;
                                    dr["scheduledShrinkagePercent"] = code.shrinkageForActivityCode.scheduledShrinkagePercent;
                                    dr["actualShrinkageSeconds"] = code.shrinkageForActivityCode.actualShrinkageSeconds;
                                    dr["actualShrinkagePercent"] = code.shrinkageForActivityCode.actualShrinkagePercent;
                                    dr["paidShrinkageSeconds"] = code.shrinkageForActivityCode.paidShrinkageSeconds;
                                    dr["unpaidShrinkageSeconds"] = code.shrinkageForActivityCode.unpaidShrinkageSeconds;
                                    dr["plannedShrinkageSeconds"] = code.shrinkageForActivityCode.plannedShrinkageSeconds;
                                    dr["unplannedShrinkageSeconds"] = code.shrinkageForActivityCode.unplannedShrinkageSeconds;
                                    dr["businessUnitIds"] = string.Join("|", entity.businessUnitIds);
                                    dr["updated"] = DateTime.UtcNow;
                                    ShrinkageDataTable.Rows.Add(dr);
                                }
                            }
                        }

                    }

                }
            }
            return ShrinkageDataTable;
        }
    }

    #region ShrinkageEntities

    public class ShrinkageJob
    {
        public string operationId { get; set; }
        public string[] downloadUrls { get; set; }
        public string state { get; set; }
    }

    public class ShrinkageJobDetail
    {
        public string operationId { get; set; }
        public string[] downloadUrls { get; set; }
        public Result result { get; set; }
    }

    public class ShrinkageDataResult
    {
        public ShrinkageDataEntity[] entities { get; set; }
    }

    public class ShrinkageDataEntity
    {
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public int totalScheduledDurationSeconds { get; set; }
        public int totalLoggedInDurationSeconds { get; set; }
        public Aggregatedshrinkage aggregatedShrinkage { get; set; }
        public Shrinkageforactivitycategory[] shrinkageForActivityCategories { get; set; }
        public string[] businessUnitIds { get; set; }
    }

    public class Aggregatedshrinkage
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    public class Shrinkageforactivitycategory
    {
        public string activityCategory { get; set; }
        public Shrinkageforactivitycode[] shrinkageForActivityCodes { get; set; }
        public ShrinkageforactivitycategoryEntity shrinkageForActivityCategory { get; set; }
    }

    public class ShrinkageforactivitycategoryEntity
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    public class Shrinkageforactivitycode
    {
        public string activityCodeId { get; set; }
        public ShrinkageforactivitycodeEntity shrinkageForActivityCode { get; set; }
    }

    public class ShrinkageforactivitycodeEntity
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    #endregion

}
