﻿using System;
using System.Data;
using System.Net;
using System.Threading;
using System.IO;
using ManUnits = GenesysCloudDefManagementUnit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;
using System.Web;
using System.IO.Compression;
using System.Security.Cryptography;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{

    public class ShrinkageData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private readonly GCUtils GCUtilities = new GCUtils();
        private readonly JsonUtils JsonActions;
        private readonly ILogger? _logger;
        public string TimeZoneConfig { get; set; }
        public DateTime ShrinkageLastUpdate { get; set; }
        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;

        public ShrinkageData(ILogger? logger = null)
        {
            _logger = logger;
            JsonActions = new JsonUtils(logger);
        }

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            Console.WriteLine("Obtaining Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }

        public DataTable GetShrinkageData(String StartDate, String EndDate)
        {
            _logger?.LogInformation("Starting shrinkage data retrieval for date range: {StartDate} to {EndDate}", StartDate, EndDate);

            DataTable ShrinkageDataTable = DBUtil.CreateInMemTable("shrinkagedata");

            string RequestBody = "{ \"startDate\": \"" + StartDate + "\", \"endDate\": \"" + EndDate + "\",\"granularity\": \"Daily\"}";

            _logger?.LogDebug("Retrieving management units from Genesys Cloud");
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits", GCApiKey);

            var MUnits = JsonConvert.DeserializeObject<ManUnits.ManagementUnits>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            if (MUnits?.entities == null)
            {
                _logger?.LogWarning("No management units found or failed to deserialize management units response");
                return ShrinkageDataTable;
            }

            _logger?.LogInformation("Found {Count} management units to process", MUnits.entities.Length);

            // Performance metrics tracking
            int totalManagementUnits = MUnits.entities.Length;
            int successfulUnits = 0;
            int failedUnits = 0;
            int permissionDeniedUnits = 0;
            int totalRecordsProcessed = 0;

            foreach (ManUnits.Entity JSON in MUnits.entities)
            {
                _logger?.LogDebug("Processing management unit: {ManagementUnitId}", JSON.id);

                _logger?.LogDebug("Creating shrinkage job for management unit: {ManagementUnitId} with request body: {RequestBody}", JSON.id, RequestBody);
                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + JSON.id + "/shrinkage/jobs", GCApiKey, RequestBody);

                ShrinkageJob shrinkageJob = null;
                try
                {
                    shrinkageJob = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                    new JsonSerializerSettings
                                                    {
                                                        NullValueHandling = NullValueHandling.Ignore
                                                    });
                }
                catch (JsonReaderException ex)
                {
                    _logger?.LogError(ex, "Failed to parse JSON response for management unit {ManagementUnitId}. Response content: {JsonString}", JSON.id, JsonString);
                    failedUnits++;
                    continue; // Skip to the next entity
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing response for management unit {ManagementUnitId}. Response content: {JsonString}", JSON.id, JsonString);
                    failedUnits++;
                    continue; // Skip to the next entity
                }

                // Check if this is a permission error
                if (JsonString.Contains("\"statusCode\": \"Forbidden\"") || JsonString.Contains("missing.any.division.permissions"))
                {
                    permissionDeniedUnits++;
                    _logger?.LogWarning("Permission denied for management unit {ManagementUnitId}", JSON.id);
                    continue;
                }

                if (shrinkageJob == null || string.IsNullOrEmpty(shrinkageJob.operationId))
                {
                    _logger?.LogWarning("No operation ID returned for management unit {ManagementUnitId}. Skipping.", JSON.id);
                    failedUnits++;
                    continue;
                }

                _logger?.LogInformation("Shrinkage job created for management unit {ManagementUnitId} with operation ID: {OperationId}", JSON.id, shrinkageJob.operationId);

                // Get initial job status
                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/shrinkage/jobs/" + shrinkageJob.operationId, GCApiKey);

                ShrinkageJob shrinkageJobDetail = null;
                try
                {
                    shrinkageJobDetail = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                            new JsonSerializerSettings
                                                            {
                                                                NullValueHandling = NullValueHandling.Ignore
                                                            });
                }
                catch (JsonReaderException ex)
                {
                    _logger?.LogError(ex, "Failed to parse job details JSON response for operation {OperationId}. Response content: {JsonString}", shrinkageJob.operationId, JsonString);
                    failedUnits++;
                    continue; // Skip to the next entity
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing job details response for operation {OperationId}. Response content: {JsonString}", shrinkageJob.operationId, JsonString);
                    failedUnits++;
                    continue; // Skip to the next entity
                }

                if (shrinkageJobDetail == null)
                {
                    _logger?.LogWarning("Failed to get job details for operation {OperationId}. Skipping.", shrinkageJob.operationId);
                    failedUnits++;
                    continue;
                }

                // Poll for job completion
                bool DataAvailable = shrinkageJobDetail.state == "Complete";
                int Counter = 0;
                const int maxPollingAttempts = 6;

                while (!DataAvailable && Counter < maxPollingAttempts)
                {
                    Counter++;
                    _logger?.LogDebug("Polling job status for operation {OperationId}: {State} (attempt {Counter}/{MaxAttempts})",
                        shrinkageJob.operationId, shrinkageJobDetail.state, Counter, maxPollingAttempts);

                    Thread.Sleep(3000);
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/shrinkage/jobs/" + shrinkageJob.operationId, GCApiKey);

                    try
                    {
                        shrinkageJobDetail = JsonConvert.DeserializeObject<ShrinkageJob>(JsonString,
                                                                new JsonSerializerSettings
                                                                {
                                                                    NullValueHandling = NullValueHandling.Ignore
                                                                });

                        if (shrinkageJobDetail == null)
                        {
                            _logger?.LogWarning("Failed to deserialize job status response for operation {OperationId}", shrinkageJob.operationId);
                            break;
                        }

                        DataAvailable = shrinkageJobDetail.state == "Complete";
                    }
                    catch (JsonReaderException ex)
                    {
                        _logger?.LogError(ex, "Failed to parse job polling JSON response for operation {OperationId}. Response content: {JsonString}", shrinkageJob.operationId, JsonString);
                        break; // Exit the polling loop on error
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing job polling response for operation {OperationId}. Response content: {JsonString}", shrinkageJob.operationId, JsonString);
                        break; // Exit the polling loop on error
                    }
                }

                if (Counter >= maxPollingAttempts)
                {
                    _logger?.LogWarning("Job polling timeout for operation {OperationId} after {MaxAttempts} attempts", shrinkageJob.operationId, maxPollingAttempts);
                    failedUnits++;
                }

                if (DataAvailable)
                {
                    _logger?.LogInformation("Job completed for operation {OperationId}. Processing {UrlCount} download URLs",
                        shrinkageJob.operationId, shrinkageJobDetail.downloadUrls?.Length ?? 0);

                    using (var Client = new WebClient())
                    {
                        Client.Headers[HttpRequestHeader.AcceptEncoding] = "gzip";

                        foreach (string url in shrinkageJobDetail.downloadUrls ?? new string[0])
                        {
                            try
                            {
                                _logger?.LogDebug("Downloading shrinkage data from URL: {Url}", url);

                                using (var ResponseStream = new GZipStream(Client.OpenRead(url), CompressionMode.Decompress))
                                using (var WebReader = new StreamReader(ResponseStream))
                                {
                                    String JsonStringData = WebReader.ReadToEnd();

                                    var shrinkageDataResult = JsonConvert.DeserializeObject<ShrinkageDataResult>(JsonStringData,
                                                                        new JsonSerializerSettings
                                                                        {
                                                                            NullValueHandling = NullValueHandling.Ignore
                                                                        });

                                    if (shrinkageDataResult?.entities != null)
                                    {
                                        int recordCount = 0;
                                        foreach (var entity in shrinkageDataResult.entities)
                                        {
                                            foreach (var activity in entity.shrinkageForActivityCategories ?? new Shrinkageforactivitycategory[0])
                                            {
                                                foreach (var code in activity.shrinkageForActivityCodes ?? new Shrinkageforactivitycode[0])
                                                {
                                                    DataRow dr = ShrinkageDataTable.NewRow();
                                                    dr["startdate"] = entity.startDate;
                                                    dr["enddate"] = entity.endDate;
                                                    dr["activitycategory"] = activity.activityCategory;

                                                    var keyId = UCAUtils.GetUInt64Hash(SHA256.Create(), JSON.id + "|" + code.activityCodeId + "|" + entity.startDate.ToString("yyyyMMddhhmmsstt") + "|" + string.Join("|", entity.businessUnitIds));
                                                    dr["keyid"] = keyId;
                                                    dr["scheduledShrinkageSeconds"] = code.shrinkageForActivityCode.scheduledShrinkageSeconds;
                                                    dr["scheduledShrinkagePercent"] = code.shrinkageForActivityCode.scheduledShrinkagePercent;
                                                    dr["actualShrinkageSeconds"] = code.shrinkageForActivityCode.actualShrinkageSeconds;
                                                    dr["actualShrinkagePercent"] = code.shrinkageForActivityCode.actualShrinkagePercent;
                                                    dr["paidShrinkageSeconds"] = code.shrinkageForActivityCode.paidShrinkageSeconds;
                                                    dr["unpaidShrinkageSeconds"] = code.shrinkageForActivityCode.unpaidShrinkageSeconds;
                                                    dr["plannedShrinkageSeconds"] = code.shrinkageForActivityCode.plannedShrinkageSeconds;
                                                    dr["unplannedShrinkageSeconds"] = code.shrinkageForActivityCode.unplannedShrinkageSeconds;
                                                    dr["businessUnitIds"] = string.Join("|", entity.businessUnitIds);
                                                    dr["updated"] = DateTime.UtcNow;
                                                    ShrinkageDataTable.Rows.Add(dr);
                                                    recordCount++;
                                                }
                                            }
                                        }
                                        _logger?.LogDebug("Processed {RecordCount} shrinkage records from URL", recordCount);
                                        totalRecordsProcessed += recordCount;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "Error downloading or processing shrinkage data from URL: {Url}", url);
                                failedUnits++;
                            }
                        }
                    }

                    if (DataAvailable)
                    {
                        successfulUnits++;
                        _logger?.LogInformation("Successfully processed management unit {ManagementUnitId}", JSON.id);
                    }
                }
                else
                {
                    _logger?.LogWarning("Job did not complete successfully for operation {OperationId}", shrinkageJob.operationId);
                    failedUnits++;
                }
            }

            // Log performance metrics summary
            _logger?.LogInformation("Shrinkage data retrieval completed. " +
                "Total management units: {TotalUnits}, " +
                "Successful: {SuccessfulUnits}, " +
                "Failed: {FailedUnits}, " +
                "Permission denied: {PermissionDeniedUnits}, " +
                "Total records processed: {TotalRecords}",
                totalManagementUnits, successfulUnits, failedUnits, permissionDeniedUnits, totalRecordsProcessed);

            return ShrinkageDataTable;
        }
    }

    #region ShrinkageEntities

    public class ShrinkageJob
    {
        public string operationId { get; set; }
        public string[] downloadUrls { get; set; }
        public string state { get; set; }
    }

    public class ShrinkageJobDetail
    {
        public string operationId { get; set; }
        public string[] downloadUrls { get; set; }
        public Result result { get; set; }
    }

    public class ShrinkageDataResult
    {
        public ShrinkageDataEntity[] entities { get; set; }
    }

    public class ShrinkageDataEntity
    {
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public int totalScheduledDurationSeconds { get; set; }
        public int totalLoggedInDurationSeconds { get; set; }
        public Aggregatedshrinkage aggregatedShrinkage { get; set; }
        public Shrinkageforactivitycategory[] shrinkageForActivityCategories { get; set; }
        public string[] businessUnitIds { get; set; }
    }

    public class Aggregatedshrinkage
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    public class Shrinkageforactivitycategory
    {
        public string activityCategory { get; set; }
        public Shrinkageforactivitycode[] shrinkageForActivityCodes { get; set; }
        public ShrinkageforactivitycategoryEntity shrinkageForActivityCategory { get; set; }
    }

    public class ShrinkageforactivitycategoryEntity
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    public class Shrinkageforactivitycode
    {
        public string activityCodeId { get; set; }
        public ShrinkageforactivitycodeEntity shrinkageForActivityCode { get; set; }
    }

    public class ShrinkageforactivitycodeEntity
    {
        public int scheduledShrinkageSeconds { get; set; }
        public float scheduledShrinkagePercent { get; set; }
        public int actualShrinkageSeconds { get; set; }
        public float actualShrinkagePercent { get; set; }
        public int paidShrinkageSeconds { get; set; }
        public int unpaidShrinkageSeconds { get; set; }
        public int plannedShrinkageSeconds { get; set; }
        public int unplannedShrinkageSeconds { get; set; }
    }

    #endregion

}
