using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using GenesysCloudUtils; // Ensure this namespace is correctly referenced

namespace GenesysAdapter
{
    class GCUpdatePermissions
    {
        private readonly ILogger? _logger;
        private readonly GCGetData _gcData;
        private readonly bool _enableForcedUpdatePermissions;

        // Constants
        private const string PERMISSIONS_FILE_NAME = "GCPermissions.json";
        private const string SCHEMA_FOLDER_NAME = "schema";

        public GCUpdatePermissions(ILogger? logger, bool enableForcedUpdatePermissions = false)
        {
            _logger = logger;
            _gcData = new GCGetData(_logger);
            _enableForcedUpdatePermissions = enableForcedUpdatePermissions;
        }

        public Boolean UpdatePermissions(string clientId)
        {
            Boolean Successful = false;
            string SyncType = "permissions";

            // Pass the _enableForcedUpdatePermissions parameter to Initialize
            _gcData.Initialize(SyncType, _enableForcedUpdatePermissions);

            try
            {
                // Try multiple possible locations for the permissions file
                string jsonFilePath = FindPermissionsJsonFile();

                if (string.IsNullOrEmpty(jsonFilePath))
                {
                    _logger?.LogError("{FileName} file could not be found in any of the expected locations", PERMISSIONS_FILE_NAME);
                    throw new FileNotFoundException("JSON file not found", PERMISSIONS_FILE_NAME);
                }

                _logger?.LogInformation("Starting to update permissions from file {FilePath}", jsonFilePath);
                Successful = _gcData.UpdatePermissionsFromFileGC(clientId, jsonFilePath);
                if (Successful)
                {
                    _logger?.LogInformation("Successfully updated permissions from file {FilePath}", jsonFilePath);
                }
                else
                {
                    _logger?.LogError("Failed to update permissions from file {FilePath}", jsonFilePath);
                    throw new InvalidOperationException("Failed to update permissions from file");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to update role permissions.");
                throw new InvalidOperationException("Failed to update role permissions", ex);
            }

            return Successful;
        }

        /// <summary>
        /// Attempts to find the permissions file in various possible locations
        /// </summary>
        /// <returns>The full path to the permissions file if found, or null if not found</returns>
        private string FindPermissionsJsonFile()
        {
            List<string> possibleLocations = new List<string>();

            // Location 1: Direct in schema folder (for Docker container)
            possibleLocations.Add(Path.Combine(AppContext.BaseDirectory, SCHEMA_FOLDER_NAME, PERMISSIONS_FILE_NAME));

            // Location 2: Up one level in schema folder
            possibleLocations.Add(Path.Combine(AppContext.BaseDirectory, "..", SCHEMA_FOLDER_NAME, PERMISSIONS_FILE_NAME));

            // Location 3: Up to the repository root (for development environment)
            possibleLocations.Add(Path.Combine(AppContext.BaseDirectory, "..", "..", "..", "..", SCHEMA_FOLDER_NAME, PERMISSIONS_FILE_NAME));

            // Location 4: Relative to current directory
            possibleLocations.Add(Path.Combine(Directory.GetCurrentDirectory(), SCHEMA_FOLDER_NAME, PERMISSIONS_FILE_NAME));

            // Location 5: In /opt/GenesysAdapter/schema (for Linux/Docker)
            possibleLocations.Add(Path.Combine("/opt", "GenesysAdapter", SCHEMA_FOLDER_NAME, PERMISSIONS_FILE_NAME));

            foreach (string location in possibleLocations)
            {
                string fullPath = Path.GetFullPath(location);
                _logger?.LogDebug("Checking for permissions file at: {FilePath}", fullPath);

                if (File.Exists(fullPath))
                {
                    _logger?.LogInformation("Found permissions file at: {FilePath}", fullPath);
                    return fullPath;
                }
            }

            // Log all the locations we checked
            _logger?.LogError("{FileName} not found in any of these locations: {Locations}",
                PERMISSIONS_FILE_NAME, string.Join(", ", possibleLocations.Select(p => Path.GetFullPath(p))));

            return null;
        }
    }
}
