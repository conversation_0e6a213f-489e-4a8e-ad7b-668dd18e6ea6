CREATE OR ALTER VIEW vwuserpresencedatadaily AS
SELECT
    userpresencedatadaily.id,
    userpresencedatadaily.userid,
    userdetail.name AS agentname,
    userdetail.managerid,
    userdetail.managername,
    userdetail.divisionid,
    dd.name AS division_name,
    userpresencedatadaily.startdate,
    DATEADD(HOUR, CASE 
                     WHEN DATENAME(TZOFFSET, SYSDATETIMEOFFSET()) = 'UTC' THEN 10  -- Adjust for Australia/Sydney timezone
                     ELSE 0
                 END, userpresencedatadaily.startdate) AS startdateusrtz,
    userpresencedatadaily.timetype,
    userpresencedatadaily.systempresenceid,
    CASE
        WHEN userpresencedatadaily.systempresenceid LIKE '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$' THEN pd.systempresence
        ELSE userpresencedatadaily.systempresenceid
    END AS systempresencename,
    userpresencedatadaily.presenceid,
    CASE
        WHEN userpresencedatadaily.timetype = 'Presence' THEN userpresencedatadaily.presencetime
        ELSE 0
    END AS presencetime,
    userpresencedatadaily.presencetime / 86400.00 AS presencetimeday,
    userpresencedatadaily.routingid,
    CASE
        WHEN userpresencedatadaily.timetype = 'Routing' THEN userpresencedatadaily.presencetime
        ELSE 0
    END AS routingtime,
    userpresencedatadaily.routingtime / 86400.00 AS routingtimeday,
    userpresencedatadaily.updated
FROM
    userpresencedatadaily
LEFT JOIN vwuserdetail userdetail ON CAST(userdetail.id AS VARCHAR) = CAST(userpresencedatadaily.userid AS VARCHAR)
LEFT JOIN divisiondetails dd ON CAST(dd.id AS VARCHAR) = CAST(userdetail.divisionid AS VARCHAR)
LEFT JOIN presencedetails pd ON pd.id = userpresencedatadaily.systempresenceid;
