IF dbo.csg_table_exists('learningmodules') = 0
CREATE TABLE [learningmodules] (
    [id] VARCHAR(50) NOT NULL,
    [name] VARCHAR(100),
    [description] VARCHAR(100),
    [version] VARCHAR(255),
    [externalId] VARCHAR(50),
    [source] VARCHAR(50),
    [enforceContentOrder] BIT,
    [isArchived] BIT,
    [isPublished] BIT,
    [completionTimeInDays] INT,
    [type] VARCHAR(50),
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [state] VARCHAR(50), -- Current state of the learning module (e.g., active, deleted, archived)
    [updated] DATETIME,
    CONSTRAINT [learningmodule_pkey] PRIMARY KEY ([id])
);

-- Add missing state column if it doesn't exist (for existing installations)
IF dbo.csg_column_exists('learningmodules', 'state') = 0
    ALTER TABLE learningmodules ADD state VARCHAR(50) NULL;
