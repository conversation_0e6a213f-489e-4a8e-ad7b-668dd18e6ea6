DROP VIEW IF EXISTS vwqueuedetails CASCADE;
CREATE OR REPLACE VIEW vwqueuedetails AS
SELECT
    qd.id
    ,qd.name
    ,qd.description
	,substring(description, 'queuegroup=([^;]+)(;|$)') AS queuegroup
	,initcap(substring(description, 'region=([^;]+)(;|$)')) AS region
	,substring(description, 'grouptype=([^;]+)(;|$)') AS grouptype
	,initcap(substring(description, 'country=([^;]+)(;|$)')) AS country
	,initcap(substring(description, 'language=([^;]+)(;|$)')) AS language
    ,initcap(substring(description, 'accountowner=([^;]+)(;|$)')) AS accountowner
    ,qd.divisionid
    ,divisiondetails.name as divisionname
    ,divisiondetails.name as vwuserpresencedata
    ,qd.enabletranscription
    ,qd.isactive
    ,qd.callslatargetperc
    ,qd.callslatargetduration
    ,qd.callbackslatargetperc
    ,qd.callbackslatargetduration
    ,qd.chatslatargetperc
    ,qd.chatslatargetduration
    ,qd.emailslatargetperc
    ,qd.emailslatargetduration
    ,qd.messageslatargetperc
    ,qd.messageslatargetduration
FROM
    queueDetails as qd
    INNER join divisiondetails as divisiondetails on qd.divisionid = divisiondetails.id;