CREATE OR REPLACE VIEW vwuserskillmappings
  COMMENT = 'View combining user-skill mappings with detailed user information (including manager and division) and corresponding skill details'
AS
SELECT
    usm.keyid,
    usm.userid,
    ud.name AS username,
    ud.managerid,
    ud.managername,
    ud.divisionid,
    ud.divisionname,
    usm.skillid,
    sd.name AS skillname,
    usm.proficiency,
    usm.state AS mappingstate,
    usm.updated AS mappingupdated,
    sd.state AS skillstate,
    sd.updated AS skillupdated
FROM
    userskillmappings usm
    LEFT JOIN skilldetails sd ON usm.skillid = sd.id
    LEFT JOIN vwuserdetail ud ON usm.userid = ud.id;