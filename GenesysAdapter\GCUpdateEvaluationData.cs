﻿using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateEvaluationData
    {
        private readonly ILogger? _logger;
        private readonly BackfillUtils BackfillUtils = new BackfillUtils();

        public GCUpdateEvaluationData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCEvaluationData(bool Backfill = false)
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string CurrentJob = "evaldata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;

            GCGetData GCData = new GCGetData(_logger);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // DateToSyncFrom is already UTC from GetSyncLastUpdate, use ToUtcSafe to avoid double conversion
            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUtcSafe();

            DataSet EvaluationDataset = GCData.EvaluationDetailedData();
            Console.WriteLine("Writing Eval Data Rows {0}", EvaluationDataset.Tables[0].Rows.Count);

            Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[0], "evaldata");
            Console.WriteLine("Writing Eval Question Group Data Rows {0}", EvaluationDataset.Tables[1].Rows.Count);
            Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[1], "evalquestiongroupdata");
            Console.WriteLine("Writing Eval Question Data Rows {0}", EvaluationDataset.Tables[2].Rows.Count);
            Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[2], "evalquestiondata");

            Console.WriteLine("Update Date is : {0}", GCData.DetailEvaluationLastUpdate);

            OldUpdateTime = GCData.DetailEvaluationLastUpdate;
            if (Successful == true)
            {
                DateTime updateTime = OldUpdateTime;

                Successful = GCData.UpdateLastSuccessDate(updateTime, "evaldata");
                Successful = GCData.UpdateLastSuccessDate(updateTime, "evalquestiongroupdata");
                Successful = GCData.UpdateLastSuccessDate(updateTime, "evalquestiondata");

                Console.WriteLine("New Update Time {0} ", updateTime);

                Console.WriteLine("Updated The Latest Update Date Successful {0}", Successful);
            }
            else
                Console.WriteLine("Will Not update the last update date - failure in processing");

            Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);
            Successful = true;

            return Successful;
        }

        public Boolean UpdateGCEvaluationCatchUp()
        {

            Boolean Successful = false;
            string SyncType = "evaluations";
            GCGetData GCData = new GCGetData(_logger);
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            Console.WriteLine("Initialize GC Data");
            GCData.Initialize(SyncType);

            String EvalCatchUpSQL = String.Empty;

            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    EvalCatchUpSQL = "delete from evalData where status !='FINISHED' and assigneddate < DATEADD(day,-360,CURRENT_TIMESTAMP)";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    EvalCatchUpSQL = "delete from evalData where status !='FINISHED' and assigneddate < DATEADD(day,-360,GETUTCDATE())";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    EvalCatchUpSQL = "delete from evalData where status !='FINISHED' and assigneddate < DATE_ADD(utc_timestamp(), INTERVAL -360 Day);";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                    EvalCatchUpSQL = "delete from evalData where status !='FINISHED' and assigneddate < (timezone('utc', now()) - INTERVAL '360 Day');";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
            Boolean SuccessfulDelete = DBAdapter.ExecuteSQLQuery(EvalCatchUpSQL);

            Console.WriteLine("Removing Stale Evaluations - {0} ", SuccessfulDelete);
            Console.WriteLine("Checking Outstanding Evaluations");

            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    EvalCatchUpSQL = "SELECT conversationid, evaluationid AS id FROM evalData WHERE (status != 'FINISHED' AND assigneddate > DATEADD(day, -360, CURRENT_TIMESTAMP)) OR (status = 'FINISHED' AND agenthasread = 'false'); ";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    EvalCatchUpSQL = "SELECT conversationid,evaluationid as id from evalData where (status !='FINISHED' and assigneddate > DATEADD(day,-360,GETUTCDATE())) or (status ='FINISHED' and agenthasread = 'false') ";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    EvalCatchUpSQL = "SELECT conversationid,evaluationid as id from evalData where (status !='FINISHED' and assigneddate > DATE_ADD(utc_timestamp(), INTERVAL -360 Day)) or (status ='FINISHED' and agenthasread = 'false') ";
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                    EvalCatchUpSQL = "SELECT conversationid,evaluationid as id from evalData where (status !='FINISHED' and assigneddate > (timezone('utc', now()) - INTERVAL '360 Day')) or (status ='FINISHED' and agenthasread = B'0') ";
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            DataTable PendingEvals = DBAdapter.GetSQLTableData(EvalCatchUpSQL, "Evaluations");

            if (PendingEvals != null && PendingEvals.Rows.Count > 0)
            {
                Console.WriteLine("Checking {0} Outstanding Evaluations", PendingEvals.Rows.Count);
                DataSet EvaluationDataset = GCData.EvalDataCatchUp(PendingEvals);

                Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[0], "evalData");
                Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[1], "evalQuestionGroupData");
                Successful = DBAdapter.WriteSQLData(EvaluationDataset.Tables[2], "evalQuestionData");

                Successful = true;
            }
            else
                Console.WriteLine("No Evaluations need Checking");

            return Successful;
        }

    }
}
