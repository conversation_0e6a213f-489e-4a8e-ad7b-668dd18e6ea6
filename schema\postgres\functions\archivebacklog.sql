CREATE OR REPLACE PROCEDURE archivebacklog()
 LANGUAGE plpgsql
AS $procedure$
declare 
	counter integer;

begin
 	for counter in 0..1 loop
	  raise notice 'Archive Counter: %', counter;
	  call archivequeueinteraction(counter,'M');
	  raise notice 'Archive Queue Int Mnthly : % Done', counter;
	  call archiveuserinteraction(counter,'M');
	  raise notice 'Archive User Int  Mnthly : % Done', counter;
	  call archiveuserpresence(counter,'M');
	  raise notice 'Archive User Pres Mnthly : % Done', counter;
   end loop;
   
   for counter in 0..4 loop
	raise notice 'Archive Counter: %', counter;
	call archivequeueinteraction(counter,'W');
	raise notice 'Archive Queue Int Weekly : % Done', counter;
	call archiveuserinteraction(counter,'W');
	raise notice 'Archive User Int  Weekly : % Done', counter;
	call archiveuserpresence(counter,'W');
	raise notice 'Archive User Pres Weekly : % Done', counter;
   end loop;
   
    for counter in 0..30 loop
	raise notice 'Archive Counter: %', counter;
	call archivequeueinteraction(counter,'D');
	raise notice 'Archive Queue Int Daily : % Done', counter;
	call archiveuserinteraction(counter,'D');
	raise notice 'Archive User Int  Daily : % Done', counter;
	call archiveuserpresence(counter,'D');
	raise notice 'Archive User Pres Daily : % Done', counter;
   end loop;
   
RAISE NOTICE 'Archive Backlog Completed:';

end;
$procedure$
;