IF dbo.csg_table_exists('adherencedayData') = 0
CREATE TABLE [adherencedayData](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [dayStartOffsetSecs] [int],
    [adherencePerc] [decimal](20, 2),
    [conformPerc] [decimal](20, 2),
    [impact] [nvarchar](50),
    [adherenceScheduleSecs] [int],
    [conformanceScheduleSecs] [int],
    [conformanceActualSecs] [int],
    [exceptionCount] [int],
    [exceptionDurationSecs] [int],
    [impactSeconds] [int],
    [scheduleLengthSecs] [int],
    [actualLengthSecs] [int],
    [updated] [datetime],
    CONSTRAINT [PK_adherencedayData] PRIMARY KEY ([keyid])
);

DECLARE @KeyidPrefix NVARCHAR(255) = 'v1_'
DECLARE @deleted_count INT
IF EXISTS (
    SELECT 1 
    FROM adherencedaydata
    WHERE LEFT(keyid, LEN(@KeyidPrefix)) <> @KeyidPrefix
)
BEGIN
    DELETE FROM adherencedaydata
    WHERE LEFT(keyid, LEN(@KeyidPrefix)) <> @KeyidPrefix;
    SET @deleted_count = @@ROWCOUNT;
    IF @deleted_count > 0
    BEGIN
        UPDATE tableDefinitions
        SET datekeyfield = DATEADD(MONTH, -12, GETDATE())
        WHERE tablename = 'adherencedayData';
        
        PRINT 'Table definition "datekeyfield" column set back to older date.';
    END
    PRINT 'Records deleted: ' + CAST(@deleted_count AS NVARCHAR(10)) + ' and tabledefinitions updated.';
END
ELSE
BEGIN
    PRINT 'No records found with old keyid in use.';
END;