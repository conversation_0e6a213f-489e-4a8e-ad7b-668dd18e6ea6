CREATE TABLE IF NOT EXISTS knowledgebasedocument (
    id VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    visible boolean,
    state VARCHAR(50),
    dateCreated timestamp without time zone,
    dateModified timestamp without time zone,
    dateImported timestamp without time zone,
    datePublished timestamp without time zone,
    lastPublishedVersionNumber integer,
    documentVersion VARCHAR(50),
    externalId integer,
    categoryId VARCHAR(50),
    knowledgeBaseId varchar(50),
    readonly boolean,
    updated timestamp without time zone,
    CONSTRAINT knowledgebasedocument_pkey PRIMARY KEY (id)
);
