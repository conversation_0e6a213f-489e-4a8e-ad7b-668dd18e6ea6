﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateAdminData
    {
        private readonly ILogger? _logger;
        private const int DIFFING_THRESHOLD = 100000;
        private readonly BackfillUtils BackfillUtils = new BackfillUtils();

        public GCUpdateAdminData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateActQMembership()
        {
            Boolean Successful = false;

            DateTime Start = DateTime.Now;

            string SyncType = "activeqmembersdata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);


            DataTable SubscriptionData = GCData.ActiveQMembersData();

            if (SubscriptionData != null && SubscriptionData.Rows.Count > 0)
            {
                _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for active queue members.", SubscriptionData.Rows.Count);

                // Create the where condition for the diffing query
                DateTime startDate = GCData.DateToSyncFrom.Subtract(TimeSpan.FromDays(7)); // Look back 7 days
                DateTime endDate = DateTime.UtcNow;

                string whereCondition = $@"
                    startdate >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                    AND startdate <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                ";

                DataTable DiffedSubscriptionData;

                // Only perform diffing if there are more than 100k rows to process
                // For smaller datasets, the overhead of diffing is not worth the performance cost
                if (SubscriptionData.Rows.Count > DIFFING_THRESHOLD)
                {
                    _logger?.LogInformation("ActiveQMembersData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                        SubscriptionData.Rows.Count, DIFFING_THRESHOLD);

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    DiffedSubscriptionData = diffHelper.DiffDataWithCondition(
                        "activeqmembersdata",
                        whereCondition,
                        SubscriptionData,
                        DBAdapter,
                        "keyid"
                    );
                }
                else
                {
                    _logger?.LogInformation("ActiveQMembersData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                        SubscriptionData.Rows.Count, DIFFING_THRESHOLD);
                    DiffedSubscriptionData = SubscriptionData;
                }

                // If there are no rows to write, we're done
                if (DiffedSubscriptionData.Rows.Count == 0)
                {
                    _logger?.LogInformation("No new or updated active queue members data to write. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "activeqmembersdata");
                    return Successful;
                }

                // Write the diffed data
                Successful = DBAdapter.WriteSQLDataBulk(DiffedSubscriptionData, "activeqmembersdata");

                if (Successful)
                {
                    _logger?.LogInformation("Active queue members data saved. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "activeqmembersdata");
                }
                else
                {
                    _logger?.LogWarning("Failed to write active queue members data; last sync date not updated.");
                }
            }
            else
            {
                _logger?.LogInformation("No active queue members data retrieved from Genesys Cloud.");
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }
        public Boolean UpdateGCSubsOverviewData()
        {

            Boolean Successful = false;

            string SyncType = "suboverviewdata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable SubscriptionData = GCData.SubOverviewData();

            if (SubscriptionData != null)
            {
                if (SubscriptionData.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLDataBulk(SubscriptionData, SyncType);
                    if (Successful == true)
                        Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }
        public Boolean UpdateHoursBlockData(int MonthOffset, bool Backfill = false)
        {
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();
            Boolean Successful = false;
            string CurrentJob = "hoursblockdata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;

            GCGetData GCHoursData = new GCGetData(_logger);
            GCHoursData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCHoursData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            DataTable HoursBlockData = GCHoursData.HoursBlockData(MonthOffset);

            if (HoursBlockData != null)
            {
                if (HoursBlockData.Rows.Count > 0)
                {
                    // Always write to the regular table, not the backfill table
                    Successful = DBAdapter.WriteSQLDataBulk(HoursBlockData, CurrentJob);

                    if (Successful)
                        // When in backfill mode, update the backfill table's watermark
                        Successful = GCHoursData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }
            else
            {
                Console.WriteLine("Hours BLock Data No Data found - Error");
                Successful = false;
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }
        public Boolean UpdateGCSubHoursDetailData()
        {
            Boolean Successful = false;

            string SyncType = "subuserusagedata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable SubHoursDetail = null;

            try
            {
                SubHoursDetail = GCData.SubHoursData();
                _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for subscription hours usage.", SubHoursDetail?.Rows.Count ?? 0);
            }
            catch (UnauthorizedAccessException ex)
            {
                // This is a permanent permission issue, log it and return false
                _logger?.LogError(ex, "Permanent permission issue when retrieving subscription hours usage data: {Message}", ex.Message);
                return false;
            }
            catch (Exception ex)
            {
                // For other exceptions, log and continue with empty dataset
                _logger?.LogWarning(ex, "Error retrieving subscription hours usage data: {Message}. Will continue with empty dataset.", ex.Message);
                SubHoursDetail = new DataTable();
            }

            // Always update the last sync date even if we got no data
            if (SubHoursDetail == null)
            {
                _logger?.LogWarning("No subscription hours usage data retrieved. Updating last sync date to {Date}.", DateTime.UtcNow);
                Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                return Successful;
            }

            // Process data if we have any rows
            if (SubHoursDetail.Rows.Count > 0)
            {
                // Create the where condition for the diffing query
                DateTime startDate = GCData.DateToSyncFrom.Subtract(TimeSpan.FromDays(30)); // Look back 30 days
                DateTime endDate = DateTime.UtcNow;

                string whereCondition = $@"
                    date >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                    AND date <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                ";

                DataTable DiffedSubHoursDetail;

                // Only perform diffing if there are more than 100k rows to process
                // For smaller datasets, the overhead of diffing is not worth the performance cost
                if (SubHoursDetail.Rows.Count > DIFFING_THRESHOLD)
                {
                    _logger?.LogInformation("SubHoursDetailData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                        SubHoursDetail.Rows.Count, DIFFING_THRESHOLD);

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    DiffedSubHoursDetail = diffHelper.DiffDataWithCondition(
                        SyncType,
                        whereCondition,
                        SubHoursDetail,
                        DBAdapter,
                        "keyid"
                    );
                }
                else
                {
                    _logger?.LogInformation("SubHoursDetailData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                        SubHoursDetail.Rows.Count, DIFFING_THRESHOLD);
                    DiffedSubHoursDetail = SubHoursDetail;
                }

                // If there are no rows to write, we're done
                if (DiffedSubHoursDetail.Rows.Count == 0)
                {
                    _logger?.LogInformation("No new or updated subscription hours usage data to write. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                    return Successful;
                }

                // Write the diffed data
                Successful = DBAdapter.WriteSQLDataBulk(DiffedSubHoursDetail, SyncType);

                if (Successful)
                {
                    _logger?.LogInformation("Subscription hours usage data saved. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
                else
                {
                    _logger?.LogWarning("Failed to write subscription hours usage data; last sync date not updated.");
                }
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }

        public Boolean UpdateGCOauthUsageData(int monthOffset)
        {
            Boolean Successful = false;

            string SyncType = "oauthusagedata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable oauthUsageData = GCData.OauthUsageData(monthOffset);
            _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for OAuth usage.", oauthUsageData?.Rows.Count ?? 0);

            if (oauthUsageData != null && oauthUsageData.Rows.Count > 0)
            {
                // Create the where condition for the diffing query
                DateTime startDate = GCData.DateToSyncFrom.Subtract(TimeSpan.FromDays(30)); // Look back 30 days
                DateTime endDate = DateTime.UtcNow;

                string whereCondition = $@"
                    rowdate >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                    AND rowdate <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                ";

                DataTable diffedOauthUsageData;

                // Only perform diffing if there are more than 100k rows to process
                // For smaller datasets, the overhead of diffing is not worth the performance cost
                if (oauthUsageData.Rows.Count > DIFFING_THRESHOLD)
                {
                    _logger?.LogInformation("OauthUsageData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                        oauthUsageData.Rows.Count, DIFFING_THRESHOLD);

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    diffedOauthUsageData = diffHelper.DiffDataWithCondition(
                        SyncType,
                        whereCondition,
                        oauthUsageData,
                        DBAdapter,
                        "keyid"
                    );
                }
                else
                {
                    _logger?.LogInformation("OauthUsageData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                        oauthUsageData.Rows.Count, DIFFING_THRESHOLD);
                    diffedOauthUsageData = oauthUsageData;
                }

                // If there are no rows to write, we're done
                if (diffedOauthUsageData.Rows.Count == 0)
                {
                    _logger?.LogInformation("No new or updated OAuth usage data to write. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                    return Successful;
                }

                // Write the diffed data
                Successful = DBAdapter.WriteSQLDataBulk(diffedOauthUsageData, SyncType);

                if (Successful)
                {
                    _logger?.LogInformation("OAuth usage data saved. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
                else
                {
                    _logger?.LogWarning("Failed to write OAuth usage data; last sync date not updated.");
                }
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }

        public Boolean UpdateGCSystemCallUsageData(int dayOffset)
        {
            Boolean Successful = false;
            string SyncType = "systemcallusage";
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable systemCallUsageData = GCData.SystemCallUsageData(dayOffset);
            _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for system call usage.", systemCallUsageData?.Rows.Count ?? 0);

            if (systemCallUsageData != null && systemCallUsageData.Rows.Count > 0)
            {
                // Create the where condition for the diffing query
                DateTime startDate = GCData.DateToSyncFrom.Subtract(TimeSpan.FromDays(7)); // Look back 7 days
                DateTime endDate = DateTime.UtcNow;

                string whereCondition = $@"
                    rowdate >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                    AND rowdate <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                ";

                DataTable diffedSystemCallUsageData;

                // Only perform diffing if there are more than 100k rows to process
                // For smaller datasets, the overhead of diffing is not worth the performance cost
                if (systemCallUsageData.Rows.Count > DIFFING_THRESHOLD)
                {
                    _logger?.LogInformation("SystemCallUsageData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                        systemCallUsageData.Rows.Count, DIFFING_THRESHOLD);

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    diffedSystemCallUsageData = diffHelper.DiffDataWithCondition(
                        SyncType + "Data",
                        whereCondition,
                        systemCallUsageData,
                        DBAdapter,
                        "keyid"
                    );
                }
                else
                {
                    _logger?.LogInformation("SystemCallUsageData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                        systemCallUsageData.Rows.Count, DIFFING_THRESHOLD);
                    diffedSystemCallUsageData = systemCallUsageData;
                }

                // If there are no rows to write, we're done
                if (diffedSystemCallUsageData.Rows.Count == 0)
                {
                    _logger?.LogInformation("No new or updated system call usage data to write. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                    return Successful;
                }

                // Write the diffed data
                Successful = DBAdapter.WriteSQLDataBulk(diffedSystemCallUsageData, SyncType + "Data");

                if (Successful)
                {
                    _logger?.LogInformation("System call usage data saved. Updating last sync date to {Date}.", DateTime.UtcNow);
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
                else
                {
                    _logger?.LogWarning("Failed to write system call usage data; last sync date not updated.");
                }
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }

        public async Task<Boolean> UpdateQueueUserAuditData(bool Backfill = false)
        {
            Boolean Successful = false;
            string CurrentJob = "queueauditdata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // DateToSyncFrom is already UTC from GetSyncLastUpdate, use ToUtcSafe to avoid double conversion
            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUtcSafe();

            DataTable SystemCallUsageData = await GCData.QueueAuditData();

            Console.WriteLine("System Call Usage : Rows Found {0} \nWrite To DB", SystemCallUsageData.Rows.Count);

            if (SystemCallUsageData != null)
            {
                if (SystemCallUsageData.Rows.Count > 0)
                {
                    Successful = true;
                    // Always write to the regular table, not the backfill table
                    Successful = DBAdapter.WriteSQLDataBulk(SystemCallUsageData, CurrentJob);
                }


                Console.WriteLine("Last Date:{0}", GCData.QueueUserAuditLastUpdate);
                // When in backfill mode, update the backfill table's watermark
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueUserAuditLastUpdate, SyncType);
            }
            else
            {
                Console.WriteLine("Queue User Audit: Not Updating Dates - Some error must have happened");
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }

        public Boolean UpdateWFMAuditData(bool Backfill = false)
        {
            Boolean Successful = false;
            string CurrentJob = "wfmauditdata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            DataTable WFMAuditData = GCData.WFMAuditData();

            Console.WriteLine("WFM Audit Data : Rows Found {0} \nWrite To DB", WFMAuditData.Rows.Count);

            if (WFMAuditData != null)
            {
                if (WFMAuditData.Rows.Count > 0)
                {
                    Successful = true;
                    // Always write to the regular table, not the backfill table
                    Successful = DBAdapter.WriteSQLDataBulk(WFMAuditData, CurrentJob);
                }

                Console.WriteLine("WFM Audit:Last Date:{0}", GCData.QueueUserAuditLastUpdate);
                // When in backfill mode, update the backfill table's watermark
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueUserAuditLastUpdate, SyncType);
            }
            else
            {
                Console.WriteLine("WFM Audit: Not Updating Dates - Some error must have happened");
            }

            // Explicitly close the database connection at the end of the job
            DBAdapter.CloseConnectionToDatabase();

            return Successful;
        }
    }
}
