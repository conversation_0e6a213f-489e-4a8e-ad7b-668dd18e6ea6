CREATE TABLE IF NOT EXISTS wrapupdetails (
    id varchar(50) NOT NULL,
    name varchar(255),
    updated timestamp without time zone,
    CONSTRAINT wrapupdetails_pkey PRIMARY KEY (id)
);

MER<PERSON> INTO wrapupdetails USING VALUES (
    '00000000-0000-0000-0000-0000000000000', 
    'ININ-WRAP-UP-TIMEOUT'
) AS source (id, name)
ON wrapupdetails.id = source.id
WHEN NOT MATCHED THEN
    INSERT (id, name)
    VALUES (source.id, source.name);
