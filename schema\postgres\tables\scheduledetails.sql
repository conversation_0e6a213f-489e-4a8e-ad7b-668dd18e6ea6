CREATE TABLE IF NOT EXISTS scheduledetails (
    keyid varchar(100) NOT NULL,
    businessunitid varchar(50),
    scheduleid varchar(50),
    weekdate date,
    weekcount bigint,
    published bit(1),
    shorttermforecastid varchar(50),
    modifiedby varchar(50),
    genresultsfailed bit(1),
    genresultsrunid varchar(50),
    updated timestamp without time zone,
    description varchar(400),
    CONSTRAINT scheduledetails_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

ALTER TABLE scheduledetails
ALTER COLUMN weekcount TYPE bigint;

COMMENT ON COLUMN scheduleDetails.businessunitid IS 'Schedule Business Unit GUID'; 
COMMENT ON COLUMN scheduleDetails.description IS 'Schedule Description'; 
COMMENT ON COLUMN scheduleDetails.genresultsfailed IS 'Schedule Results Failed ?'; 
COMMENT ON COLUMN scheduleDetails.genresultsrunid IS 'Schedule Results GUID'; 
COMMENT ON COLUMN scheduleDetails.modifiedby IS 'Schedule Modified By GUID'; 
COMMENT ON COLUMN scheduleDetails.published IS 'Schedule Published ?'; 
COMMENT ON COLUMN scheduleDetails.shorttermforecastid IS 'Schedule Short Term Forecast GUID'; 
COMMENT ON COLUMN scheduleDetails.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN scheduleDetails.weekcount IS 'Schedule Week Count'; 
COMMENT ON COLUMN scheduleDetails.weekdate IS 'Schedule Week'; 
COMMENT ON TABLE scheduleDetails is 'Schedule Details Lookup Data'; 