IF dbo.csg_table_exists('kq_analysis') = 0
CREATE TABLE [kq_analysis] (
    [kq_analysisid] [uniqueidentifier] NOT NULL,
    [conversationid] [uniqueidentifier],
    [communicationid] [uniqueidentifier],
    [callsummary] [nvarchar](max),
    [callresolution] [nvarchar](max),
    [callbackrequired] [bit],
    [selfserviceattempted] [bit],
    [selfserviceproblems] [bit],
    [satisfactionsentiment] [decimal](5,2),
    CONSTRAINT [PK_kq_analysis] PRIMARY KEY ([kq_analysisid])
);

-- Create indexes on foreign key columns for performance
IF dbo.csg_index_exists('IX_kq_analysis_communicationid', 'kq_analysis') = 0
CREATE INDEX [IX_kq_analysis_communicationid] ON [kq_analysis]([communicationid]);

IF dbo.csg_index_exists('IX_kq_analysis_conversationid', 'kq_analysis') = 0
CREATE INDEX [IX_kq_analysis_conversationid] ON [kq_analysis]([conversationid]);
