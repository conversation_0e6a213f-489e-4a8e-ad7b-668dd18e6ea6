CREATE
OR ALTER VIEW [vwConvSummaryData] AS
SELECT
    cs.conversationid,
    cs.conversationstartdate,
    cs.conversationenddate,
    cs.conversationstartdateltc,
    cs.conversationstartdate AS convstartdateusrtz,
    cs.conversationenddate AS convenddateusrtz,
    cs.conversationenddateltc,
    cs.originaldirection,
    cs.firstmediatype,
    cs.lastmediatype,
    cs.ani,
    cs.dnis,
    cs.firstagentid,
    ud1.name AS firstagentName,
    ud3.name AS firstagentManager,
    ud1.department AS firstagentdepartment,
    ud2.department AS lastagentdepartment,
    cs.lastagentid,
    ud2.name AS lastagentName,
    ud4.name AS lastagentManager,
    cs.firstqueueid,
    qd1.name AS firstqueueName,
    cs.lastqueueid,
    qd2.name AS lastqueueName,
    cs.firstwrapupcode,
    wd1.name AS firstwrapName,
    cs.lastwrapupcode,
    wd2.name AS lastwrapName,
    1 as callans,
    CASE
        WHEN cs.ttalkcomplete BETWEEN 0 AND 10 THEN 1
        ELSE 0
    END as [000-010],
    CASE
        WHEN cs.ttalkcomplete BETWEEN 10.01 AND 20 THEN 1
        ELSE 0
    END as [010-020],
    CASE
        WHEN cs.ttalkcomplete BETWEEN 20.01 AND 30 THEN 1
        ELSE 0
    END as [020-030],
    CASE
        WHEN cs.ttalkcomplete BETWEEN 30.01 AND 60 THEN 1
        ELSE 0
    END as [030-060],
    CASE
        WHEN cs.ttalkcomplete BETWEEN 60.01 AND 120 THEN 1
        ELSE 0
    END as [060-120],
    CASE
        WHEN cs.ttalkcomplete BETWEEN 120.01 AND 360 THEN 1
        ELSE 0
    END as [120-360],
    CASE
        WHEN cs.ttalkcomplete > 360.01 THEN 1
        ELSE 0
    END as [360plus],
    CASE
        WHEN wd1.name IS NULL THEN 'System Default'
        ELSE wd2.name
    END as wrapname,
    CASE
        WHEN (cs.originaldirection = 'inbound' AND cs.lastdisconnect = 'peer')
             OR (cs.originaldirection = 'outbound' AND cs.lastdisconnect = 'peer') THEN 'Cust Disc'
        ELSE 'Agt Disc'
    END as disccause,
    cs.ttalkcomplete,
    cs.divisionid,
    cs.ttalkcomplete / 86400.00 AS ttalkcompleteDay,
    cs.tqueuetime,
    cs.tqueuetime / 86400.00 AS tqueuetimeDay,
    cs.tacw,
    cs.tacw / 86400.00 AS tacwDay,
    cs.tabandonedCount,
    CONVERT(decimal, cs.tabandonedcount) / 86400.00 AS tabandonedcountday,
    cs.tansweredCount,
    cs.tanswered,
    cs.tanswered / 86400.00 AS tansweredDay,
    cs.tresponseCount,
    cs.tresponse,
    cs.tresponse / 86400.00 AS tresponseDay,
    cs.thandleCount,
    cs.thandle,
    cs.thandle / 86400.00 AS thandleDay,
    cs.theldcompleteCount,
    cs.theldcomplete,
    cs.theldcomplete / 86400.00 AS theldcompleteDay,
    cs.nconsulttransferred,
    cs.nblindtransferred,
    lastdisconnect,
    lastpurpose,
    lastsegmenttime,
    lastsegmenttime / 86400.00 AS lastsegmenttimeDay,
    cs.updated
FROM
    convSummaryData AS cs
    LEFT OUTER JOIN userDetails AS ud1 ON ud1.id = cs.firstagentid
    LEFT OUTER JOIN userDetails AS ud2 ON ud2.id = cs.lastagentid
    LEFT OUTER JOIN userDetails AS ud3 ON ud3.id = ud1.manager
    LEFT OUTER JOIN userDetails AS ud4 ON ud4.id = ud2.manager
    LEFT OUTER JOIN queueDetails AS qd1 ON qd1.id = cs.firstqueueid
    LEFT OUTER JOIN queueDetails AS qd2 ON qd2.id = cs.lastqueueid
    LEFT OUTER JOIN wrapupDetails AS wd1 ON wd1.id = cs.firstwrapupcode
    LEFT OUTER JOIN wrapupDetails AS wd2 ON wd2.id = cs.lastwrapupcode;