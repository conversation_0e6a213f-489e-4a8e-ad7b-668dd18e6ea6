# Schema Review Checklist

This document provides a comprehensive checklist for reviewing schema files across different database types.

## MSSQL Schema Files

### Table Creation
- [ ] All table creation scripts use `IF dbo.csg_table_exists(...) = 0`
- [ ] Primary keys are defined consistently
- [ ] Data types are appropriate and consistent with other database types
- [ ] Default values are specified where appropriate

### Column Additions
- [ ] All column additions use `IF dbo.csg_column_exists(...) = 0`
- [ ] Data types match the table definition
- [ ] NULL/NOT NULL constraints are specified

### Index Creation
- [ ] All index creations use `IF dbo.csg_index_exists(...) = 0`
- [ ] Indexes are created on appropriate columns (foreign keys, frequently queried columns)
- [ ] Index names follow a consistent naming convention

### Views
- [ ] View creation uses `IF dbo.csg_view_definition_contains_string(...) = 0`
- [ ] Views have appropriate comments explaining their purpose

### General
- [ ] No direct SQL that could be replaced with functions
- [ ] Consistent use of schema prefixes (e.g., `dbo.`)
- [ ] Proper error handling in stored procedures
- [ ] Consistent use of GO statements

## PostgreSQL Schema Files

### Table Creation
- [ ] All table creation scripts use `CREATE TABLE IF NOT EXISTS` or `IF csg_table_exists(...) = 0`
- [ ] Primary keys are defined consistently
- [ ] Data types are appropriate and consistent with other database types
- [ ] Default values are specified where appropriate

### Column Additions
- [ ] All column additions use `ALTER TABLE ... ADD COLUMN IF NOT EXISTS`
- [ ] Data types match the table definition
- [ ] NULL/NOT NULL constraints are specified

### Index Creation
- [ ] Indexes are created with `CREATE INDEX IF NOT EXISTS`
- [ ] Indexes are created on appropriate columns
- [ ] Index names follow a consistent naming convention

### Partitioning
- [ ] Large tables use appropriate partitioning strategies
- [ ] Partitioning functions are used consistently

### Views
- [ ] View creation uses `CREATE OR REPLACE VIEW`
- [ ] Materialized views have refresh procedures

### General
- [ ] Timezone handling is consistent
- [ ] Proper use of schema prefixes
- [ ] Consistent use of language specifications in functions/procedures

## Snowflake Schema Files

### Table Creation
- [ ] All table creation scripts use `CREATE TABLE IF NOT EXISTS`
- [ ] Primary keys are defined consistently
- [ ] Data types are appropriate for Snowflake
- [ ] Default values are specified where appropriate

### Column Additions
- [ ] All column additions use `ALTER TABLE ... ADD COLUMN IF NOT EXISTS`
- [ ] Data types match the table definition
- [ ] NULL/NOT NULL constraints are specified

### Index Management
- [ ] No explicit index creation (Snowflake uses automatic indexing)
- [ ] Comments indicate which columns would benefit from clustering

### JavaScript Procedures
- [ ] JavaScript procedures follow a consistent pattern
- [ ] Error handling is implemented properly
- [ ] Procedures are documented with comments

### General
- [ ] Proper use of Snowflake-specific features (e.g., time travel, zero-copy cloning)
- [ ] Consistent naming conventions
- [ ] Appropriate use of Snowflake data types

## Cross-Database Consistency

### Table Definitions
- [ ] Table names are consistent across database types
- [ ] Column names are consistent across database types
- [ ] Primary keys are defined consistently

### Data Types
- [ ] Data types are compatible across database types
- [ ] Length/precision specifications are consistent where applicable

### Constraints
- [ ] Primary key constraints are consistent
- [ ] Foreign key constraints are consistent where supported
- [ ] Unique constraints are consistent

### Indexes
- [ ] Index strategies are appropriate for each database type
- [ ] Indexed columns are consistent across database types
