-- Template for cron
/*
SELECT
    cron.schedule_in_database(
        'Description',
        'Cron schedule (see: crontab.guru',
        'Command to run',
        'Database to process against'
    );
*/

SET
    search_path TO cron;

SELECT
    cron.schedule_in_database(
        'mvwconvvoiceoverviewdata',
        '20 */1 * * *',
        'call update_mvwconvvoiceoverviewdata()',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'mvwconvvoicetopicdetaildata',
        '25 */1 * * *',
        'call update_mvwconvvoicetopicdetaildata()',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'mvwconvvoicesentimentdetaildata',
        '30 */1 * * *',
        'call update_mvwconvvoicesentimentdetaildata()',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'mvwevaluationoverview',
        '40 */1 * * *',
        'SET search_path TO public; REFRESH MATERIALIZED VIEW CONCURRENTLY mvwevaluationoverview',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'mvwevaluationgroupdata',
        '35 */1 * * *',
        'call update_mvwevaluationgroupdata()',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'evaluationquestiondata',
        '45 */1 * * *',
        'SET search_path TO public; REFRESH MATERIALIZED VIEW CONCURRENTLY mvwevaluationquestiondata',
        'contactcentredb'
    );
SELECT
    cron.schedule_in_database(
        'archivequeueinteraction',
        '0 0 */1 * *',
        'call archivequeueinteraction(0,''D'')',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archiveuserinteraction',
        '15 0 */1 * *',
        'call archiveuserinteraction(0,''D'')',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archiveuserpresence',
        '30 0 */1 * *',
        'call archiveuserpresence(0,''D'');',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archivebacklog',
        '0 1 * * *',
        'call archivebacklog()',
        'contactcentredb'
    );
