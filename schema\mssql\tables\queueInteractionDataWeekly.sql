IF dbo.csg_table_exists('queueInteractionDataWeekly') = 0
CREATE TABLE [queueInteractionDataWeekly](
    [keyid] [nvarchar](255) NOT NULL,
    [direction] [nvarchar](50),
    [queueid] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [wrapupcode] [nvarchar](255),
    [startdate] [datetime],
    [talertcount] [int],
    [talerttimesum] [decimal](20, 2),
    [talerttimemax] [decimal](20, 2),
    [talerttimemin] [decimal](20, 2),
    [tansweredcount] [int],
    [tansweredtimesum] [decimal](20, 2),
    [tansweredtimemax] [decimal](20, 2),
    [tansweredtimemin] [decimal](20, 2),
    [ttalkcount] [int],
    [ttalktimesum] [decimal](20, 2),
    [ttalktimemax] [decimal](20, 2),
    [ttalktimemin] [decimal](20, 2),
    [ttalkcompletecount] [int],
    [ttalkcompletetimesum] [decimal](20, 2),
    [ttalkcompletetimemax] [decimal](20, 2),
    [ttalkcompletetimemin] [decimal](20, 2),
    [tnotrespondingcount] [int],
    [tnotrespondingtimesum] [decimal](20, 2),
    [tnotrespondingtimemax] [decimal](20, 2),
    [tnotrespondingtimemin] [decimal](20, 2),
    [theldcount] [int],
    [theldtimesum] [decimal](20, 2),
    [theldtimemax] [decimal](20, 2),
    [theldtimemin] [decimal](20, 2),
    [theldcompletecount] [int],
    [theldcompletetimesum] [decimal](20, 2),
    [theldcompletetimemax] [decimal](20, 2),
    [theldcompletetimemin] [decimal](20, 2),
    [thandlecount] [int],
    [thandletimesum] [decimal](20, 2),
    [thandletimemax] [decimal](20, 2),
    [thandletimemin] [decimal](20, 2),
    [tacwcount] [int],
    [tacwtimesum] [decimal](20, 2),
    [tacwtimemax] [decimal](20, 2),
    [tacwtimemin] [decimal](20, 2),
    [nconsult] [int],
    [nconsulttransferred] [int],
    [noutbound] [int],
    [nerror] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nconnected] [int],
    [noffered] [int],
    [noversla] [int],
    [tacdcount] [int],
    [tacdtimesum] [decimal](20, 2),
    [tacdtimemax] [decimal](20, 2),
    [tacdtimemin] [decimal](20, 2),
    [tdialingcount] [int],
    [tdialingtimesum] [decimal](20, 2),
    [tdialingtimemax] [decimal](20, 2),
    [tdialingtimemin] [decimal](20, 2),
    [tcontactingcount] [int],
    [tcontactingtimesum] [decimal](20, 2),
    [tcontactingtimemax] [decimal](20, 2),
    [tcontactingtimemin] [decimal](20, 2),
    [tvoicemailcount] [int],
    [tvoicemailtimesum] [decimal](20, 2),
    [tvoicemailtimemax] [decimal](20, 2),
    [tvoicemailtimemin] [decimal](20, 2),
    [tflowoutcount] [int],
    [tflowouttimesum] [decimal](20, 2),
    [tflowouttimemax] [decimal](20, 2),
    [tflowouttimemin] [decimal](20, 2),
    [twaitcount] [int],
    [twaittimesum] [decimal](20, 2),
    [twaittimemax] [decimal](20, 2),
    [twaittimemin] [decimal](20, 2),
    [tabandoncount] [int],
    [tabandontimesum] [decimal](20, 2),
    [tabandontimemax] [decimal](20, 2),
    [tabandontimemin] [decimal](20, 2),
    [servicelevelnumerator] [decimal](20, 2),
    [serviceleveldenominator] [decimal](20, 2),
    [av1count] [int],
    [av1timesum] [decimal](20, 2),
    [av1timemax] [decimal](20, 2),
    [av1timemin] [decimal](20, 2),
    [av2count] [int],
    [av2timesum] [decimal](20, 2),
    [av2timemax] [decimal](20, 2),
    [av2timemin] [decimal](20, 2),
    [av3count] [int],
    [av3timesum] [decimal](20, 2),
    [av3timemax] [decimal](20, 2),
    [av3timemin] [decimal](20, 2),
    [av4count] [int],
    [av4timesum] [decimal](20, 2),
    [av4timemax] [decimal](20, 2),
    [av4timemin] [decimal](20, 2),
    [av5count] [int],
    [av5timesum] [decimal](20, 2),
    [av5timemax] [decimal](20, 2),
    [av5timemin] [decimal](20, 2),
    [av6count] [int],
    [av6timesum] [decimal](20, 2),
    [av6timemax] [decimal](20, 2),
    [av6timemin] [decimal](20, 2),
    [av7count] [int],
    [av7timesum] [decimal](20, 2),
    [av7timemax] [decimal](20, 2),
    [av7timemin] [decimal](20, 2),
    [av8count] [int],
    [av8timesum] [decimal](20, 2),
    [av8timemax] [decimal](20, 2),
    [av8timemin] [decimal](20, 2),
    [av9count] [int],
    [av9timesum] [decimal](20, 2),
    [av9timemax] [decimal](20, 2),
    [av9timemin] [decimal](20, 2),
    [av10count] [int],
    [av10timesum] [decimal](20, 2),
    [av10timemax] [decimal](20, 2),
    [av10timemin] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_queueInteractionDataWeekly] PRIMARY KEY ([keyid])
);
