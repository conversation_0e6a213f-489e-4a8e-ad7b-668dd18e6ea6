IF dbo.csg_table_exists('odcampaigndetails') = 0
CREATE TABLE [odcampaigndetails](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](200),
    [datecreated] [datetime],
    [datecreatedltc] [datetime],
    [datemodified] [datetime],
    [datemodifiedltc] [datetime],
    [version] [int],
    [division] [nvarchar](50),
    [divisionname] [nvarchar](200),
    [contactlist] [nvarchar](50),
    [contactlistname] [nvarchar](200),
    [script] [nvarchar](50),
    [scriptname] [nvarchar](200),
    [queue] [nvarchar](50),
    [dialingmode] [nvarchar](50),
    [campaignstatus] [nvarchar](50),
    [abandonrate] [decimal](20, 2),
    [callanalysisresponseset] [nvarchar](50),
    [callanalysisresponsesetname] [nvarchar](200),
    [callername] [nvarchar](200),
    [calleraddress] [nvarchar](200),
    [outboundlinecount] [int],
    [skippreviewdisabled] [bit],
    [previewtimeoutseconds] [decimal](20, 2),
    [singlenumberpreview] [bit],
    [alwaysrunning] [bit],
    [noanswertimeout] [decimal](20, 2),
    [priority] [int],
    [automatictimezonemapping] [bit],
    [updated] [datetime],
    CONSTRAINT [PK_odcampaigndetails] PRIMARY KEY ([id])
);
IF dbo.csg_column_exists('odcampaigndetails', 'automatictimezonemapping') = 0
    ALTER TABLE dbo.odcampaigndetails ADD automatictimezonemapping [bit];
ELSE
    ALTER TABLE odcampaigndetails ALTER COLUMN automatictimezonemapping [bit];