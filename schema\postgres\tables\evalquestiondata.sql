CREATE TABLE IF NOT EXISTS evalquestiondata (
    keyid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    evaluationformid varchar(50) NOT NULL,
    questiongroupid varchar(50),
    questionid varchar(50),
    answerid varchar(50),
    score numeric(20, 2),
    markedna bit(1),
    failedkillquestions bit(1),
    comments text,
    updated timestamp without time zone,
    CONSTRAINT evalquestiondata_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evalquestionsevaluationid ON evalquestiondata USING btree (
    evaluationid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evalquestionsformid ON evalquestiondata USING btree (
    evaluationformid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evalquestionsgroupid ON evalquestiondata USING btree (
    questiongroupid ASC NULLS LAST
) TABLESPACE pg_default;