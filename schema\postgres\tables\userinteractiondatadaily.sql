CREATE TABLE IF NOT EXISTS userinteractiondatadaily (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tuserresponsetimecount integer,
    tuserresponsetimetimesum numeric(20, 2),
    tuserresponsetimetimemax numeric(20, 2),
    tuserresponsetimetimemin numeric(20, 2),
    tagentresponsetimecount integer,
    tagentresponsetimetimesum numeric(20, 2),
    tagentresponsetimetimemax numeric(20, 2),
    tagentresponsetimetimemin numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userinteractiondatadaily_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS "UserInteractionDailyDate" ON userinteractiondatadaily USING btree (startdate ASC NULLS LAST) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS "UserInteractionDailyMedia" ON userinteractiondatadaily USING btree (
    mediatype ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS "UserInteractionDailyUser" ON userinteractiondatadaily USING btree (
    userid ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS "UserInteractionDailyWrapUp" ON userinteractiondatadaily USING btree (
    wrapupcode ASC NULLS LAST
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS "UserinteractionDailyQueue" ON userinteractiondatadaily USING btree (
    queueid ASC NULLS LAST
) TABLESPACE pg_default;

COMMENT ON COLUMN userInteractionDataDaily.av10count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av10timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av10timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av10timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av1count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av1timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av1timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av1timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av2count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av2timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av2timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av2timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av3count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av3timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av3timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av3timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av4count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av4timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av4timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av4timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av5count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av5timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av5timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av5timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av6count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av6timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av6timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av6timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av7count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av7timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av7timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av7timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av8count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av8timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av8timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av8timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av9count IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av9timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av9timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.av9timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.direction IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.keyid IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.mediatype IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.nblindtransferred IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.nconnected IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.nconsult IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.nconsulttransferred IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.nerror IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.noutbound IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ntransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.queueid IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.startdate IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tacwcount IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tacwtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tacwtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tacwtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tagentresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tagentresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tagentresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tagentresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.talertcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.talerttimemax IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.talerttimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.talerttimesum IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tansweredcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tansweredtimemax IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tansweredtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tansweredtimesum IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tcontactingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tcontactingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tcontactingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tcontactingtimesum IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tdialingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tdialingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tdialingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tdialingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.thandlecount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.thandletimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.thandletimemin IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.thandletimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.theldtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tnotrespondingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tnotrespondingtimemax IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tnotrespondingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tnotrespondingtimesum IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.ttalkcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalkcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalkcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalkcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalkcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalktimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.ttalktimemin IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.ttalktimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tuserresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tuserresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tuserresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tuserresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tvoicemailcount IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tvoicemailtimemax IS ' ';
COMMENT ON COLUMN userInteractionDataDaily.tvoicemailtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.tvoicemailtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.updated IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.userid IS ' '; 
COMMENT ON COLUMN userInteractionDataDaily.wrapupcode IS ' '; 
COMMENT ON TABLE userInteractionDataDaily IS 'User Interaction Data Daily Data - LTC - See UserInteractionData for Field Descriptions'; 