CREATE OR REPLACE VIEW z_vwCallAbandonedSummary AS
SELECT
    det.conversationid,
    det.conversationstartdate,
    det.conversationstartdate AT TIME ZONE 'Australia/Sydney' as conversationstartdateLTC,
    det.conversationenddate,
    det.conversationenddate AT TIME ZONE 'Australia/Sydney' as conversationenddateLTC,
    det.segmentstartdate,
    det.segmentstartdate AT TIME ZONE 'Australia/Sydney' as segmentstartdateLTC,
    det.segmentenddate,
    det.segmentenddate AT TIME ZONE 'Australia/Sydney' as segmentenddateLTC,
    det.convtosegmentendtime as TotalCallTime,
    det.segmenttime as QueueTime,
    det.ani,
    det.dnis,
    det.queueid,
    que.name as queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    detailedInteractionData det
    LEFT OUTER JOIN queueDetails que ON det.queueid = que.id
WHERE
    det.segmenttype IN ('delay', 'Interact', 'alert', 'ivr')
    AND det.purpose IN ('acd', 'ivr')
    AND det.disconnectiontype = 'peer'
    AND det.conversationenddate = det.segmentenddate;

COMMENT ON COLUMN z_vwCallAbandonedSummary.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN z_vwCallAbandonedSummary.conversationstartdate IS 'Conversation Start Date (UTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.conversationstartdateLTC IS 'Conversation Start Date (LTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.conversationenddate IS 'Conversation End Date (UTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.conversationenddateLTC IS 'Conversation End Date (LTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.segmentstartdate IS 'Segment Start Date (UTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.segmentstartdateLTC IS 'Segment Start Date (LTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.segmentenddate IS 'Segment End Date (UTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.segmentenddateLTC IS 'Segment End Date (LTC)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.TotalCallTime IS 'Total Call Time (Seconds)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.QueueTime IS 'Queue Time (Seconds)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.ani IS 'ANI (Automatic Number Identification)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.dnis IS 'DNIS (Dialed Number Identification Service)';
COMMENT ON COLUMN z_vwCallAbandonedSummary.queueid IS 'Queue ID';
COMMENT ON COLUMN z_vwCallAbandonedSummary.queueName IS 'Queue Name';
COMMENT ON COLUMN z_vwCallAbandonedSummary.purpose IS 'Purpose';
COMMENT ON COLUMN z_vwCallAbandonedSummary.segmenttype IS 'Segment Type';
COMMENT ON COLUMN z_vwCallAbandonedSummary.disconnectiontype IS 'Disconnection Type';

COMMENT ON VIEW z_vwCallAbandonedSummary IS 'See CallAbandonedSummary: Summary of abandoned calls with relevant details';
