﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RealCN = RealUserPushConversations;
using RealUA = RealUserPushActivityDef;
using RealUC = RealUserPushCallStatsDef;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class UserRealPush
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        //private GCUtils GCUtilities = new GCUtils();
        private JsonRestChilkat GCUtilities = new JsonRestChilkat();
        private JsonUtils JsonActions = new JsonUtils();
        public DateTime LastAPIKeyGet { get; set; }
        public WebSocketDetail WSSocketAct { get; set; }
        public WebSocketDetail WSSocketCall { get; set; }
        public WebSocketDetail WSSocketConv { get; set; }
        public String APIURL { get; set; }
        ClientWebSocket SocketConv = new ClientWebSocket();
        ClientWebSocket SocketAct = new ClientWebSocket();
        ClientWebSocket SocketCall = new ClientWebSocket();

        public void Initialize()
        {
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCApiKey = GCUtilities.APIKey;
            LastAPIKeyGet = DateTime.Now;
        }

        public void ReInitializeGCKey()
        {
            Console.WriteLine("GC User Realtime Notification Data - Obtaining Key");
            Boolean Successful = GCUtilities.GetAuthAPIKey();
            GCApiKey = GCUtilities.APIKey;
        }

        public void CreateWebSocket(WebSocketDetail WebSocket)
        {
            Console.WriteLine("\nCreating Channel For :{0}", WebSocket.ReportName);
            GCUtilities.CreateWebSocket(GCUtilities.APISockURL, WebSocket.connectUri, WebSocket.ReportName);
        }

        public Boolean DeleteChannels()
        {
            Boolean Successful = false;
            if (!string.IsNullOrEmpty(WSSocketAct?.id))
                Successful = JsonActions.DeleteNotificationSubscription(APIURL, WSSocketAct.id, GCApiKey);
            if (!string.IsNullOrEmpty(WSSocketCall?.id))
                Successful = JsonActions.DeleteNotificationSubscription(APIURL, WSSocketCall.id, GCApiKey);
            if (!string.IsNullOrEmpty(WSSocketConv?.id))
                Successful = JsonActions.DeleteNotificationSubscription(APIURL, WSSocketConv.id, GCApiKey);

            return Successful;
        }

        public void CreateUserActivitySubs(ref DataTable DTUserMainTable)
        {
            Console.WriteLine("Creating Activity Channel For Users");

            //Create Subscriptions for Notification - Activity and WFM.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DTUserMainTable.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".workforcemanagement.adherence\"}," +
                                        " \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".activity\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = APIURL + "/api/v2/notifications/channels/" + WSSocketAct.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WSSocketAct.id, GCApiKey.Substring(0, 6));

                string JsonString = GCUtilities.ReturnJson(URL, JSONBodyString);

            }


        }

        public void CreateUserCallSubs(ref DataTable DTUserMainTable)
        {

            Console.WriteLine("Creating Call Summary Channel For Users");

            //Create Subscriptions for Notification - Calls.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DTUserMainTable.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversationsummary\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = APIURL + "/api/v2/notifications/channels/" + WSSocketAct.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WSSocketAct.id, GCApiKey.Substring(0, 6));

                string JsonString = GCUtilities.ReturnJson(URL, JSONBodyString);

            }


        }

        public void CreateUserConvSubs(ref DataTable DTUserMainTable)
        {
            Console.WriteLine("Creating Conversation Channel For User Conversations");

            //Create Subscriptions for Notification - Conversations.

            StringBuilder SubscriptionJSON = new StringBuilder();
            int Counter = 0;
            foreach (DataRow DRRow in DTUserMainTable.Rows)
            {
                SubscriptionJSON.Append(" \n{ \"id\": \"v2.users." + DRRow["id"].ToString() + ".conversations\"},");
                ++Counter;
            }

            if (Counter > 0)
            {
                SubscriptionJSON.Length = SubscriptionJSON.Length - 1;
                string JSONBodyString = "[" + SubscriptionJSON.ToString() + " ]";
                string URL = APIURL + "/api/v2/notifications/channels/" + WSSocketAct.id + "/subscriptions";

                Console.WriteLine("API Key: {1} Acti Sock ID: {0} ", WSSocketAct.id, GCApiKey.Substring(0, 6));

                string JsonString = GCUtilities.ReturnJson(URL, JSONBodyString);

            }
        }


    }


}
// spell-checker: ignore: acti
