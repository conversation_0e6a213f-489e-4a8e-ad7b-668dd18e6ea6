CREATE
OR ALTER VIEW [vwAdherenceDayData] AS
SELECT
    ad.userid,
    ad.startdate,
    ad.startdateltc,
    ad.dayStartOffsetSecs,
    ad.dayStartOffsetSecs / 86400.00 as dayStartOffsetSecsDay,
    ad.adherencePerc,
    ad.conformPerc,
    ad.impact,
    ad.adherenceScheduleSecs,
    ad.adherenceScheduleSecs / 86400.00 as adherenceScheduleSecsDay,
    ad.conformanceScheduleSecs,
    ad.conformanceScheduleSecs / 86400.00 as conformanceScheduleSecsDay,
    ad.conformanceActualSecs,
    ad.conformanceActualSecs / 86400.00 as conformanceActualSecsDay,
    ad.exceptionCount,
    ad.exceptionDurationSecs,
    ad.exceptionDurationSecs / 86400.00 as exceptionDurationSecsDay,
    ad.impactSeconds,
    ad.impactSeconds / 86400.00 as impactSecondsDay,
    ad.scheduleLengthSecs,
    ad.scheduleLengthSecs / 86400.00 as scheduleLengthSecsDay,
    ad.actualLengthSecs,
    ad.actualLengthSecs / 86400.00 as actualLengthSecsDay,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername
FROM
    adherencedayData ad
    LEFT JOIN vwuserdetail ud ON CAST(ud.id AS NVARCHAR) = CAST(ad.userid AS NVARCHAR);