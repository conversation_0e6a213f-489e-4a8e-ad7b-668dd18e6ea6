DROP VIEW IF EXISTS vwrealtimeuser;

CREATE
OR REPLACE VIEW vwrealtimeuser as
SELECT
    rd.name as user_name,
    rd.id AS user_id,
    rd.divisionid,
    rd.groupname,
    rd.managername,
    rd.managerid,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatus
        WHEN rd.systempresence = 'ON_QUEUE' THEN rd.routingstatus 
        ELSE upper(
            replace(
                rd.orgpresence,
                'On Queue' ,
                'ON_QUEUE' 
            )
        )
    END AS agentstatus,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE'  THEN rd.routstattime
        ELSE rd.presencetime
    END AS agenttime,
    CASE
    --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE' THEN TO_VARCHAR(rd.routstattime * 1000, 'HH24:MI:SS')  -- Assuming rd.routstattime is in seconds
        ELSE TO_VARCHAR(rd.presencetime * 1000, 'HH24:MI:SS') -- Assuming rd.presencetime is in seconds
    END AS agenttime_formatted,
    CASE
        --WHEN rd.convstatus IS NOT NULL THEN rd.convstatustime
        WHEN rd.systempresence = 'ON_QUEUE'  THEN rd.routstattime
        ELSE rd.presencetime
    END :: numeric / 86400.00 AS agenttimeday,
    rd.systempresence,
    rd.orgpresence,
    rd.routingstatus,
    rd.routstarttime,
    rd.routstattime,
    rd.routstattimeday,
    rd.presenceid,
    rd.presstarttime,
    rd.presencetime,
    rd.presencetimeday,
    rd.queuename,
    rd.conversationid,
    rd.media,
    rd.direction,
    --rd.convstatus,
    --rd.convstatustime,
    rd.adherencestate,
    rd.adherencestarttime,
    rd.impact,
    rd.scheduledactivitycategory
FROM
    (
        SELECT
            rl.id,
            rl.name,
            ud.managerid,
            ud.managername,
            ud.divisionid,
            upper(
                replace(
                    rl.systempresence ,
                    'On Queue' ,
                    'ON_QUEUE' 
                )
            ) AS systempresence,
            upper(
                replace(
                    pr.orgpresence ,
                    'On Queue' ,
                    'ON_QUEUE' 
                )
            ) AS orgpresence,
            CASE
                WHEN rl.routingstatus  = 'IDLE' 
                AND (
                    rl.cccallactive + rl.othcallactive + rl.cbcallactive + rl.cbothcallactive + rl.ccemailactive
                ) > 0 THEN 'ALERT' :: varchar
                ELSE rl.routingstatus
            END AS routingstatus,
            rl.routstarttime,
            datediff(
                'second',
                rl.routstarttime,
                CURRENT_TIMESTAMP()
            ) AS routstattime,
            datediff(
                'second',
                rl.routstarttime,
                CURRENT_TIMESTAMP()
            ) :: numeric / 86400.00 AS routstattimeday,
            rl.presenceid,
            rl.presstarttime,
            datediff(
                'second',
                rl.presstarttime,
                CURRENT_TIMESTAMP()
            ) AS presencetime,
            datediff(
                'second',
                rl.presstarttime,
                CURRENT_TIMESTAMP()
            ) :: numeric / 86400.00 AS presencetimeday,
            qd.name AS queuename,
            qd.id AS queueid,
            uc.conversationid,
            uc.media,
            uc.direction,
            uc.conversationstate,
            --uc.talktimeltc  * '-1'::integer AS convstatustime,
            gd.name AS groupname,
            rl.adherencestate,
            rl.adherencestarttime,
            rl.impact,
            rl.scheduledactivitycategory
        FROM
            userrealtimedata rl
            LEFT JOIN presencedetails pr ON pr.id  = rl.presenceid 
            LEFT JOIN userrealtimeconvdata uc ON uc.userid  = rl.id 
            LEFT JOIN queuedetails qd ON qd.id  = uc.queueid 
            LEFT JOIN vwuserdetail ud ON ud.id  = rl.id 
            LEFT JOIN usergroupmappings ug ON ug.userid  = rl.id 
            LEFT JOIN groupdetails gd ON gd.id  = ug.groupid     ) rd
