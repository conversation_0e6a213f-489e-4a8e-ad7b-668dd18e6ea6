-- Learning Assignment Results Table
-- Stores detailed results and completion data for learning module assignments
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user completed the assignment
-- - moduleid: Links to learningmodules.id to identify which learning module was completed
-- - Correlates with learningmoduleassignments table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment result response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningmoduleassignments on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

CREATE TABLE IF NOT EXISTS learningassignmentresults (
    id VARCHAR(50) NOT NULL,
    userid VARCHAR(50), -- User ID from Genesys Cloud - links assignment results to specific users
    moduleid VARCHAR(50), -- Learning Module ID - links results to specific learning modules (standardized lowercase)
    assessmentId VARCHAR(50),
    assessmentFormId varchar(50),
    passPercent numeric(20, 2),
    assessmentPercentageScore numeric(20, 2),
    assessmentCompletionPercentage numeric(20, 2),
    completionPercentage numeric(20, 2),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningassignmentresults_pkey PRIMARY KEY (id)
);

-- Add missing columns if they don't exist (for existing installations)
ALTER TABLE learningassignmentresults
ADD COLUMN IF NOT EXISTS userid VARCHAR(50);

-- Standardize moduleId to moduleid for consistency with learningmoduleassignments table
DO $$
BEGIN
    -- Check if old moduleId column exists and new moduleid doesn't
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'learningassignmentresults' AND column_name = 'moduleId')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'learningassignmentresults' AND column_name = 'moduleid') THEN
        -- Add new moduleid column
        ALTER TABLE learningassignmentresults ADD COLUMN moduleid VARCHAR(50);
        -- Copy data from old column to new column
        UPDATE learningassignmentresults SET moduleid = "moduleId";
        -- Drop old column
        ALTER TABLE learningassignmentresults DROP COLUMN "moduleId";
    END IF;
END $$;

-- Add comprehensive table and column comments for documentation
COMMENT ON TABLE learningassignmentresults IS
'Learning Assignment Results Table - Stores detailed results and completion data for learning module assignments.

This table captures the outcome and performance metrics when users complete learning module assignments.
It serves as the results repository that correlates with learningmoduleassignments to provide complete
assignment lifecycle tracking from assignment through completion and assessment.

Key Performance Optimizations:
- Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
- User IDs extracted from assignment result response data rather than query parameters
- Enables efficient user-module assignment and completion analytics

Cross-table Relationships:
- userid: Links to userdetails.id for user identification and demographics
- moduleid: Links to learningmodules.id for module details and metadata
- Correlates with learningmoduleassignments on userid+moduleid for assignment-to-completion tracking

Analytics Capabilities:
- Supports user assignment summaries and module completion rate analysis
- Enables performance metrics and learning analytics across user-module relationships
- Provides foundation for learning dashboard, reporting, and performance tracking
- Tracks assessment scores, completion percentages, and timing metrics';

-- Column-level comments for detailed documentation
COMMENT ON COLUMN learningassignmentresults.id IS
'Primary key - Unique identifier for each learning assignment result record from Genesys Cloud';

COMMENT ON COLUMN learningassignmentresults.userid IS
'User ID from Genesys Cloud - Links assignment results to specific users.
Enables user-centric analytics and correlates with userdetails.id for user demographics.
Populated from assignment result response data during module-based API retrieval for optimal performance.';

COMMENT ON COLUMN learningassignmentresults.moduleid IS
'Learning Module ID - Links results to specific learning modules.
Standardized to lowercase for consistency across all database types (MSSQL, PostgreSQL, Snowflake).
Correlates with learningmodules.id for module metadata and learningmoduleassignments.moduleid for assignment tracking.';

COMMENT ON COLUMN learningassignmentresults.assessmentId IS
'Assessment identifier associated with the learning assignment result';

COMMENT ON COLUMN learningassignmentresults.assessmentFormId IS
'Assessment form identifier used for the evaluation';

COMMENT ON COLUMN learningassignmentresults.passPercent IS
'Minimum percentage required to pass the assessment';

COMMENT ON COLUMN learningassignmentresults.assessmentPercentageScore IS
'Actual percentage score achieved on the assessment';

COMMENT ON COLUMN learningassignmentresults.assessmentCompletionPercentage IS
'Percentage completion of the assessment component';

COMMENT ON COLUMN learningassignmentresults.completionPercentage IS
'Overall completion percentage of the learning module';

COMMENT ON COLUMN learningassignmentresults.dateCreated IS
'Timestamp when the result was created in Genesys Cloud';

COMMENT ON COLUMN learningassignmentresults.dateModified IS
'Timestamp when the result was last modified in Genesys Cloud';

COMMENT ON COLUMN learningassignmentresults.lengthInMinutes IS
'Duration spent completing the learning module in minutes';

COMMENT ON COLUMN learningassignmentresults.updated IS
'Timestamp when this record was last updated in the local database';
