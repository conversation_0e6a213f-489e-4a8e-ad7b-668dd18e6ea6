CREATE TABLE IF NOT EXISTS kq_analysis (
    kq_analysisid UUID NOT NULL,
    conversationid UUID,
    communicationid UUID,
    callsummary TEXT,
    callresolution TEXT,
    callbackrequired BOOLEAN,
    selfserviceattempted BOOLEAN,
    selfserviceproblems BOOLEAN,
    satisfactionsentiment DECIMAL(5,2),
    CONSTRAINT kq_analysis_pkey PRIMARY KEY (kq_analysisid)
);

-- Create indexes on foreign key columns for performance
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'kq_analysis'
        AND indexname = 'idx_kq_analysis_communicationid'
    ) THEN
        CREATE INDEX idx_kq_analysis_communicationid ON kq_analysis (communicationid);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'kq_analysis'
        AND indexname = 'idx_kq_analysis_conversationid'
    ) THEN
        CREATE INDEX idx_kq_analysis_conversationid ON kq_analysis (conversationid);
    END IF;
END $$;
