-- PostgreSQL Table Template
-- Use this template when creating new tables in PostgreSQL

-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS table_name (
    id varchar(50) NOT NULL,
    name varchar(100),
    description text,
    created_date timestamp without time zone,
    updated_date timestamp without time zone,
    CONSTRAINT table_name_pkey PRIMARY KEY (id)
);

-- Add columns if they don't exist
ALTER TABLE table_name
ADD COLUMN IF NOT EXISTS new_column varchar(100);

-- Create indexes if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'table_name'
        AND indexname = 'ix_table_name_column'
    ) THEN
        CREATE INDEX ix_table_name_column ON table_name(column);
        RAISE NOTICE 'Created index ix_table_name_column on table_name.column';
    END IF;
END $$;

-- Example of adding a foreign key
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_table_name_reference_table'
        AND table_name = 'table_name'
    ) THEN
        ALTER TABLE table_name
        ADD CONSTRAINT fk_table_name_reference_table
        FOREIGN KEY (reference_id) REFERENCES reference_table(id);
    END IF;
END $$;
