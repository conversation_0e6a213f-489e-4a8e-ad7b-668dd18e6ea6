CREATE TABLE IF NOT EXISTS presencedetails (
    id varchar(50) NOT NULL,
    systempresence varchar(255),
    orgpresence varchar(255),
    deactivated bit(1),
    type varchar(50),
    divisionid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT presencedetails_pkey PRIMARY KEY (id)
);

ALTER TABLE presencedetails 
ADD column IF NOT exists divisionid varchar(50);

DO $$
BEGIN
IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'presencedetails' 
    AND column_name = 'primaryfield'
    ) THEN
    ALTER TABLE presencedetails DROP COLUMN primaryfield CASCADE;
END IF;
END $$;

COMMENT ON COLUMN presenceDetails.deactivated IS 'Presence Active ?'; 
COMMENT ON COLUMN presenceDetails.id IS 'Primary Key'; 
COMMENT ON COLUMN presenceDetails.orgpresence IS 'Presence Organisation Presence Name'; 
COMMENT ON COLUMN presenceDetails.systempresence IS 'Presence System Presence Name'; 
COMMENT ON COLUMN presenceDetails.type IS 'Presence Type'; 
COMMENT ON COLUMN presenceDetails.divisionid IS 'Division ID'; 
COMMENT ON COLUMN presenceDetails.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON TABLE presenceDetails is 'Persence Code Lookup Data'; 