CREATE TABLE IF NOT EXISTS oauthusagedata (
    keyid varchar(100) NOT NULL,
    clientid varchar(50),
    rowdate timestamp without time zone,
    clientname varchar(200),
    organizationid varchar(50),
    userid varchar(50),
    status200 integer,
    status300 integer,
    status400 integer,
    status500 integer,
    status429 integer,
    updated timestamp without time zone,
    CONSTRAINT oauthusagedata_pkey PRIMARY KEY (keyid)
);

COMMENT ON COLUMN oauthusageData.clientid IS 'Oauth Client GUID'; 
COMMENT ON COLUMN oauthusageData.clientname IS 'Oauth Client Name'; 
COMMENT ON COLUMN oauthusageData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN oauthusageData.organizationid IS 'Home Organization GUID'; 
COMMENT ON COLUMN oauthusageData.rowdate IS 'Date'; 
COMMENT ON COLUMN oauthusageData.status200 IS 'Count of 200 Response Codes'; 
COMMENT ON COLUMN oauthusageData.status300 IS 'Count of 200 Response Codes'; 
COMMENT ON COLUMN oauthusageData.status400 IS 'Count of 200 Response Codes'; 
COMMENT ON COLUMN oauthusageData.status429 IS 'Count of 429 Response Codes - Rate limiting Responses'; 
COMMENT ON COLUMN oauthusageData.status500 IS 'Count of 200 Response Codes'; 
COMMENT ON COLUMN oauthusageData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN oauthusageData.userid IS 'Oauth Agent GUID'; 
COMMENT ON TABLE oauthusageData IS 'Oauth Usage by Client ID per Day'; 