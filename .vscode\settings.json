{"cSpell.caseSensitive": false, "cSpell.diagnosticLevel": "Information", "cSpell.enableFiletypes": ["*"], "cSpell.flagWords": ["HACK", "TODO"], "cSpell.ignoreWords": ["ActiveQMembersData", "AddOrRemove", "AdherencEexc", "AdherencEexcData", "AgentId", "ApiClid", "ApiClsc", "Attribs", "Buildah", "BusinessUnitId", "CbCallActive", "CbCallAcw", "CbOthCallActive", "CbOthCallAcw", "CcCallActive", "CcCallAcw", "CcChatActive", "CcChatAcw", "CcEmailActive", "CcEmailAcw", "Chilkat", "ClientId", "Conv", "ConversationEndDateLtc", "ConversationId", "ConversationStartdateLtc", "ConversationStartLtc", "Conv<PERSON><PERSON>mary", "ConvSummaryData", "ConvVoiceOverviewData", "ConvVoiceSentimentDetailData", "ConvVoiceTopicDetailData", "CustomerKeyId", "CustomerScience", "DateCreatedLtc", "DateModifiedLtc", "Dets", "Dialing", "DialingMode", "DivisionId", "D<PERSON>", "EndDateLtc", "Evals", "EvaluationCatchup", "EvaluationId", "FactData", "FirstAgentId", "FirstQueueId", "FlowId", "Funcs", "GcApi", "GcOd", "GcWfm", "GroupId", "HeadCountForecast", "HoursBlockData", "Inin", "KeyId", "LastAgentId", "LastModifiedBy", "LastQueueId", "LengthInMinutes", "ModifiedBy", "MSSQL", "MyPurecloud", "nBlindTransferred", "nConsultTransferred", "n<PERSON><PERSON><PERSON>", "OAuthData", "OAuthUsage", "OdCampaignDetails", "OdContactListData", "OdContactListDetails", "OfferedForecast", "OthCallActive", "OthCallAcw", "OthChatActive", "OthChatAcw", "OthEmailActive", "OthEmailAcw", "ParticipantId", "PresenceDetail", "PresenceId", "PrevQueueId", "QueueConvReal", "QueueId", "QueueMembership", "QueueRealTimeConv", "QueueRealTimeConvData", "RoutingId", "Sched", "ScheduleDetails", "ScheduleId", "SegmentEndDateLtc", "segmentStartDateLtc", "SequenceId", "SessionDnis", "ShortTermForecastId", "SqlConnectionString", "SqlDatabaseSchema", "SqlDatabaseType", "StartDateLtc", "StartOfWeek", "SubsUser", "SubsUsers", "SysConvUsage", "TAbandon", "TAbandonedCount", "TAcw", "TalkTimeLtc", "TAnswered", "TAnsweredCount", "TeamDetail", "TeamDetails", "T<PERSON><PERSON>le", "THandleCount", "THeldComplete", "THeldCompleteCount", "THelpCompleteCount", "TimeOffReq", "TimeSheet", "TQueueTime", "TResponse", "TResponseCount", "TTalkComplete", "UCArchitects", "UserQueueAudit", "UserQueueMapping", "UserRealTimeConvData", "UsersQueueAudit", "VoiceAnalysis", "WfmAuditData", "WfmSchedule", "WrapUp", "xUnit"], "cSpell.language": "en-GB", "cSpell.words": ["Genesys"], "conventionalCommits.autoCommit": false, "conventionalCommits.gitmoji": false, "conventionalCommits.scopes": ["factdata", "install", "interaction", "schema", "survey", "teamsdetails", "telemetry"]}