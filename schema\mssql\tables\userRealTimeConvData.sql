IF dbo.csg_table_exists('userRealTimeConvData') = 0
CREATE TABLE [userRealTimeConvData](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),
    [id] [nvarchar](50),
    [conversationid] [nvarchar](50),
    [media] [nvarchar](50),
    [direction] [nvarchar](50),
    [conversationstate] [nvarchar](50),
    [acwstate] [bit],
    [acwstring] [nvarchar](50),
    [acwtime] [datetime],
    [heldstate] [bit],
    [heldtime] [datetime],
    [talktime] [datetime],
    [talktimeltc] [datetime],
    [startdate] [datetime],
    [startdateltc] [datetime],
    [actingas] [nvarchar](50),
    [queueid] [nvarchar](50),
    [skill1] [nvarchar](50),
    [skill2] [nvarchar](50),
    [skill3] [nvarchar](50),
    [intialpriority] [int],
    [updated] [datetime],
    [initialpriority] [int],
    [requestedrout1] [nvarchar](50),
    [requestedrout2] [nvarchar](50),
    [ani] [nvarchar](400),
    [dnis] [nvarchar](400),
    [usedrout] [nvarchar](50),
    [segmenttime] [datetime],
    [participantname] [nvarchar](200),
    [state] [nvarchar](50),
    CONSTRAINT [PK_userRealTimeConvData] PRIMARY KEY ([keyid])
);
IF dbo.csg_column_exists('userRealTimeConvData', 'id') = 0
    ALTER TABLE userRealTimeConvData ADD [id] [nvarchar](50);