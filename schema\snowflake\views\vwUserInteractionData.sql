DROP VIEW IF EXISTS vwUserInteractionData;
CREATE
OR REPLACE VIEW vwuserinteractiondata AS
SELECT
    userinteractiondata.keyid,
    userinteractiondata.startdate as startdateUTC,
    userinteractiondata.startDateLTC,
    userinteractiondata.userid,
    userdetail.name as agentname,
    userdetail.managerid as managerid,
    userdetail.managername,
    userdetail.divisionid as agentdivisionid,
    userdetail.divisionname as agentdivisionname,
    userinteractiondata.direction,
    userinteractiondata.queueid,
    queuedetail.name as queuename,
    queuedetail.divisionid,
    queuedetail.divisionname,
    userinteractiondata.mediatype,
    userinteractiondata.wrapupcode,
    wrapupdetails.name as wrapupdesc,
    userinteractiondata.talertcount,
    userinteractiondata.talerttimesum,
    userinteractiondata.talerttimesum / 86400.00 as talerttimesumDay,
    userinteractiondata.talerttimemax,
    userinteractiondata.talerttimemax / 86400.00 as talerttimemaxDay,
    userinteractiondata.talerttimemin,
    userinteractiondata.talerttimemin / 86400.00 as talerttimeminDay,
    userinteractiondata.tansweredcount,
    userinteractiondata.tansweredtimesum,
    userinteractiondata.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    userinteractiondata.tansweredtimemax,
    userinteractiondata.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    userinteractiondata.tansweredtimemin,
    userinteractiondata.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    userinteractiondata.ttalkcount,
    userinteractiondata.ttalktimesum,
    userinteractiondata.ttalktimesum / 86400.00 as ttalktimesumDay,
    userinteractiondata.ttalktimemax,
    userinteractiondata.ttalktimemax / 86400.00 as ttalktimemaxDay,
    userinteractiondata.ttalktimemin,
    userinteractiondata.ttalktimemin / 86400.00 as ttalktimeminDay,
    userinteractiondata.ttalkcompletecount,
    userinteractiondata.ttalkcompletetimesum,
    userinteractiondata.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    userinteractiondata.ttalkcompletetimemax,
    userinteractiondata.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    userinteractiondata.ttalkcompletetimemin,
    userinteractiondata.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    userinteractiondata.tnotrespondingcount,
    userinteractiondata.tnotrespondingtimesum,
    userinteractiondata.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    userinteractiondata.tnotrespondingtimemax,
    userinteractiondata.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    userinteractiondata.tnotrespondingtimemin,
    userinteractiondata.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    userinteractiondata.theldcount,
    userinteractiondata.theldtimesum,
    userinteractiondata.theldtimesum / 86400.00 as theldtimesumDay,
    userinteractiondata.theldtimemax,
    userinteractiondata.theldtimemax / 86400.00 as theldtimemaxDay,
    userinteractiondata.theldtimemin,
    userinteractiondata.theldtimemin / 86400.00 as theldtimeminDay,
    userinteractiondata.theldcompletecount,
    userinteractiondata.theldcompletetimesum,
    userinteractiondata.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    userinteractiondata.theldcompletetimemax,
    userinteractiondata.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    userinteractiondata.theldcompletetimemin,
    userinteractiondata.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    userinteractiondata.thandlecount,
    userinteractiondata.thandletimesum,
    userinteractiondata.thandletimesum / 86400.00 as thandletimesumDay,
    userinteractiondata.thandletimemax,
    userinteractiondata.thandletimemax / 86400.00 as thandletimemaxDay,
    userinteractiondata.thandletimemin,
    userinteractiondata.thandletimemin / 86400.00 as thandletimeminDay,
    userinteractiondata.tacwcount,
    userinteractiondata.tacwtimesum,
    userinteractiondata.tacwtimesum / 86400.00 as tacwtimesumDay,
    userinteractiondata.tacwtimemax,
    userinteractiondata.tacwtimemax / 86400.00 as tacwtimemaxDay,
    userinteractiondata.tacwtimemin,
    userinteractiondata.tacwtimemin / 86400.00 as tacwtimeminDay,
    userinteractiondata.nconsult,
    userinteractiondata.nconsulttransferred,
    userinteractiondata.noutbound,
    userinteractiondata.nerror,
    userinteractiondata.ntransferred,
    userinteractiondata.nblindtransferred,
    userinteractiondata.nconnected,
    userinteractiondata.tdialingcount,
    userinteractiondata.tdialingtimesum,
    userinteractiondata.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    userinteractiondata.tdialingtimemax,
    userinteractiondata.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    userinteractiondata.tdialingtimemin,
    userinteractiondata.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    userinteractiondata.tcontactingcount,
    userinteractiondata.tcontactingtimesum,
    userinteractiondata.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    userinteractiondata.tcontactingtimemax,
    userinteractiondata.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    userinteractiondata.tcontactingtimemin,
    userinteractiondata.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    userinteractiondata.tvoicemailcount,
    userinteractiondata.tvoicemailtimesum,
    userinteractiondata.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    userinteractiondata.tvoicemailtimemax,
    userinteractiondata.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    userinteractiondata.tvoicemailtimemin,
    userinteractiondata.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    userinteractiondata.updated
FROM
    userInteractionData
    left outer join vwUserDetail userdetail on userdetail.id = userinteractiondata.userid
    left outer join vwqueuedetails queuedetail on queuedetail.id = userinteractiondata.queueid
    left outer join wrapupDetails wrapupdetails on wrapupdetails.id = userinteractiondata.wrapupcode;