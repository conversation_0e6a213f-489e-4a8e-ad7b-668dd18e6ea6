CREATE
OR REPLACE VIEW vwevalquestiondata AS
SELECT
    DISTINCT eq.evaluationid,
    eq.questionid,
    eq.answerid,
    eq.score,
    ed.questionanswervalue,
    eq.markedna,
    eq.failedkillquestions,
    eq.comments,
    ed.evaluationformid,
    ed.evaluationname,
    ed.questiongroupid,
    ed.questiongroupname,
    ed.questiontext,
    ed.questionhelptext,
    ed.quesiontype,
    ed.questionanwserid,
    ed.questionanswertext,
    cd.divisionid
FROM
    evalquestiondata eq
    LEFT OUTER JOIN evaldetails ed on ed.evaluationformid = eq.evaluationformid
    AND ed.questiongroupid = eq.questiongroupid
    AND ed.questionid = eq.questionid
    AND ed.questionanwserid = eq.answerid
    LEFT OUTER JOIN evaldata eda on eda.evaluationid = eq.evaluationid
    LEFT OUTER JOIN convsummarydata cd on cd.conversationid = eda.conversationid
;