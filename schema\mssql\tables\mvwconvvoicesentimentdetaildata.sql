IF dbo.csg_table_exists('mvwconvvoicesentimentdetaildata') = 0
CREATE TABLE mvwconvvoicesentimentdetaildata (
    [keyid] VARCHAR(100) NOT NULL PRIMARY KEY,
    [conversationid] VARCHAR(50),
    [starttime] DATETIME NULL,
    [starttimeltc] DATETIME NULL,
    [duration] DECIMAL(20, 2),
    [participant] VARCHAR(50),
    [phrase] VARCHAR(400),
    [sentiment] DECIMAL(20, 2),
    [phraseindex] INT NULL,
    [updated] DATETIME NULL,
    [conversationstartdate] DATETIME NULL,
    [conversationstartdateltc] DATETIME NULL,
    [conversationenddate] DATETIME NULL,
    [conversationenddateltc] DATETIME NULL,
    [ttalkcomplete] INT NULL,
    [ani] VARCHAR(400),
    [dnis] VARCHAR(400),
    [firstmediatype] VARCHAR(50),
    [divisionid] VARCHAR(50),
    [firstqueueid] VARCHAR(50),
    [firstqueuename] VARCHAR(255),
    [lastqueueid] VARCHAR(50),
    [lastqueuename] VARCHAR(255),
    [firstagentid] VARCHAR(50),
    [firstagentname] VARCHAR(200),
    [firstagentdept] VARCHAR(200),
    [firstagentmanagerid] VARCHAR(50),
    [firstagentmanagername] VARCHAR(200),
    [lastagentid] VARCHAR(50),
    [lastagentname] VARCHAR(200),
    [lastagentdept] VARCHAR(200),
    [lastagentmanagerid] VARCHAR(50),
    [lastagentmanagername] VARCHAR(200),
    [firstwrapupcode] VARCHAR(255),
    [firstwrapupname] VARCHAR(255),
    [lastwrapupcode] VARCHAR(255),
    [lastwrapupname] VARCHAR(255) NULL
);


IF dbo.csg_index_exists('mvconvvoicesentiment_agent', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_agent] ON [mvwconvvoicesentimentdetaildata] ([firstagentid]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_conv', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_conv] ON [mvwconvvoicesentimentdetaildata] ([conversationid]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_division', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_division] ON [mvwconvvoicesentimentdetaildata] ([divisionid]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_end', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_end] ON [mvwconvvoicesentimentdetaildata] ([conversationenddate]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_endltc', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_endltc] ON [mvwconvvoicesentimentdetaildata] ([conversationenddateltc]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_keyid', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE UNIQUE INDEX [mvconvvoicesentiment_keyid] ON [mvwconvvoicesentimentdetaildata] ([keyid]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_queue', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_queue] ON [mvwconvvoicesentimentdetaildata] ([firstqueueid]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_sentiment', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_sentiment] ON [mvwconvvoicesentimentdetaildata] ([sentiment]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_start', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_start] ON [mvwconvvoicesentimentdetaildata] ([conversationstartdate]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_startltc', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_startltc] ON [mvwconvvoicesentimentdetaildata] ([conversationstartdateltc]);
END;

IF dbo.csg_index_exists('mvconvvoicesentiment_wrapup', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvconvvoicesentiment_wrapup] ON [mvwconvvoicesentimentdetaildata] ([firstwrapupcode]);
END;

IF dbo.csg_index_exists('mvwconvvoicesentiment_participant', 'mvwconvvoicesentimentdetaildata') = 0
BEGIN
    CREATE INDEX [mvwconvvoicesentiment_participant] ON [mvwconvvoicesentimentdetaildata] ([participant]);
END;
