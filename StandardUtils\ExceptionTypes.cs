using System.Runtime.Serialization;

namespace CSG.Common.Exceptions;

[Serializable]
public class AdapterException : Exception
{
    public AdapterException() : base()
    {
    }

    public AdapterException(string message) : base(message)
    {
    }

    public AdapterException(string message, Exception innerException)
        : base(message, innerException)
    {
    }

    protected AdapterException(SerializationInfo info, StreamingContext context) 
        : base(info, context)
    {
    }
}

[Serializable]
public class SchemaException : AdapterException
{
    public SchemaException() : base()
    {
    }

    public SchemaException(string message) : base(message)
    {
    }

    public SchemaException(string message, Exception innerException)
        : base(message, innerException)
    {
    }

    protected SchemaException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
    }
}
