CREATE OR ALTER VIEW [vwSurveyData] AS
SELECT
    surveyId,
    conversationId,
    surveyFormId,
    surveyName,
    agentId,
    agent.name AS agentName,
    agent.department AS agentDepartment,
    manager.name AS agentManager,
    agentTeamId,
    queueId,
    queue.name AS queueName,
    status,
    totalScore,
    completedDate,
    completedDateLtc,
    surveyData.updated,
    lastPoll
FROM
    surveyData
    LEFT JOIN userDetails agent ON agent.id = surveyData.agentid
    LEFT JOIN userDetails manager ON manager.id = agent.manager
    LEFT JOIN queueDetails queue ON queue.id = surveyData.queueid;
