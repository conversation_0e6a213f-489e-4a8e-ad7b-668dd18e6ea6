CREATE TABLE IF NOT EXISTS kq_analysis_taxonomy (
    kq_analysistaxonomyid UUID NOT NULL,
    kq_analysisid UUID,
    taxonomy VARCHAR(200),
    CONSTRAINT kq_analysis_taxonomy_pkey PRIMARY KEY (kq_analysistaxonomyid)
);



-- Create indexes on foreign key columns for performance
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'kq_analysis_taxonomy'
        AND indexname = 'idx_kq_analysis_taxonomy_kq_analysisid'
    ) THEN
        CREATE INDEX idx_kq_analysis_taxonomy_kq_analysisid ON kq_analysis_taxonomy (kq_analysisid);
    END IF;
END $$;
