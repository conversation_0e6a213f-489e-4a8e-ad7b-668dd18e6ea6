DROP VIEW IF EXISTS vwSurveyQuestionAnswers;
CREATE OR REPLACE VIEW vwSurveyQuestionAnswers AS
SELECT surveyquestionanswers.surveyid,
    surveyquestionanswers.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyquestionanswers.surveyformid,
    surveyquestionanswers.surveyname,
    surveyquestionanswers.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyquestionanswers.agentteamid,
    surveyquestionanswers.queueid,
    queue.name AS queuename,
    surveyquestionanswers.questiongroupid,
    surveyquestionanswers.questiongroupname,
    surveyquestionanswers.questionid,
    surveyquestionanswers.questiontext,
    surveyquestionanswers.questiontype,
    surveyquestionanswers.questionanswerid,
    surveyquestionanswers.questionanswertext,
    surveyquestionanswers.questionanswervalue,
    surveyquestionanswers.questionscore,
    surveyquestionanswers.questionmarkedna,
    surveyquestionanswers.updated
FROM surveyquestionanswers
    LEFT JOIN surveydata survey ON survey.surveyid::text = surveyquestionanswers.surveyid::text
    LEFT JOIN userdetails agent ON agent.id::text = surveyquestionanswers.agentid::text
    LEFT JOIN userdetails manager ON manager.id::text = agent.manager::text
    LEFT JOIN queuedetails queue ON queue.id::text = surveyquestionanswers.queueid::text;

COMMENT ON COLUMN vwSurveyQuestionAnswers.surveyid IS 'Survey GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.completeddate IS 'Survey Completed Date (UTC)';
COMMENT ON COLUMN vwSurveyQuestionAnswers.completeddateltc IS 'Survey Completed Date (LTC)';
COMMENT ON COLUMN vwSurveyQuestionAnswers.surveyformid IS 'Survey Form GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.surveyname IS 'Survey Name';
COMMENT ON COLUMN vwSurveyQuestionAnswers.agentid IS 'Agent GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.agentname IS 'Agent Name';
COMMENT ON COLUMN vwSurveyQuestionAnswers.agentdepartment IS 'Agent Department';
COMMENT ON COLUMN vwSurveyQuestionAnswers.agentmanager IS 'Agent Manager';
COMMENT ON COLUMN vwSurveyQuestionAnswers.agentteamid IS 'Agent Team GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.queuename IS 'Queue Name';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questiongroupid IS 'Question Group GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionid IS 'Question GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questiontext IS 'Question Text';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questiontype IS 'Question Type';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionanswerid IS 'Question Answer GUID';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionanswertext IS 'Question Answer Text';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionanswervalue IS 'Question Answer Value';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionscore IS 'Question Score';
COMMENT ON COLUMN vwSurveyQuestionAnswers.questionmarkedna IS 'Question Marked as N/A';
COMMENT ON COLUMN vwSurveyQuestionAnswers.updated IS 'Last Updated';
COMMENT ON VIEW vwSurveyQuestionAnswers IS 'Survey Question Answers View';


-- spell-checker: ignore: surveyid questiongroupid questionid questionanswerid