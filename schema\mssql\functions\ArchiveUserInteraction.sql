CREATE OR ALTER PROCEDURE [dbo].[ArchiveUserInteraction]  
    @AggOffSet INT,   
    @AggType NVARCHAR(1)   
AS   
BEGIN
    SET NOCOUNT ON;

    DECLARE @CurrentTimeUTC DATETIME;
    DECLARE @CurrentTimeLTC DATETIME;
    DECLAR<PERSON> @SystemTime DATETIME;
    DECLAR<PERSON> @Offset INT;
    DECLARE @CurrentDOW INT;
    DECLAR<PERSON> @StartDate DATETIME;
    DECLARE @EndDate DATETIME;
    DECLARE @TableName NVARCHAR(50);
    DECLARE @DelsqlCommand NVARCHAR(1000);
    DECLARE @InssqlCommand NVARCHAR(MAX);

    SET ANSI_WARNINGS OFF;

    SET @SystemTime = GETDATE();
    SET @CurrentTimeUTC = GETUTCDATE();
    SET @CurrentTimeLTC = CONVERT(DATETIME, SWITCHOFFSET(GETUTCDATE(), DATEPART(TZOFFSET, (GETUTCDATE() AT TIME ZONE 'AUS Eastern Standard Time')))); 
    SET @Offset = DATEDIFF(MINUTE, @CurrentTimeUTC, @CurrentTimeLTC);
    SET @CurrentDOW = 1 + ((5 + DATEPART(dw, @CurrentTimeLTC) + @@DATEFIRST) % 7);

    SET @StartDate = CASE @AggType
            WHEN 'M' THEN DATEADD(MONTH, @AggOffSet * -1, @CurrentTimeLTC)
            WHEN 'W' THEN DATEADD(WEEK, @AggOffSet * -1, DATEADD(DAY, (@CurrentDOW -1) * -1, @CurrentTimeLTC))
            WHEN 'D' THEN DATEADD(DAY, @AggOffSet * -1, @CurrentTimeLTC)
    END;

    SET @StartDate = CASE @AggType
            WHEN 'M' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-01 00:00:00'
            WHEN 'W' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-' + RIGHT('0' + CAST(DAY(@StartDate) AS NVARCHAR(2)), 2) + ' 00:00:00'
            WHEN 'D' THEN CAST(YEAR(@StartDate) AS NVARCHAR(4)) + '-' + RIGHT('0' + CAST(MONTH(@StartDate) AS NVARCHAR(2)), 2) + '-' + RIGHT('0' + CAST(DAY(@StartDate) AS NVARCHAR(2)), 2) + ' 00:00:00'
    END;

    SET @EndDate = CASE @AggType
            WHEN 'M' THEN DATEADD(MINUTE, -1, DATEADD(MONTH, 1, @StartDate))
            WHEN 'W' THEN DATEADD(MINUTE, -1, DATEADD(WEEK, 1, @StartDate))
            WHEN 'D' THEN DATEADD(MINUTE, -1, DATEADD(DAY, 1, @StartDate))
    END;

    SET @TableName = CASE @AggType
            WHEN 'M' THEN 'userInteractionDataMonthly'
            WHEN 'W' THEN 'userInteractionDataWeekly'
            WHEN 'D' THEN 'userInteractionDataDaily'
    END;

    SET @StartDate = DATEADD(MINUTE, @Offset * -1, @StartDate);
    SET @EndDate = DATEADD(MINUTE, @Offset * -1, @EndDate);

    SET @DelsqlCommand = N'DELETE FROM ' + @TableName + N' WHERE startdate = ''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + ''' ';

    PRINT 'Delete Old Rows';
    PRINT @DelsqlCommand;

    EXEC sp_executesql @DelsqlCommand;

    -- Construct the INSERT INTO statement with dynamic SQL in parts for clarity
    SET @InssqlCommand = N'INSERT INTO ' + @TableName + N' ('
        + N'keyid, userid, direction, queueid, mediatype, wrapupcode, startdate, '
        + N'talertcount, talerttimesum, talerttimemax, talerttimemin, '
        + N'tansweredcount, tansweredtimesum, tansweredtimemax, tansweredtimemin, '
        + N'ttalkcount, ttalktimesum, ttalktimemax, ttalktimemin, '
        + N'ttalkcompletecount, ttalkcompletetimesum, ttalkcompletetimemax, ttalkcompletetimemin, '
        + N'tnotrespondingcount, tnotrespondingtimesum, tnotrespondingtimemax, tnotrespondingtimemin, '
        + N'theldcount, theldtimesum, theldtimemax, theldtimemin, '
        + N'theldcompletecount, theldcompletetimesum, theldcompletetimemax, theldcompletetimemin, '
        + N'thandlecount, thandletimesum, thandletimemax, thandletimemin, '
        + N'tacwcount, tacwtimesum, tacwtimemax, tacwtimemin, '
        + N'nconsult, nconsulttransferred, noutbound, nerror, ntransferred, nblindtransferred, nconnected, '
        + N'tdialingcount, tdialingtimesum, tdialingtimemax, tdialingtimemin, '
        + N'tcontactingcount, tcontactingtimesum, tcontactingtimemax, tcontactingtimemin, '
        + N'tvoicemailcount, tvoicemailtimesum, tvoicemailtimemax, tvoicemailtimemin, '
        + N'av1count, av1timesum, av1timemax, av1timemin, '
        + N'av2count, av2timesum, av2timemax, av2timemin, '
        + N'av3count, av3timesum, av3timemax, av3timemin, '
        + N'av4count, av4timesum, av4timemax, av4timemin, '
        + N'av5count, av5timesum, av5timemax, av5timemin, '
        + N'av6count, av6timesum, av6timemax, av6timemin, '
        + N'av7count, av7timesum, av7timemax, av7timemin, '
        + N'av8count, av8timesum, av8timemax, av8timemin, '
        + N'av9count, av9timesum, av9timemax, av9timemin, '
        + N'av10count, av10timesum, av10timemax, av10timemin, '
        + N'updated) ';

    -- Begin SELECT statement for INSERT INTO
    SET @InssqlCommand += N'SELECT '
        + N'userid + ''|'' + direction + ''|'' + ISNULL(queueid,''NOQUEUE'') + ''|'' + mediatype + ''|'' + ISNULL(wrapupcode,''NOWRAP'') + ''|'' + ''' 
        + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) 
        + N''' AS keyid, '
        + N'userid, direction, queueid, mediatype, wrapupcode, '
        + N'''' + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + N''' AS startdate, '
        + N'SUM(talertcount), SUM(talerttimesum), MAX(talerttimemax), MIN(talerttimemin), '
        + N'SUM(tansweredcount), SUM(tansweredtimesum), MAX(tansweredtimemax), MIN(tansweredtimemin), '
        + N'SUM(ttalkcount), SUM(ttalktimesum), MAX(ttalktimemax), MIN(ttalktimemin), '
        + N'SUM(ttalkcompletecount), SUM(ttalkcompletetimesum), MAX(ttalkcompletetimemax), MIN(ttalkcompletetimemin), '
        + N'SUM(tnotrespondingcount), SUM(tnotrespondingtimesum), MAX(tnotrespondingtimemax), MIN(tnotrespondingtimemin), '
        + N'SUM(theldcount), SUM(theldtimesum), MAX(theldtimemax), MIN(theldtimemin), '
        + N'SUM(theldcompletecount), SUM(theldcompletetimesum), MAX(theldcompletetimemax), MIN(theldcompletetimemin), '
        + N'SUM(thandlecount), SUM(thandletimesum), MAX(thandletimemax), MIN(thandletimemin), '
        + N'SUM(tacwcount), SUM(tacwtimesum), MAX(tacwtimemax), MIN(tacwtimemin), '
        + N'SUM(nconsult), SUM(nconsulttransferred), SUM(noutbound), SUM(nerror), SUM(ntransferred), SUM(nblindtransferred), SUM(nconnected), '
        + N'SUM(tdialingcount), SUM(tdialingtimesum), MAX(tdialingtimemax), MIN(tdialingtimemin), '
        + N'SUM(tcontactingcount), SUM(tcontactingtimesum), MAX(tcontactingtimemax), MIN(tcontactingtimemin), '
        + N'SUM(tvoicemailcount), SUM(tvoicemailtimesum), MAX(tvoicemailtimemax), MIN(tvoicemailtimemin), '
        + N'SUM(av1count), SUM(av1timesum), MAX(av1timemax), MIN(av1timemin), '
        + N'SUM(av2count), SUM(av2timesum), MAX(av2timemax), MIN(av2timemin), '
        + N'SUM(av3count), SUM(av3timesum), MAX(av3timemax), MIN(av3timemin), '
        + N'SUM(av4count), SUM(av4timesum), MAX(av4timemax), MIN(av4timemin), '
        + N'SUM(av5count), SUM(av5timesum), MAX(av5timemax), MIN(av5timemin), '
        + N'SUM(av6count), SUM(av6timesum), MAX(av6timemax), MIN(av6timemin), '
        + N'SUM(av7count), SUM(av7timesum), MAX(av7timemax), MIN(av7timemin), '
        + N'SUM(av8count), SUM(av8timesum), MAX(av8timemax), MIN(av8timemin), '
        + N'SUM(av9count), SUM(av9timesum), MAX(av9timemax), MIN(av9timemin), '
        + N'SUM(av10count), SUM(av10timesum), MAX(av10timemax), MIN(av10timemin), '
        + N'GETUTCDATE() AS updated '
        + N'FROM userInteractionData '
        + N'WHERE startdate BETWEEN ''' + CAST(@StartDate AS NVARCHAR(25)) + N''' AND ''' + CAST(@EndDate AS NVARCHAR(25)) + N''' '
        + N'GROUP BY userid + ''|'' + direction + ''|'' + ISNULL(queueid,''NOQUEUE'') + ''|'' + mediatype + ''|'' + ISNULL(wrapupcode,''NOWRAP'') + ''|'' + ''' 
        + CAST(DATEADD(MINUTE, @Offset, @StartDate) AS NVARCHAR(25)) + N''', '
        + N'userid, direction, queueid, mediatype, wrapupcode';

    PRINT 'Insert New Rows';
    PRINT @InssqlCommand;  -- Check for syntax errors before execution

    EXEC sp_executesql @InssqlCommand;
END;
GO


