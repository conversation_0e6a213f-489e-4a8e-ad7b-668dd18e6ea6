CREATE TABLE IF NOT EXISTS odcampaigndetails (
    id varchar(50),
    name varchar(200),
    datecreated timestamp without time zone,
    datecreatedltc timestamp without time zone,
    datemodified timestamp without time zone,
    datemodifiedltc timestamp without time zone,
    version integer,
    automatictimezonemapping number,
    division varchar(50),
    divisionname varchar(200),
    contactlist varchar(50),
    contactlistname varchar(200),
    script varchar(50),
    scriptname varchar(200),
    queue varchar(50),
    dialingmode varchar(50),
    campaignstatus varchar(50),
    abandonrate numeric(20, 2),
    callanalysisresponseset varchar(50),
    callanalysisresponsesetname varchar(200),
    callername varchar(200),
    calleraddress varchar(200),
    outboundlinecount int,
    skippreviewdisabled number,
    previewtimeoutseconds numeric(20, 2),
    singlenumberpreview number,
    alwaysrunning number,
    noanswertimeout decimal(20, 2),
    priority int,
    updated timestamp without time zone,
    PRIMARY KEY (id)
);