-- Check if table exists before creating
IF dbo.csg_table_exists('dimension_date') = 0
BEGIN
    CREATE TABLE dimension_date
    (
        date_dim_id              INT NOT NULL PRIMARY KEY,
        date_actual              DATE NOT NULL,
        epoch                    BIGINT NOT NULL,
        day_suffix               VARCHAR(4) NOT NULL,
        day_name                 VARCHAR(9) NOT NULL,
        day_of_week              INT NOT NULL,
        day_of_month             INT NOT NULL,
        day_of_quarter           INT NOT NULL,
        day_of_year              INT NOT NULL,
        week_of_month            INT NOT NULL,
        week_of_year             INT NOT NULL,
        week_of_year_iso         CHAR(10) NOT NULL,
        month_actual             INT NOT NULL,
        month_name               VARCHAR(9) NOT NULL,
        month_name_abbreviated   CHAR(3) NOT NULL,
        quarter_actual           INT NOT NULL,
        quarter_name             VARCHAR(9) NOT NULL,
        year_actual              INT NOT NULL,
        first_day_of_week        DATE NOT NULL,
        last_day_of_week         DATE NOT NULL,
        first_day_of_month       DATE NOT NULL,
        last_day_of_month        DATE NOT NULL,
        first_day_of_quarter     DATE NOT NULL,
        last_day_of_quarter      DATE NOT NULL,
        first_day_of_year        DATE NOT NULL,
        last_day_of_year         DATE NOT NULL,
        mmyyyy                   CHAR(6) NOT NULL,
        mmddyyyy                 CHAR(10) NOT NULL,
        weekend_indr             BIT NOT NULL
    );
END
ELSE
BEGIN
    -- If table exists, truncate it to refresh the data
    TRUNCATE TABLE dimension_date;
END

INSERT INTO dimension_date
SELECT
    CAST(FORMAT(datum, 'yyyyMMdd') AS INT) AS date_dim_id,
    datum AS date_actual,
    DATEDIFF(SECOND, '1970-01-01', datum) AS epoch,
    FORMAT(datum, 'Dth') AS day_suffix,
    FORMAT(datum, 'dddd') AS day_name,
    DATEPART(WEEKDAY, datum) AS day_of_week,
    DATEPART(DAY, datum) AS day_of_month,
    DATEPART(DAY, datum) - DATEPART(DAY, DATEADD(QUARTER, DATEDIFF(QUARTER, 0, datum), 0)) + 1 AS day_of_quarter,
    DATEPART(DAYOFYEAR, datum) AS day_of_year,
    DATEPART(WEEK, datum) AS week_of_month,
    DATEPART(WEEK, datum) AS week_of_year,
    FORMAT(datum, 'IYYY-WIW') AS week_of_year_iso,
    DATEPART(MONTH, datum) AS month_actual,
    FORMAT(datum, 'MMMM') AS month_name,
    FORMAT(datum, 'MMM') AS month_name_abbreviated,
    DATEPART(QUARTER, datum) AS quarter_actual,
    CASE
        WHEN DATEPART(QUARTER, datum) = 1 THEN 'First'
        WHEN DATEPART(QUARTER, datum) = 2 THEN 'Second'
        WHEN DATEPART(QUARTER, datum) = 3 THEN 'Third'
        ELSE 'Fourth'
    END AS quarter_name,
    DATEPART(YEAR, datum) AS year_actual,
    DATEADD(DAY, 1 - DATEPART(WEEKDAY, datum), datum) AS first_day_of_week,
    DATEADD(DAY, 7 - DATEPART(WEEKDAY, datum), datum) AS last_day_of_week,
    DATEFROMPARTS(YEAR(datum), MONTH(datum), 1) AS first_day_of_month,
    DATEADD(DAY, -1, DATEADD(MONTH, 1, DATEFROMPARTS(YEAR(datum), MONTH(datum), 1))) AS last_day_of_month,
    DATEADD(QUARTER, DATEDIFF(QUARTER, 0, datum), 0) AS first_day_of_quarter,
    DATEADD(DAY, -1, DATEADD(QUARTER, DATEDIFF(QUARTER, 0, datum) + 1, 0)) AS last_day_of_quarter,
    DATEFROMPARTS(YEAR(datum), 1, 1) AS first_day_of_year,
    DATEFROMPARTS(YEAR(datum), 12, 31) AS last_day_of_year,
    FORMAT(datum, 'MMyyyy') AS mmyyyy,
    FORMAT(datum, 'MMddyyyy') AS mmddyyyy,
    CASE
        WHEN DATEPART(WEEKDAY, datum) IN (1, 7) THEN 0
        ELSE 1
    END AS weekend_indr
FROM (
    SELECT DATEADD(DAY, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) - 1, '2018-01-01') AS datum
    FROM master.dbo.spt_values
    WHERE type = 'P' AND number BETWEEN 0 AND 29219
) DQ
ORDER BY 1;
