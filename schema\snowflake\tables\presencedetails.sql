CREATE TABLE IF NOT EXISTS presencedetails (
    id varchar(50) NOT NULL,
    systempresence varchar(255),
    orgpresence varchar(255),
    deactivated number,
    type varchar(50),
    divisionid varchar(50),
    updated timestamp without time zone,
    CONSTRAINT presencedetails_pkey PRIMARY KEY (id)
) ;

ALTER TABLE presencedetails 
ADD column IF NOT exists divisionid varchar(50);


CREATE OR REPLACE PROCEDURE drop_primaryfield()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  var sql_command = "SELECT COUNT(*) AS column_count FROM information_schema.columns WHERE table_name = 'PRESENCEDETAILS' AND column_name = 'PRIMARYFIELD';";
  var stmt = snowflake.createStatement({sqlText: sql_command});
  var resultSet = stmt.execute();

  if (resultSet.next()) {
    var column_count = resultSet.getColumnValue("COLUMN_COUNT");
    if (column_count > 0) {
      sql_command = "ALTER TABLE presencedetails DROP COLUMN PRIMARYFIELD;";
      stmt = snowflake.createStatement({sqlText: sql_command});
      stmt.execute();
      return "Column PRIMARYFIELD dropped successfully.";
    } else {
      return "Column PRIMARYFIELD does not exist.";
    }
  } else {
    return "Error occurred while checking for column PRIMARYFIELD.";
  }
$$;

CALL drop_primaryfield();
