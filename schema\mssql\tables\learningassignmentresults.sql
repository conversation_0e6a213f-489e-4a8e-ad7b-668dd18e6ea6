-- Learning Assignment Results Table
-- Stores detailed results and completion data for learning module assignments
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user completed the assignment
-- - moduleid: Links to learningmodules.id to identify which learning module was completed
-- - Correlates with learningmoduleassignments table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment result response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningmoduleassignments on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

IF dbo.csg_table_exists('learningassignmentresults') = 0
CREATE TABLE [learningassignmentresults] (
    [id] VARCHAR(50) NOT NULL,
    [userid] VARCHAR(50), -- User ID from Genesys Cloud - links assignment results to specific users
    [moduleid] VARCHAR(50), -- Learning Module ID - links results to specific learning modules (standardized lowercase)
    [assessmentId] VARCHAR(50),
    [assessmentFormId] VARCHAR(50),
    [passPercent] DECIMAL(20, 2),
    [assessmentPercentageScore] DECIMAL(20, 2),
    [assessmentCompletionPercentage] DECIMAL(20, 2),
    [completionPercentage] DECIMAL(20, 2),
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [updated] DATETIME,
    CONSTRAINT [learningassignmentresults_pkey] PRIMARY KEY ([id])
);

-- Add missing columns if they don't exist (for existing installations)
IF dbo.csg_column_exists('learningassignmentresults', 'userid') = 0
    ALTER TABLE learningassignmentresults ADD userid VARCHAR(50) NULL;

-- Standardize moduleId to moduleid for consistency with learningmoduleassignments table
IF dbo.csg_column_exists('learningassignmentresults', 'moduleId') = 1 AND dbo.csg_column_exists('learningassignmentresults', 'moduleid') = 0
BEGIN
    -- Add new moduleid column
    ALTER TABLE learningassignmentresults ADD moduleid VARCHAR(50) NULL;
    -- Copy data from old column to new column
    UPDATE learningassignmentresults SET moduleid = moduleId;
    -- Drop old column
    ALTER TABLE learningassignmentresults DROP COLUMN moduleId;
END;
