CREATE OR ALTER VIEW [vwSurveyQuestionGroupScores] AS
SELECT
    surveyQuestionGroupScores.surveyid,
    surveyQuestionGroupScores.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyQuestionGroupScores.surveyformid,
    surveyQuestionGroupScores.surveyname,
    surveyQuestionGroupScores.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyQuestionGroupScores.agentteamid,
    surveyQuestionGroupScores.queueid,
    queue.name AS queuename,
    surveyQuestionGroupScores.questiongroupid,
    surveyQuestionGroupScores.questiongroupname,
    surveyQuestionGroupScores.questiongrouptotalscore,
    surveyQuestionGroupScores.questiongroupmaxtotalscore,
    surveyQuestionGroupScores.questiongroupmarkedna,
    surveyQuestionGroupScores.updated
FROM
    surveyQuestionGroupScores
    LEFT JOIN surveydata survey ON survey.surveyid = surveyQuestionGroupScores.surveyid
    LEFT JOIN userdetails agent ON agent.id = surveyQuestionGroupScores.agentid
    LEFT JOIN userdetails manager ON manager.id = agent.manager
    LEFT JOIN queuedetails queue ON queue.id = surveyQuestionGroupScores.queueid;
