CREATE TABLE IF NOT EXISTS timeoffrequestdata (
    keyid varchar(100) NOT NULL,
    id varchar(50),
    userid varchar(50),
    isfulldayrequest number,
    status varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    notes varchar(400),
    timeoffduration integer,
    submittedbyid varchar(50),
    submittedate timestamp without time zone,
    reviewedbyid varchar(50),
    revieweddate timestamp without time zone,
    modifiedbyid varchar(50),
    modifieddate timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT timeoffrequestdata_pkey PRIMARY KEY (keyid)
);