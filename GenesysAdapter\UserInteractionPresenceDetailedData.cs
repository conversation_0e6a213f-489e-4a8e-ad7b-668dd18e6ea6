﻿using System.Data;
using DBUtils;
using Microsoft.Extensions.Logging;
using StandardUtils;

namespace GenesysAdapter;

#nullable enable
class UserInteractionPresenceDetailedData
{
    // Conversation segments not containing the segment types below will be disregarded as they aren't related to
    // interacting. All consecutive segments are merged together regardless of the segment type, but will only be added
    // as a row if at least one of the required segments are present in the merged segment.
    private string[] _mustHaveSegmentType = new[]{"interact", "wrapup"};
    // Small gaps between conversation segments can be attributed to the following segment.
    private TimeSpan _joinThreshold = TimeSpan.FromSeconds(5);
    private TimeZoneInfo _timeZone;
    private TimeSpan _maxSyncSpan;
    private TimeSpan _lookBackSpan;
    private readonly ILogger? _logger;

    public UserInteractionPresenceDetailedData(ILogger logger, TimeZoneInfo timeZone, TimeSpan? syncSpan, TimeSpan? lookBackSpan = null)
    {
        _logger = logger;
        _timeZone = timeZone;
        _maxSyncSpan = syncSpan ?? TimeSpan.FromDays(1);
        _lookBackSpan = lookBackSpan ?? TimeSpan.FromHours(2);
    }

    private class TableRow
    {
        private DataRow _row;
        TimeZoneInfo _customerTimeZone;

        public TableRow(DataRow row, TimeZoneInfo timeZone)
        {
            _row = row;
            _customerTimeZone = timeZone;
        }

        public TableRow(DataRow row, TimeZoneInfo timeZone, DataRow cloneFrom) : this(row, timeZone)
        {
            foreach (DataColumn col in cloneFrom.Table.Columns)
            {
                _row[col.ColumnName] = cloneFrom[col.ColumnName];
            }
        }

        public DataRow DataRow { get => _row; }

        public object this[string key]
        {
            get => _row[key];
            set
            {
                if (value != _row[key])
                {
                    _row[key] = value;
                    FieldChanged(key);
                }
            }
        }

        private void FieldChanged(string key)
        {
            // Ensure the LTC times are consistently set
            if (key.Equals("startTime", StringComparison.OrdinalIgnoreCase)
                && this["startTime"] != null)
                _row["startTimeLtc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)_row["startTime"], _customerTimeZone);

            if (key.Equals("endTime", StringComparison.OrdinalIgnoreCase)
                && this["endTime"] != null)
                _row["endTimeLtc"] = TimeZoneInfo.ConvertTimeFromUtc((DateTime)_row["endTime"], _customerTimeZone);

            // Ensure the timeInState field is kept current
            if (this["startTime"] != null && this["endTime"] != null)
            {
                decimal duration = (decimal)((DateTime)this["endTime"] - (DateTime)this["startTime"]).TotalSeconds;
                _row["timeInState"] = duration;
            }

            if (!key.Equals("updated", StringComparison.OrdinalIgnoreCase))
                _row["updated"] = DateTime.UtcNow;

            if ( key.Equals("userId", StringComparison.OrdinalIgnoreCase)
                || key.Equals("routingStatus", StringComparison.OrdinalIgnoreCase)
                || key.Equals("conversationId", StringComparison.OrdinalIgnoreCase))
            {
                _row["keyId"] =
                    _row["userId"] + "|" +
                    _row["routingStatus"] + "|" +
                    _row["conversationId"];
            }
        }
    }

    /// <summary>
    /// Populate the UserInteractionPresenceDetailedData table with a merge of the userPresenceDetailedData and
    /// detailedInteractionData tables.
    /// </summary>
    /// <remarks>
    /// <para>
    /// This table is intended from the presence point of view to enrich the presence data with the conversation segment
    /// data occurring during the user presence blocks.
    /// </para>
    /// <para>
    /// Genesys doesn't link presence data with conversation data and does not intend the two tables to be merged, as
    /// such there are a number of considerations and complications when merging this data:
    /// <list>
    /// <item>
    /// Email segments can be interacting at the same time as segments of other media types. This means if the
    /// interaction time is summed the time can be higher than expected.
    /// </item>
    /// <item>
    /// The wrapup segment of a voice call can overlap the interacting segment of another voice call this could
    /// potentially be handled by not considering wrapup segments if they overlap, but would require refactoring the way
    /// the code works.
    /// </item>
    /// <item>
    /// There can be periods where the presence data reports the user as interacting, but the user has no active
    /// conversations.
    /// </item>
    /// <item>
    /// There can be periods where the presence data reports the user as idle, but the user has active conversations.
    /// </item>
    /// </list>
    /// As such, this table should be used for workforce management reporting, i.e. Gantt charts of interacting time
    /// to allow the exclusion of specific media types (i.e., email) or to get a list of conversations a user is
    /// interacting with.
    /// </para>
    /// <para>
    /// Other points:
    /// <list>
    /// <item>
    /// No Genesys calls are performed. Requires the two tables to be populated by other means.
    /// </item>
    /// <item>
    /// Requires a number of SQL queries to detailedInteractionData, has a fairly high performance overhead.
    /// </item>
    /// </list>
    /// </para>
    /// <para>
    /// An example of how the tables will be merged:
    /// |- Routing status IDLE --|- Routing status INTERACTING --------------------------------------------------------|
    /// |                             | - Conversation 1 --- |
    /// |                                                    |- COnversation 2 -|
    /// |                                                          |- Conversation 3 --|
    /// |                                                                                  |- Conversation 4 -|
    /// |                                                                                                     |- Conv5-|
    /// Will result in the following 8 rows
    /// | A                      | B  | C                    | D                |
    ///                                                            | E                 | F | G                | H      |
    ///                                                            XXXXXXXXXXXXXX &lt;- overlapping conversations
    /// </para>
    /// <para>
    /// NOTE: The UserInteractionPresenceDetailedData table should not be used for aggregation of interaction time.
    /// </para>
    /// </remarks>
    public void UpdateUserInteractionPresenceDetailedData()
    {
        var ucaUtils = new StandardUtils.Utils();
        DataTable clientFeatures = ucaUtils.GetGCCustomerConfig();
        System.Diagnostics.Stopwatch timer = System.Diagnostics.Stopwatch.StartNew();
        DateTime segmentStartTime = DateTime.MinValue;
        DateTime segmentEndTime = DateTime.MinValue;
        using DataTable userInteractionPresenceDetailedData = new DataTable(nameof(userInteractionPresenceDetailedData));

        DBUtils.DBUtils db = new();
        db.Initialize();

        DateTime lastSync = db.GetSyncLastUpdate(nameof(userInteractionPresenceDetailedData).ToLower());

        // This sync job relies on the detailedInteractionData and userPresenceDetailedData tables being up-to-date.
        DateTime lastSyncInteractions = db.GetSyncLastUpdate("detailedInteractionData".ToLower());
        DateTime lastSyncPresence = db.GetSyncLastUpdate("userPresenceDetailedData".ToLower());
        // TODO: When interaction has been fixed to handle in-progress conversations the 4 hour delay will no longer be
        // required.
        DateTime nowTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(4));
        DateTime lastSyncDependencies = new[]{lastSyncInteractions, lastSyncPresence, nowTime}.Min();
        Console.WriteLine(
            "{0}: Dependency sync times, interaction {1}Z, presence {2}Z, min {3}Z",
            nameof(userInteractionPresenceDetailedData),
            lastSyncInteractions.ToString("s"),
            lastSyncPresence.ToString("s"),
            lastSyncDependencies.ToString("s"));

        if (lastSyncDependencies > lastSync.Add(_maxSyncSpan))
            lastSyncDependencies = lastSync.Add(_maxSyncSpan);

        // Apply LookBackSpan to create effective synchronization window
        DateTime syncFrom = lastSync.Subtract(_lookBackSpan).AddMinutes(-1);
        DateTime syncTo = lastSyncDependencies.AddMinutes(-1);

        _logger?.LogInformation("InteractionPresence: Effective sync window - From: {SyncFrom}, To: {SyncTo}, LookBackSpan: {LookBackSpan}, MaxSyncSpan: {MaxSyncSpan}",
            syncFrom, syncTo, _lookBackSpan, _maxSyncSpan);
        if (syncFrom >= syncTo)
        {
            Console.WriteLine(
                "{0}: Previous sync {1}Z at or ahead of dependency tables {2}Z, nothing to do",
                nameof(userInteractionPresenceDetailedData),
                lastSync.ToString("s"),
                lastSyncDependencies.ToString("s")
            );
            return;
        }

        Console.WriteLine(
            "{0}: sync {1}Z to {2}Z",
            nameof(userInteractionPresenceDetailedData),
            syncFrom.ToString("s"),
            syncTo.ToString("s"));

        // Copying data from userPresenceDetailedData
        // TODO: Switch to SQL bind parameters when the database code is uplifted to support it.
        string sql = string.Format(
            "SELECT * FROM userPresenceDetailedData " +
            "WHERE endTime IS NOT NULL AND endTime >= '{0}' AND endTime <= '{1}' " +
            "AND timeInState > 0 " +
            "ORDER BY startTime",
            syncFrom.ToString("s"),             // 0
            syncTo.ToString("s")                // 1
        );
        using DataTable? userPresenceDetailedData = db.GetSQLTableData(sql, nameof(userPresenceDetailedData));
        if (userPresenceDetailedData == null)
            throw new DataException($"Failed to retrieve {nameof(userPresenceDetailedData)} table");
        Console.WriteLine("{0}: {1} {2} rows to process",
            nameof(userInteractionPresenceDetailedData),
            userPresenceDetailedData.Rows.Count,
            nameof(userPresenceDetailedData));

        // Taking all columns from userPresenceDetailedData
        foreach (DataColumn col in userPresenceDetailedData.Columns)
        {
            userInteractionPresenceDetailedData.Columns.Add(col.ColumnName, col.DataType);
        }
        // Adding conversationId and mediaType to the destination table
        userInteractionPresenceDetailedData.Columns.Add("conversationId", typeof(string));
        userInteractionPresenceDetailedData.Columns.Add("mediaType", typeof(string));

        TableRow? pendingNewRow = null;
        var progressCounter = 0;
        foreach (DataRow rowPresence in userPresenceDetailedData.Rows)
        {
            progressCounter++;
            if (progressCounter % 1000 == 0)
                Console.WriteLine("{0}: Processed {1}/{2} {3} rows (running time {4:N3} secs)",
                    nameof(userInteractionPresenceDetailedData),    // 0
                    progressCounter,                                // 1
                    userPresenceDetailedData.Rows.Count,            // 2
                    nameof(userPresenceDetailedData),               // 3
                    timer.Elapsed.TotalSeconds);                    // 4
            var routingStatus = rowPresence["routingStatus"].ToString();
            // Directly copy in rows where the routingStatus is not interacting.
            if (!String.Equals(routingStatus, "interacting", StringComparison.OrdinalIgnoreCase))
            {
                pendingNewRow = new TableRow(
                    userInteractionPresenceDetailedData.NewRow(),
                    timeZone: _timeZone,
                    cloneFrom: rowPresence);
                userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                pendingNewRow = null;
                continue;
            }

            // Get all conversation segments starting or ending in the period where the user is interacting.
            // TODO: Remove null condition on segmentEndDate and throw an error once interaction job is fixed to
            // correctly wait for in-progress conversations to end.
            sql = string.Format(
                "SELECT conversationId, mediaType, segmentType, " +
                "segmentStartDate, segmentEndDate " + 
                "FROM detailedInteractionData " +
                "WHERE userid = '{0}' and " +
                "segmentType != 'hold' and " +
                "purpose = 'agent' and " +
                "((segmentStartDate >= '{1}' AND segmentStartDate <= '{2}' AND segmentEndDate IS NOT NULL) " +
                "OR (segmentEndDate >= '{1}' AND segmentEndDate <= '{2}')) " +
                "ORDER BY segmentStartDate",
                rowPresence["userId"],                              // 0
                ((DateTime)rowPresence["startTime"]).ToString("s"), // 1
                ((DateTime)rowPresence["endTime"]).ToString("s")    // 2
            );
            using DataTable? detailedInteractionData = db.GetSQLTableData(sql, nameof(detailedInteractionData));
            if (detailedInteractionData == null)
                throw new DataException($"Failed to retrieve {nameof(detailedInteractionData)} table");

            bool mergedSegmentRequirementsMet = false;
            // Segment with the latest endTime that has been added to the table.
            TableRow? latestSegment = null;
            if (detailedInteractionData.Rows.Count == 0)
            {
                // No conversation segments in an interaction presence state, just add the presence state.
                pendingNewRow = new TableRow(
                    userInteractionPresenceDetailedData.NewRow(),
                    timeZone: _timeZone,
                    cloneFrom: rowPresence);
                userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                pendingNewRow = null;
                continue;
            }
            foreach (DataRow rowInteraction in detailedInteractionData.Rows)
            {
                if (pendingNewRow != null
                    && pendingNewRow["conversationId"].ToString() == rowInteraction["conversationId"].ToString())
                {
                    // Another segment of the same conversation. Summarise all consecutive conversation segments
                    // together.
                    var segmentType = (string)rowInteraction["segmentType"];
                    if (!mergedSegmentRequirementsMet && _mustHaveSegmentType.Contains(segmentType))
                        mergedSegmentRequirementsMet = true;

                    pendingNewRow["endTime"] = (DateTime)rowInteraction["segmentEndDate"];
                    continue;
                }
                // New conversationId, add the pending row if requirements are met.
                if (mergedSegmentRequirementsMet
                    && pendingNewRow != null
                    && (decimal)pendingNewRow["timeInState"] > 0)
                {
                    userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                    if (latestSegment == null
                        || (DateTime)pendingNewRow["endTime"] > (DateTime)latestSegment["endTime"])
                    {
                        latestSegment = pendingNewRow;
                    }
                    pendingNewRow = null;
                }

                pendingNewRow = new TableRow(
                    userInteractionPresenceDetailedData.NewRow(),
                    timeZone: _timeZone,
                    cloneFrom: rowPresence);

                segmentStartTime = (DateTime)rowInteraction["segmentStartDate"];
                segmentEndTime = (DateTime)rowInteraction["segmentEndDate"];
                DateTime previousEndTime = (DateTime)rowPresence["startTime"];
                if (latestSegment != null && (DateTime)latestSegment["endTime"] > previousEndTime)
                    previousEndTime = (DateTime)latestSegment["endTime"];

                if (segmentStartTime >= (DateTime)rowPresence["endTime"])
                {
                    pendingNewRow = null;
                    continue;
                }
                else if (previousEndTime < segmentStartTime
                        && previousEndTime.Add(_joinThreshold) >= segmentStartTime)
                {
                    // There is a gap between this and the previous interaction segment, but it is below the threshold.
                    // This prevents a huge number of rows for 1 - 2 second gaps between conversation segments.
                    segmentStartTime = previousEndTime;
                }
                else if (previousEndTime < segmentStartTime)
                {
                    // There is a gap between this and the previous interaction segment greater than the threshold,
                    // add a padding row.
                    pendingNewRow["startTime"] = previousEndTime;
                    pendingNewRow["endTime"] = segmentStartTime;
                    if ((decimal)pendingNewRow["timeInState"] > 0)
                    {
                        userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                        if (latestSegment == null
                            || (DateTime)pendingNewRow["endTime"] > (DateTime)latestSegment["endTime"])
                        {
                            latestSegment = pendingNewRow;
                        }
                        pendingNewRow = null;
                    }
                    pendingNewRow = new TableRow(
                        userInteractionPresenceDetailedData.NewRow(),
                        timeZone: _timeZone,
                        cloneFrom: rowPresence);
                }
                else if (segmentStartTime < (DateTime)rowPresence["startTime"])
                {
                    // The interaction segment started before the presence changed to interacting
                    segmentStartTime = (DateTime)rowPresence["startTime"];
                }
                // Prepare the current interaction segment to be added as a row
                pendingNewRow["startTime"] = segmentStartTime;
                pendingNewRow["endTime"] = segmentEndTime;
                pendingNewRow["conversationId"] = rowInteraction["conversationId"];
                pendingNewRow["mediaType"] = rowInteraction["mediaType"];
            }
            if (mergedSegmentRequirementsMet
                && pendingNewRow != null
                && (decimal)pendingNewRow["timeInState"] > 0)
            {
                userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                if (latestSegment == null
                    || (DateTime)pendingNewRow["endTime"] > (DateTime)latestSegment["endTime"])
                {
                    latestSegment = pendingNewRow;
                }
                pendingNewRow = null;
            }
            if (latestSegment != null
               && (DateTime)latestSegment["endTime"] < (DateTime)rowPresence["endTime"])
            {
                pendingNewRow = new TableRow(
                    userInteractionPresenceDetailedData.NewRow(),
                    timeZone: _timeZone,
                    cloneFrom: rowPresence);
                pendingNewRow["startTime"] = (DateTime)latestSegment["endTime"];
                userInteractionPresenceDetailedData.Rows.Add(pendingNewRow.DataRow);
                pendingNewRow = null;
            }
        }
        Console.WriteLine("{0}: {1} total rows to merge",
            nameof(userInteractionPresenceDetailedData),
            userInteractionPresenceDetailedData.Rows.Count);

        db.WriteSQLDataBulk(userInteractionPresenceDetailedData, new List<string>{"keyid", "starttime"});
        db.SetSyncLastUpdate(syncTo, nameof(userInteractionPresenceDetailedData).ToLower());
        Console.WriteLine("{0}: took {1:N3} secs",
            nameof(userInteractionPresenceDetailedData),
            timer.Elapsed.TotalSeconds);
    }
}
#nullable restore
