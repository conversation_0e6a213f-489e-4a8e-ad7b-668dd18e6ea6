using System;

namespace GenesysAdapter.UnitTests;

public class Utils_Tests
{
    readonly StandardUtils.Utils _utils;

    public Utils_Tests()
    {
        _utils = new StandardUtils.Utils();
    }

    [Theory]
    [InlineData(1666493109, "2022-10-23T02:45:09.000Z")]
    public void Utils_ConvertFromUnixTimestamp(double unixTimestamp, string expectedDate)
    {
        DateTime result = _utils.ConvertFromUnixTimestamp(unixTimestamp);
        DateTime expect = DateTime.Parse(expectedDate).ToUniversalTime();
        Assert.Equal(result, expect);
    }

    [Theory]
    [InlineData(1666493109151, "2022-10-23T02:45:09.151Z")]
    public void Utils_ConvertFromUnixTimestampMS(double unixTimestamp, string expectedDate)
    {
        DateTime result = _utils.ConvertFromUnixTimestampMS(unixTimestamp);
        DateTime expect = DateTime.Parse(expectedDate).ToUniversalTime();
        Assert.Equal(result, expect);
    }

    [Theory]
    [InlineData("2022-10-23T02:45:09.251Z", 1666493109)]
    [InlineData("2022-10-23T02:45:09.651Z", 1666493109)] // Rounds down.
    public void Utils_ConvertToUnixTimestamp(string date, double expect)
    {

        DateTime dateParsed = DateTime.Parse(date).ToUniversalTime();
        double result = _utils.ConvertToUnixTimestamp(dateParsed);
        Assert.Equal(result, expect);
    }
}
