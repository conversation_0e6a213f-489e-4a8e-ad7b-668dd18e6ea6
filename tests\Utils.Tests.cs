using System;
using Newtonsoft.Json;
using Xunit;

namespace GenesysAdapter.UnitTests;

public class Utils_Tests
{
    readonly StandardUtils.Utils _utils;

    public Utils_Tests()
    {
        _utils = new StandardUtils.Utils();
    }

    [Theory]
    [InlineData(1666493109, "2022-10-23T02:45:09.000Z")]
    public void Utils_ConvertFromUnixTimestamp(double unixTimestamp, string expectedDate)
    {
        DateTime result = _utils.ConvertFromUnixTimestamp(unixTimestamp);
        DateTime expect = DateTime.Parse(expectedDate, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        Assert.Equal(result, expect);
    }

    [Theory]
    [InlineData(1666493109151, "2022-10-23T02:45:09.151Z")]
    public void Utils_ConvertFromUnixTimestampMS(double unixTimestamp, string expectedDate)
    {
        DateTime result = _utils.ConvertFromUnixTimestampMS(unixTimestamp);
        DateTime expect = DateTime.Parse(expectedDate, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        Assert.Equal(result, expect);
    }

    [Theory]
    [InlineData("2022-10-23T02:45:09.251Z", 1666493109)]
    [InlineData("2022-10-23T02:45:09.651Z", 1666493109)] // Rounds down.
    public void Utils_ConvertToUnixTimestamp(string date, double expect)
    {

        DateTime dateParsed = DateTime.Parse(date, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        double result = _utils.ConvertToUnixTimestamp(dateParsed);
        Assert.Equal(result, expect);
    }

    [Fact]
    public void DateTime_JsonDeserialization_ShouldHaveCorrectKind()
    {
        // Test how DateTime objects are deserialized from JSON
        string jsonWithDateTime = @"{""SegmentStart"": ""2022-10-23T02:45:09.251Z"", ""SegmentEnd"": ""2022-10-23T03:45:09.251Z""}";

        var testObject = JsonConvert.DeserializeObject<TestSegment>(jsonWithDateTime,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });

        // Check what DateTimeKind the deserialized DateTime has
        Assert.NotNull(testObject);
        Assert.Equal(DateTimeKind.Utc, testObject.SegmentStart.Kind);
        Assert.Equal(DateTimeKind.Utc, testObject.SegmentEnd.Kind);

        // Test ToUniversalTime() behavior
        var originalStart = testObject.SegmentStart;
        var convertedStart = testObject.SegmentStart.ToUniversalTime();

        // If Kind is already UTC, ToUniversalTime() should return the same value
        Assert.Equal(originalStart, convertedStart);
    }

    public class TestSegment
    {
        public DateTime SegmentStart { get; set; }
        public DateTime SegmentEnd { get; set; }
    }
}
