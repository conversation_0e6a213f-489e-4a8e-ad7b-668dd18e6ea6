DROP VIEW IF EXISTS vwuserqueuemappings CASCADE;
CREATE OR REPLACE VIEW vwuserqueuemappings AS
SELECT 
    qm.queueid AS queueid,
    qd.name AS queuename,
    qm.userid AS userid,
    ud.name AS name,
    ud.department AS department,
    ud.managername AS managername,
    qm.divisionid AS divisionid
FROM
    userQueueMappings qm 
    LEFT OUTER JOIN vwUserDetail ud ON qm.userid = ud.id
    LEFT OUTER JOIN vwQueueDetails qd ON qm.queueid = qd.id;

COMMENT ON COLUMN vwuserqueuemappings.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwuserqueuemappings.queuename IS 'Queue Name';
COMMENT ON COLUMN vwuserqueuemappings.userid IS 'User GUID';
COMMENT ON COLUMN vwuserqueuemappings.name IS 'User Name';
COMMENT ON COLUMN vwuserqueuemappings.department IS 'Department';
COMMENT ON COLUMN vwuserqueuemappings.managername IS 'Manager Name';
COMMENT ON COLUMN vwuserqueuemappings.divisionid IS 'Division GUID';
COMMENT ON VIEW vwuserqueuemappings IS 'UserQueueMappings: View for mappings in between User and Queue';
