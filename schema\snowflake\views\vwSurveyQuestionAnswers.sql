DROP VIEW IF EXISTS vwSurveyQuestionAnswers;
CREATE
OR REPLACE VIEW vwSurveyQuestionAnswers AS
SELECT surveyquestionanswers.surveyid,
    surveyquestionanswers.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyquestionanswers.surveyformid,
    surveyquestionanswers.surveyname,
    surveyquestionanswers.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyquestionanswers.agentteamid,
    surveyquestionanswers.queueid,
    queue.name AS queuename,
    surveyquestionanswers.questiongroupid,
    surveyquestionanswers.questiongroupname,
    surveyquestionanswers.questionid,
    surveyquestionanswers.questiontext,
    surveyquestionanswers.questiontype,
    surveyquestionanswers.questionanswerid,
    surveyquestionanswers.questionanswertext,
    surveyquestionanswers.questionanswervalue,
    surveyquestionanswers.questionscore,
    surveyquestionanswers.questionmarkedna,
    surveyquestionanswers.updated
FROM surveyquestionanswers
    LEFT JOIN surveydata survey ON survey.surveyid::text = surveyquestionanswers.surveyid::text
    LEFT JOIN userdetails agent ON agent.id::text = surveyquestionanswers.agentid::text
    LEFT JOIN userdetails manager ON manager.id::text = agent.manager::text
    LEFT JOIN queuedetails queue ON queue.id::text = surveyquestionanswers.queueid::text;

-- spell-checker: ignore: surveyid questiongroupid questionid questionanswerid