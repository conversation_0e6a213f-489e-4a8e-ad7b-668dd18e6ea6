﻿using System;
using System.Data;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class GroupConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
            GCApiKey = GCUtilities.GCApiKey;
            JsonActions = new JsonUtils();
        }

        public DataTable GetGroupMembershipDataFromGC(DataTable Groups)
        {
            Console.WriteLine("Retrieving Group Membership");
            DataTable GroupMembership = DBUtil.CreateInMemTable("usergroupMappings");

            foreach (DataRow DRgroup in Groups.Rows)
            {
                int CurrentPage = 1;
                int PageCount = 1;
                JsonActions.MaxPages = 1;
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                int Counter = 0;

                while (CurrentPage <= JsonActions.MaxPages)
                {

                    JsonActions = new JsonUtils();
                    JsonActions.MaxPages = PageCount;

                    if (Counter % 25 == 0)
                    {

                        Console.Write("\nNew Key:");
                        bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
                        GCApiKey = GCUtilities.GCApiKey;

                        Console.Write("\nNew Key:{0}", GCApiKey.Substring(0, 5));
                    }

                    Console.Write("NG:");

                    // Use JsonReturnHttpResponseGet for proper rate limit handling
                    var response = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/groups/" + DRgroup["id"] + "/members?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                    // Check HTTP status code before processing JSON
                    if (response.StatusCode != 200)
                    {
                        if (response.StatusCode == 429)
                        {
                            Console.WriteLine($"Rate limit encountered for group {DRgroup["id"]}, page {CurrentPage}. Response: {response.Content}");
                            throw new Exception($"Rate limiting exceeded retry limit for group {DRgroup["id"]}");
                        }
                        else if (response.StatusCode == 404)
                        {
                            Console.WriteLine($"Group {DRgroup["id"]} not found or no members available");
                            break; // Skip to next group
                        }
                        else
                        {
                            throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                        }
                    }

                    string JsonString = response.Content;

                    if (JsonString.Length > 10)
                    {
                        GroupMembershipObject GroupMembershipData = new GroupMembershipObject();

                        GroupMembershipData = JsonConvert.DeserializeObject<GroupMembershipObject>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore
                                       });

                        JsonActions.MaxPages = GroupMembershipData.pageCount;

                        foreach (GroupMembers JSON in GroupMembershipData.entities)
                        {

                            Console.Write("A:");

                            DataRow GroupMembershipRow = GroupMembership.NewRow();

                            GroupMembershipRow["id"] = DRgroup["id"] + "|" + JSON.id;
                            GroupMembershipRow["userid"] = JSON.id;
                            GroupMembershipRow["groupid"] = DRgroup["id"];
                            GroupMembershipRow["name"] = DRgroup["name"];
                            GroupMembership.Rows.Add(GroupMembershipRow);
                        }
                    }
                    else
                        Console.WriteLine("Group ID {0} Has No Members ", DRgroup["id"]);

                    Counter++;
                    CurrentPage++;
                }
            }
            Console.WriteLine("\nTotal Group Membership:{0} ", GroupMembership.Rows.Count);
            return GroupMembership;
        }

        public DataTable GetGroupDataFromGC()
        {
            Console.WriteLine("Retrieving Groups");
            DataTable Groups = DBUtil.CreateInMemTable("groupDetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int CurrentPage = 1;
            JsonActions.MaxPages = 1;

            while (CurrentPage <= JsonActions.MaxPages)
            {

                Console.Write("*");

                // Use JsonReturnHttpResponseGet for proper rate limit handling
                var response = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/groups?pageSize=100&pageNumber=" + CurrentPage + "&state=active", GCApiKey);

                // Check HTTP status code before processing JSON
                if (response.StatusCode != 200)
                {
                    if (response.StatusCode == 429)
                    {
                        Console.WriteLine($"Rate limit encountered for groups page {CurrentPage}. Response: {response.Content}");
                        throw new Exception($"Rate limiting exceeded retry limit for groups page {CurrentPage}");
                    }
                    else
                    {
                        throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                    }
                }

                string JsonString = response.Content;

                if (JsonString.Length > 10)
                {
                    GroupObject GroupData = new GroupObject();

                    GroupData = JsonConvert.DeserializeObject<GroupObject>(JsonString,
                                    new JsonSerializerSettings
                                    {
                                        NullValueHandling = NullValueHandling.Ignore
                                    });

                    JsonActions.MaxPages = GroupData.pageCount;

                    foreach (Groups JSON in GroupData.entities)
                    {

                        Console.Write("A:");

                        DataRow GroupsRow = Groups.NewRow();

                        GroupsRow["id"] = JSON.id;
                        GroupsRow["name"] = JSON.name;
                        GroupsRow["description"] = JSON.description;
                        GroupsRow["membercount"] = JSON.memberCount;
                        GroupsRow["state"] = JSON.state;
                        GroupsRow["type"] = JSON.type;
                        GroupsRow["selfuri"] = JSON.selfUri;

                        Groups.Rows.Add(GroupsRow);
                    }
                }
                else
                    Console.WriteLine("No Groups found in Organisation");

                CurrentPage += 1;
            }
            Console.WriteLine("\nTotal Groups:{0} ", Groups.Rows.Count);

            return Groups;
        }

        public bool AddGroupsToUserData(ref DataTable Users)
        {
            bool Successful = false;

            try
            {
                Users.Columns.Add("Group", typeof(String));
                DataTable Groups = GetGroupDataFromGC();
                DataTable GroupMembership = GetGroupMembershipDataFromGC(Groups);
                foreach (DataRow DRUser in Users.Rows)
                {
                    StringBuilder UserGroups = new StringBuilder();
                    foreach (DataRow DRMemberGroup in GroupMembership.Select("userid = '" + DRUser["id"] + "'"))
                    {

                        UserGroups.Append((string)DRMemberGroup["name"] + ",");
                    }

                    if (UserGroups.Length > 0)
                        UserGroups.Length = UserGroups.Length - 1;

                    DRUser["Group"] = UserGroups.ToString();

                }
                Users.AcceptChanges();
                Successful = true;

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                Successful = false;
            }

            return Successful;
        }

        internal DataTable CreateGroupsTable()
        {
            DataTable DTTemp = new DataTable("groupDetails");
            
            DTTemp.Columns.Add("id", typeof(string));
            DTTemp.Columns.Add("name", typeof(String));
            DTTemp.Columns.Add("email", typeof(String));
            DTTemp.Columns.Add("membercount", typeof(int));
            DTTemp.Columns.Add("state", typeof(String));
            DTTemp.Columns.Add("type", typeof(String));
            DTTemp.Columns.Add("selfuri", typeof(String));
            DTTemp.Columns.Add("updated", typeof(DateTime));
            
            //Add Key For Emite Purposes
            DataColumn[] key = new DataColumn[1];
            key[0] = DTTemp.Columns[0];
            DTTemp.PrimaryKey = key;

            return DTTemp;
        }

        internal DataTable ConvGroups(dynamic json)
        {

            DataTable DtTemp = CreateGroupsTable();

            JsonActions.ConvJson(json, ref DtTemp);

            return DtTemp;
        }

        internal DataTable CreateGroupMembersTable()
        {
            DataTable DTTemp = new DataTable("groupMembership");
            DTTemp.Columns.Add("id", typeof(string));
            DTTemp.Columns.Add("name", typeof(String));
            DTTemp.Columns.Add("userid", typeof(String));
            DTTemp.Columns.Add("groupid", typeof(String));
            DTTemp.Columns.Add("updated", typeof(DateTime));

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["id"] };

            return DTTemp;
        }
    }

    public class GroupMembershipObject
    {
        public GroupMembers[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class GroupMembers
    {
        public string id { get; set; }
        public string name { get; set; }
        public Division division { get; set; }
        public Chat chat { get; set; }
        public string department { get; set; }
        public string email { get; set; }
        public Primarycontactinfo[] primaryContactInfo { get; set; }
        public Address[] addresses { get; set; }
        public string state { get; set; }
        public string title { get; set; }
        public string username { get; set; }
        public int version { get; set; }
        public bool acdAutoAnswer { get; set; }
        public string selfUri { get; set; }
        public Image[] images { get; set; }
    }

    public class Division
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class Chat
    {
        public string jabberId { get; set; }
    }

    public class Primarycontactinfo
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string display { get; set; }
        public string extension { get; set; }
    }

    public class Address
    {
        public string address { get; set; }
        public string display { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string extension { get; set; }
    }

    public class Image
    {
        public string resolution { get; set; }
        public string imageUri { get; set; }
    }
    
    public class GroupObject
    {
        public Groups[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class Groups
    {
        public string id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string memberCount { get; set; }
        public string state { get; set; }
        public string type { get; set; }
        public string selfUri { get; set; }
    }
}
// spell-checker: ignore: emite
