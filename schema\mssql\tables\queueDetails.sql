IF dbo.csg_table_exists('queueDetails') = 0
CREATE TABLE [queueDetails](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](255),
    [description] [nvarchar](255),
    [divisionid] [nvarchar](50),
    [enabletranscription] [bit],
    [isactive] [bit],
    [callslatargetperc] [decimal](20, 2),
    [callslatargetduration] [int],
    [callbackslatargetperc] [decimal](20, 2),
    [callbackslatargetduration] [int],
    [chatslatargetperc] [decimal](20, 2),
    [chatslatargetduration] [int],
    [emailslatargetperc] [decimal](20, 2),
    [emailslatargetduration] [int],
    [messageslatargetperc] [decimal](20, 2),
    [messageslatargetduration] [int],
    [updated] [datetime],
    CONSTRAINT [PK_queueDetails] PRIMARY KEY ([id])
);

IF dbo.csg_column_exists('queueDetails', 'description') = 0
    ALTER TABLE queueDetails ADD description NVARCHAR(255);
ELSE
    ALTER TABLE queueDetails ALTER COLUMN description NVARCHAR(255);

IF dbo.csg_column_exists('queueDetails', 'enabletranscription') = 0
    ALTER TABLE queueDetails ADD enabletranscription BIT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN enabletranscription BIT;

IF dbo.csg_column_exists('queueDetails', 'isactive') = 0
    ALTER TABLE queueDetails ADD isactive BIT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN isactive BIT;

IF dbo.csg_column_exists('queueDetails', 'callslatargetperc') = 0
    ALTER TABLE queueDetails ADD callslatargetperc FLOAT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN callslatargetperc FLOAT;
IF dbo.csg_column_exists('queueDetails', 'callslatargetduration') = 0
    ALTER TABLE queueDetails ADD callslatargetduration INT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN callslatargetduration INT;

IF dbo.csg_column_exists('queueDetails', 'callbackslatargetperc') = 0
    ALTER TABLE queueDetails ADD callbackslatargetperc FLOAT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN callbackslatargetperc FLOAT;
IF dbo.csg_column_exists('queueDetails', 'callbackslatargetduration') = 0
    ALTER TABLE queueDetails ADD callbackslatargetduration INT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN callbackslatargetduration INT;

IF dbo.csg_column_exists('queueDetails', 'chatslatargetperc') = 0
    ALTER TABLE queueDetails ADD chatslatargetperc FLOAT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN chatslatargetperc FLOAT;
IF dbo.csg_column_exists('queueDetails', 'chatslatargetduration') = 0
    ALTER TABLE queueDetails ADD chatslatargetduration INT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN chatslatargetduration INT;

IF dbo.csg_column_exists('queueDetails', 'emailslatargetperc') = 0
    ALTER TABLE queueDetails ADD emailslatargetperc FLOAT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN emailslatargetperc FLOAT;
IF dbo.csg_column_exists('queueDetails', 'emailslatargetduration') = 0
    ALTER TABLE queueDetails ADD emailslatargetduration INT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN emailslatargetduration INT;

IF dbo.csg_column_exists('queueDetails', 'messageslatargetperc') = 0
    ALTER TABLE queueDetails ADD messageslatargetperc FLOAT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN messageslatargetperc FLOAT;
IF dbo.csg_column_exists('queueDetails', 'messageslatargetduration') = 0
    ALTER TABLE queueDetails ADD messageslatargetduration INT;
ELSE
    ALTER TABLE queueDetails ALTER COLUMN messageslatargetduration INT;