CREATE TABLE IF NOT EXISTS convsummarydata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationenddate timestamp without time zone,
    conversationstartdateltc timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    originaldirection varchar(50),
    firstmediatype varchar(50),
    lastmediatype varchar(50),
    peer varchar(50),
    ani varchar(400),
    dnis varchar(400),
    firstagentid varchar(50),
    lastagentid varchar(50),
    firstqueueid varchar(50),
    lastqueueid varchar(50),
    ttalkcomplete numeric(20, 2),
    tqueuetime numeric(20, 2),
    tacw numeric(20, 2),
    tansweredcount integer,
    tanswered numeric(20, 2),
    tabandonedcount integer,
    tresponsecount integer,
    tresponse numeric(20, 2),
    thandlecount integer,
    thandle numeric(20, 2),
    firstwrapupcode varchar(255),
    lastwrapupcode varchar(255),
    theldcompletecount integer,
    theldcomplete numeric(20, 2),
    nconsulttransferred integer,
    nblindtransferred integer,
    lastdisconnect varchar(50),
    lastpurpose varchar(50),
    lastsegmenttime numeric(20, 2),
    divisionid varchar(50),
    divisionid2 varchar(50),
    divisionid3 varchar(50),
    updated timestamp without time zone,
    CONSTRAINT convsummarynwdata_pkey PRIMARY KEY (keyid, conversationstartdate)
);
