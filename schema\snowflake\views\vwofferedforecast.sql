DROP VIEW IF EXISTS VWOFFEREDFORECAST;
create or replace view VWOFFEREDFORECAST
AS
SELECT DISTINCT
ofd.KEY<PERSON>,
ofd.BUSINESSUNITID,
bu.NAME AS BUSINESSUNITNAME,
ofd.SCHEDU<PERSON>ID,
scd.DE<PERSON><PERSON><PERSON>TION AS SCHEDULEDESC,
ofd.PLANNINGGROUP AS PLANNINGGROUPID,
pgd.NAME AS PLANNINGGROUPNAME,
ofd.SHORTTERMFORECASTID,
ofd.STARTDATE,
ofd.STARTDATELTC,
ofd.WEEKDATE,
ofd.WEEK,
ofd.AV<PERSON><PERSON><PERSON>LEPERINTERVAL,
ofd.OFFEREDPERINTERVAL,
ofd.CANUSEFORSCHEDULING,
ofd.UPDATED
FROM
OFFEREDFORECASTDATA ofd
    LEFT JOIN VWBUDETAILS bu ON bu.id = ofd.BUSINESSUNITID
    LEFT JOIN SCHEDULEDE<PERSON>ILS scd ON scd.SCHEDULEID = ofd.SCHEDULEID
    LEFT JOIN PLAN<PERSON><PERSON><PERSON><PERSON><PERSON>DE<PERSON>ILS pgd ON pgd.id = ofd.<PERSON><PERSON><PERSON><PERSON>NGGROUP;