CREATE OR ALTER VIEW [vwQueueConvRealTime] AS
SELECT
    queuerealtimeconvdata.conversationid,
    queuerealtimeconvdata.media,
    queuerealtimeconvdata.actingas,
    queuerealtimeconvdata.conversationstate,
    DATEDIFF(SECOND, queuerealtimeconvdata.startdate, GETUTCDATE()) AS StatusTimeSecs,
    DATEDIFF(SECOND, queuerealtimeconvdata.startdate, GETUTCDATE()) / 86400.00 AS statusDays,
   FORMAT(
    DATEADD(SECOND, DATEDIFF(SECOND, queuerealtimeconvdata.startdate, GETUTCDATE()), '1900-01-01T00:00:00'),
    'HH:mm:ss'
) AS [StatusTimeFormatted],
    queuerealtimeconvdata.skill1,
    skill1.name AS SkillName1,
    queuerealtimeconvdata.skill2,
    skill2.name AS SkillName2,
    queuerealtimeconvdata.skill3,
    skill3.name AS SkillName3,
    queuerealtimeconvdata.initialpriority,
    queuerealtimeconvdata.participantname,
    queuerealtimeconvdata.queueid,
    queuedetails.name AS queuename,
    queuedetails.divisionid AS division,
    divisiondetails.name AS divisionname,
    queuerealtimeconvdata.userid,
    userdetail.name,
    userdetail.department,
    userdetail.managername,
    queuerealtimeconvdata.direction,
    queuerealtimeconvdata.ani,
    queuerealtimeconvdata.dnis,
    queuerealtimeconvdata.requestedrout1,
    queuerealtimeconvdata.requestedrout2,
    queuerealtimeconvdata.usedrout
FROM
    queuerealtimeconvdata
LEFT OUTER JOIN skilldetails skill1 ON skill1.id = queuerealtimeconvdata.skill1
LEFT OUTER JOIN skillDetails skill2 ON skill2.id = queuerealtimeconvdata.skill2
LEFT OUTER JOIN skillDetails skill3 ON skill3.id = queuerealtimeconvdata.skill3
LEFT OUTER JOIN queuedetails queuedetails ON queuedetails.id = queuerealtimeconvdata.queueid
LEFT OUTER JOIN divisiondetails divisiondetails ON divisiondetails.id = queuedetails.divisionid
LEFT OUTER JOIN vwUserDetail userdetail ON userdetail.id = queuerealtimeconvdata.userid;

