CREATE TABLE IF NOT EXISTS adherencedaydata (
    keyid VARCHAR(100) NOT NULL,
    userid VARCHAR(50),
    startdate TIMESTAMP WITHOUT TIME ZONE,
    startdateltc TIMESTAMP WITHOUT TIME ZONE,
    daystartoffsetsecs INTEGER,
    adherenceperc NUMERIC(20, 2),
    conformperc NUMERIC(20, 2),
    impact VARCHAR(50),
    adherenceschedulesecs INTEGER,
    conformanceschedulesecs INTEGER,
    conformanceactualsecs INTEGER,
    exceptioncount INTEGER,
    exceptiondurationsecs INTEGER,
    impactseconds INTEGER,
    schedulelengthsecs INTEGER,
    actuallengthsecs INTEGER,
    updated TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT adherencedaydata_pkey PRIMARY KEY (keyid)
);

CREATE OR REPLACE PROCEDURE check_and_update_adherencedaydata()
  RETURNS STRING
  LANGUAGE JAVASCRIPT
  EXECUTE AS CALLER
AS
$$
  var KeyidPrefix = 'v1_';
  
  var resultSet = snowflake.execute({
    sqlText: `SELECT COUNT(*) 
              FROM adherencedaydata
              WHERE SUBSTR(keyid, 1, ` + KeyidPrefix.length + `) <> '` + KeyidPrefix + `'`
  });
  
  resultSet.next();
  
  var exists_flag = resultSet.getColumnValue(1);

  if (exists_flag > 0) {
    snowflake.execute({ 
      sqlText: `DELETE FROM adherencedaydata
                WHERE LEFT(keyid, ` + KeyidPrefix.length + `) <> '` + KeyidPrefix + `'`
    });
    
    snowflake.execute({ 
      sqlText: `UPDATE tabledefinitions 
                SET datekeyfield = DATEADD(month, -12, CURRENT_TIMESTAMP()) 
                WHERE tablename = 'adherencedaydata'`
    });
    
    return 'Records deleted and tabledefinitions updated successfully';
  } else {
    return 'No records found with old keyid in use.';
  }
$$;

CALL check_and_update_adherencedaydata();

drop procedure check_and_update_adherencedaydata();