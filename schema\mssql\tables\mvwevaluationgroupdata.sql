IF dbo.csg_table_exists('mvwevaluationgroupdata') = 0
CREATE TABLE mvwevaluationgroupdata (
    [keyid] varchar(50) NOT NULL PRIMARY KEY,
    [evaluationid] varchar(50),
    [questiongroupid] varchar(50),
    [questiongroupname] varchar(200),
    [totalscore] numeric(20, 2),
    [maxtotalscore] numeric(20, 2),
    [markedna] bit,
    [totalcriticalscore] numeric(20, 2),
    [maxtotalcriticalscore] numeric(20, 2),
    [totalnoncriticalscore] numeric(20, 2),
    [maxtotalnoncriticalscore] numeric(20, 2),
    [totalscoreunweighted] numeric(20, 2),
    [maxtotalscoreunweighted] numeric(20, 2),
    [failedkillquestions] bit,
    [divisionid] varchar(50),
    [comments] text NULL,
    [conversationid] varchar(50) NULL
);

IF dbo.csg_index_exists('mvwevaluationgroupdata_divisionid', 'mvwevaluationgroupdata') = 0
CREATE INDEX [mvwevaluationgroupdata_divisionid] ON [mvwevaluationgroupdata]([divisionid]);
IF dbo.csg_index_exists('mvwevaluationgroupdata_keyid', 'mvwevaluationgroupdata') = 0
CREATE INDEX [mvwevaluationgroupdata_keyid] ON [mvwevaluationgroupdata]([keyid]);
IF dbo.csg_index_exists('mvwevaluationgroupdata_evaluationid', 'mvwevaluationgroupdata') = 0
CREATE INDEX [mvwevaluationgroupdata_evaluationid] ON [mvwevaluationgroupdata]([evaluationid]);
