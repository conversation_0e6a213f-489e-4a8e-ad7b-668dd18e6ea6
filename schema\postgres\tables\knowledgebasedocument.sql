CREATE TABLE IF NOT EXISTS knowledgebasedocument (
    id VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    visible BOOLEAN,
    state VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified <PERSON>IM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
    dateImported TIMESTAMP WITHOUT TIME ZONE,
    datePublished TIMESTAMP WITHOUT TIME ZONE,
    lastPublishedVersionNumber INTEGER,
    documentVersion VARCHAR(50),
    externalId INTEGER,
    categoryId VARCHAR(50),
    knowledgeBaseId VARCHAR(50),
    readonly BOOLEAN,
    updated timestamp without time zone,
    CONSTRAINT knowledgebasedocument_pkey PRIMARY KEY (id)
);
