CREATE
OR REPLACE VIEW vwtimeoffrequestData AS
SELECT
    tr.id,
    tr.userid,
    tr.isfulldayrequest,
    tr.status,
    tr.startdate,
    tr.notes,
    tr.timeoffduration,
    tr.submittedbyid,
    tr.submittedate,
    tr.reviewedbyid,
    tr.revieweddate,
    tr.modifiedbyid,
    tr.modifieddate,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername
FROM
    timeoffrequestData tr
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = tr.userid;


COMMENT ON COLUMN vwtimeoffrequestdata.id IS 'Time Off Request GUID';
COMMENT ON COLUMN vwtimeoffrequestdata.userid IS 'User GUID';
COMMENT ON COLUMN vwtimeoffrequestdata.isfulldayrequest IS 'Full Day Request';
COMMENT ON COLUMN vwtimeoffrequestdata.status IS 'Request Status';
COMMENT ON COLUMN vwtimeoffrequestdata.startdate IS 'Start Date';
COMMENT ON COLUMN vwtimeoffrequestdata.notes IS 'Notes/Comments';
COMMENT ON COLUMN vwtimeoffrequestdata.timeoffduration IS 'Time Off Duration';
COMMENT ON COLUMN vwtimeoffrequestdata.submittedbyid IS 'Submitted By';
COMMENT ON COLUMN vwtimeoffrequestdata.submittedate IS 'Submission Date';
COMMENT ON COLUMN vwtimeoffrequestdata.reviewedbyid IS 'Reviewed By';
COMMENT ON COLUMN vwtimeoffrequestdata.revieweddate IS 'Review Date';
COMMENT ON COLUMN vwtimeoffrequestdata.modifiedbyid IS 'Last Modified By';
COMMENT ON COLUMN vwtimeoffrequestdata.modifieddate IS 'Last Modified Date';
COMMENT ON COLUMN vwtimeoffrequestdata.agentname IS 'Agent Name';
COMMENT ON COLUMN vwtimeoffrequestdata.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwtimeoffrequestdata.managername IS 'Manager Name';

COMMENT ON VIEW vwtimeoffrequestData IS 'See TimeOffRequestData - Expands all the GUIDs with their lookups';