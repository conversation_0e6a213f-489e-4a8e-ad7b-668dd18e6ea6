namespace CSG.Adapter.Configuration;

public sealed class Options
{
    [Description("Database settings")]
    public Database? Database { get; set; }
    [Description("Genesys Cloud settings")]
    public GenesysApi? GenesysApi { get; set; }
    [Description("Application logging level")]
    public LogLevel? LogLevel { get; set; }
    [Description("Preferences")]
    public Preferences? Preferences { get; set; }
    [Description("Operation to perform")]
    public Job? Job { get; set; }
}

public sealed class Database
{
    [Description("Type of database")]
    public DatabaseType? Type { get; set; }
    [Description("Address of the database server")]
    public string? Address { get; set; }
    [Description("TCP port of the database server")]
    public int Port { get; set; }
    [Description("Name of the database")]
    public string? Name { get; set; }
    [Description("User with rights to the database")]
    public string? User { get; set; }
    [Description("Password of database user")]
    public string? Password { get; set; }
    [Description("Database schema where application tables are located")]
    public string? Schema { get; set; }
    [Description("Shared Database")]
    public bool? SharedDatabase { get; set; }
    [Description("Additional options to pass in the connection string")]
    public string? ConnectOptions { get; set; }
}

public enum DatabaseType
{
    [Description("Microsoft SQL database server")]
    MSSQL,
#if MYSQL
    [Description("MySQL database server (* limited support, please contact CSG support before using)")]
    MySQL,
#endif
    [Description("PostgreSQL database server")]
    PostgreSQL,
    [Description("Snowflake database server")]
    Snowflake
}

public sealed class GenesysApi
{
    [Description("OAuth client ID with appropriate access to Genesys Cloud")]
    public string? ClientId { get; set; }
    [Description("OAuth client secret")]
    public string? ClientSecret { get; set; }
    [Description("Genesys Cloud API endpoint for instance region")]
    public Uri? Endpoint { get; set; }
}

public enum LogLevel
{
    [Description("Log trace and higher events")]
    Verbose,
    [Description("Log debug and higher events")]
    Debug,
    [Description("Log informational and higher events")]
    Information,
    [Description("Log warning and higher events")]
    Warning,
    [Description("Log error and higher events")]
    Error,
    [Description("Log fatal events")]
    Fatal
}

public sealed class Preferences
{
    // Enable telemetry reporting
    public bool Telemetry { get; set; }
    [Description("Backfill historical data for the specified job instead of current data (selected jobs only)")]
    public bool? Backfill { get; set; }
    [Description("Operation to perform when running job=FactData")]
    public FactDataJob[]? FactDataJobs { get; set; }
    [Description("Number of months to synchronise")]
    public int OffsetMonths { get; set; }
    [Description("The time zone to use for local time conversion, https://learn.microsoft.com/en-us/dotnet/api/system.timezoneinfo.id")]
    public string? TimeZone { get; set; }
    [Description("The maximum time span that will be attempt to sync at one time. Specified as 'd.hh:mm:ss'")]
    public TimeSpan? MaxSyncSpan { get; set; }
    [Description("The maximum time span that is needed to Look back. Specified as 'd.hh:mm:ss'")]
    public TimeSpan? LookBackSpan { get; set; }
    [Description("The granularity for Genesys Cloud API data retrieval in ISO-8601 duration format (e.g., PT15M, PT30M, PT1H, P1D)")]
    public string? Granularity { get; set; }
    [Description("Participant attributes to ignore and not import. Specified as a .NET regular expression.")]
    public string[]? BlockParticipantAttributes { get; set; }
    [Description("Replace text in participant attributes names. Find is a .NET regular expression, matching text is replaced with Replace, capture groups are supported ($1..$9).")]
    public RegexReplacement[]? RenameParticipantAttributeNames { get; set; }
    [Description("Permissions settings")]
    public Permissions? Permissions { get; set; }
}

public sealed class Permissions
{
    [Description("Enable update permissions module")]
    public bool Update { get; set; }

    [Description("Force permission updates even when existing role data cannot be retrieved")]
    public bool ForcedUpdate { get; set; }
}

public enum PermissionOption
{
    [Description("Enable update permissions module")]
    Update,
    [Description("Force permission updates even when existing role data cannot be retrieved")]
    ForcedUpdate
}

public sealed class RegexReplacement
{
    public string? Find { get; set; }
    public string? Replace { get; set; }
}

public enum FactDataJob
{
    [Description("All fact data jobs")]
    All,
    [Description("Activity code lookup data")]
    ActivityCodeDetails,
    [Description("Assistants lookup data")]
    Assistants,
    [Description("Business unit lookup data")]
    BUDetails,
    [Description("Division lookup data")]
    DivisionDetails,
    [Description("Evaluation lookup data")]
    EvaluationDetails,
    EvaluationsDetails,
    [Description("Flow Outcome data")]
    FlowOutcomeDetails,
    [Description("User group lookup data")]
    GroupDetails,
    [Description("Knowledge Base data")]
    KnowledgeBaseDetails,
    [Description("Learning Data")]
    LearningDataDetails,
    [Description("Management unit lookup data")]
    MUDetails,
    [Description("OD lookup data")]
    ODDetails,
    [Description("Planning group lookup data")]
    PlanningGroupDetails,
    [Description("Time presence lookup data")]
    PresenceDetails,
    [Description("Queue lookup data")]
    QueueDetails,
    [Description("Service Goal lookup data")]
    ServiceGoalDetails,
    [Description("Skills lookup data")]
    SkillDetails,
    [Description("Teams lookup data")]
    TeamDetails,
    TeamsDetails,
    [Description("User lookup data")]
    UserDetails,
    [Description("Wrap up code lookup data")]
    WrapupDetails,
    [Description("Schedule details lookup data")]
    ScheduleDetails
}

public enum Job
{
    [Description("Agent adherence to published schedules (*)")]
    Adherence,
    [Description("Aggregated user and queue information. Aggregated user presence data")]
    Aggregation,
    [Description("The aggregated chat data")]
    Chat,
    [Description("Evaluation data (*)")]
    Evaluation,
    [Description("Evaluation catchup for existing pending evaluations (*)")]
    EvaluationCatchup,
    [Description("The synchronisation of lookup data")]
    FactData,
    [Description("Using schedules to get the Genesys predicted requirements for headcount by each 15 min")]
    HeadCountForecast,
    [Description("Creating timesheet data with blocks of hours, breaking out time spent on breaks, and meetings. Must have run scheduledetails first")]
    HoursBlockData,
    [Description("Show command line help, configuration and other information")]
    Information,
    [Description("Install or update the database schema")]
    Install,
    [Description("Detailed interaction data, conversation summary, participant summary, attributes")]
    Interaction,
    [Description("Populate the UserInteractionPresenceDetailedData table. Should not be used for aggregation of interaction time.")]
    InteractionPresence,
    [Description("Knowledge Base data")]
    KnowledgeBaseDetails,
    [Description("The aggregated message data")]
    Message,
    [Description("Knowledge data")]
    Knowledge,
    [Description("Learning Data")]
    Learning,
    [Description("Learning Data Details")]
    LearningDataDetails,
    [Description("API usage on a per call basis (*)")]
    OAuthUsage,
    [Description("Updates the outbound dialling contact lists")]
    ODContactLists,
    [Description("Updates the outbound dialling metadata")]
    ODDetails,
    [Description("Using the forecast data from Genesys to see offered and AHT figures predicted")]
    OfferedForecast,
    [Description("Detailed presence information")]
    PresenceDetail,
    [Description("Queue membership")]
    QueueMembership,
    [Description("Real-time statistics")]
    Realtime,
    [Description("Get the details of schedules for the last 26 weeks and next 26 weeks (*)")]
    ScheduleDetails,
    [Description("Overview of license usage (*)")]
    Shrinkage,
    [Description("Synchronise the Shrinkage historical report.")]
    Subscription,
    [Description("Synchronise surveys, survey question groups and survey answers")]
    Survey,
    [Description("")]
    SysConvUsage,
    [Description("Time off requests by agents (*)")]
    TimeOffReq,
    [Description("User individual license times (*)")]
    SubsUsers,
    [Description("User to queue mappings")]
    UserQueueMapping,
    [Description(" ")]
    UserQueueAudit,
    [Description("Voice analysis - overview, topic detail and sentiment detail")]
    VoiceAnalysis,
    [Description(" ")]
    WFMAudit,
    [Description("Agent published schedules (*)")]
    WFMSchedule
}

#if OPTIONS_V3_COMPAT
public sealed class LegacyOptions
{
    [Description("Customer key ID")]
    public string? CustomerKeyId { get; set; }
    [Description("Type of database")]
    public LegacyDatabaseType? SqlDatabaseType { get; set; }
    [Description("Database schema where application tables are located")]
    public string? SqlDatabaseSchema { get; set; }
    [Description("Additional options to pass in the connection string")]
    public string? SqlConnectionString { get; set; }
    [Description("Shared Database")]
    public bool? SqlSharedDatabase { get; set; }
}

public enum LegacyDatabaseType
{
    PostSQL,
#if MYSQL
    MySQL,
#endif
    MSSQL
}
#endif

public class DescriptionAttribute: Attribute
{
    public DescriptionAttribute(string description) => Description = description;
    public string Description { get; set; }
}
