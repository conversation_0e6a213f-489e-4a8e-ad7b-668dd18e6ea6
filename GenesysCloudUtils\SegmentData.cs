﻿using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using CSG.Common.ExtensionMethods;
using DBUtils;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractionSegments;
using PartAttribs = GenesysCloudDefParticipantAttrib;
using Newtonsoft.Json;
using StandardUtils;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CallSumm = GenesysCloudDefCallSummary;
using GenesysCloudUtils;

#nullable enable

namespace GenesysCloudUtils
{
    public class DetailData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime DetailInteractionLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string? TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private DataTable ConversationSummaryData;
        private CSG.Adapter.Configuration.RegexReplacement[]? _renameParticipantAttributeNames;
        private readonly ILogger? _logger;
        private readonly object _detailInteractionLock = new object();
        private readonly object _participantAttributeLock = new object();
        private readonly object _participantSummaryLock = new object();
        private readonly object _flowOutcomesLock = new object();

        public DetailData(
            ILogger? logger,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            _logger = logger;
            _renameParticipantAttributeNames = renameParticipantAttributeNames;

            GCUtilities.Initialize();
            DBUtil.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            ConversationSummaryData = DBUtil.CreateInMemTable("convsummarydata");
        }

        private string RenameParticipantAttributeNames(
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            string attributeName)
        {
            if (_renameParticipantAttributeNames == null || _renameParticipantAttributeNames.Length == 0)
                return attributeName;

            foreach (var rule in _renameParticipantAttributeNames)
            {
                if (string.IsNullOrEmpty(rule.Find))
                {
                    _logger?.LogWarning("Empty RenameParticipantAttributeNames option Find value");
                    continue;
                }
                var pre = attributeName;
                attributeName = Regex.Replace(attributeName, rule.Find, rule.Replace ?? "");
                if (string.IsNullOrEmpty(attributeName))
                    throw new ArgumentNullException(pre, $"Attribute name '{pre}' was cleared by rule '{rule}'.");

                if (pre != attributeName)
                {
                    _logger?.LogDebug("Attribute '{0}' renamed to '{1}'", pre, attributeName);
                    return attributeName;
                }
            }

            return attributeName;
        }

        public class JobCompletionResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        private async Task<JobCompletionResult> WaitForJobCompletionViaPollingAsync(string uri, string jobId)
        {
            var result = new JobCompletionResult();

            try
            {
                _logger?.LogInformation("Polling for job {JobId} status", jobId);

                // Set up a timeout - 15 minutes should be enough for most jobs
                var timeout = TimeSpan.FromMinutes(15);
                var startTime = DateTime.UtcNow;
                var endTime = startTime.Add(timeout);

                // Poll interval - start with 2 seconds, then increase
                var pollInterval = TimeSpan.FromSeconds(2);
                var maxPollInterval = TimeSpan.FromSeconds(10);

                // Wait 3 seconds before the first poll
                _logger?.LogDebug("Waiting 3 seconds before first poll for job {JobId}", jobId);
                await Task.Delay(TimeSpan.FromSeconds(3));

                // Poll for job status until it completes or times out
                while (DateTime.UtcNow < endTime)
                {
                    _logger?.LogInformation("Checking status of job {JobId}", jobId);
                    string jobStatusJson = JsonActions.JsonReturnString(uri + $"/api/v2/analytics/conversations/details/jobs/{jobId}", GCApiKey);

                    if (string.IsNullOrWhiteSpace(jobStatusJson))
                    {
                        _logger?.LogWarning("Empty response when checking job {JobId} status", jobId);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    if (jobStatusJson.Contains("\"error\": true"))
                    {
                        _logger?.LogWarning("Error response when checking job {JobId} status: {Response}", jobId, jobStatusJson);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    var jobStatus = JsonConvert.DeserializeObject<DetInt.ReportJobStatus>(jobStatusJson,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (jobStatus == null)
                    {
                        _logger?.LogWarning("Failed to deserialize job status for job {JobId}", jobId);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    _logger?.LogInformation("Job {JobId} status: {State}", jobId, jobStatus.state);

                    // Check if the job has completed
                    if (jobStatus.state == "FULFILLED" || jobStatus.state == "SUCCESS" || jobStatus.state == "COMPLETE")
                    {
                        _logger?.LogInformation("Job {JobId} completed successfully with state: {State}", jobId, jobStatus.state);
                        result.Success = true;
                        return result;
                    }

                    // Check if the job has failed
                    if (jobStatus.state == "FAILED" || jobStatus.state == "ERROR")
                    {
                        string errorMessage = $"Job failed with state: {jobStatus.state}";

                        _logger?.LogError("Job {JobId} failed: {ErrorMessage}", jobId, errorMessage);
                        result.ErrorMessage = errorMessage;
                        return result;
                    }

                    // Job is still running, wait and check again
                    _logger?.LogInformation("Job {JobId} is still running with state: {State}, checking again in {Interval} seconds",
                        jobId, jobStatus.state, pollInterval.TotalSeconds);

                    await Task.Delay(pollInterval);

                    // Increase poll interval for next iteration (up to max)
                    pollInterval = TimeSpan.FromMilliseconds(Math.Min(pollInterval.TotalMilliseconds * 1.5, maxPollInterval.TotalMilliseconds));
                }

                // If we get here, the job has timed out
                result.ErrorMessage = $"Timeout waiting for job to complete after {timeout.TotalMinutes} minutes";
                _logger?.LogError("Timeout waiting for job {JobId} to complete after {Timeout} minutes", jobId, timeout.TotalMinutes);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Error waiting for job completion: {ex.Message}";
                _logger?.LogError(ex, "Error waiting for job {JobId} completion", jobId);
            }

            return result;
        }

        public DataSet GetDetailInteractionDataFromGC(string SyncType, String StartDate, String EndDate)
        {
            return GetDetailInteractionDataFromGCAsync(SyncType, StartDate, EndDate).GetAwaiter().GetResult();
        }

        private async Task<DataSet> GetDetailInteractionDataFromGCAsync(string SyncType, String StartDate, String EndDate)
        {
            Console.WriteLine("Initiating data retrieval job...");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/jobs/availability", GCApiKey);

            JobDateLimit DateMax = JsonConvert.DeserializeObject<JobDateLimit>(JsonString,
                                         new JsonSerializerSettings
                                         {
                                             NullValueHandling = NullValueHandling.Ignore
                                         }) ?? new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };

            DateTime FromDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();
            DateTime ToDate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

            Console.WriteLine($"Data fetch parameters:\n- Start date: {StartDate}\n- End date: {EndDate}\n- From date: {FromDate}\n- To date: {ToDate}\n- Data availability date: {DateMax.dataAvailabilityDate}\n- Current time (UTC): {DateTime.UtcNow}");

            bool RunJob = false;
            bool RunQuery = false;

            if (FromDate < DateMax.dataAvailabilityDate)
            {
                Console.WriteLine("\nInitiating job to retrieve complete or partial data set");
                RunJob = true;

                if (ToDate < DateMax.dataAvailabilityDate)
                {
                    Console.WriteLine("\nEnd date is within data availability window - can retrieve all data with a single job");
                    RunQuery = false;
                }
                else
                {
                    Console.WriteLine("\nEnd date exceeds data availability window - adjusting end date and using partial query for remaining data");
                    RunQuery = true;
                    ToDate = DateMax.dataAvailabilityDate;
                }
            }
            else
            {
                RunJob = false;
                RunQuery = true;
            }

            DataSet DSDetailInteraction = new DataSet();

            if (RunJob)
            {
                Console.WriteLine("\nExecuting data retrieval job");
                DSDetailInteraction = await GetDetailInteractionDataFromGCJob(
                    FromDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"),
                    ToDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"));

                if (RunQuery)
                {
                    DateTime maxDate = DateMax.dataAvailabilityDate;
                    DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(maxDate.AddMinutes(-30).ToString("yyyy-MM-ddTHH:mm:00.000Z"), EndDate);
                    DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                    MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
                }
            }
            else
            {
                DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(StartDate, EndDate);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[0].Copy());
                DSDetailInteraction.Tables.Add(TempAttribs);
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[1].Copy());
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[2].Copy());
            }

            DataTable OutConversations = DBUtil.GetSQLTableData("select conversationid from convsummarydata where conversationenddateltc is null and firstmediatype = 'voice'", "ouytconversations");
            Console.WriteLine("Total Outstanding Convs :{0}", OutConversations.Rows.Count);

            if (OutConversations.Rows.Count > 0)
            {
                Console.WriteLine("Processing Outstanding Conversations");
                DataSet TempDataSet = GetOutStandingConversations(OutConversations);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
            }

            Console.Write("\n");
            if (DSDetailInteraction.Tables.Count > 0)
            {
                DSDetailInteraction.Tables.Add(GetInteractionSummary(DSDetailInteraction.Tables[0]));
            }

            Console.WriteLine("Returning Data to Calling Method");
            return DSDetailInteraction;
        }

        private void MergeDataSets(DataSet DSDetailInteraction, DataSet TempDataSet, DataTable? TempAttribs)
        {
            // Check if both datasets have the required tables
            if (DSDetailInteraction.Tables.Count < 1 || TempDataSet.Tables.Count < 1)
            {
                Console.WriteLine("Warning: One of the datasets doesn't have enough tables for merging. DSDetailInteraction has {0} tables, TempDataSet has {1} tables.",
                    DSDetailInteraction.Tables.Count, TempDataSet.Tables.Count);
                return;
            }

            // Process the first table (detailed interaction data)
            foreach (DataRow TempInt in TempDataSet.Tables[0].Rows)
            {
                try
                {
                    if (DSDetailInteraction.Tables[0].Select("keyid = '" + TempInt["keyid"] + "'").Length == 0)
                    {
                        DSDetailInteraction.Tables[0].ImportRow(TempInt);
                        _logger?.LogDebug("Added new interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    }
                    else
                    {
                        DataRow DetailedRowTemp = DSDetailInteraction.Tables[0].Select("keyid = '" + TempInt["keyid"] + "'").FirstOrDefault();
                        foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[0].Columns)
                        {
                            if (!DetailedColumn.ReadOnly && DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempInt[DetailedColumn.ColumnName].ToString())
                                DetailedRowTemp[DetailedColumn.ColumnName] = TempInt[DetailedColumn.ColumnName];
                        }
                        _logger?.LogDebug("Updated existing interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    }
                }
                catch (Exception e)
                {
                    _logger?.LogDebug("Failed to process interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    Console.WriteLine("Exception caught in Detailed Interaction Merge.\nError Message: {0}\nInner Exception: {1}", e.ToString(), e.InnerException);
                }
            }

            // Process participant attributes if available
            if (TempAttribs != null && DSDetailInteraction.Tables.Count >= 2)
            {
                DataColumnCollection columns = DSDetailInteraction.Tables[1].Columns;
                foreach (DataColumn AttribCol in TempAttribs.Columns)
                {
                    if (!columns.Contains(AttribCol.ColumnName))
                    {
                        DSDetailInteraction.Tables[1].Columns.Add(AttribCol.ColumnName, AttribCol.DataType);
                        _logger?.LogDebug("Added new attribute column: {ColumnName}", AttribCol.ColumnName);
                    }
                }

                Console.Write("\n");
                foreach (DataRow TempAttrib in TempAttribs.Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[1].Select("keyid = '" + TempAttrib["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[1].ImportRow(TempAttrib);
                            _logger?.LogDebug("Added new attribute record with keyid: {KeyId}", TempAttrib["keyid"]);
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[1].Select("keyid = '" + TempAttrib["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[1].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempAttrib.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempAttrib[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempAttrib[DetailedColumn.ColumnName];
                                }
                            }
                            _logger?.LogDebug("Updated existing attribute record with keyid: {KeyId}", TempAttrib["keyid"]);
                        }
                    }
                    catch (Exception e)
                    {
                        Console.Write("ATD:");
                        Console.WriteLine("Exception caught in Part Attrib Merge Module.\nError Message: {0}\nInner Exception: {1}", e.ToString(), e.InnerException);
                    }
                }

                Console.Write("\n");
            }

            // Process participant summary data if available
            if (TempDataSet.Tables.Count >= 2 && DSDetailInteraction.Tables.Count >= 3)
            {
                foreach (DataRow TempPartSumm in TempDataSet.Tables[1].Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[2].Select("keyid = '" + TempPartSumm["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[2].ImportRow(TempPartSumm);
                            Console.Write("PSA:");
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[2].Select("keyid = '" + TempPartSumm["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[2].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempPartSumm.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempPartSumm[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempPartSumm[DetailedColumn.ColumnName];
                                }
                            }
                            Console.Write("PSU:");
                        }
                    }
                    catch (Exception e)
                    {
                        Console.Write("PSD:");
                        Console.WriteLine("Exception caught in Part Summary Merge Module.\nError Message: {0}\nInner Exception: {1}", e.ToString(), e.InnerException);
                    }
                }

                Console.Write("\n");
            }

            // Process flow outcome data if available
            if (TempDataSet.Tables.Count >= 3 && DSDetailInteraction.Tables.Count >= 4)
            {
                foreach (DataRow TempFlowOutcome in TempDataSet.Tables[2].Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[3].Select("keyid = '" + TempFlowOutcome["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[3].ImportRow(TempFlowOutcome);
                            Console.Write("FOA:");
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[3].Select("keyid = '" + TempFlowOutcome["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[3].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempFlowOutcome.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempFlowOutcome[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempFlowOutcome[DetailedColumn.ColumnName];
                                }
                            }
                            Console.Write("FOU:");
                        }
                    }
                    catch (Exception e)
                    {
                        Console.Write("FOD:");
                        Console.WriteLine("Exception caught in Flow Outcome Merge Module.\nError Message: {0}\nInner Exception: {1}", e.ToString(), e.InnerException);
                    }
                }

                Console.Write("\n");
            }
        }

        private DataSet GetOutStandingConversations(DataTable OutConversations)
        {
            Console.WriteLine("Processing Outstanding Conversation Count:{0}", OutConversations.Rows.Count);

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Console.WriteLine("Creating Memory Table Detailed Interaction");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");

            Console.WriteLine("Creating Memory Table Participant Summary Data");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Creating Memory Table Flow Outcome Data");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            int MaxRowsToSend = 75;
            int currentPage = 1;

            int totalPages = (OutConversations.Rows.Count % MaxRowsToSend == 0) ?
                (OutConversations.Rows.Count / MaxRowsToSend) :
                (OutConversations.Rows.Count / MaxRowsToSend) + 1;

            while (currentPage <= totalPages)
            {
                Console.WriteLine("Working On Batch Page : {0}", currentPage);
                StringBuilder SelectString = new StringBuilder("");
                DataTable dtTemp = OutConversations.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = OutConversations.TableName;

                foreach (DataRow DRRow in dtTemp.Rows)
                {
                    // Processing conversation - using debug level to avoid log flooding
                    SelectString.Append(DRRow["conversationid"] + ",");
                }

                SelectString.Length = SelectString.Length - 1;

                Console.Write("#");
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details?id=" + SelectString.ToString(), GCApiKey);

                if (JsonString.Length > 50)
                {
                    Console.WriteLine("\nInteractions To Process");
                    Interactions.InteractionSegmentStruct DetailedData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                    bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                    foreach (Interactions.Conversation ConvData in DetailedData.conversations)
                    {
                        if (ConvData != null)
                        {
                            Console.WriteLine("\nCurrently Looking at Conversation :{0}", ConvData.conversationId);
                            int PartCode = 0;
                            int SessCode = 0;
                            int SegCode = 0;
                            foreach (Interactions.Participant ConvPart in ConvData.participants)
                            {
                                DataRow CheckPartExists = ParticipantSummary.Select("keyid= '" + ConvData.conversationId + "|" + ConvPart.participantId + "'").FirstOrDefault();

                                DataRow DRPartSumm = ParticipantSummary.NewRow();
                                DRPartSumm["keyid"] = ConvData.conversationId + "|" + ConvPart.participantId;
                                DRPartSumm["conversationid"] = ConvData.conversationId;
                                DRPartSumm["participantid"] = ConvPart.participantId;
                                DRPartSumm["purpose"] = ConvPart.purpose;

                                if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                {
                                    DRPartSumm["divisionid"] = ConvData.divisionIds[0];
                                }
                                else
                                {
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }

                                if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value || DRPartSumm["divisionid"] is DBNull)
                                {
                                    _logger?.LogDebug("Setting default division ID for participant summary");
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }
                                if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                    DRPartSumm["divisionid2"] = ConvData.divisionIds[1];
                                if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                    DRPartSumm["divisionid3"] = ConvData.divisionIds[2];

                                DRPartSumm["conversationstartdate"] = ConvData.conversationStart.ToUniversalTime();
                                DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart.ToUniversalTime(), AppTimeZone);

                                if (ConvData.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                {
                                    DRPartSumm["conversationenddate"] = ConvData.conversationEnd.ToUniversalTime();
                                    DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd.ToUniversalTime(), AppTimeZone);
                                }
                                else
                                {
                                    DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                    DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                }

                                PartCode++;
                                SessCode = 0;
                                SegCode = 0;

                                foreach (Interactions.Session ConvSess in ConvPart.sessions)
                                {
                                    SessCode++;
                                    SegCode = 0;
                                    Interactions.Flow ConvSessFlow = ConvSess.flow;

                                    int SegmentCount = ConvSess.segments.Length;
                                    int CurrentSegment = 1;

                                    foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                                    {
                                        SegCode++;

                                        if (!timeFieldsMillisecondResolution)
                                        {
                                            ConvSeg.segmentStart = new DateTime(
                                                ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentStart.Kind
                                            );
                                            ConvSeg.segmentEnd = new DateTime(
                                                ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentEnd.Kind
                                            );
                                        }

                                        string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();
                                        DataRow NewRow = DetailInteraction.NewRow();
                                        string TempKeyid = ConvData.conversationId + "|" + IterationCode;
                                        NewRow["keyid"] = ConvData.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);
                                        NewRow["conversationid"] = ConvData.conversationId;

                                        if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                        {
                                            NewRow["divisionid"] = ConvData.divisionIds[0];
                                        }
                                        else
                                        {
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (NewRow["divisionid"] == null || NewRow["divisionid"] == System.DBNull.Value|| NewRow["divisionid"] is DBNull)
                                        {
                                            _logger?.LogDebug("Setting default division ID for detailed interaction");
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                            NewRow["divisionid2"] = ConvData.divisionIds[1];
                                        if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                            NewRow["divisionid3"] = ConvData.divisionIds[2];

                                        NewRow["conversationstartdate"] = ConvData.conversationStart.ToUniversalTime();
                                        NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart.ToUniversalTime(), AppTimeZone);

                                        if (ConvData.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                        {
                                            NewRow["conversationenddate"] = ConvData.conversationEnd.ToUniversalTime();
                                            NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd.ToUniversalTime(), AppTimeZone);
                                        }
                                        else
                                        {
                                            NewRow["conversationenddate"] = System.DBNull.Value;
                                            NewRow["conversationenddateltc"] = System.DBNull.Value;
                                        }

                                        NewRow["gencode"] = IterationCode;
                                        NewRow["peer"] = ConvSess.peerId;

                                        DateTime MaxDateTest = ConvData.conversationStart.ToUniversalTime();
                                        if (MaxDateTest > DetailInteractionLastUpdate)
                                        {
                                            DetailInteractionLastUpdate = MaxDateTest;
                                            Console.Write("@");
                                        }

                                        NewRow["conversationminmos"] = decimal.Round(ConvData.mediaStatsMinConversationMos, 2);
                                        NewRow["originaldirection"] = ConvData.originatingDirection;
                                        NewRow["participantid"] = ConvPart.participantId;
                                        if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                            NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                        else
                                            NewRow["participantname"] = ConvPart.participantName;
                                        NewRow["purpose"] = ConvPart.purpose;

                                        NewRow["mediatype"] = ConvSess.mediaType;
                                        DRPartSumm["mediaType"] = ConvSess.mediaType;

                                        if (ConvSess.ani != null && ConvSess.ani.Length > 300)
                                            NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                        else
                                            NewRow["ani"] = ConvSess.ani;

                                        if (ConvSeg.queueId != null)
                                        {
                                            NewRow["queueid"] = ConvSeg.queueId;
                                            DRPartSumm["queueid"] = ConvSeg.queueId;
                                        }
                                        if (ConvPart.userId != null)
                                        {
                                            NewRow["userid"] = ConvPart.userId;
                                            DRPartSumm["userid"] = ConvPart.userId;
                                        }

                                        if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                            NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                        else
                                            NewRow["dnis"] = ConvSess.dnis;

                                        if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                        else
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis;

                                        NewRow["sessiondirection"] = ConvSess.direction;
                                        NewRow["edgeId"] = ConvSess.edgeId;
                                        if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                            NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                        else
                                            NewRow["remotedisplayable"] = ConvSess.remote;

                                        NewRow["conversationminrfactor"] = decimal.Round(ConvData.mediaStatsMinConversationRFactor, 2);

                                        if (ConvData.externalTag != null)
                                            NewRow["externalTag"] = ConvData.externalTag;

                                        NewRow["segmentstartdate"] = ConvSeg.segmentStart.ToUniversalTime();
                                        NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentStart.ToUniversalTime(), AppTimeZone);

                                        System.TimeSpan Diff = new System.TimeSpan();

                                        if (ConvSeg.segmentEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                        {
                                            NewRow["segmentenddate"] = ConvSeg.segmentEnd.ToUniversalTime();
                                            NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentEnd.ToUniversalTime(), AppTimeZone);
                                            Diff = ConvSeg.segmentEnd.ToUniversalTime() - ConvSeg.segmentStart.ToUniversalTime();
                                            NewRow["segmenttime"] = Diff.TotalSeconds;
                                            Diff = ConvSeg.segmentEnd.ToUniversalTime() - ConvData.conversationStart.ToUniversalTime();
                                            NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                        }
                                        else
                                        {
                                            NewRow["segmentenddate"] = System.DBNull.Value;
                                            NewRow["segmenttime"] = System.DBNull.Value;
                                            NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                        }

                                        Diff = ConvSeg.segmentStart.ToUniversalTime() - ConvData.conversationStart.ToUniversalTime();
                                        NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                                        NewRow["segmenttype"] = ConvSeg.segmentType;
                                        NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                        NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                                        string RowWrapUp = ConvSeg.wrapUpCode;
                                        string RowWrapUpNote = ConvSeg.wrapUpNote;
                                        if (RowWrapUp != null)
                                        {
                                            if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                            NewRow["wrapupcode"] = RowWrapUp;
                                            DRPartSumm["wrapupcode"] = RowWrapUp;
                                            if (RowWrapUpNote != null)
                                            {
                                                NewRow["wrapupnote"] = RowWrapUpNote;
                                                DRPartSumm["wrapupnote"] = RowWrapUpNote;
                                            }
                                            else
                                            {
                                                NewRow["wrapupnote"] = "";
                                            }
                                        }
                                        else
                                        {
                                            NewRow["wrapupcode"] = "";
                                            NewRow["wrapupnote"] = "";
                                        }

                                        if (ConvSeg.disconnectType == null)
                                            NewRow["disconnectiontype"] = "none";
                                        else
                                            NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                        NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                        NewRow["sessionprovider"] = ConvSess.provider;

                                        if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                        {
                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                            NewRow["flowname"] = ConvSessFlow.flowName;
                                            try
                                            {
                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                            }
                                            catch
                                            {
                                                NewRow["flowversion"] = 1.0;
                                            }

                                            NewRow["flowtype"] = ConvSessFlow.flowType;
                                            NewRow["exitreason"] = ConvSessFlow.exitReason;
                                            NewRow["entryreason"] = ConvSessFlow.entryReason;
                                            NewRow["entrytype"] = ConvSessFlow.entryType;
                                            NewRow["transfertype"] = ConvSessFlow.transferType;
                                            NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;
                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                            List<Interactions.Outcomes> ConvSessFlowOutcomes = ConvSessFlow.outcomes?.ToList() ?? new List<Interactions.Outcomes>();
                                            foreach (Interactions.Outcomes SessFlowOutcome in ConvSessFlowOutcomes)
                                            {
                                                try
                                                {
                                                    if (ConvSessFlowOutcomes != null && SessFlowOutcome.flowOutcomeId != null)
                                                    {
                                                        DataRow DRFlowOutcomes = FlowOutcomes.NewRow();

                                                        DRFlowOutcomes["keyid"] = ConvData.conversationId + "|" + ConvSessFlow.flowId + "|" + SessFlowOutcome.flowOutcomeId;
                                                        DRFlowOutcomes["flowid"] = ConvSessFlow.flowId;
                                                        DRFlowOutcomes["flowname"] = ConvSessFlow.flowName;
                                                        try
                                                        {
                                                            DRFlowOutcomes["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                        }
                                                        catch
                                                        {
                                                            DRFlowOutcomes["flowversion"] = 1.0;
                                                        }
                                                        DRFlowOutcomes["flowtype"] = ConvSessFlow.flowType;
                                                        DRFlowOutcomes["conversationid"] = ConvData.conversationId;
                                                        DRFlowOutcomes["conversationstartdate"] = ConvData.conversationStart.ToUniversalTime();
                                                        DRFlowOutcomes["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart.ToUniversalTime(), AppTimeZone);

                                                        if (ConvData.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                                        {
                                                            DRFlowOutcomes["conversationenddate"] = ConvData.conversationEnd.ToUniversalTime();
                                                            DRFlowOutcomes["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd.ToUniversalTime(), AppTimeZone);
                                                        }
                                                        else
                                                        {
                                                            DRFlowOutcomes["conversationenddate"] = System.DBNull.Value;
                                                            DRFlowOutcomes["conversationenddateltc"] = System.DBNull.Value;
                                                        }

                                                        if (DateTime.TryParse(SessFlowOutcome.flowOutcomeStartTimestamp, out var parsedStartTimestamp))
                                                        {
                                                            // Convert to UTC
                                                            var flowOutcomeStartDateUtc = parsedStartTimestamp.ToUniversalTime();
                                                            DRFlowOutcomes["flowoutcomestartdate"] = flowOutcomeStartDateUtc;

                                                            // Convert from UTC to the specified time zone
                                                            var flowOutcomeStartDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeStartDateUtc, AppTimeZone);
                                                            DRFlowOutcomes["flowoutcomestartdateltc"] = flowOutcomeStartDateLtc;
                                                        }
                                                        else
                                                        {
                                                            throw new FormatException("Invalid date format in flowOutcomeStartTimestamp.");
                                                        }


                                                        if (!string.IsNullOrEmpty(SessFlowOutcome.flowOutcomeEndTimestamp) &&
                                                            DateTime.TryParse(SessFlowOutcome.flowOutcomeEndTimestamp, out var parsedEndTimestamp))
                                                        {
                                                            // Convert to UTC
                                                            var flowOutcomeEndDateUtc = parsedEndTimestamp.ToUniversalTime();
                                                            DRFlowOutcomes["flowoutcomeenddate"] = flowOutcomeEndDateUtc;

                                                            // Convert from UTC to the specified time zone
                                                            var flowOutcomeEndDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeEndDateUtc, AppTimeZone);
                                                            DRFlowOutcomes["flowoutcomeenddateltc"] = flowOutcomeEndDateLtc;
                                                        }
                                                        else if (SessFlowOutcome.flowOutcomeEndTimestamp != null) // Only throw for non-null invalid dates
                                                        {
                                                            throw new FormatException("Invalid date format in flowOutcomeEndTimestamp.");
                                                        }

                                                        DRFlowOutcomes["flowoutcome"] = SessFlowOutcome.flowOutcome;
                                                        DRFlowOutcomes["flowoutcomeid"] = SessFlowOutcome.flowOutcomeId;
                                                        DRFlowOutcomes["flowoutcomevalue"] = SessFlowOutcome.flowOutcomeValue;

                                                        FlowOutcomes.Rows.Add(DRFlowOutcomes);

                                                    }
                                                }
                                                catch (System.Data.ConstraintException ex)
                                                {
                                                    _logger?.LogDebug(ex, "Duplicate flow outcome detected for conversation {ConversationId}", ConvData.conversationId);
                                                }
                                            }

                                        }

                                        if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                        {
                                            foreach (Interactions.Metric ConvSessMetric in ConvSess.metrics)
                                            {
                                                string FirstChar = ConvSessMetric.name.Substring(0, 1);
                                                try
                                                {
                                                    switch (FirstChar)
                                                    {
                                                        case "n":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                            }
                                                            break;
                                                        case "t":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                    ConvSessMetric.value += 100;

                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                }
                                                                else
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                                catch (Exception e)
                                                {
                                                    Console.WriteLine("No Row For {0}\n Error: {1} \nSource {2}", ConvSessMetric.name, e.ToString(), e.Source);
                                                }
                                            }
                                        }

                                        try
                                        {
                                            DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + NewRow["keyid"] + "'").FirstOrDefault();
                                            if (CheckRowExists == null)
                                            {
                                                DetailInteraction.Rows.Add(NewRow);
                                                // Record added - using debug level to avoid log flooding
                                            }
                                            else
                                            {
                                                // Duplicate record found - using debug level to avoid log flooding
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("Exception caught in Interaction Detail Module.\nError Message: {0}\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                                            throw;
                                        }

                                        CurrentSegment++;
                                        // Segment processed - using debug level to avoid log flooding
                                    }
                                }

                                if (CheckPartExists == null)
                                {
                                    ParticipantSummary.Rows.Add(DRPartSumm);
                                    // Participant added - using debug level to avoid log flooding
                                }
                                else
                                {
                                    // Participant duplicate found - using debug level to avoid log flooding
                                }
                            }
                        }
                    }
                }

                currentPage++;
            }

            Console.WriteLine("\nReturning {0} Row(s)", DetailInteraction.Rows.Count);
            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);
            return ReturnInteractionData;
        }

        public DataTable GetInteractionSummary(DataTable InteractionData)
        {
            return GetInteractionSummaryAsync(InteractionData).GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetInteractionSummaryAsync(DataTable InteractionData)
        {
            _logger?.LogInformation("Producing Conversation Summary Data");

            // Create a stopwatch to measure total processing time
            var totalStopwatch = Stopwatch.StartNew();
            var segmentStopwatch = Stopwatch.StartNew();

            // Create a table to hold unique conversation IDs
            DataTable dt = new DataTable();
            dt.Clear();
            dt.TableName = "InteractionIds";
            dt.Columns.Add("conversationId");

            // Get unique conversation IDs
            DataView view = new DataView(InteractionData);
            view.Sort = "conversationId";
            string LastConversationId = string.Empty;

            foreach (DataRow InteractionRow in InteractionData.Rows)
            {
                if (LastConversationId != InteractionRow["conversationId"].ToString())
                {
                    DataRow InteractionIdRow = dt.NewRow();
                    InteractionIdRow["conversationId"] = InteractionRow["conversationId"];
                    dt.Rows.Add(InteractionIdRow);
                    LastConversationId = InteractionRow["conversationId"].ToString();
                }
            }

            _logger?.LogInformation("Found {Count} unique conversations to process", dt.Rows.Count);

            // Since all data is already in memory, we can process in parallel
            // but need to be careful with thread safety
            var syncLock = new object();
            int CounterCSRows = 0;

            // Create batches of conversations to process in parallel
            // Use a larger batch size to reduce the number of tasks
            int batchSize = 500;
            var batches = new List<List<DataRow>>();

            for (int i = 0; i < dt.Rows.Count; i += batchSize)
            {
                var batch = new List<DataRow>();
                for (int j = i; j < Math.Min(i + batchSize, dt.Rows.Count); j++)
                {
                    batch.Add(dt.Rows[j]);
                }
                batches.Add(batch);
            }

            // Limit the number of concurrent tasks to avoid thread contention
            // This is similar to how it's done in the interaction processing
            int maxConcurrentTasks = Math.Min(Environment.ProcessorCount, 4); // Use at most 4 threads or number of CPU cores, whichever is less
            _logger?.LogInformation("Processing with maximum {MaxThreads} concurrent threads", maxConcurrentTasks);

            // Process batches with limited concurrency
            var semaphore = new SemaphoreSlim(maxConcurrentTasks);
            var tasks = new List<Task>();

            foreach (var batch in batches)
            {
                await semaphore.WaitAsync();

                tasks.Add(Task.Run(async () => {
                    try
                    {
                        // Process each conversation in this batch
                        foreach (DataRow InteractionRow in batch)
                        {
                            // Create a filtered table with just this conversation's data
                            DataTable InteractDetails = InteractionData.Clone();
                            foreach (DataRow DRInteraction in InteractionData.Select("conversationid ='" + InteractionRow["conversationId"] + "'"))
                            {
                                InteractDetails.ImportRow(DRInteraction);
                            }

                            // Process this conversation
                            lock (syncLock) // Lock when calling ConvSummary to avoid thread contention on ConversationSummaryData
                            {
                                ConvSummary(InteractDetails);
                            }

                            // Thread-safe increment of counter
                            int localCount;
                            lock (syncLock)
                            {
                                CounterCSRows++;
                                localCount = CounterCSRows;
                            }

                            // Log progress every 100 rows
                            if (localCount % 100 == 0)
                            {
                                _logger?.LogDebug("Conversation summary processing: {RowCount} of {TotalRows} rows processed",
                                    localCount, dt.Rows.Count);
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }

            // Wait for all tasks to complete
            await Task.WhenAll(tasks);

            _logger?.LogInformation("Processed all {Count} conversation summaries in {ElapsedSeconds:F2} seconds",
                CounterCSRows, totalStopwatch.Elapsed.TotalSeconds);

            return ConversationSummaryData;
        }

        private Boolean ConvSummary(DataTable Conversation)
        {
            DataView view = new DataView(Conversation);
            view.Sort = "segmentstartdate asc";

            if (Conversation.Rows.Count > 0)
            {
                DataRow ConvSummaryRow = ConversationSummaryData.NewRow();

                decimal TalkTime = 0;
                decimal WrapUpTime = 0;
                decimal QueueTime = 0;
                DateTime LastSegmentEnd = (DateTime)Conversation.Rows[0]["conversationstartdate"];
                string ANI = Conversation.Rows[0]["ani"].ToString();
                string DNIS = Conversation.Rows[0]["dnis"].ToString();

                string FirstAgentId = string.Empty;
                string LastAgentId = string.Empty;

                string FirstQueueId = string.Empty;
                string LastQueueId = string.Empty;

                string LastPurpose = string.Empty;
                string LastDisconnect = string.Empty;

                string FirstWrapUpCode = string.Empty;
                string LastWrapUpCode = string.Empty;

                string LastPeer = string.Empty;

                bool FirstAgent = false;
                bool FirstWrapUp = false;
                bool FirstQueue = false;

                int TotalHold = 0;
                decimal TotalHoldTime = 0;

                string LastMediaType = Conversation.Rows[0]["mediatype"].ToString();
                int TotalColdTrans = 0;
                int TotalWarmTrans = 0;
                int HandleCount = 0;
                decimal TotalHandleTime = 0;
                int AnswerCount = 0;
                decimal TotalAnswerTime = 0;

                int AbandonCount = 0;

                int ResponseCount = 0;
                decimal TotalResponseTime = 0;

                bool CallBackDetected = false;
                int CallBackPart = 0;

                decimal LastSegmentTime = 0;

                foreach (DataRow Segment in Conversation.Rows)
                {
                    if (Segment["purpose"].ToString() == "agent" || Segment["purpose"].ToString() == "acd")
                    {
                        string[] GenCodeSplit = Segment["gencode"].ToString().Split('|');

                        if (Segment["mediatype"].ToString() == "callback")
                        {
                            CallBackDetected = true;
                            CallBackPart = int.Parse(GenCodeSplit[0]);
                        }

                        if (Segment["segmenttype"].ToString() == "wrapup")
                        {
                            if (!FirstWrapUp)
                            {
                                FirstWrapUp = true;
                                FirstWrapUpCode = Segment["wrapupcode"].ToString();
                            }

                            LastWrapUpCode = Segment["wrapupcode"].ToString();
                        }

                        if (Segment["tabandon"] != null && Segment["tabandon"] != System.DBNull.Value && float.TryParse(Segment["tabandon"].ToString(), out float tabandonValue) && tabandonValue > 0)
                            AbandonCount = AbandonCount + 1;

                        if (Segment["peer"] != null && Segment["peer"].ToString() != "")
                            LastPeer = Segment["peer"].ToString();

                        if (CallBackDetected == true && int.Parse(GenCodeSplit[0]) == CallBackPart && Segment["mediatype"].ToString() != "callback")
                        {
                        }
                        else
                        {
                            if (Segment["segmenttype"].ToString() == "interact")
                            {
                                LastMediaType = Segment["mediatype"].ToString();

                                if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                                {
                                    if (Segment["purpose"].ToString() == "agent")
                                    {
                                        TalkTime += Convert.ToDecimal(Segment["segmenttime"]);
                                        LastAgentId = Segment["userid"].ToString();
                                        LastQueueId = Segment["queueid"].ToString();

                                        if (!FirstAgent)
                                        {
                                            FirstAgentId = Segment["userid"].ToString();
                                            FirstAgent = true;
                                        }
                                    }
                                    else
                                    {
                                        QueueTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }

                                    LastSegmentEnd = (DateTime)Segment["segmentenddate"];
                                }

                                if (!FirstQueue)
                                {
                                    FirstQueueId = Segment["queueid"].ToString();
                                    LastQueueId = Segment["queueid"].ToString();
                                    FirstQueue = true;
                                }
                                else
                                {
                                    LastQueueId = Segment["queueid"].ToString();
                                }
                            }

                            if (Segment["segmenttype"].ToString() == "wrapup")
                            {
                                if (Segment["segmenttime"] != null  && Segment["segmenttime"] != System.DBNull.Value)
                                {
                                    if (Convert.ToInt32(Segment["segmenttime"]) == 0)
                                    {
                                        decimal TempWrapUpTime = Convert.ToDecimal((LastSegmentEnd - (DateTime)Segment["segmentstartdate"]).TotalSeconds);
                                        WrapUpTime += TempWrapUpTime;
                                        TalkTime = TalkTime - TempWrapUpTime;
                                    }
                                    else
                                    {
                                        WrapUpTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }
                                }
                            }

                            if (Segment["ani"] != null && Segment["ani"] != System.DBNull.Value)
                            {
                                if (Conversation.Rows[0]["mediatype"].ToString() == "email" || Conversation.Rows[0]["mediatype"].ToString() == "chat")
                                    ANI = Segment["remotedisplayable"].ToString();
                            }

                            if (Segment["theldcomplete"] != null && Segment["theldcomplete"] != System.DBNull.Value && Segment["theldcomplete"].ToString() != "")
                                TotalHoldTime += Convert.ToDecimal(Segment["theldcomplete"]);

                            if (Segment["nconsulttransferred"] != null && Segment["nconsulttransferred"] != System.DBNull.Value && Segment["nconsulttransferred"].ToString() != "")
                                TotalWarmTrans += int.Parse(Segment["nconsulttransferred"].ToString());

                            if (Segment["nblindtransferred"] != null && Segment["nblindtransferred"] != System.DBNull.Value && Segment["nblindtransferred"].ToString() != "")
                                TotalColdTrans += int.Parse(Segment["nblindtransferred"].ToString());

                            if (Segment["thandle"] != null && Segment["thandle"] != System.DBNull.Value && Segment["thandle"].ToString() != "")
                            {
                                HandleCount += 1;
                                TotalHandleTime += Convert.ToDecimal(Segment["thandle"]);
                            }

                            if (Segment["tanswered"] != null && Segment["tanswered"] != System.DBNull.Value && Segment["tanswered"].ToString() != "")
                            {
                                AnswerCount += 1;
                                TotalAnswerTime += Convert.ToDecimal(Segment["tanswered"]);
                            }

                            if (Segment["tagentresponsetime"] != null && Segment["tagentresponsetime"] != System.DBNull.Value && Segment["tagentresponsetime"].ToString() != "")
                            {
                                ResponseCount += 1;
                                TotalResponseTime += Convert.ToDecimal(Segment["tagentresponsetime"]);
                            }

                            if (Segment["segmenttype"].ToString() == "hold")
                                TotalHold = TotalHold + 1;
                        }

                        if (CallBackPart > 0 && int.Parse(GenCodeSplit[0]) > CallBackPart)
                        {
                            CallBackPart = 0;
                            CallBackDetected = false;
                        }

                        LastDisconnect = Segment["disconnectiontype"].ToString();
                        LastPurpose = Segment["purpose"].ToString();

                        if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                        {
                            LastSegmentTime = Convert.ToDecimal(Segment["segmenttime"]);
                        }
                    }
                }

                ConvSummaryRow["conversationid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["keyid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["conversationstartdate"] = Conversation.Rows[0]["conversationstartdate"];
                ConvSummaryRow["conversationenddate"] = Conversation.Rows[0]["conversationenddate"];
                ConvSummaryRow["conversationstartdateltc"] = Conversation.Rows[0]["conversationstartdateltc"];
                ConvSummaryRow["conversationenddateltc"] = Conversation.Rows[0]["conversationenddateltc"];
                ConvSummaryRow["originaldirection"] = Conversation.Rows[0]["originaldirection"];
                ConvSummaryRow["firstmediatype"] = Conversation.Rows[0]["mediatype"];
                ConvSummaryRow["lastmediatype"] = LastMediaType;
                ConvSummaryRow["ani"] = ANI;
                ConvSummaryRow["dnis"] = DNIS;
                ConvSummaryRow["peer"] = LastPeer;
                ConvSummaryRow["firstagentid"] = FirstAgentId;
                ConvSummaryRow["lastagentid"] = LastAgentId;
                ConvSummaryRow["firstqueueid"] = FirstQueueId;
                ConvSummaryRow["lastqueueid"] = LastQueueId;
                ConvSummaryRow["ttalkcomplete"] = TalkTime;
                ConvSummaryRow["tqueuetime"] = QueueTime;
                ConvSummaryRow["tacw"] = WrapUpTime;
                ConvSummaryRow["tabandonedcount"] = AbandonCount;
                ConvSummaryRow["firstwrapupcode"] = FirstWrapUpCode;
                ConvSummaryRow["lastwrapupcode"] = LastWrapUpCode;
                ConvSummaryRow["theldcompletecount"] = TotalHold;
                ConvSummaryRow["theldcomplete"] = TotalHoldTime;
                ConvSummaryRow["thandlecount"] = HandleCount;
                ConvSummaryRow["thandle"] = TotalHandleTime;
                ConvSummaryRow["tansweredcount"] = AnswerCount;
                ConvSummaryRow["tanswered"] = TotalAnswerTime;
                ConvSummaryRow["tresponsecount"] = ResponseCount;
                ConvSummaryRow["tresponse"] = TotalResponseTime;
                ConvSummaryRow["nconsulttransferred"] = TotalWarmTrans;
                ConvSummaryRow["nblindtransferred"] = TotalColdTrans;
                ConvSummaryRow["lastdisconnect"] = LastDisconnect;
                ConvSummaryRow["lastpurpose"] = LastPurpose;
                ConvSummaryRow["lastsegmenttime"] = LastSegmentTime;
                ConvSummaryRow["divisionid"] = Conversation.Rows[0]["divisionid"];
                ConvSummaryRow["divisionid2"] = Conversation.Rows[0]["divisionid2"];
                ConvSummaryRow["divisionid3"] = Conversation.Rows[0]["divisionid3"];

                try
                {
                    ConversationSummaryData.Rows.Add(ConvSummaryRow);
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug(ex, "Duplicate conversation summary detected for conversation {ConversationId}",
                        Conversation.Rows.Count > 0 ? Conversation.Rows[0]["conversationid"] : "unknown");
                }
            }

            return true;
        }

        public DataSet GetDetailInteractionDataFromGCQuery(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            Console.WriteLine("Retrieving Detail Interaction Data From Date:{0} ", StartDate);
            int PageNumber = 1;
            bool FoundData = true;

            Console.WriteLine("Creating Memory Table Detailed Interaction");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");

            Console.WriteLine("Creating Memory Table Participant Summary Dynamic");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Creating Memory Table Flow Outcome Data");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            while (FoundData)
            {
                Console.WriteLine("Processing Page: {0}", PageNumber);
                string RequestBody = "{" +
                                " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                " \"order\": \"asc\"," +
                                " \"orderBy\": \"conversationEnd\"," +
                                " \"paging\": {" +
                                " \"pageSize\": 100," +
                                "  \"pageNumber\": " + PageNumber.ToString() +
                                "}}";

                Console.WriteLine("Request Body : {0} ", RequestBody);

                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/query", GCApiKey, RequestBody);

                if (JsonString.Length < 50)
                {
                    Console.WriteLine("\nInteractions: No More Data To Process");
                    FoundData = false;
                    break;
                }

                Interactions.InteractionSegmentStruct DetailData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                foreach (Interactions.Conversation Conv in DetailData.conversations)
                {
                    int PartCode = 0;
                    int SessCode = 0;
                    int SegCode = 0;
                    foreach (Interactions.Participant ConvPart in Conv.participants)
                    {
                        DataRow CheckPartExists = ParticipantSummary.Select("keyid= '" + Conv.conversationId + "|" + ConvPart.participantId + "'").FirstOrDefault();

                        DataRow DRPartSumm = ParticipantSummary.NewRow();
                        DRPartSumm["keyid"] = Conv.conversationId + "|" + ConvPart.participantId;
                        DRPartSumm["conversationid"] = Conv.conversationId;
                        DRPartSumm["participantid"] = ConvPart.participantId;
                        DRPartSumm["purpose"] = ConvPart.purpose;

                        if (Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                        {
                            DRPartSumm["divisionid"] = Conv.divisionIds[0];
                        }
                        else
                        {
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }

                        if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                        {
                            _logger?.LogDebug("Setting default division ID for participant summary in query method");
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }
                        if (Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                            DRPartSumm["divisionid2"] = Conv.divisionIds[1];

                        if (Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                            DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                        DRPartSumm["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                        DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                        if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                        {
                            DRPartSumm["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                            DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                        }
                        else
                        {
                            DRPartSumm["conversationenddate"] = System.DBNull.Value;
                            DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                        }

                        PartCode++;
                        SessCode = 0;
                        SegCode = 0;

                        foreach (Interactions.Session ConvSess in ConvPart.sessions)
                        {
                            SessCode++;
                            SegCode = 0;
                            Interactions.Flow ConvSessFlow = ConvSess.flow;
                            int SegmentCount = ConvSess.segments.Length;
                            int CurrentSegment = 1;

                            foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                            {
                                SegCode++;

                                if (!timeFieldsMillisecondResolution)
                                {
                                    ConvSeg.segmentStart = new DateTime(
                                          ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                          ConvSeg.segmentStart.Kind
                                        );

                                    ConvSeg.segmentEnd = new DateTime(
                                        ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                        ConvSeg.segmentEnd.Kind
                                     );
                                }

                                string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                                DataRow NewRow = DetailInteraction.NewRow();
                                string TempKeyid = Conv.conversationId + "|" + IterationCode;
                                NewRow["keyid"] = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);
                                NewRow["conversationid"] = Conv.conversationId;

                                if (Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                                {
                                    NewRow["divisionid"] = Conv.divisionIds[0];
                                }
                                else
                                {
                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }

                                if (NewRow["divisionid"] == null || NewRow["divisionid"] == System.DBNull.Value|| NewRow["divisionid"] is DBNull)
                                {
                                    _logger?.LogDebug("Setting default division ID for detailed interaction in query method");
                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }
                                if (Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                                    NewRow["divisionid2"] = Conv.divisionIds[1];
                                if (Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                                    NewRow["divisionid3"] = Conv.divisionIds[2];

                                NewRow["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                                NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                                if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                {
                                    NewRow["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                    NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                                }
                                else
                                {
                                    NewRow["conversationenddate"] = System.DBNull.Value;
                                    NewRow["conversationenddateltc"] = System.DBNull.Value;
                                }

                                NewRow["gencode"] = IterationCode;
                                NewRow["peer"] = ConvSess.peerId;

                                DateTime MaxDateTest = Conv.conversationStart.ToUniversalTime();
                                if (MaxDateTest > DetailInteractionLastUpdate)
                                {
                                    DetailInteractionLastUpdate = MaxDateTest;
                                    Console.Write("@");
                                }

                                NewRow["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2);
                                NewRow["originaldirection"] = Conv.originatingDirection;
                                NewRow["participantid"] = ConvPart.participantId;
                                if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                    NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                else
                                    NewRow["participantname"] = ConvPart.participantName;
                                NewRow["purpose"] = ConvPart.purpose;

                                NewRow["mediatype"] = ConvSess.mediaType;
                                DRPartSumm["mediaType"] = ConvSess.mediaType;

                                if (ConvSess.ani != null && ConvSess.ani.Length > 300)
                                    NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                else
                                    NewRow["ani"] = ConvSess.ani;

                                if (ConvSeg.queueId != null)
                                {
                                    NewRow["queueid"] = ConvSeg.queueId;
                                    DRPartSumm["queueid"] = ConvSeg.queueId;
                                }
                                if (ConvPart.userId != null)
                                {
                                    NewRow["userid"] = ConvPart.userId;
                                    DRPartSumm["userid"] = ConvPart.userId;
                                }

                                if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                    NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                else
                                    NewRow["dnis"] = ConvSess.dnis;

                                if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                    NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                else
                                    NewRow["sessiondnis"] = ConvSess.sessionDnis;

                                NewRow["sessiondirection"] = ConvSess.direction;
                                NewRow["edgeId"] = ConvSess.edgeId;
                                if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                    NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                else
                                    NewRow["remotedisplayable"] = ConvSess.remote;

                                NewRow["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2);

                                if (Conv.externalTag != null)
                                    NewRow["externalTag"] = Conv.externalTag;

                                NewRow["segmentstartdate"] = ConvSeg.segmentStart.ToUniversalTime();
                                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentStart.ToUniversalTime(), AppTimeZone);

                                TimeSpan Diff = new TimeSpan();

                                if (ConvSeg.segmentEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                {
                                    NewRow["segmentenddate"] = ConvSeg.segmentEnd.ToUniversalTime();
                                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentEnd.ToUniversalTime(), AppTimeZone);
                                    Diff = ConvSeg.segmentEnd.ToUniversalTime() - ConvSeg.segmentStart.ToUniversalTime();
                                    NewRow["segmenttime"] = Diff.TotalSeconds;
                                    Diff = ConvSeg.segmentEnd.ToUniversalTime() - Conv.conversationStart.ToUniversalTime();
                                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                }
                                else
                                {
                                    NewRow["segmentenddate"] = System.DBNull.Value;
                                    NewRow["segmenttime"] = System.DBNull.Value;
                                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                }

                                Diff = ConvSeg.segmentStart.ToUniversalTime() - Conv.conversationStart.ToUniversalTime();
                                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                                NewRow["segmenttype"] = ConvSeg.segmentType;
                                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                                string RowWrapUp = ConvSeg.wrapUpCode;
                                string RowWrapUpNote = ConvSeg.wrapUpNote;
                                if (RowWrapUp != null)
                                {
                                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                    NewRow["wrapupcode"] = RowWrapUp;
                                    DRPartSumm["wrapupcode"] = RowWrapUp;
                                    if (RowWrapUpNote != null)
                                    {
                                        NewRow["wrapupnote"] = RowWrapUpNote;
                                        DRPartSumm["wrapupnote"] = RowWrapUpNote;
                                    }
                                    else
                                    {
                                        NewRow["wrapupnote"] = "";
                                    }
                                }
                                else
                                {
                                    NewRow["wrapupcode"] = "";
                                    NewRow["wrapupnote"] = "";
                                }

                                if (ConvSeg.disconnectType == null)
                                    NewRow["disconnectiontype"] = "none";
                                else
                                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                NewRow["sessionprovider"] = ConvSess.provider;

                                if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                {
                                    NewRow["flowid"] = ConvSessFlow.flowId;
                                    NewRow["flowname"] = ConvSessFlow.flowName;

                                    try
                                    {
                                        NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                    }
                                    catch
                                    {
                                        NewRow["flowversion"] = 1.0;
                                    }

                                    NewRow["flowtype"] = ConvSessFlow.flowType;
                                    NewRow["exitreason"] = ConvSessFlow.exitReason;
                                    NewRow["entryreason"] = ConvSessFlow.entryReason;
                                    NewRow["entrytype"] = ConvSessFlow.entryType;
                                    NewRow["transfertype"] = ConvSessFlow.transferType;
                                    if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                                        NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                                    else
                                        NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                                    NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                    List<Interactions.Outcomes> ConvSessFlowOutcomes = ConvSessFlow.outcomes?.ToList() ?? new List<Interactions.Outcomes>();
                                    foreach (Interactions.Outcomes SessFlowOutcome in ConvSessFlowOutcomes)
                                    {
                                        try
                                        {
                                            if (ConvSessFlowOutcomes != null && SessFlowOutcome.flowOutcomeId != null)
                                            {
                                                DataRow DRFlowOutcomes = FlowOutcomes.NewRow();
                                                DRFlowOutcomes["keyid"] = Conv.conversationId + "|" + ConvSessFlow.flowId + "|" + SessFlowOutcome.flowOutcomeId;
                                                DRFlowOutcomes["flowid"] = ConvSessFlow.flowId;
                                                DRFlowOutcomes["flowname"] = ConvSessFlow.flowName;
                                                try
                                                {
                                                    DRFlowOutcomes["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                }
                                                catch
                                                {
                                                    DRFlowOutcomes["flowversion"] = 1.0;
                                                }
                                                DRFlowOutcomes["flowtype"] = ConvSessFlow.flowType;
                                                DRFlowOutcomes["conversationid"] = Conv.conversationId;
                                                DRFlowOutcomes["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                                                DRFlowOutcomes["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                                                if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    DRFlowOutcomes["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                                    DRFlowOutcomes["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                                                }
                                                else
                                                {
                                                    DRFlowOutcomes["conversationenddate"] = System.DBNull.Value;
                                                    DRFlowOutcomes["conversationenddateltc"] = System.DBNull.Value;
                                                }

                                                if (DateTime.TryParse(SessFlowOutcome.flowOutcomeStartTimestamp, out var parsedStartTimestamp))
                                                {
                                                    // Convert to UTC
                                                    var flowOutcomeStartDateUtc = parsedStartTimestamp.ToUniversalTime();
                                                    DRFlowOutcomes["flowoutcomestartdate"] = flowOutcomeStartDateUtc;

                                                    // Convert from UTC to the specified time zone
                                                    var flowOutcomeStartDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeStartDateUtc, AppTimeZone);
                                                    DRFlowOutcomes["flowoutcomestartdateltc"] = flowOutcomeStartDateLtc;
                                                }
                                                else
                                                {
                                                    throw new FormatException("Invalid date format in flowOutcomeStartTimestamp.");
                                                }


                                                try
                                                {
                                                    if (DateTime.TryParse(SessFlowOutcome.flowOutcomeEndTimestamp, out var parsedEndTimestamp))
                                                    {
                                                        // Convert to UTC
                                                        var flowOutcomeEndDateUtc = parsedEndTimestamp.ToUniversalTime();
                                                        DRFlowOutcomes["flowoutcomeenddate"] = flowOutcomeEndDateUtc;

                                                        // Convert from UTC to the specified time zone
                                                        var flowOutcomeEndDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeEndDateUtc, AppTimeZone);
                                                        DRFlowOutcomes["flowoutcomeenddateltc"] = flowOutcomeEndDateLtc;
                                                    }
                                                    else
                                                    {
                                                        // Log the error with the actual invalid timestamp value
                                                        _logger?.LogWarning("Invalid date format in flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}. Using null instead.",
                                                            SessFlowOutcome.flowOutcomeEndTimestamp,
                                                            DRFlowOutcomes["conversationid"]);

                                                        // Also log to console for immediate visibility during debugging
                                                        Console.WriteLine($"Invalid flowOutcomeEndTimestamp: '{SessFlowOutcome.flowOutcomeEndTimestamp}' for conversation {DRFlowOutcomes["conversationid"]}");

                                                        // Use null values instead of throwing an exception
                                                        DRFlowOutcomes["flowoutcomeenddate"] = System.DBNull.Value;
                                                        DRFlowOutcomes["flowoutcomeenddateltc"] = System.DBNull.Value;
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    // Log the exception but don't throw
                                                    _logger?.LogError(ex, "Error processing flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}",
                                                        SessFlowOutcome.flowOutcomeEndTimestamp,
                                                        DRFlowOutcomes["conversationid"]);

                                                    // Also log to console for immediate visibility during debugging
                                                    Console.WriteLine($"Exception processing flowOutcomeEndTimestamp: '{SessFlowOutcome.flowOutcomeEndTimestamp}' for conversation {DRFlowOutcomes["conversationid"]}: {ex.Message}");

                                                    // Use null values
                                                    DRFlowOutcomes["flowoutcomeenddate"] = System.DBNull.Value;
                                                    DRFlowOutcomes["flowoutcomeenddateltc"] = System.DBNull.Value;
                                                }

                                                DRFlowOutcomes["flowoutcome"] = SessFlowOutcome.flowOutcome;
                                                DRFlowOutcomes["flowoutcomeid"] = SessFlowOutcome.flowOutcomeId;
                                                DRFlowOutcomes["flowoutcomevalue"] = SessFlowOutcome.flowOutcomeValue;

                                                FlowOutcomes.Rows.Add(DRFlowOutcomes);

                                            }
                                        }
                                        catch (System.Data.ConstraintException ex)
                                        {
                                            _logger?.LogDebug(ex, "Duplicate flow outcome detected for conversation {ConversationId}", Conv.conversationId);
                                        }
                                    }
                                }

                                if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                {
                                    foreach (Interactions.Metric ConvSessMetric in ConvSess.metrics)
                                    {
                                        string FirstChar = ConvSessMetric.name.Substring(0, 1);
                                        try
                                        {
                                            switch (FirstChar)
                                            {
                                                case "n":
                                                    if (ConvSessMetric.value > 0)
                                                    {
                                                        NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                        DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                    }
                                                    break;
                                                case "t":
                                                    if (ConvSessMetric.value > 0)
                                                    {
                                                        if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                            ConvSessMetric.value += 100;

                                                        if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                        {
                                                            NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                            DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                        }
                                                        else
                                                        {
                                                            NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                            DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                        }
                                                    }
                                                    break;
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            Console.WriteLine("No Row For {0}\n Error: {1} \nSource {2}", ConvSessMetric.name, e.ToString(), e.Source);
                                        }
                                    }
                                }

                                try
                                {
                                    DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + NewRow["keyid"] + "'").FirstOrDefault();
                                    if (CheckRowExists == null)
                                    {
                                        DetailInteraction.Rows.Add(NewRow);
                                        // Record added - using debug level to avoid log flooding
                                    }
                                    else
                                    {
                                        // Duplicate record found - using debug level to avoid log flooding
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("Exception caught in Interaction Detail Module.\nError Message: {0}\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                                    throw;
                                }

                                CurrentSegment++;
                                // Segment processed - using debug level to avoid log flooding
                            }
                        }

                        if (CheckPartExists == null)
                        {
                            ParticipantSummary.Rows.Add(DRPartSumm);
                            // Participant added - using debug level to avoid log flooding
                        }
                        else
                        {
                            // Participant duplicate found - using debug level to avoid log flooding
                        }
                    }
                }

                PageNumber++;
            }

            Console.WriteLine("\nReturning {0} Row(s)", DetailInteraction.Rows.Count);

            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);

            return ReturnInteractionData;
        }

        public DataTable GetParticipantAttributes(DataTable InteractionData)
        {
            int MaxNVarCharLength = 0;
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    MaxNVarCharLength = 200;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    MaxNVarCharLength = 50;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    MaxNVarCharLength = 100;
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            Console.WriteLine("Retrieving Participant Attribute Data");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            string LastConversationId = string.Empty;
            DataTable SmallConvTable = CreateSmallConversationDetails();
            foreach (DataRow Conversation in InteractionData.Select("conversationstartdate > '" + DateTime.Now.AddDays(-92).ToUniversalTime() + "'"))
            {
                if (LastConversationId != Conversation["conversationId"].ToString())
                {
                    DataRow SmallConvRow = SmallConvTable.NewRow();
                    LastConversationId = Conversation["conversationId"].ToString();
                    SmallConvRow["conversationid"] = LastConversationId;
                    SmallConvRow["mediatype"] = Conversation["mediatype"];
                    SmallConvRow["conversationstartdate"] = Conversation["conversationstartdate"];
                    SmallConvRow["conversationenddate"] = Conversation["conversationenddate"];
                    SmallConvRow["conversationstartdateltc"] = Conversation["conversationstartdateltc"];
                    SmallConvRow["conversationenddateltc"] = Conversation["conversationenddateltc"];
                    SmallConvTable.Rows.Add(SmallConvRow);
                    // Conversation processed - using debug level to avoid log flooding
                }
            }

            Console.WriteLine("\nFinished Getting Conversation IDs. Count {0}", SmallConvTable.Rows.Count);

            LastConversationId = "";
            int ConversationCounter = 0;
            foreach (DataRow SmallConvRow in SmallConvTable.Rows)
            {
                ConversationCounter++;

                if (LastConversationId != SmallConvRow["conversationid"].ToString())
                {
                    LastConversationId = SmallConvRow["conversationid"].ToString();

                    string media;
                    switch (SmallConvRow["mediatype"].ToString())
                    {
                        case "voice":
                            media = "calls";
                            break;
                        default:
                            media = SmallConvRow["mediatype"].ToString() + "s";
                            break;
                    }

                    if ((ConversationCounter % 275) == 0)
                    {
                        _logger?.LogDebug("Processing conversation {ConversationCounter}, refreshing API key", ConversationCounter);
                        bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
                        GCApiKey = GCUtilities.GCApiKey;
                    }

                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/conversations/" + media + "/" + LastConversationId, GCApiKey);

                    if (JsonString.Length > 10)
                    {
                        if (ConversationCounter % 100 == 0)
                        {
                            _logger?.LogDebug("Processed {ConversationCounter} participant attributes", ConversationCounter);
                        }

                        PartAttribs.ParticipantAttributes DetailData = JsonConvert.DeserializeObject<PartAttribs.ParticipantAttributes>(JsonString,
                                          new JsonSerializerSettings
                                          {
                                              NullValueHandling = NullValueHandling.Ignore
                                          });

                        if (DetailData != null)
                        {
                            DataRow DRPartAttrib = ParticipantAttributes.NewRow();
                            DRPartAttrib["keyid"] = SmallConvRow["conversationid"];
                            DRPartAttrib["conversationid"] = SmallConvRow["conversationid"];
                            DRPartAttrib["conversationstartdate"] = SmallConvRow["conversationstartdate"];
                            DRPartAttrib["conversationstartdateltc"] = SmallConvRow["conversationstartdateltc"];
                            if (SmallConvRow["conversationenddate"] != null && SmallConvRow["conversationenddate"] != System.DBNull.Value)
                            {
                                if (Convert.ToDateTime(SmallConvRow["conversationenddate"]) > DateTime.UtcNow.AddYears(-20))
                                {
                                    DRPartAttrib["conversationenddate"] = SmallConvRow["conversationenddate"];
                                    DRPartAttrib["conversationenddateltc"] = SmallConvRow["conversationenddateltc"];
                                }
                            }

                            // Add null check for participants collection
                            if (DetailData.participants != null)
                            {
                                foreach (PartAttribs.Participant ConvPart in DetailData.participants)
                            {
                                // Add null check for participant
                                if (ConvPart == null)
                                {
                                    _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.",
                                        SmallConvRow["conversationid"]);
                                    continue;
                                }

                                if (ConvPart.attributes != null)
                                {
                                    Dictionary<string, string> values = null;
                                    try
                                    {
                                        string attributesJson = ConvPart.attributes?.ToString();
                                        if (!string.IsNullOrWhiteSpace(attributesJson))
                                        {
                                            values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger?.LogWarning(ex, "Failed to deserialize participant attributes for conversation {ConversationId}. Skipping attributes processing.",
                                            SmallConvRow["conversationid"]);
                                        continue; // Skip this participant's attributes and move to the next
                                    }

                                    if (values == null)
                                    {
                                        _logger?.LogDebug("No valid participant attributes found for conversation {ConversationId}",
                                            SmallConvRow["conversationid"]);
                                        continue; // Skip this participant's attributes and move to the next
                                    }

                                    DataColumnCollection columns = ParticipantAttributes.Columns;

                                    foreach (KeyValuePair<string, string> kvp in values)
                                    {
                                        if (!string.IsNullOrEmpty(kvp.Key))
                                        {
                                            string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                            if (ConParamName != null && ConParamName.Length <= 55)
                                                ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                            else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                            if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                ConParamName = ConParamName.ToLower();

                                            if (!columns.Contains(ConParamName))
                                            {
                                                ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                Console.WriteLine("Adding Column:{0} as {1}", kvp.Key, ConParamName);
                                            }
                                        }
                                    }

                                    ParticipantAttributes.AcceptChanges();

                                    foreach (KeyValuePair<string, string> kvp in values)
                                    {
                                        string ConParamName = String.Empty;
                                        string AttribValue = String.Empty;
                                        string AttribName = String.Empty;

                                        if (!string.IsNullOrEmpty(kvp.Key))
                                        {
                                            ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                            if (ConParamName == null) continue;

                                            if (ConParamName != null && ConParamName.Length <= 55)
                                                ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                            else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                            if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                ConParamName = ConParamName.ToLower();

                                            switch (DBUtil.DBType)
                                            {
                                                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                    AttribName = ConParamName;
                                                    break;
                                                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                    if (ConParamName != null && ConParamName.Length <= 55)
                                                        AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                    else if (kvp.Value != null && ConParamName.Length >= 55)
                                                        AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                    break;
                                            default:
                                                throw new NotImplementedException("Database type is not implemented");
                                            }

                                            AttribValue = kvp.Value ?? "";
                                            if (AttribValue.Length > MaxNVarCharLength)
                                                AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                            DRPartAttrib[AttribName] = AttribValue;
                                        }
                                    }
                                }
                            }
                            }
                            else
                            {
                                _logger?.LogDebug("No participants found for conversation {ConversationId} in GetParticipantAttributes",
                                    SmallConvRow["conversationid"]);
                            }

                            ParticipantAttributes.Rows.Add(DRPartAttrib);
                            // Participant attribute added - using debug level to avoid log flooding
                        }
                    }
                }
            }

            Console.WriteLine("Number of Part Rows Returning: {0} ", ParticipantAttributes.Rows.Count);
            return ParticipantAttributes;
        }

        public DataTable CreateSmallConversationDetails()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.Columns.Add("conversationid", typeof(String));
            DTTemp.Columns.Add("conversationstartdate", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddate", typeof(DateTime));
            DTTemp.Columns.Add("conversationstartdateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationminmos", typeof(decimal));
            DTTemp.Columns.Add("conversationminrfactor", typeof(decimal));
            DTTemp.Columns.Add("originaldirection", typeof(String));
            DTTemp.Columns.Add("mediatype", typeof(String));
            DTTemp.Columns.Add("ani", typeof(String));
            DTTemp.Columns.Add("dnis", typeof(String));
            DTTemp.Columns.Add("remoteName", typeof(String));

            DTTemp.TableName = "simpleInteractionData";

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;
        }

        private int ConvertBoolean(bool Inbound)
        {
            return Inbound ? 1 : 0;
        }

        private int DetermineMaxNVarCharLength()
        {
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    return 200;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    return 50;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    return 100;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }

        public async Task<DataSet> GetDetailInteractionDataFromGCJob(String StartDate, String EndDate)
        {
            int MaxNVarCharLength = DetermineMaxNVarCharLength();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Console.WriteLine("Using timezone: {0}", TimeZoneConfig);
            Console.WriteLine("Data retrieval window: {0} to {1}", StartDate, EndDate);

            DataSet DSDetailInteraction = new DataSet();
            Console.WriteLine("Initializing detailed interaction data table");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");
            bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);

            Console.WriteLine("Initializing participant attributes data table");
            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            Console.WriteLine("Initializing participant summary data table");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Initializing flow outcome data table");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            Console.WriteLine("\nRetrieving detailed interaction data starting from: {0}", StartDate);

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";

            string RequestBody = "{" +
                                 " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                 " \"order\": \"asc\"," +
                                 " \"orderBy\": \"conversationEnd\"" +
                                 "}";

            Console.WriteLine("API request body:\n{0}", RequestBody);
            var apiResponse = JsonActions.JsonReturnHttpResponse(URI + "/api/v2/analytics/conversations/details/jobs", GCApiKey, RequestBody);

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                Console.WriteLine("\nError: Empty response received from API - aborting data retrieval");
                return DSDetailInteraction;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess && !apiResponse.IsAccepted)
            {
                _logger?.LogError("API Error in GetDetailInteractionDataFromGCJob: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types that should halt processing
                if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                {
                    _logger?.LogError("Critical API error detected (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                    throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode}: {apiResponse.Content}");
                }

                // For other errors, return empty dataset to indicate failure
                Console.WriteLine("\nError: API returned error response - aborting data retrieval");
                return DSDetailInteraction;
            }

            DetInt.ReportJob? JobID = null;
            try
            {
                JobID = JsonConvert.DeserializeObject<DetInt.ReportJob>(apiResponse.Content,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });
            }
            catch (JsonException jsonEx)
            {
                // Include problematic JSON in the error message for debugging
                string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                _logger?.LogError(jsonEx, "JSON Deserialization Error in GetDetailInteractionDataFromGCJob. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                    apiResponse.StatusCode, jsonPreview);
                throw;
            }

            if (JobID == null)
            {
                Console.WriteLine("Error: Failed to parse job ID from API response");
                return DSDetailInteraction;
            }

            // Wait for job completion using polling
            _logger?.LogInformation("Waiting for job {JobId} completion via polling", JobID.jobId);
            var jobCompletionResult = await WaitForJobCompletionViaPollingAsync(URI, JobID.jobId);

            if (!jobCompletionResult.Success)
            {
                _logger?.LogError("Interactions: Job {JobId} failed - {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);
                Console.WriteLine($"\nInteractions: Job Failed - {jobCompletionResult.ErrorMessage}");

                // If the error is related to a critical failure, we should throw an exception
                // to make sure the job doesn't exit with a success code
                if (jobCompletionResult.ErrorMessage.Contains("Timeout waiting for job") ||
                    jobCompletionResult.ErrorMessage.Contains("Error waiting for job completion"))
                {
                    _logger?.LogError("Failed to poll job {JobId} status: {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);
                    throw new InvalidOperationException($"Failed to poll job {JobID.jobId} status: {jobCompletionResult.ErrorMessage}");
                }

                return DSDetailInteraction;
            }

            _logger?.LogInformation("Job {JobId} completed successfully via polling", JobID.jobId);

            Console.WriteLine("\nInteractions: Job ID:{0} Status:FULFILLED", JobID.jobId);

            string LastCursor = String.Empty;
            int cursorLoopCount=0;
            List<DetInt.DetailedInteractions> detailedInteractionsList = new List<DetInt.DetailedInteractions>();

            var detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
            while (detailedInteractions != null && detailedInteractions.conversations != null && detailedInteractions.conversations.Count() > 0)
            {
                Console.WriteLine("Retrieving data page {1} with cursor: {0}", detailedInteractions.cursor, cursorLoopCount);
                detailedInteractionsList.Add(detailedInteractions);
                if (string.IsNullOrEmpty(detailedInteractions.cursor))
                    break;
                LastCursor = detailedInteractions.cursor;
                detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
                cursorLoopCount++;
            }

            // Flatten all conversations into one list
            var allConversations = detailedInteractionsList
                .Where(d => d.conversations != null && d.conversations.Count() > 0)
                .SelectMany(d => d.conversations)
                .ToList();

            int batchSize = 50000;
            if (allConversations.Count > 0)
            {
                var batches = allConversations
                    .Select((Conv, index) => new { Conv, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(g => g.Select(x => x.Conv).ToList())
                    .ToList();
                Console.WriteLine($"Processing data in {batches.Count} batches");

                bool result = await ProcessDataAsync(batches, DetailInteraction, ParticipantSummary, ParticipantAttributes, FlowOutcomes, AppTimeZone, timeFieldsMillisecondResolution, MaxNVarCharLength);
                Console.WriteLine("All data batches processed successfully");
            }
            else
            {
                Console.WriteLine("\nNo conversation data returned from API");
            }

            DSDetailInteraction.Tables.Add(DetailInteraction);
            DSDetailInteraction.Tables.Add(ParticipantAttributes);
            DSDetailInteraction.Tables.Add(ParticipantSummary);
            DSDetailInteraction.Tables.Add(FlowOutcomes);
            Console.Write("\n");
            Console.WriteLine("Latest conversation date found: {0}", DetailInteractionLastUpdate);

            return DSDetailInteraction;
        }

        private async Task<DetInt.DetailedInteractions> FetchDataAsync(string uri, string apiKey, string jobID, string LastCursor)
        {
            try
            {
                string CursorString = string.IsNullOrEmpty(LastCursor) ? "" : "?cursor=" + HttpUtility.UrlEncode(LastCursor);
                string url = $"{uri}/api/v2/analytics/conversations/details/jobs/{jobID}/results{CursorString}";

                var apiResponse = await JsonActions.JsonReturnHttpResponseGetAsync(url, apiKey);

                // Validate response using proper HTTP status code detection
                if (string.IsNullOrWhiteSpace(apiResponse.Content))
                {
                    _logger?.LogWarning("Empty response received in FetchDataAsync - returning null");
                    return null;
                }

                // Handle different HTTP status codes appropriately
                if (!apiResponse.IsSuccess)
                {
                    _logger?.LogError("API Error in FetchDataAsync: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                        apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                    // Check for specific error types that should halt processing
                    if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                    {
                        _logger?.LogError("Critical API error detected in FetchDataAsync (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                        throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode} in FetchDataAsync: {apiResponse.Content}");
                    }

                    // For other errors, return null to indicate failure
                    _logger?.LogWarning("Returning null from FetchDataAsync due to HTTP {StatusCode} error", apiResponse.StatusCode);
                    return null;
                }

                DetInt.DetailedInteractions detailData = null;
                try
                {
                    detailData = JsonConvert.DeserializeObject<DetInt.DetailedInteractions>(apiResponse.Content,
                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    // Include problematic JSON in the error message for debugging
                    string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                    _logger?.LogError(jsonEx, "JSON Deserialization Error in FetchDataAsync. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                        apiResponse.StatusCode, jsonPreview);
                    throw;
                }

                return detailData;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving data from API in FetchDataAsync: {Message}", ex.Message);
                Console.WriteLine($"Error retrieving data from API: {ex.Message}");
                return null;
            }
        }

        private async Task<bool> ProcessDataAsync(
            IEnumerable<IEnumerable<DetInt.Conversation>> batches,
            DataTable DetailInteraction,
            DataTable ParticipantSummary,
            DataTable ParticipantAttributes,
            DataTable FlowOutcomes,
            TimeZoneInfo AppTimeZone,
            bool timeFieldsMillisecondResolution,
            int MaxNVarCharLength)
        {
            var taskExceptions = new ConcurrentBag<Exception>();

            var detailInteractionRows = new ConcurrentBag<DataRow>();
            var participantSummaryRows = new ConcurrentBag<DataRow>();
            var participantAttributeRows = new ConcurrentBag<DataRow>();
            var flowOutcomesRows = new ConcurrentBag<DataRow>();

            int processedCount = 0;
            Stopwatch totalStopwatch = Stopwatch.StartNew();
            Stopwatch segmentStopwatch = Stopwatch.StartNew();

            // We will print a status message every time we process another 1000 records
            int printInterval = 100;

            var tasks = batches.Select(batch =>
                Task.Run(() =>
                {
                    try
                    {
                        foreach (var Conv in batch)
                        {
                            DataRow DRPartAttrib = ParticipantAttributes.NewRow();
                            DRPartAttrib["keyid"] = Conv.conversationId;
                            DRPartAttrib["conversationid"] = Conv.conversationId;
                            DRPartAttrib["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                            DRPartAttrib["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                            if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                            {
                                DRPartAttrib["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                DRPartAttrib["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                            }

                            int PartCode = 0;
                            if (Conv.participants != null)
                            {
                                foreach (var ConvPart in Conv.participants)
                                {
                                    // Add null check for participant
                                    if (ConvPart == null)
                                    {
                                        _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    // Add null check for critical participant properties
                                    if (string.IsNullOrEmpty(ConvPart.participantId))
                                    {
                                        _logger?.LogWarning("Participant with null or empty participantId found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    DataRow DRPartSumm = ParticipantSummary.NewRow();
                                    DRPartSumm["keyid"] = Conv.conversationId + "|" + ConvPart.participantId;
                                    DRPartSumm["conversationid"] = Conv.conversationId;
                                    DRPartSumm["participantid"] = ConvPart.participantId;
                                    DRPartSumm["purpose"] = ConvPart.purpose;

                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                                    {
                                        DRPartSumm["divisionid"] = Conv.divisionIds[0];
                                    }
                                    else
                                    {
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }

                                    if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                                    {
                                        _logger?.LogDebug("Setting default division ID for participant summary in async processing");
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                                        DRPartSumm["divisionid2"] = Conv.divisionIds[1];
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                                        DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                                    DRPartSumm["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                                    DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                                    if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                    {
                                        DRPartSumm["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                        DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                                    }
                                    else
                                    {
                                        DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                        DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                    }

                                    PartCode++;
                                    int SessCode = 0;
                                    int SegCode = 0;

                                    // Handle participant attributes safely
                                    if (ConvPart.attributes != null)
                                    {
                                        Dictionary<string, string> values = null;
                                        try
                                        {
                                            string attributesJson = ConvPart.attributes?.ToString();
                                            if (!string.IsNullOrWhiteSpace(attributesJson))
                                            {
                                                values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogWarning(ex, "Failed to deserialize participant attributes for conversation {ConversationId} in async processing. Skipping attributes processing.",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        if (values == null)
                                        {
                                            _logger?.LogDebug("No valid participant attributes found for conversation {ConversationId} in async processing",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        DataColumnCollection columns = ParticipantAttributes.Columns;

                                        // Add columns if needed
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                    ConParamName = ConParamName.ToLower();

                                                if (!columns.Contains(ConParamName))
                                                {
                                                    lock (_participantAttributeLock)
                                                    {
                                                        if (!columns.Contains(ConParamName))
                                                            ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                    }
                                                }
                                            }
                                        }

                                        // Assign attribute values
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            string ConParamName = String.Empty;
                                            string AttribValue = String.Empty;
                                            string AttribName = String.Empty;

                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                                if (ConParamName == null) continue;

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                    ConParamName = ConParamName.ToLower();

                                                switch (DBUtil.DBType)
                                                {
                                                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                        AttribName = ConParamName;
                                                        break;
                                                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                        if (ConParamName != null && ConParamName.Length <= 55)
                                                            AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        else if (kvp.Value != null && ConParamName.Length >= 55)
                                                            AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        break;
                                                default:
                                                    throw new NotImplementedException("Database type is not implemented");
                                                }

                                                AttribValue = kvp.Value ?? "";
                                                if (AttribValue.Length > MaxNVarCharLength)
                                                    AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                                DRPartAttrib[AttribName] = AttribValue;
                                            }
                                        }
                                    }

                                    if (ConvPart.sessions != null)
                                    {
                                        foreach (DetInt.Session ConvSess in ConvPart.sessions)
                                        {
                                            SessCode++;
                                            SegCode = 0;
                                            //Console.WriteLine("Currently Looking at Session  :{0}", ConvSess.sessionId);

                                            DetInt.Flow ConvSessFlow = ConvSess.flow;
                                            int SegmentCount = ConvSess.segments.Length;
                                            int CurrentSegment = 1;

                                            foreach (DetInt.Segment ConvSeg in ConvSess.segments)
                                            {
                                                SegCode++;
                                                //Console.WriteLine("Currently Looking at Segment  :{0}", ConvSeg.segmentType);

                                                string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                                                DataRow NewRow = DetailInteraction.NewRow();
                                                string TempKeyid = Conv.conversationId + "|" + IterationCode;
                                                string rowKey = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);

                                                NewRow["keyid"] = rowKey;
                                                //NewRow["keyid"] = Conv.conversationId;
                                                NewRow["conversationid"] = Conv.conversationId;


                                                if (Conv.divisionIds[0] != null && Conv.divisionIds[0] != "")
                                                {
                                                    NewRow["divisionid"] = Conv.divisionIds[0];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                                }

                                                if (NewRow["divisionid"] == null
                                                    || NewRow["divisionid"] is DBNull
                                                    || (NewRow["divisionid"] is string str && string.IsNullOrWhiteSpace(str)))
                                                {
                                                    _logger?.LogDebug("Setting default division ID for detailed interaction in async processing");
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-000000000000";
                                                }

                                                if (Conv.divisionIds.Count() > 1 && Conv.divisionIds[1] != null && Conv.divisionIds[1] != "")
                                                {
                                                    NewRow["divisionid2"] = Conv.divisionIds[1];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid2"] = "";
                                                }
                                                if (Conv.divisionIds.Count() > 2 && Conv.divisionIds[2] != null && Conv.divisionIds[2] != "")
                                                {
                                                    NewRow["divisionid3"] = Conv.divisionIds[2];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid3"] = "";
                                                }
                                                NewRow["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                                                NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                                                if (Conv.conversationEnd.ToUniversalTime() != null && Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    NewRow["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                                    NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                                                }

                                                NewRow["gencode"] = IterationCode;

                                                DateTime MaxDateTest = Conv.conversationStart.ToUniversalTime();
                                                if (MaxDateTest > DetailInteractionLastUpdate)
                                                {
                                                    DetailInteractionLastUpdate = MaxDateTest;
                                                    // Console.Write("@");
                                                }

                                                NewRow["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2);
                                                NewRow["originaldirection"] = Conv.originatingDirection;
                                                NewRow["participantid"] = ConvPart.participantId;
                                                NewRow["peer"] = ConvSess.peerId;
                                                if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                                    NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                                else
                                                    NewRow["participantname"] = ConvPart.participantName;
                                                NewRow["purpose"] = ConvPart.purpose;
                                                NewRow["mediatype"] = ConvSess.mediaType;

                                                DRPartSumm["mediaType"] = ConvSess.mediaType;

                                                if (ConvSess.ani != null && ConvSess.ani.Length > 300
                                                                        && (ConvSess.ani.IndexOf("tel:") > 0 || ConvSess.ani.IndexOf("sip:") > 0))
                                                    NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                                else
                                                    NewRow["ani"] = ConvSess.ani;

                                                if (ConvSeg.queueId != null)
                                                {
                                                    NewRow["queueid"] = ConvSeg.queueId;
                                                    DRPartSumm["queueid"] = ConvSeg.queueId;
                                                }
                                                if (ConvPart.userId != null)
                                                {
                                                    NewRow["userid"] = ConvPart.userId;
                                                    DRPartSumm["userid"] = ConvPart.userId;
                                                }



                                                if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                                    NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                                else
                                                    NewRow["dnis"] = ConvSess.dnis;


                                                if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                                else
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis;


                                                NewRow["sessiondirection"] = ConvSess.direction;
                                                NewRow["edgeId"] = ConvSess.edgeId;
                                                if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                                    NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                                else
                                                    NewRow["remotedisplayable"] = ConvSess.remote;

                                                NewRow["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2);
                                                if (Conv.externalTag != null)
                                                    NewRow["externalTag"] = Conv.externalTag;

                                                if (! timeFieldsMillisecondResolution)
                                                {
                                                    ConvSeg.segmentStart = new DateTime(
                                                        ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentStart.Kind
                                                    );

                                                    ConvSeg.segmentEnd = new DateTime(
                                                        ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentEnd.Kind
                                                    );
                                                }
                                                NewRow["segmentstartdate"] = ConvSeg.segmentStart.ToUniversalTime();
                                                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentStart.ToUniversalTime(), AppTimeZone);


                                                System.TimeSpan Diff = new System.TimeSpan();

                                                if (ConvSeg.segmentEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    NewRow["segmentenddate"] = ConvSeg.segmentEnd.ToUniversalTime();
                                                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvSeg.segmentEnd.ToUniversalTime(), AppTimeZone);
                                                    Diff = ConvSeg.segmentEnd.ToUniversalTime() - ConvSeg.segmentStart.ToUniversalTime();
                                                    NewRow["segmenttime"] = Diff.TotalSeconds;
                                                    Diff = ConvSeg.segmentEnd.ToUniversalTime() - Conv.conversationStart.ToUniversalTime();
                                                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                                }
                                                else
                                                {
                                                    NewRow["segmentenddate"] = System.DBNull.Value;
                                                    NewRow["segmenttime"] = System.DBNull.Value;
                                                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                                }

                                                Diff = ConvSeg.segmentStart.ToUniversalTime() - Conv.conversationStart.ToUniversalTime();
                                                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;


                                                NewRow["segmenttype"] = ConvSeg.segmentType;
                                                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;


                                                string RowWrapUp = ConvSeg.wrapUpCode;
                                                string RowWrapUpNote = ConvSeg.wrapUpNote;

                                                if (RowWrapUp != null)
                                                {
                                                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                                    NewRow["wrapupcode"] = RowWrapUp;
                                                    DRPartSumm["wrapupcode"] = RowWrapUp;
                                                    if (RowWrapUpNote != null)
                                                    {
                                                        NewRow["wrapupnote"]= ConvSeg.wrapUpNote;
                                                        DRPartSumm["wrapupnote"] =ConvSeg.wrapUpNote;
                                                    }
                                                    else
                                                    {
                                                        NewRow["wrapupnote"]= "";
                                                    }

                                                }
                                                else
                                                {
                                                    NewRow["wrapupcode"] = "";
                                                    NewRow["wrapupnote"]= "";
                                                }

                                                if (ConvSeg.disconnectType == null)
                                                    NewRow["disconnectiontype"] = "none";
                                                else
                                                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                                NewRow["sessionprovider"] = ConvSess.provider;


                                                if (CurrentSegment == SegmentCount)
                                                {
                                                    //Insert Flow Data into last Segment
                                                    if (ConvSessFlow != null)
                                                    {
                                                        if (ConvSessFlow.flowId != null)
                                                        {
                                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                                            if (ConvSessFlow.flowName != null && ConvSessFlow.flowName.Length > 254)
                                                                NewRow["flowname"] = ConvSessFlow.flowName.Substring(0, 254);
                                                            else
                                                                NewRow["flowname"] = ConvSessFlow.flowName;
                                                            try
                                                            {
                                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                            }
                                                            catch
                                                            {
                                                                NewRow["flowversion"] = 1.0;
                                                            }
                                                            NewRow.SetFieldValue(rowKey, "flowtype", ConvSessFlow.flowType);
                                                            NewRow.SetFieldValue(rowKey, "exitreason", ConvSessFlow.exitReason);
                                                            NewRow.SetFieldValue(rowKey, "entryreason", ConvSessFlow.entryReason);
                                                            NewRow.SetFieldValue(rowKey, "entrytype", ConvSessFlow.entryType);
                                                            NewRow.SetFieldValue(rowKey, "transfertype", ConvSessFlow.transferType);
                                                            if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                                                            else
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                                            List<DetInt.Outcomes> ConvSessFlowOutcomes = ConvSessFlow.outcomes?.ToList() ?? new List<DetInt.Outcomes>();
                                                            foreach (DetInt.Outcomes SessFlowOutcome in ConvSessFlowOutcomes)
                                                            {
                                                                try
                                                                {
                                                                    if (ConvSessFlowOutcomes != null && SessFlowOutcome.flowOutcomeId != null)
                                                                    {
                                                                        DataRow DRFlowOutcomes = FlowOutcomes.NewRow();

                                                                        DRFlowOutcomes["keyid"] = Conv.conversationId + "|" + ConvSessFlow.flowId + "|" + SessFlowOutcome.flowOutcomeId;
                                                                        DRFlowOutcomes["flowid"] = ConvSessFlow.flowId;
                                                                        DRFlowOutcomes["flowname"] = ConvSessFlow.flowName;
                                                                        try
                                                                        {
                                                                            DRFlowOutcomes["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                                        }
                                                                        catch
                                                                        {
                                                                            DRFlowOutcomes["flowversion"] = 1.0;
                                                                        }
                                                                        DRFlowOutcomes.SetFieldValue(rowKey, "flowtype", ConvSessFlow.flowType);
                                                                        DRFlowOutcomes["conversationid"] = Conv.conversationId;
                                                                        DRFlowOutcomes["conversationstartdate"] = Conv.conversationStart.ToUniversalTime();
                                                                        DRFlowOutcomes["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);

                                                                        if (Conv.conversationEnd.ToUniversalTime() > DateTime.UtcNow.AddYears(-20))
                                                                        {
                                                                            DRFlowOutcomes["conversationenddate"] = Conv.conversationEnd.ToUniversalTime();
                                                                            DRFlowOutcomes["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd.ToUniversalTime(), AppTimeZone);
                                                                        }
                                                                        else
                                                                        {
                                                                            DRFlowOutcomes["conversationenddate"] = System.DBNull.Value;
                                                                            DRFlowOutcomes["conversationenddateltc"] = System.DBNull.Value;
                                                                        }

                                                                        if (DateTime.TryParse(SessFlowOutcome.flowOutcomeStartTimestamp, out var parsedStartTimestamp))
                                                                        {
                                                                            // Convert to UTC
                                                                            var flowOutcomeStartDateUtc = parsedStartTimestamp.ToUniversalTime();
                                                                            DRFlowOutcomes["flowoutcomestartdate"] = flowOutcomeStartDateUtc;

                                                                            // Convert from UTC to the specified time zone
                                                                            var flowOutcomeStartDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeStartDateUtc, AppTimeZone);
                                                                            DRFlowOutcomes["flowoutcomestartdateltc"] = flowOutcomeStartDateLtc;
                                                                        }
                                                                        else
                                                                        {
                                                                            throw new FormatException("Invalid date format in flowOutcomeStartTimestamp.");
                                                                        }


                                                                        try
                                                                        {
                                                                            if (DateTime.TryParse(SessFlowOutcome.flowOutcomeEndTimestamp, out var parsedEndTimestamp))
                                                                            {
                                                                                // Convert to UTC
                                                                                var flowOutcomeEndDateUtc = parsedEndTimestamp.ToUniversalTime();
                                                                                DRFlowOutcomes["flowoutcomeenddate"] = flowOutcomeEndDateUtc;

                                                                                // Convert from UTC to the specified time zone
                                                                                var flowOutcomeEndDateLtc = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeEndDateUtc, AppTimeZone);
                                                                                DRFlowOutcomes["flowoutcomeenddateltc"] = flowOutcomeEndDateLtc;
                                                                            }
                                                                            else if (SessFlowOutcome.flowOutcomeEndTimestamp != null)
                                                                            {
                                                                                // Log the error with the actual invalid timestamp value
                                                                                _logger?.LogWarning("Invalid date format in flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}. Will throw exception.",
                                                                                    SessFlowOutcome.flowOutcomeEndTimestamp,
                                                                                    DRFlowOutcomes["conversationid"]);

                                                                                // Also log to console for immediate visibility during debugging
                                                                                Console.WriteLine($"Invalid flowOutcomeEndTimestamp: '{SessFlowOutcome.flowOutcomeEndTimestamp}' for conversation {DRFlowOutcomes["conversationid"]}. Will throw exception.");

                                                                                // Throw the original exception to maintain existing behavior
                                                                                throw new FormatException($"Invalid date format in flowOutcomeEndTimestamp: '{SessFlowOutcome.flowOutcomeEndTimestamp}'");
                                                                            }
                                                                        }
                                                                        catch (Exception ex)
                                                                        {
                                                                            // Log the exception with detailed information
                                                                            _logger?.LogError(ex, "Error processing flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}. Will rethrow exception.",
                                                                                SessFlowOutcome.flowOutcomeEndTimestamp,
                                                                                DRFlowOutcomes["conversationid"]);

                                                                            // Also log to console for immediate visibility during debugging
                                                                            Console.WriteLine($"Exception processing flowOutcomeEndTimestamp: '{SessFlowOutcome.flowOutcomeEndTimestamp}' for conversation {DRFlowOutcomes["conversationid"]}: {ex.Message}. Will rethrow exception.");

                                                                            // Re-throw the exception to maintain existing behavior
                                                                            throw;
                                                                        }

                                                                        DRFlowOutcomes["flowoutcome"] = SessFlowOutcome.flowOutcome;
                                                                        DRFlowOutcomes["flowoutcomeid"] = SessFlowOutcome.flowOutcomeId;
                                                                        DRFlowOutcomes["flowoutcomevalue"] = SessFlowOutcome.flowOutcomeValue;

                                                                        flowOutcomesRows.Add(DRFlowOutcomes);

                                                                    }
                                                                }
                                                                catch (System.Data.ConstraintException ex)
                                                                {
                                                                    _logger?.LogDebug(ex, "Duplicate flow outcome detected for conversation {ConversationId}", Conv.conversationId);
                                                                }
                                                            }
                                                        }
                                                    }

                                                    if (ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                                    {

                                                        foreach (DetInt.Metric ConvSessMetric in ConvSess.metrics)
                                                        {
                                                            string FirstChar = ConvSessMetric.name.Substring(0, 1);

                                                            try
                                                            {
                                                                // Check if the column exists in both tables before attempting assignment
                                                                bool detailColumnExists = DetailInteraction.Columns.Contains(ConvSessMetric.name);
                                                                bool summaryColumnExists = ParticipantSummary.Columns.Contains(ConvSessMetric.name);

                                                                if (!detailColumnExists && !summaryColumnExists)
                                                                {
                                                                    _logger?.LogDebug("Metric column '{MetricName}' not found in database schema - skipping assignment", ConvSessMetric.name);
                                                                    continue;
                                                                }

                                                                switch (FirstChar)
                                                                {
                                                                    case "n":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (detailColumnExists)
                                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                            if (summaryColumnExists)
                                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                                        }
                                                                        break;
                                                                    case "t":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                                ConvSessMetric.value = ConvSessMetric.value + 100;

                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                            {
                                                                                var adjustedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = adjustedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = adjustedValue;
                                                                            }
                                                                            else
                                                                            {
                                                                                var roundedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = roundedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = roundedValue;
                                                                            }
                                                                        }
                                                                        break;
                                                                }
                                                            }
                                                            catch (Exception ex)
                                                            {
                                                                _logger?.LogWarning(ex, "Failed to assign metric '{MetricName}' with value {MetricValue}", ConvSessMetric.name, ConvSessMetric.value);
                                                            }

                                                        }
                                                    }

                                                    //if (ConvSess.metrics != null || ConvSessFlow != null)
                                                    //    Console.Write("%");
                                                }
                                                detailInteractionRows.Add(NewRow);

                                                CurrentSegment++;
                                                // Console.Write("#");
                                            }
                                        }
                                    }

                                    participantSummaryRows.Add(DRPartSumm);
                                }
                            }

                            participantAttributeRows.Add(DRPartAttrib);

                            // Increment the processed counter for every conversation
                            int localCount = Interlocked.Increment(ref processedCount);

                            // Every 1000 records, print a status
                            if (localCount % printInterval == 0)
                            {
                                var elapsed = segmentStopwatch.Elapsed;
                                Console.WriteLine($"Processing progress: {localCount} records processed in {elapsed.TotalSeconds:F2} seconds");
                                segmentStopwatch.Restart(); // reset the segment stopwatch
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        taskExceptions.Add(ex);
                    }
                })
            ).ToList();

            try
            {
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                taskExceptions.Add(ex);
            }

            if (taskExceptions.Count > 0)
            {
                Console.WriteLine("Error: Exceptions occurred during data processing:");
                foreach (var ex in taskExceptions)
                {
                    Console.WriteLine(ex.ToString());
                }
                return false;
            }

            totalStopwatch.Stop();

            Console.WriteLine("Merging processed data into database tables...");

            lock (_detailInteractionLock)
            {
                foreach (var row in detailInteractionRows)
                {
                    try
                    {
                        DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckRowExists == null)
                            DetailInteraction.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to detailed interaction table: {0}", ex.Message);
                    }
                }
            }

            lock (_participantSummaryLock)
            {
                foreach (var row in participantSummaryRows)
                {
                    try
                    {
                        DataRow CheckPartSumm = ParticipantSummary.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartSumm == null)
                            ParticipantSummary.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to participant summary table: {0}", ex.Message);
                    }
                }
            }

            lock (_participantAttributeLock)
            {
                foreach (var row in participantAttributeRows)
                {
                    try
                    {
                        DataRow CheckPartAttrib = ParticipantAttributes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartAttrib == null)
                            ParticipantAttributes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to participant attributes table: {0}", ex.Message);
                    }
                }
            }

            lock (_flowOutcomesLock)
            {
                foreach (var row in flowOutcomesRows)
                {
                    try
                    {
                        DataRow CheckFlowOutcome = FlowOutcomes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckFlowOutcome == null)
                            FlowOutcomes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to flow outcomes table: {0}", ex.Message);
                    }
                }
            }

            Console.WriteLine("All conversation data processed successfully.");
            Console.WriteLine($"Processing summary: {processedCount} total interactions processed in {totalStopwatch.Elapsed.TotalSeconds:F2} seconds");

            return true;
        }

    }

    public class JobDateLimit
    {
        public DateTime dataAvailabilityDate { get; set; }
    }
}
