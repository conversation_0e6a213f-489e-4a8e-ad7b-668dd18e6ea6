IF dbo.csg_table_exists('flowoutcomedetails') = 0
CREATE TABLE [flowoutcomedetails](
    [id] [nvarchar](255) NOT NULL,
    [name] [nvarchar](50) NOT NULL,
    [divisionid] [nvarchar](50),
    [description] [nvarchar](50),
    [operationid] [nvarchar](50),
    [operationcomplete] [bit],
    [userid] [nvarchar](50),
    [clientid] [nvarchar](50),
    [errormessage] [nvarchar](255),
    [errorcode] [nvarchar](50),
    [actionname] [nvarchar](50),
    [actionstatus] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK__flowOutcomeDetails] PRIMARY KEY ([id])
);
