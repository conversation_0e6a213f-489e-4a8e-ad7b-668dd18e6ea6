DROP VIEW IF EXISTS vwSurveyQuestionGroupScores;
CREATE OR REPLACE VIEW vwSurveyQuestionGroupScores AS
SELECT
    surveyQuestionGroupScores.surveyid,
    surveyQuestionGroupScores.conversationid,
    survey.completeddate,
    survey.completeddateltc,
    surveyQuestionGroupScores.surveyformid,
    surveyQuestionGroupScores.surveyname,
    surveyQuestionGroupScores.agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    surveyQuestionGroupScores.agentteamid,
    surveyQuestionGroupScores.queueid,
    queue.name AS queuename,
    surveyQuestionGroupScores.questiongroupid,
    surveyQuestionGroupScores.questiongroupname,
    surveyQuestionGroupScores.questiongrouptotalscore,
    surveyQuestionGroupScores.questiongroupmaxtotalscore,
    surveyQuestionGroupScores.questiongroupmarkedna,
    surveyQuestionGroupScores.updated
FROM
    surveyQuestionGroupScores
    LEFT JOIN surveydata survey ON survey.surveyid::text = surveyQuestionGroupScores.surveyid::text
    LEFT JOIN userdetails agent ON agent.id::text = surveyQuestionGroupScores.agentid::text
    LEFT JOIN userdetails manager ON manager.id::text = agent.manager::text
    LEFT JOIN queuedetails queue ON queue.id::text = surveyQuestionGroupScores.queueid::text;
-- spell-checker: ignore: surveyid questiongroupid