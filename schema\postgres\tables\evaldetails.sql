CREATE TABLE IF NOT EXISTS evaldetails (
    id varchar(200) NOT NULL,
    evaluationid varchar(50),
    evaluationformid varchar(50),
    evaluationname varchar(200),
    questiongroupid varchar(50),
    questiongroupname varchar(200),
    questiongrouptohighest bit(1),
    questiongrouptona bit(1),
    questiongroupwieght numeric(20, 2),
    questiongroupmanwieght bit(1),
    questionid varchar(50),
    questiontext varchar(400),
    questionhelptext text,
    quesiontype varchar(50),
    questionnaenabled bit(1),
    questioncommentsreq bit(1),
    questioniskill bit(1),
    questioniscritical bit(1),
    questionanwserid varchar(50),
    questionanswertext varchar(200),
    questionanswervalue numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT evaldetails_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS evaldetailsformsid ON evaldetails USING btree (
    evaluationformid ASC NULLS LAST
) TABLESPACE pg_default;

COMMENT ON COLUMN evalDetails.evaluationformid IS 'Evaluation Form GUID';
COMMENT ON COLUMN evalDetails.evaluationid IS 'Evaluation Form GUID (Historical column name - this actually contains the form ID, not the evaluation ID)';
COMMENT ON COLUMN evalDetails.evaluationname IS 'Evaluation Name';
COMMENT ON COLUMN evalDetails.id IS 'Primary Key';
COMMENT ON COLUMN evalDetails.quesiontype IS 'Question Type';
COMMENT ON COLUMN evalDetails.questionanswertext IS 'Question Answer Text';
COMMENT ON COLUMN evalDetails.questionanswervalue IS 'Question Value';
COMMENT ON COLUMN evalDetails.questionanwserid IS 'Question Answer GUID';
COMMENT ON COLUMN evalDetails.questioncommentsreq IS 'Question Comments Required (True/False';
COMMENT ON COLUMN evalDetails.questiongroupmanwieght IS 'Question Group Man Weight';
COMMENT ON COLUMN evalDetails.questiongroupid IS 'Question Group GUID';
COMMENT ON COLUMN evalDetails.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN evalDetails.questiongroupToHighest IS 'Question Group To Highest (True/False)';
COMMENT ON COLUMN evalDetails.questiongroupToNA IS 'Question Group Not Applicable (True/False)';
COMMENT ON COLUMN evalDetails.questiongroupwieght IS 'Question Group Wieght';
COMMENT ON COLUMN evalDetails.questionhelptext IS 'Question Help Text';
COMMENT ON COLUMN evalDetails.questionid IS 'Question Answer Value';
COMMENT ON COLUMN evalDetails.questioniskill IS 'Question Is a Kill Question (True/False)';
COMMENT ON COLUMN evalDetails.questionnaenabled IS 'Question Enabled (True/False)';
COMMENT ON COLUMN evalDetails.questiontext IS 'Question Text';
COMMENT ON COLUMN evalDetails.updated IS 'Date Row Updated (UTC)';
COMMENT ON TABLE evalDetails IS 'Evaluation Lookup Data';
