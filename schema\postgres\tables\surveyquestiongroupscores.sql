CREATE TABLE IF NOT EXISTS surveyquestiongroupscores (
    surveyid varchar(50) NOT NULL,
    conversationid varchar(50) NOT NULL,
    surveyformid varchar(50),
    surveyname varchar(200),
    agentid varchar(50),
    agentteamid varchar(50),
    queueid varchar(50),
    questiongroupid varchar(50) NOT NULL,
    questiongroupname varchar(200),
    questiongrouptotalscore numeric(20, 2),
    questiongroupmaxtotalscore numeric(20, 2),
    questiongroupmarkedna bit(1),
    updated timestamp without time zone,
    CONSTRAINT surveyquestiongroupscores_pkey PRIMARY KEY (surveyid, questiongroupid)
) TABLESPACE pg_default;

-- spell-checker: ignore: surveyid pkey questiongroupid