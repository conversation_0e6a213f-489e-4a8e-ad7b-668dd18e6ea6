CREATE OR REPLACE VIEW vwSurveyData AS
SELECT
    surveyid,
    conversationid,
    surveyformid,
    surveyname,
    agentid,
    agent.name AS agentname,
    agent.department AS agentdepartment,
    manager.name AS agentmanager,
    agentteamid,
    queueid,
    queue.name AS queuename,
    status,
    totalscore,
    completeddate,
    completeddateltc,
    surveyData.updated,
    lastpoll
FROM
    surveyData
    LEFT JOIN userdetails agent ON agent.id::text = surveyData.agentid::text
    LEFT JOIN userdetails manager ON manager.id::text = agent.manager::text
    LEFT JOIN queuedetails queue ON queue.id::text = surveyData.queueid::text;

-- spell-checker: ignore: surveyid completeddateltc

COMMENT ON COLUMN vwSurveyData.surveyid IS 'Survey GUID';
COMMENT ON COLUMN vwSurveyData.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwSurveyData.surveyformid IS 'Survey Form GUID';
COMMENT ON COLUMN vwSurveyData.surveyname IS 'Survey Name';
COMMENT ON COLUMN vwSurveyData.agentid IS 'Agent GUID';
COMMENT ON COLUMN vwSurveyData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwSurveyData.agentdepartment IS 'Agent Department';
COMMENT ON COLUMN vwSurveyData.agentmanager IS 'Agent Manager';
COMMENT ON COLUMN vwSurveyData.agentteamid IS 'Agent Team GUID';
COMMENT ON COLUMN vwSurveyData.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwSurveyData.queuename IS 'Queue Name';
COMMENT ON COLUMN vwSurveyData.status IS 'Survey Status';
COMMENT ON COLUMN vwSurveyData.totalscore IS 'Total Score';
COMMENT ON COLUMN vwSurveyData.completeddate IS 'Completion Date(UTC)';
COMMENT ON COLUMN vwSurveyData.completeddateltc IS 'Completion Date (LTC)';
COMMENT ON COLUMN vwSurveyData.updated IS 'Last Updated';
COMMENT ON COLUMN vwSurveyData.lastpoll IS 'Last Poll';
COMMENT ON VIEW vwSurveyData IS 'Survey Data View';
