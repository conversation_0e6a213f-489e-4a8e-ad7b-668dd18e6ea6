CREATE OR ALTER VIEW [vwEvalData] AS
SELECT DISTINCT
    ed.keyid,
    ed.agent<PERSON><PERSON>,
    ed.assigneddate,
    ed.assigneddateLTC,
    ed.conversationid,
    ed.evaluationformid,
    ed.evaluationid, -- This is the actual evaluation ID from evaldata table, not to be confused with evaldetails.evaluationid which contains form IDs
    ed.evaluatorid,
    ed.releasedate,
    ed.releasedateLTC,
    ed.status,
    ed.totalcriticalscore,
    ed.totalnoncriticalscore,
    ed.totalscore,
    ed.calibrationid,
    ed.averagescore,
    ed.highscore,
    ed.lowscore,
    ed.updated,
    ed.userid,
    evDet.evaluationname,
    ud.managerid as managerid,
    ud.managername,
    ud.name as agentname
FROM
    evalData ed
    left outer join vwUserDetail ud on ud.id = ed.userid
    -- Note: evalDetails.evaluationid actually contains the form ID, not the evaluation ID
    left outer join evalDetails evDet on evDet.evaluationid = ed.evaluationformid;
