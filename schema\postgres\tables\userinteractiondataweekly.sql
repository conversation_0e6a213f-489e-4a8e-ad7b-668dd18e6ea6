CREATE TABLE IF NOT EXISTS userinteractiondataweekly (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tuserresponsetimecount integer,
    tuserresponsetimetimesum numeric(20, 2),
    tuserresponsetimetimemax numeric(20, 2),
    tuserresponsetimetimemin numeric(20, 2),
    tagentresponsetimecount integer,
    tagentresponsetimetimesum numeric(20, 2),
    tagentresponsetimetimemax numeric(20, 2),
    tagentresponsetimetimemin numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userinteractiondataweekly_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN userInteractionDataWeekly.av10count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av10timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av10timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av10timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av1count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av1timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av1timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av1timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av2count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av2timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av2timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av2timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av3count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av3timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av3timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av3timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av4count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av4timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av4timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av4timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av5count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av5timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av5timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av5timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av6count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av6timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av6timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av6timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av7count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av7timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av7timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av7timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av8count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av8timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av8timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av8timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av9count IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av9timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av9timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.av9timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.direction IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.keyid IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.mediatype IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.nblindtransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.nconnected IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.nconsult IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.nconsulttransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.nerror IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.noutbound IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ntransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.queueid IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.startdate IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tacwcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tacwtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tacwtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tacwtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tagentresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tagentresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tagentresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tagentresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.talertcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.talerttimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.talerttimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.talerttimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tansweredcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tansweredtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tansweredtimemin IS ' ';
COMMENT ON COLUMN userInteractionDataWeekly.tansweredtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tcontactingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tcontactingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tcontactingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tcontactingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tdialingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tdialingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tdialingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tdialingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.thandlecount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.thandletimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.thandletimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.thandletimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.theldtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tnotrespondingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tnotrespondingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tnotrespondingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tnotrespondingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalkcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalkcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalkcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalkcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalkcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalktimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalktimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.ttalktimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tuserresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tuserresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tuserresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tuserresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tvoicemailcount IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tvoicemailtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tvoicemailtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.tvoicemailtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.updated IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.userid IS ' '; 
COMMENT ON COLUMN userInteractionDataWeekly.wrapupcode IS ' '; 
COMMENT ON TABLE  userInteractionDataWeekly IS 'User Interaction Data Weekly Data - LTC - See UserInteractionData for Field Descriptions';