CREATE TABLE IF NOT EXISTS userinteractiondatamonthly (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tuserresponsetimecount integer,
    tuserresponsetimetimesum numeric(20, 2),
    tuserresponsetimetimemax numeric(20, 2),
    tuserresponsetimetimemin numeric(20, 2),
    tagentresponsetimecount integer,
    tagentresponsetimetimesum numeric(20, 2),
    tagentresponsetimetimemax numeric(20, 2),
    tagentresponsetimetimemin numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userinteractiondatamonthly_pkey PRIMARY KEY (keyid)
) TABLESPACE pg_default;

COMMENT ON COLUMN userInteractionDataMonthly.av10count IS ' ';
COMMENT ON COLUMN userInteractionDataMonthly.av10timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av10timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av10timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av1count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av1timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av1timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av1timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av2count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av2timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av2timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av2timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av3count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av3timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av3timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av3timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av4count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av4timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av4timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av4timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av5count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av5timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av5timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av5timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av6count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av6timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av6timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av6timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av7count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av7timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av7timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av7timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av8count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av8timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av8timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av8timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av9count IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av9timemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av9timemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.av9timesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.direction IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.keyid IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.mediatype IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.nblindtransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.nconnected IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.nconsult IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.nconsulttransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.nerror IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.noutbound IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ntransferred IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.queueid IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.startdate IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tacwcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tacwtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tacwtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tacwtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tagentresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tagentresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tagentresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tagentresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.talertcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.talerttimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.talerttimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.talerttimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tansweredcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tansweredtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tansweredtimemin IS ' ';
COMMENT ON COLUMN userInteractionDataMonthly.tansweredtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tcontactingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tcontactingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tcontactingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tcontactingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tdialingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tdialingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tdialingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tdialingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.thandlecount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.thandletimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.thandletimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.thandletimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.theldtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tnotrespondingcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tnotrespondingtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tnotrespondingtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tnotrespondingtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalkcompletecount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalkcompletetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalkcompletetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalkcompletetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalkcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalktimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalktimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.ttalktimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tuserresponsetimecount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tuserresponsetimetimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tuserresponsetimetimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tuserresponsetimetimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tvoicemailcount IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tvoicemailtimemax IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tvoicemailtimemin IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.tvoicemailtimesum IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.updated IS ' ';
COMMENT ON COLUMN userInteractionDataMonthly.userid IS ' '; 
COMMENT ON COLUMN userInteractionDataMonthly.wrapupcode IS ' '; 
COMMENT ON TABLE  userInteractionDataMonthly IS 'User Interaction Data Monthly Data - LTC - See UserInteractionData for Field Descriptions';