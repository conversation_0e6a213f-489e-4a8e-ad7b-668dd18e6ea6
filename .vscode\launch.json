{
    "version": "0.2.0",
    "configurations": [
        {
            // Use IntelliSense to find out which attributes exist for C# debugging
            // Use hover for the description of the existing attributes
            // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
            "name": "Compile and Launch",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            // If you have changed target frameworks, make sure to update the program path.
            "program": "${workspaceFolder}/GenesysAdapter/bin/Debug/net6.0/GenesysAdapter.dll",
            "args": [
                //"information"
                //"factdata"
                //"interaction"
                //"realtime"
                //"aggregation"
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
            "console": "internalConsole",
            "stopAtEntry": false,
            "logging": {
                "moduleLoad": false
            }
        },
        {
            "name": "Launch",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/GenesysAdapter/bin/Debug/net6.0/GenesysAdapter.dll",
            "args": [
                //"/Job=information", "/Preferences:RenameParticipantAttributeNames:0:Find=^(?i:ivr\.)", "/Preferences:RenameParticipantAttributeNames:0:Replace="
                //"/Job=information"
                //"/Job=install"
                //"/Job=factdata", "/Preferences:FactDataJobs:0=GroupDetails", "/Preferences:FactDataJobs:1=QueueDetails"
                //"/Job=interaction"
                //"/Job=realtime"
                //"information"
                //"factdata", "All"
                //"factdata", "GroupDetails:QueueDetails"
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false,
            "logging": {
                "moduleLoad": false
            }
        },
        {
            "name": "Genesys Adapter Support Tools",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/GenesysAdapterSupportTool/bin/Debug/net6.0/GenesysAdapterSupportTool.dll",
            "args": [
                //"encrypt", ""
                //"decrypt", ""
                //"GetCustomerInfo", ""
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false,
            "logging": {
                "moduleLoad": false
            }
        }
    ]
}