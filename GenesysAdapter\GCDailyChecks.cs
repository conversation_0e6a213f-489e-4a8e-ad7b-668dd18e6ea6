﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using System.Configuration;
using System.IO;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCDailyCheck
    {
        private readonly ILogger? _logger;

        public GCDailyCheck(ILogger? logger)
        {
            _logger = logger;
        }

        public bool UpdateHeadcountForecast()
        {
            bool successful = true;

            string syncType = "headcountforecastdata";

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger?.LogInformation("Starting UpdateHeadcountForecast job");

            DBUtils.DBUtils dbAdapter = new DBUtils.DBUtils(_logger);
            dbAdapter.Initialize();

            GCGetData gcData = new GCGetData(_logger);
            gcData.Initialize(syncType);

            DataTable headCountForecast = gcData.HeadcountForecastData();

            if (headCountForecast != null)
            {
                int recordCount = headCountForecast.Rows.Count;
                _logger?.LogInformation("Retrieved {RecordCount} headcount forecast records", recordCount);

                successful = dbAdapter.WriteSQLDataBulk(headCountForecast, "headcountforecastdata");

                if (successful)
                {
                    successful = gcData.UpdateLastSuccessDate(DateTime.UtcNow, "headcountforecastdata");
                }
            }
            else
            {
                _logger?.LogWarning("No headcount forecast data retrieved");
            }

            stopwatch.Stop();
            _logger?.LogInformation("UpdateHeadcountForecast job completed in {ElapsedSeconds:N2} seconds. Success: {Success}",
                stopwatch.Elapsed.TotalSeconds, successful);

            return successful;
        }

        public bool UpdateOfferedForecast()
        {
            bool successful = true;

            string syncType = "offeredforecastdata";

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger?.LogInformation("Starting UpdateOfferedForecast job");

            DBUtils.DBUtils dbAdapter = new DBUtils.DBUtils(_logger);
            dbAdapter.Initialize();

            GCGetData gcData = new GCGetData(_logger);
            gcData.Initialize(syncType);

            string startDate = gcData.DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z");

            // Get the count of existing records before deletion
            string countQuery = "SELECT COUNT(*) FROM offeredforecastdata";
            DataTable countResult = dbAdapter.GetSQLTableData(countQuery, "CountResult");
            int totalRecordsBeforeDelete = 0;
            if (countResult != null && countResult.Rows.Count > 0)
            {
                totalRecordsBeforeDelete = Convert.ToInt32(countResult.Rows[0][0]);
                _logger?.LogInformation("Total records in offeredforecastdata before delete: {TotalRecords}", totalRecordsBeforeDelete);
            }

            // Get the count of records that will be deleted (26 weeks)
            string deleteCountQuery = "";
            if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.Snowflake)
            {
                deleteCountQuery = $"SELECT COUNT(*) FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, CURRENT_DATE)";
            }
            else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
            {
                deleteCountQuery = $"SELECT COUNT(*) FROM offeredforecastdata WHERE weekdate >= CURRENT_DATE - INTERVAL '26 WEEK'";
            }
            else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.MSSQL)
            {
                deleteCountQuery = $"SELECT COUNT(*) FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, GETDATE())";
            }

            DataTable deleteCountResult = dbAdapter.GetSQLTableData(deleteCountQuery, "DeleteCountResult");
            int recordsToDelete = 0;
            if (deleteCountResult != null && deleteCountResult.Rows.Count > 0)
            {
                recordsToDelete = Convert.ToInt32(deleteCountResult.Rows[0][0]);
                _logger?.LogInformation("Records to be deleted (last 26 weeks): {RecordsToDelete}", recordsToDelete);
            }

            // Get the min and max dates of records to be deleted
            string dateRangeQuery = "";
            if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.Snowflake)
            {
                dateRangeQuery = $"SELECT MIN(weekdate), MAX(weekdate) FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, CURRENT_DATE)";
            }
            else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
            {
                dateRangeQuery = $"SELECT MIN(weekdate), MAX(weekdate) FROM offeredforecastdata WHERE weekdate >= CURRENT_DATE - INTERVAL '26 WEEK'";
            }
            else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.MSSQL)
            {
                dateRangeQuery = $"SELECT MIN(weekdate), MAX(weekdate) FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, GETDATE())";
            }

            DataTable dateRangeResult = dbAdapter.GetSQLTableData(dateRangeQuery, "DateRangeResult");
            if (dateRangeResult != null && dateRangeResult.Rows.Count > 0 && dateRangeResult.Rows[0][0] != DBNull.Value)
            {
                DateTime minDate = Convert.ToDateTime(dateRangeResult.Rows[0][0]);
                DateTime maxDate = Convert.ToDateTime(dateRangeResult.Rows[0][1]);
                _logger?.LogInformation("Date range of records to be deleted: {MinDate} to {MaxDate}",
                    minDate.ToString("yyyy-MM-dd"), maxDate.ToString("yyyy-MM-dd"));
            }

            DataTable offeredForecast = gcData.OfferedForecastData(startDate);

            if (offeredForecast != null)
            {
                int recordCount = offeredForecast.Rows.Count;
                _logger?.LogInformation("Retrieved {RecordCount} new forecast records to insert", recordCount);

                // Get the min and max dates of new records
                if (recordCount > 0)
                {
                    var dates = offeredForecast.AsEnumerable()
                        .Select(row => row.Field<DateTime>("weekdate"))
                        .Where(d => d != DateTime.MinValue);

                    if (dates.Any())
                    {
                        DateTime minNewDate = dates.Min();
                        DateTime maxNewDate = dates.Max();
                        _logger?.LogInformation("Date range of new records to be inserted: {MinDate} to {MaxDate}",
                            minNewDate.ToString("yyyy-MM-dd"), maxNewDate.ToString("yyyy-MM-dd"));
                    }
                }

                try
                {
                    string deleteQuery = "";
                    // Set a higher command timeout for the delete operation (10 minutes)
                    int commandTimeout = 600;

                    if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.Snowflake)
                    {
                        deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, CURRENT_DATE)";
                    }
                    else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                    {
                        deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= CURRENT_DATE - INTERVAL '26 WEEK'";
                    }
                    else if (dbAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.MSSQL)
                    {
                        deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, GETDATE())";
                    }

                    _logger?.LogInformation("Executing delete query with timeout of {CommandTimeout} seconds: {Query}", commandTimeout, deleteQuery);
                    var deleteTimer = System.Diagnostics.Stopwatch.StartNew();
                    int rowsDeleted = dbAdapter.ExecuteSqlNonQuery(deleteQuery, commandTimeout);
                    deleteTimer.Stop();
                    _logger?.LogInformation("Delete operation completed in {ElapsedSeconds:N2} seconds. Rows affected: {RowsDeleted}",
                        deleteTimer.Elapsed.TotalSeconds, rowsDeleted);

                    var insertTimer = System.Diagnostics.Stopwatch.StartNew();
                    successful = dbAdapter.WriteSQLDataBulk(offeredForecast, "offeredforecastdata");
                    insertTimer.Stop();
                    _logger?.LogInformation("Insert operation completed in {ElapsedSeconds:N2} seconds. Success: {Success}",
                        insertTimer.Elapsed.TotalSeconds, successful);

                    if (successful)
                    {
                        successful = gcData.UpdateLastSuccessDate(DateTime.UtcNow, "offeredforecastdata");
                    }

                    // Get the final count after all operations
                    DataTable finalCountResult = dbAdapter.GetSQLTableData(countQuery, "FinalCountResult");
                    if (finalCountResult != null && finalCountResult.Rows.Count > 0)
                    {
                        int totalRecordsAfterOperations = Convert.ToInt32(finalCountResult.Rows[0][0]);
                        _logger?.LogInformation("Total records in offeredforecastdata after operations: {TotalRecords}", totalRecordsAfterOperations);
                        _logger?.LogInformation("Net change in records: {NetChange}", totalRecordsAfterOperations - totalRecordsBeforeDelete);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Failed to process offered forecast data");
                    successful = false;
                }
            }
            else
            {
                _logger?.LogWarning("No offered forecast data retrieved");
            }

            stopwatch.Stop();
            _logger?.LogInformation("UpdateOfferedForecast job completed in {ElapsedSeconds:N2} seconds. Success: {Success}",
                stopwatch.Elapsed.TotalSeconds, successful);

            return successful;
        }

        public bool UpdateGCAdherence()
        {
            bool successful = true;

            string syncType = "adherence";

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger?.LogInformation("Starting UpdateGCAdherence job");

            DBUtils.DBUtils dbAdapter = new DBUtils.DBUtils(_logger);
            dbAdapter.Initialize();

            GCGetData gcData = new GCGetData(_logger);
            gcData.Initialize(syncType);

            DataSet adherenceData = gcData.AdherenceData();

            if (adherenceData != null)
            {
                _logger?.LogInformation("Processing adherence data");

                successful = dbAdapter.WriteSQLData(adherenceData.Tables["adherencedayData"], "adherencedayData");

                if (successful)
                    successful = dbAdapter.WriteSQLData(adherenceData.Tables["adherenceexcData"], "adherenceexcData");

                if (successful)
                    successful = dbAdapter.WriteSQLData(adherenceData.Tables["adherenceactData"], "adherenceactData");

                if (successful)
                {
                    DateTime oldUpdateTime = gcData.AdherenceLastUpdate;
                    DateTime originalTime = gcData.AdherenceFromDate;

                    if (oldUpdateTime >= originalTime)
                    {
                        _logger?.LogInformation("oldUpdateTime >= originalTime");
                        originalTime = oldUpdateTime;
                    }

                    if (originalTime >= DateTime.UtcNow.AddHours(-12))
                    {
                        _logger?.LogInformation("originalTime greater than today, setting to {LastUpdate}", gcData.AdherenceLastUpdate);
                        originalTime = gcData.AdherenceLastUpdate;
                    }

                    successful = gcData.UpdateLastSuccessDate(originalTime, syncType + "Start");

                    _logger?.LogInformation("Original: {OriginalTime}, OldUpdateTime: {OldUpdateTime}, Date Now: {DateNow}",
                        originalTime, oldUpdateTime, DateTime.UtcNow.AddHours(-12));
                    _logger?.LogInformation("Updated the latest update date. Success: {Success}", successful);
                }
                else
                {
                    Environment.ExitCode = -15000;
                    _logger?.LogError("Will not update the last update date - failure in processing");
                }
            }
            else
            {
                Environment.ExitCode = -15001;
                _logger?.LogError("No data updated - failure in processing");
            }

            stopwatch.Stop();
            _logger?.LogInformation("UpdateGCAdherence job completed in {ElapsedSeconds:N2} seconds. Success: {Success}",
                stopwatch.Elapsed.TotalSeconds, successful);

            return successful;
        }
    }
}
