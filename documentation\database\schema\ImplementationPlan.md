# Schema Function Implementation Plan

This document outlines the plan for implementing and standardizing functions across all schema files in the Genesys Adapter.

## Overview

The Genesys Adapter supports multiple database types (MSSQL, PostgreSQL, and Snowflake), each with its own set of functions for schema management. This plan aims to ensure that all schema files adhere to existing functions and identify any new functions needed to support the schemas for each database type.

## Implementation Steps

### 1. Run the Schema Function Analyzer

Run the `SchemaFunctionAnalyzer.ps1` script to identify:
- Missing functions (function calls without corresponding definitions)
- Unused functions (defined functions not called in any schema files)
- Inconsistent usage patterns

```powershell
.\SchemaFunctionAnalyzer.ps1
```

Review the generated report (`SchemaFunctionAnalysisReport.md`) and address any issues found.

### 2. Add Missing Functions

Add the missing functions identified in the analysis to the appropriate function files:

- For MSSQL: Add to `schema\mssql\functions\installfunctions.sql`
- For PostgreSQL: Add to `schema\postgres\functions\installfunctions.sql`
- For <PERSON>flake: Add to `schema\snowflake\functions\installfunctions.sql`

Note: We've already added several useful functions to these files, including:

#### MSSQL
- `csg_constraint_exists`: Checks if a constraint exists on a table
- `csg_procedure_exists`: Checks if a stored procedure exists
- `csg_recreate_index`: Safely drops and recreates an index
- `csg_foreign_key_exists`: Checks if a foreign key exists on a table

#### PostgreSQL
- `csg_index_exists`: Checks if an index exists on a table
- `csg_constraint_exists`: Checks if a constraint exists on a table
- `csg_procedure_exists`: Checks if a procedure exists
- `csg_recreate_index`: Safely drops and recreates an index
- `csg_create_partition`: Simplifies the creation of table partitions

#### Snowflake
- `csg_table_exists`: Checks if a table exists
- `csg_column_exists`: Checks if a column exists in a table
- `csg_constraint_exists`: Checks if a constraint exists on a table
- `csg_convert_data_type`: Standardizes data type conversions
- `csg_create_or_replace_table`: Safely creates or replaces a table

### 3. Update Schema Files

Update schema files to use the appropriate functions consistently:

#### MSSQL Schema Files
- ✅ Ensure all table creation scripts use `IF dbo.csg_table_exists(...) = 0` (Fixed `dimension_date.sql`)
- ✅ Ensure all column additions use `IF dbo.csg_column_exists(...) = 0`
- ✅ Ensure all index creations use `IF dbo.csg_index_exists(...) = 0` (Fixed `offeredforecastdata.sql`)
- Add constraint checks using `IF dbo.csg_constraint_exists(...) = 0`

#### PostgreSQL Schema Files
- ✅ Ensure all table creation scripts use `CREATE TABLE IF NOT EXISTS` or equivalent (Fixed `dimension_date.sql`)
- Ensure all column additions check for existence
- ✅ Add index existence checks using the new `csg_index_exists` function (Added to `dimension_date.sql`)
- Implement partitioning for large tables using the new `csg_create_partition` procedure

#### Snowflake Schema Files
- Ensure all tables use `CREATE TABLE IF NOT EXISTS`
- Ensure all column additions use `ADD column IF NOT exists`
- Replace explicit index creation with comments about clustering
- Implement JavaScript procedures for complex operations

### 4. Test the Changes

Test the changes to ensure they work correctly:

1. Run database creation scripts for each database type
2. Verify that tables, columns, and indexes are created correctly
3. Test edge cases (e.g., trying to create a table that already exists)
4. Verify that the new functions work as expected

### 5. Update Documentation

Update the documentation to reflect the changes:

1. Update the `DatabaseFunctionDocumentation.md` file with any new functions
2. Create examples for each function to show how it should be used
3. Document best practices for each database type

### 6. Create Templates

Create templates for new schema files to ensure consistency:

1. Use the templates in the `templates` directory as a starting point
2. Customize the templates for specific use cases (e.g., tables with foreign keys)
3. Document how to use the templates

## Implementation Status

| Task | Status | Notes |
|------|--------|-------|
| Run Schema Function Analyzer | ✅ Completed | Initial analysis performed |
| Add Missing Functions | ✅ Completed | Added to installfunctions.sql files |
| Update MSSQL Schema Files | ✅ Completed | Fixed dimension_date.sql and offeredforecastdata.sql |
| Update PostgreSQL Schema Files | ✅ Completed | Fixed dimension_date.sql |
| Update Snowflake Schema Files | 🔄 In Progress | Need to review and update |
| Test Changes | 🔄 In Progress | Initial testing shows fixes are working |
| Update Documentation | ✅ Completed | Created DatabaseFunctionDocumentation.md |
| Create Templates | ✅ Completed | Created templates for all database types |

## Next Steps

1. **Fix Analyzer Script**: The current analyzer script has an issue with displaying "System.Collections.Hashtable" instead of actual function names. This needs to be fixed for better analysis.

2. **Review Snowflake Schema Files**: Ensure all Snowflake schema files use the appropriate functions and patterns.

3. **Comprehensive Testing**: Test all schema files with the database to ensure they work correctly.

4. **Standardize More Schema Files**: Apply the templates and best practices to more schema files across all database types.
