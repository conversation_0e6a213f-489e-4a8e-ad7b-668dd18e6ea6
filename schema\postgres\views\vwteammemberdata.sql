CREATE
OR REPLACE VIEW vwteammemberdata AS
SELECT
	tm.USERID,
	ud.NAME AS "Agent Name",
	td.NAME AS "Team Name"
from 
	TEAMMEMBERDATA tm
	LEFT JOIN VWUSERDETAIL ud ON tm.USERID = ud.ID
	LEFT JOIN TEAMDETAILS td ON tm.TEAMID = td.ID;

COMMENT ON COLUMN vwteammemberdata.userid IS 'User ID';
COMMENT ON COLUMN vwteammemberdata."Agent Name" IS 'Agent Name';
COMMENT ON COLUMN vwteammemberdata."Team Name" IS 'Team Name';
COMMENT ON VIEW vwteammemberdata IS 'Team Member Data View';
