﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Handles rate limiting for Genesys Cloud API requests
    /// </summary>
    internal class RateLimitHandler
    {
        private readonly ILogger? _logger;
        private readonly GCUtils _gcUtils;
        private string _lastResponseContent = string.Empty;
        private const int MAX_RETRY_ATTEMPTS = 5;
        private const int DEFAULT_RETRY_SECONDS = 5;
        private const int MAX_WAIT_TIME_SECONDS = 60; // Increased from 30 to handle larger Retry-After values

        public RateLimitHandler(ILogger? logger, GCUtils gcUtils)
        {
            _logger = logger;
            _gcUtils = gcUtils;
        }

        /// <summary>
        /// Determines if a rate limit response is token-related
        /// </summary>
        public bool IsTokenRateLimit(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            return responseContent.Contains("oauth.token.rate") ||
                   responseContent.Contains("token.rate") ||
                   responseContent.Contains("authorization.token") ||
                   responseContent.Contains("client.credentials.token");
        }

        /// <summary>
        /// Extracts rate limit information from response content
        /// </summary>
        public (int retryAfterSeconds, string limitKey, string limitNamespace, int? limitValue) ExtractRateLimitInfo(string responseContent)
        {
            int retryAfterSeconds = DEFAULT_RETRY_SECONDS;
            string limitKey = string.Empty;
            string limitNamespace = string.Empty;
            int? limitValue = null;

            try
            {
                // Extract retry-after value
                if (responseContent.Contains("Retry the request in"))
                {
                    var match = Regex.Match(responseContent, @"Retry the request in \[(\d+)\] seconds");
                    if (match.Success && match.Groups.Count > 1)
                    {
                        retryAfterSeconds = int.Parse(match.Groups[1].Value);
                    }
                }

                // Extract limit information
                if (responseContent.Contains("\"limit\""))
                {
                    var keyMatch = Regex.Match(responseContent, @"""key"":""([^""]+)""");
                    if (keyMatch.Success && keyMatch.Groups.Count > 1)
                    {
                        limitKey = keyMatch.Groups[1].Value;
                    }

                    var namespaceMatch = Regex.Match(responseContent, @"""namespace"":""([^""]+)""");
                    if (namespaceMatch.Success && namespaceMatch.Groups.Count > 1)
                    {
                        limitNamespace = namespaceMatch.Groups[1].Value;
                    }

                    var valueMatch = Regex.Match(responseContent, @"""value"":(\d+)");
                    if (valueMatch.Success && valueMatch.Groups.Count > 1)
                    {
                        limitValue = int.Parse(valueMatch.Groups[1].Value);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to extract information from rate limit response content");
            }

            return (retryAfterSeconds, limitKey, limitNamespace, limitValue);
        }

        /// <summary>
        /// Refreshes the API key when a token-related rate limit is encountered
        /// </summary>
        public string? RefreshApiKey(string responseContent)
        {
            try
            {
                bool isTokenRateLimit = IsTokenRateLimit(responseContent);

                if (isTokenRateLimit)
                {
                    _logger?.LogInformation("Refreshing API key due to token-related rate limit");

                    // Initialize GCUtils with credentials from configuration
                    bool success = _gcUtils.GetGCAPIKey();

                    if (success)
                    {
                        string newApiKey = _gcUtils.GCApiKey;
                        _logger?.LogInformation("Successfully refreshed API key");
                        return newApiKey;
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to refresh API key");
                    }
                }
                else
                {
                    _logger?.LogInformation("Not refreshing API key as rate limit is not token-related");
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to refresh API key after rate limit response");
                return null;
            }
        }

        /// <summary>
        /// Handles rate limiting with consistent retry logic and logging
        /// </summary>
        public (string apiKey, bool shouldRetry) HandleRateLimit(
            string retryAfterHeaderValue,
            string responseContent,
            int attempt,
            string apiKey)
        {
            _lastResponseContent = responseContent;
            bool shouldRetry = attempt < MAX_RETRY_ATTEMPTS;

            // Log rate limit details
            var (retryAfterSeconds, limitKey, limitNamespace, limitValue) = ExtractRateLimitInfo(responseContent);

            // Use retry-after header if available, otherwise use extracted value
            if (!string.IsNullOrEmpty(retryAfterHeaderValue) && int.TryParse(retryAfterHeaderValue, out int headerValue))
            {
                retryAfterSeconds = headerValue;
            }

            _logger?.LogWarning(
                "Rate limit encountered - Attempt: {Attempt}, Retry-After: {RetryAfter}s, Key: {LimitKey}, Namespace: {LimitNamespace}, Value: {LimitValue}, Source: {Source}",
                attempt,
                retryAfterSeconds,
                limitKey,
                limitNamespace,
                limitValue,
                !string.IsNullOrEmpty(retryAfterHeaderValue) ? "Header" : "Response Body");

            // Try to get a new API key if this is a token-related rate limit
            string? newApiKey = RefreshApiKey(responseContent);
            if (newApiKey != null)
            {
                // Use the new API key for the next request
                apiKey = newApiKey;
                _logger?.LogInformation("Using new API key for next request");
            }
            else
            {
                // Calculate wait time - prioritize the Retry-After value from the server
                // but apply a reasonable cap to prevent excessive waits
                int waitTime = Math.Min(retryAfterSeconds, MAX_WAIT_TIME_SECONDS);

                // Add a small additional delay for subsequent attempts
                if (attempt > 1)
                {
                    waitTime = Math.Min(waitTime + (2 * (attempt - 1)), MAX_WAIT_TIME_SECONDS);
                }

                _logger?.LogInformation("Waiting {WaitTime} seconds before retry (Retry-After was {RetryAfter}s)",
                    waitTime, retryAfterSeconds);
                System.Threading.Thread.Sleep(waitTime * 1000);
            }

            return (apiKey, shouldRetry);
        }
    }

    /// <summary>
    /// Represents an HTTP response with status code and content
    /// </summary>
    public class HttpApiResponse
    {
        public int StatusCode { get; set; }
        public string Content { get; set; } = string.Empty;
        public bool IsSuccess => StatusCode >= 200 && StatusCode < 300;
        public bool IsAccepted => StatusCode == 202;
        public string StatusDescription { get; set; } = string.Empty;
    }

    public class JsonUtils
    {
        public int MaxPages { get; set; }
        public int RateLimitTimeToGo { get; set; }
        public string? responseCode { get; set; }
        public HttpResponseHeaders? responseHeaders { get; set; }
        private readonly ILogger? _logger;
        private readonly GCUtils _gcUtils;
        private readonly RateLimitHandler _rateLimitHandler;
        private readonly PermissionErrorTracker _permissionErrorTracker;

        public JsonUtils()
        {
            _gcUtils = new GCUtils();
            _rateLimitHandler = new RateLimitHandler(null, _gcUtils);
            _permissionErrorTracker = new PermissionErrorTracker(null);
        }

        public JsonUtils(ILogger? logger)
        {
            _logger = logger;
            _gcUtils = new GCUtils(logger);
            _rateLimitHandler = new RateLimitHandler(logger, _gcUtils);
            _permissionErrorTracker = new PermissionErrorTracker(logger);
        }

        // Store the last response content to check for token-related rate limits
        private string _lastResponseContent = string.Empty;

        internal JArray JsonReturn(string URI, string apiKey)
        {
            int MaxPagesToRetrieve = 30;
            int attempts = 1;
            const int maxAttempts = 5;
            JArray json = null;

            while (attempts <= maxAttempts)
            {
                try
                {
                    // Use the improved JsonReturnHttpResponseGet method for proper rate limit handling
                    var response = JsonReturnHttpResponseGet(URI, apiKey);

                    // Check HTTP status code before processing JSON
                    if (response.StatusCode != 200)
                    {
                        if (response.StatusCode == 429)
                        {
                            _logger?.LogWarning("Rate limit encountered in JsonReturn for URI: {URI}. Response: {Response}", URI, response.Content);
                            // The JsonReturnHttpResponseGet method already handles rate limiting internally
                            // If we get here, it means the retry limit was exceeded
                            _logger?.LogError("Rate limiting exceeded retry limit in JsonReturn for URI: {URI}", URI);
                            return null; // Return null to maintain backward compatibility
                        }
                        else if (response.StatusCode == 403)
                        {
                            _logger?.LogWarning("Access forbidden in JsonReturn for URI: {URI}. Response: {Response}", URI, response.Content);
                            return null; // Return null to maintain backward compatibility
                        }
                        else
                        {
                            _logger?.LogError("API call failed in JsonReturn for URI: {URI} with status {StatusCode}: {Response}", URI, response.StatusCode, response.Content);
                            return null; // Return null to maintain backward compatibility
                        }
                    }

                    string jsonData = response.Content;

                    // Validate that we have valid JSON data
                    if (string.IsNullOrWhiteSpace(jsonData) || jsonData.Length < 10)
                    {
                        _logger?.LogWarning("Empty or invalid JSON response in JsonReturn for URI: {URI}", URI);
                        return null;
                    }

                    // Parse pageCount for backward compatibility
                    try
                    {
                        int lastchar = jsonData.LastIndexOf(@"}");
                        int pageCount = jsonData.IndexOf(@"""pageCount"":");

                        if (pageCount > 0 && lastchar > pageCount)
                        {
                            MaxPages = int.Parse(jsonData.Substring(pageCount + 12, (lastchar - (pageCount + 12))));
                            if (MaxPages > MaxPagesToRetrieve)
                            {
                                MaxPages = MaxPagesToRetrieve;
                            }
                        }
                        else
                        {
                            MaxPages = 1; // Default to 1 if pageCount not found
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Failed to parse pageCount in JsonReturn for URI: {URI}. Defaulting to 1 page.", URI);
                        MaxPages = 1;
                    }

                    // Extract entities array for backward compatibility
                    try
                    {
                        int start = jsonData.IndexOf("[");
                        int end = jsonData.LastIndexOf("]");

                        if (start >= 0 && end > start)
                        {
                            jsonData = jsonData.Substring(start, (end - (start - 1)));
                        }

                        if (jsonData.IndexOf("[") >= 0)
                        {
                            json = JArray.Parse(jsonData) as JArray;
                        }
                        else
                        {
                            json = JArray.Parse("[ " + jsonData + "]") as JArray;
                        }

                        return json; // Success - return the parsed JArray
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger?.LogError(jsonEx, "JSON parsing failed in JsonReturn for URI: {URI}. Response: {Response}", URI, jsonData);
                        return null; // Return null to maintain backward compatibility
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Exception in JsonReturn attempt {Attempt}/{MaxAttempts} for URI: {URI}", attempts, maxAttempts, URI);

                    if (attempts >= maxAttempts)
                    {
                        _logger?.LogError("JsonReturn failed after {MaxAttempts} attempts for URI: {URI}", maxAttempts, URI);
                        return null; // Return null to maintain backward compatibility
                    }

                    attempts++;
                    // Add a small delay before retry
                    System.Threading.Thread.Sleep(1000 * attempts);
                }
            }

            return null; // Return null if all attempts failed
        }

        internal string JsonReturnString(string URI, string apiKey)
        {
            int attempts = 1;
            string jsonData = string.Empty;
            responseCode = string.Empty;

            while (attempts <= 5) // Use <= to match the MAX_RETRY_ATTEMPTS constant
            {
                Task<string> result = JsonReturnStringGetAsync(URI, apiKey);
                var finalResult = result.Result;
                if (finalResult != null && responseCode == string.Empty)
                {
                    jsonData = finalResult.ToString();
                    break;
                }
                else
                {
                    attempts++;

                    switch (responseCode)
                    {
                        case "Forbidden":
                            // Use the permission error tracker to determine if this is a temporary or permanent issue
                            string resourceKey = URI.Split('?')[0]; // Remove query parameters to get the base resource

                            // Check for permanent permission issues (missing permissions) first
                            if (_lastResponseContent != null &&
                                (_lastResponseContent.Contains("missing.division.permission") ||
                                 _lastResponseContent.Contains("missing.permission") ||
                                 _lastResponseContent.Contains("You are missing the following permission")))
                            {
                                _logger?.LogError("Permanent permission issue detected for resource {Resource}. Missing required permissions. Throwing UnauthorizedAccessException", resourceKey);
                                throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                            }

                            // For other forbidden errors, use the permission error tracker
                            bool isPermanentError = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                            if (isPermanentError)
                            {
                                _logger?.LogError("Permanent access forbidden for resource {Resource}. Throwing UnauthorizedAccessException", resourceKey);
                                throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                            }
                            else
                            {
                                _logger?.LogWarning("Temporary access forbidden for resource {Resource}. Will retry.", resourceKey);
                                // Add exponential backoff delay based on attempt number
                                int delayMs = (int)Math.Pow(2, attempts) * 1000;
                                Thread.Sleep(delayMs);
                                responseCode = "";
                                jsonData = null;
                                break;
                            }

                        case "429":
                        case "TooManyRequests":
                            string retryAfter = string.Empty;

                            if (responseHeaders != null && responseHeaders.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                                _logger?.LogInformation("Rate limit encountered on attempt {Attempt}. Retry-After header: {RetryAfter} seconds", attempts, retryAfter);
                            }
                            else
                            {
                                _logger?.LogInformation("Rate limit encountered on attempt {Attempt}. No Retry-After header, using default backoff", attempts);
                            }

                            // Use the standardized rate limit handler
                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                _lastResponseContent,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogWarning("Rate limiting exceeded retry limit after {Attempts} attempts. Returning structured error response instead of throwing exception.", attempts);
                                // Return structured error response instead of throwing exception
                                jsonData = "{\"error\": true, \"message\": \"Rate limiting exceeded retry limit\", \"statusCode\": \"TooManyRequests\", \"attempts\": " + attempts + "}";
                                attempts = 6; // Exit the loop
                                break;
                            }

                            _logger?.LogInformation("Rate limit handled successfully. Continuing with retry attempt {NextAttempt} using updated API key", attempts + 1);
                            responseCode = "";
                            jsonData = null;
                            break;

                        case "NotFound":
                            _logger?.LogWarning("NotFound: Resource not found at {URI}", URI);
                            jsonData = "{\"error\": true, \"message\": \"Resource not found\", \"statusCode\": \"NotFound\"}";
                            attempts = 6; // Exit the loop
                            break;
                    }
                }
            }

            return jsonData;
        }

        internal string JsonReturnString(string URI, string apiKey, string selectBody)
        {
            int attempts = 1;
            string jsonData = string.Empty;
            responseCode = string.Empty;

            while (attempts <= 5) // Use <= to match the MAX_RETRY_ATTEMPTS constant
            {
                Task<string> result = JsonReturnStringPostAsync(URI, apiKey, selectBody);
                var finalResult = result.Result;
                if (finalResult != null)
                {
                    // Check for common error patterns in the response
                    if (finalResult.ToString().Contains("Access Forbidden"))
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (finalResult.ToString().Contains("missing.division.permission") ||
                            finalResult.ToString().Contains("missing.permission") ||
                            finalResult.ToString().Contains("You are missing the following permission") ||
                            finalResult.ToString().Contains("\"isPermanent\": true"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions in response: {Response}", resourceKey, finalResult);
                            throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource} in response: {Response}", resourceKey, finalResult);
                            throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource} in response: {Response}", resourceKey, finalResult);
                            // Return a JSON with error information but don't throw an exception
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": false}}";
                        }
                    }

                    // Check for JSON parsing errors in the response
                    if (finalResult.ToString().Contains("After parsing a value an unexpected character was encountered") ||
                        finalResult.ToString().Contains("Unexpected character encountered while parsing value"))
                    {
                        _logger?.LogError("JSON parsing error in response: {Response}", finalResult);
                        // Return a valid JSON with error information instead of throwing
                        return $"{{\"error\": true, \"message\": \"JSON parsing error: {finalResult.Replace("\"", "\\\"")}\", \"statusCode\": \"BadRequest\"}}";
                    }

                    jsonData = finalResult.ToString();
                    break;
                }
                else
                {
                    attempts++;
                    switch (responseCode)
                    {
                        case "429":
                        case "TooManyRequests":
                            string retryAfter = string.Empty;

                            if (responseHeaders != null && responseHeaders.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                                _logger?.LogInformation("Rate limit encountered on POST attempt {Attempt}. Retry-After header: {RetryAfter} seconds", attempts, retryAfter);
                            }
                            else
                            {
                                _logger?.LogInformation("Rate limit encountered on POST attempt {Attempt}. No Retry-After header, using default backoff", attempts);
                            }

                            // Use the standardized rate limit handler
                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                _lastResponseContent,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogWarning("Rate limiting exceeded retry limit after {Attempts} attempts on POST request. Returning structured error response instead of throwing exception.", attempts);
                                // Return structured error response instead of throwing exception
                                jsonData = "{\"error\": true, \"message\": \"Rate limiting exceeded retry limit\", \"statusCode\": \"TooManyRequests\", \"attempts\": " + attempts + "}";
                                attempts = 6; // Exit the loop
                                break;
                            }

                            _logger?.LogInformation("Rate limit handled successfully on POST request. Continuing with retry attempt {NextAttempt} using updated API key", attempts + 1);
                            responseCode = "";
                            jsonData = null;
                            break;

                        case "NotFound":
                            _logger?.LogInformation("Resource not found at {URI}", URI);
                            jsonData = "{}";
                            attempts = 6; // Exit the loop
                            break;

                        case "Forbidden":
                            // Extract resource key from URI (use the endpoint path without query parameters)
                            string resourceKey = URI.Split('?')[0];

                            // Check for permanent permission issues (missing permissions) first
                            if (_lastResponseContent != null &&
                                (_lastResponseContent.Contains("missing.division.permission") ||
                                 _lastResponseContent.Contains("missing.permission") ||
                                 _lastResponseContent.Contains("You are missing the following permission")))
                            {
                                _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Exiting retry loop.", resourceKey);
                                jsonData = $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: Missing required permissions\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                                attempts = 6; // Exit the loop
                            }
                            else
                            {
                                // For other forbidden errors, use the permission error tracker
                                bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                                if (isPermanentIssue)
                                {
                                    _logger?.LogError("Permanent permission issue detected for {Resource}. Exiting retry loop.", resourceKey);
                                    jsonData = $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                                    attempts = 6; // Exit the loop
                                }
                                else
                                {
                                    _logger?.LogWarning("Temporary permission issue detected for {Resource}. Will retry.", resourceKey);
                                    // Add exponential backoff delay based on attempt number
                                    int delayMs = (int)Math.Pow(2, attempts) * 1000;
                                    if (delayMs > 30000) delayMs = 30000; // Cap at 30 seconds

                                    _logger?.LogInformation("Waiting {Delay}ms before retry attempt {Attempt}", delayMs, attempts);
                                    Thread.Sleep(delayMs);

                                    responseCode = "";
                                    jsonData = null;
                                }
                            }
                            break;
                    }
                }
            }
            return jsonData;
        }

        /// <summary>
        /// Makes an HTTP POST request and returns both status code and response content
        /// This method properly handles HTTP 202 Accepted responses for asynchronous operations
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="selectBody">The JSON body to send</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal async Task<HttpApiResponse> JsonReturnHttpResponseAsync(string URI, string apiKey, string selectBody)
        {
            var response = new HttpApiResponse();
            int attempts = 1;
            const int maxAttempts = 5;

            while (attempts <= maxAttempts)
            {
                try
                {
                    using (var handler = new HttpClientHandler())
                    using (var client = new HttpClient(handler) { Timeout = TimeSpan.FromMinutes(5) })
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);

                        var httpResponse = await client.PostAsync(URI, new StringContent(selectBody, Encoding.UTF8, "application/json"));

                        response.StatusCode = (int)httpResponse.StatusCode;
                        response.StatusDescription = httpResponse.ReasonPhrase ?? string.Empty;
                        response.Content = await httpResponse.Content.ReadAsStringAsync();

                        // Handle different status codes appropriately
                        if (httpResponse.StatusCode == HttpStatusCode.OK || httpResponse.StatusCode == HttpStatusCode.Accepted)
                        {
                            // Both 200 OK and 202 Accepted are valid responses
                            return response;
                        }
                        else if (httpResponse.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            // Handle rate limiting
                            string retryAfter = string.Empty;
                            if (httpResponse.Headers.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                response.Content,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                response.StatusCode = 429;
                                response.Content = "Rate limiting exceeded retry limit";
                                return response;
                            }

                            attempts++;
                            continue;
                        }
                        else
                        {
                            // For other HTTP status codes, return the response as-is
                            // This allows the caller to handle specific status codes appropriately
                            return response;
                        }
                    }
                }
                catch (TaskCanceledException ex)
                {
                    bool isTimeout = ex.InnerException is TimeoutException ||
                                     (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                     ex.Message.Contains("timed out") ||
                                     ex.Message.Contains("timeout");

                    if (isTimeout)
                    {
                        _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                        response.StatusCode = 408; // Request Timeout
                        response.Content = "Request timed out";
                        return response;
                    }
                    else
                    {
                        _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                        attempts++;
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Exception during HTTP request to {URI}", URI);
                    attempts++;
                    if (attempts > maxAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"Request failed after {maxAttempts} attempts: {ex.Message}";
                        return response;
                    }
                    continue;
                }
            }

            response.StatusCode = 500;
            response.Content = $"Request failed after {maxAttempts} attempts";
            return response;
        }

        /// <summary>
        /// Synchronous wrapper for JsonReturnHttpResponseAsync
        /// Makes an HTTP POST request and returns both status code and response content
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <param name="selectBody">The JSON body to send</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal HttpApiResponse JsonReturnHttpResponse(string URI, string apiKey, string selectBody)
        {
            var task = JsonReturnHttpResponseAsync(URI, apiKey, selectBody);
            return task.Result;
        }

        /// <summary>
        /// Makes an HTTP GET request and returns both status code and response content
        /// This method provides consistent error handling and status code detection for GET requests
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal async Task<HttpApiResponse> JsonReturnHttpResponseGetAsync(string URI, string apiKey)
        {
            var response = new HttpApiResponse();
            int attempts = 1;
            const int maxAttempts = 5;

            while (attempts <= maxAttempts)
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(5);
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                        var httpResponse = await client.GetAsync(URI);
                        response.StatusCode = (int)httpResponse.StatusCode;
                        response.StatusDescription = httpResponse.ReasonPhrase ?? string.Empty;
                        response.Content = await httpResponse.Content.ReadAsStringAsync();

                        // Handle rate limiting
                        if (httpResponse.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            string retryAfter = string.Empty;
                            if (httpResponse.Headers.TryGetValues("Retry-After", out var values))
                            {
                                retryAfter = values.First();
                            }

                            var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                retryAfter,
                                response.Content,
                                attempts,
                                apiKey);

                            apiKey = rateLimitResult.apiKey;

                            if (!rateLimitResult.shouldRetry)
                            {
                                _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                response.StatusCode = 429;
                                response.Content = "Rate limiting exceeded retry limit";
                                return response;
                            }

                            attempts++;
                            continue;
                        }

                        // Return the response regardless of status code - let the caller decide how to handle it
                        return response;
                    }
                }
                catch (TaskCanceledException ex)
                {
                    bool isTimeout = ex.InnerException is TimeoutException ||
                                     (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                     ex.Message.Contains("timed out") ||
                                     ex.Message.Contains("timeout");

                    if (isTimeout)
                    {
                        _logger?.LogWarning(ex, "GET request timed out after {Timeout} minutes for URI: {URI}", 5, URI);
                        response.StatusCode = 408; // Request Timeout
                        response.Content = $"{{\"error\": true, \"message\": \"Request timed out after 5 minutes\", \"statusCode\": \"RequestTimeout\"}}";
                        return response;
                    }
                    else
                    {
                        _logger?.LogWarning(ex, "GET request cancelled (not timeout related) for URI: {URI}", URI);
                        response.StatusCode = 499; // Client Closed Request
                        response.Content = $"{{\"error\": true, \"message\": \"Request was cancelled\", \"statusCode\": \"RequestCancelled\"}}";
                        return response;
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger?.LogError(ex, "HTTP error on GET request attempt {Attempt}/{MaxAttempts} for URI: {URI}", attempts, maxAttempts, URI);

                    if (attempts >= maxAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"{{\"error\": true, \"message\": \"HTTP error after {maxAttempts} attempts: {ex.Message}\", \"statusCode\": \"HttpRequestException\"}}";
                        return response;
                    }

                    attempts++;
                    await Task.Delay(1000 * attempts); // Exponential backoff
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Unexpected error on GET request attempt {Attempt}/{MaxAttempts} for URI: {URI}", attempts, maxAttempts, URI);

                    if (attempts >= maxAttempts)
                    {
                        response.StatusCode = 500;
                        response.Content = $"{{\"error\": true, \"message\": \"Unexpected error after {maxAttempts} attempts: {ex.Message}\", \"statusCode\": \"UnexpectedException\"}}";
                        return response;
                    }

                    attempts++;
                    await Task.Delay(1000 * attempts); // Exponential backoff
                }
            }

            // This should never be reached, but just in case
            response.StatusCode = 500;
            response.Content = $"{{\"error\": true, \"message\": \"Maximum retry attempts exceeded\", \"statusCode\": \"MaxRetriesExceeded\"}}";
            return response;
        }

        /// <summary>
        /// Synchronous wrapper for JsonReturnHttpResponseGetAsync
        /// Makes an HTTP GET request and returns both status code and response content
        /// </summary>
        /// <param name="URI">The API endpoint URL</param>
        /// <param name="apiKey">The API key for authentication</param>
        /// <returns>HttpApiResponse containing status code and response content</returns>
        internal HttpApiResponse JsonReturnHttpResponseGet(string URI, string apiKey)
        {
            var task = JsonReturnHttpResponseGetAsync(URI, apiKey);
            return task.Result;
        }

        internal async Task<string> JsonReturnStringPostAsync(string URI, string apiKey, string selectBody)
        {
            using (var cts = new CancellationTokenSource())
            {
                try
                {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                // Increase timeout to handle slow API responses
                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(5) };



                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);

                Response = await Client.PostAsync(URI, new StringContent(selectBody, Encoding.UTF8, "application/json"));

                // Only HTTP 200 is considered successful - fail-fast for all other responses
                if (Response.StatusCode == HttpStatusCode.OK)
                {
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Validate that the response content is likely JSON before returning
                    if (string.IsNullOrWhiteSpace(Contents) || (!Contents.TrimStart().StartsWith("{") && !Contents.TrimStart().StartsWith("[")))
                    {
                        _logger?.LogError("POST request to {URI} returned HTTP 200 but non-JSON content: {ContentPreview}",
                            URI, Contents?.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents);
                        throw new HttpRequestException($"API returned non-JSON content: {Contents?.Substring(0, Math.Min(Contents.Length, 200))}", null, HttpStatusCode.OK);
                    }

                    _logger?.LogDebug("POST request to {URI} successful with valid JSON response", URI);
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;
                }
                else
                {
                    responseCode = Response.StatusCode.ToString();
                    responseHeaders = Response.Headers;
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Store the response content for checking token-related rate limits
                    _lastResponseContent = Contents;

                    // Enhanced error logging with comprehensive diagnostic information
                    _logger?.LogError("API Error: HTTP {StatusCode} from POST {URI}", responseCode, URI);
                    _logger?.LogError("Request body: {RequestBody}", selectBody);
                    _logger?.LogError("Response content: {ResponseContent}", Contents?.Length > 500 ? Contents.Substring(0, 500) + "..." : Contents);

                    // Additional context for troubleshooting
                    _logger?.LogError("Response headers: {Headers}", string.Join(", ", Response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

                    if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
                    {
                        _logger.LogDebug("Full response content: {Contents}", Contents);
                    }

                    // For all error responses, return a valid JSON with error information
                    string safeContent = Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n");

                    // Handle specific error types with custom messages
                    if (responseCode == "Forbidden")
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (Contents.Contains("missing.division.permission") ||
                            Contents.Contains("missing.permission") ||
                            Contents.Contains("You are missing the following permission"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Returning permanent error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: Missing required permissions\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Returning permanent error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Permanent Access Forbidden for {resourceKey}: {Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n")}\", \"statusCode\": \"Forbidden\", \"isPermanent\": true}}";
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource}. Returning temporary error JSON", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\", \"isPermanent\": false}}";
                        }
                    }
                    else if (responseCode == "BadRequest")
                    {
                        // HTTP 400 Bad Request indicates a client-side error that should halt processing
                        string errorMessage = $"HTTP 400 Bad Request from {URI}: {Contents}";
                        _logger?.LogError("API Error: {ErrorMessage}", errorMessage);

                        // Throw an exception to halt processing - HTTP 400 errors should not be silently handled
                        throw new HttpRequestException(errorMessage, null, HttpStatusCode.BadRequest);
                    }
                    // Handle rate limiting consistently across the codebase
                    else if (responseCode == "TooManyRequests")
                    {
                        _logger?.LogWarning("{ResponseCode}: Handling rate limit", responseCode);

                        // Extract retry-after header if available
                        string retryAfter = string.Empty;
                        if (Response.Headers.TryGetValues("Retry-After", out var values))
                        {
                            retryAfter = values.First();
                        }

                        // Use the standardized rate limit handler
                        var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                            retryAfter,
                            Contents,
                            1, // First attempt in this method
                            apiKey);

                        // Return null to signal that the request should be retried with the new API key
                        return null;
                    }
                    else
                    {
                        // Check if this is a critical client error that should halt processing
                        if (responseCode == "Unauthorized" || responseCode == "NotFound" || responseCode == "MethodNotAllowed" ||
                            responseCode == "NotAcceptable" || responseCode == "Conflict" || responseCode == "Gone" ||
                            responseCode == "UnprocessableEntity" || responseCode == "InternalServerError")
                        {
                            // These are critical errors that indicate fundamental issues
                            string criticalErrorMessage = $"HTTP {responseCode} from {URI}: {Contents}";
                            _logger?.LogError("Critical API Error: {ErrorMessage}", criticalErrorMessage);

                            // Throw an exception for critical errors
                            throw new HttpRequestException(criticalErrorMessage, null, (HttpStatusCode)Enum.Parse(typeof(HttpStatusCode), responseCode));
                        }
                        else
                        {
                            // For other errors (like temporary server issues), return error JSON to allow retry
                            _logger?.LogWarning("{ResponseCode}: Returning error JSON for potential retry", responseCode);
                            // Create a safe version of the content for the error message
                            string safeCont = Contents.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents;

                            return $"{{\"error\": true, \"message\": \"{responseCode}: {safeCont}\", \"statusCode\": \"{responseCode}\"}}";
                        }
                    }


                }
            }
            catch (TaskCanceledException ex)
            {
                // Check if this is a timeout
                bool isTimeout = ex.InnerException is TimeoutException ||
                                 (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                 ex.Message.Contains("timed out") ||
                                 ex.Message.Contains("timeout");

                if (isTimeout)
                {
                    _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                    // Return a valid JSON with timeout error information
                    return $"{{\"error\": true, \"message\": \"Error calling PostToken: The operation has timed out.\", \"statusCode\": \"RequestTimeout\"}}";
                }
                else
                {
                    _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception caught in JsonReturnStringPostAsync Module. Inner Exception: {InnerException}",
                    ex.InnerException?.Message ?? "None");
                return ex.Message;
            }
            }
        }

        internal async Task<string> JsonReturnStringGetAsync(string URI, string apiKey)
        {
            using (var cts = new CancellationTokenSource())
            {
                try
                {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                // Increase timeout to handle slow API responses
                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(5) };


                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);
                Client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                Response = await Client.GetAsync(URI);

                // Only HTTP 200 is considered successful - fail-fast for all other responses
                if (Response.StatusCode == HttpStatusCode.OK)
                {
                    var Contents = await Response.Content.ReadAsStringAsync();

                    // Validate that the response content is likely JSON before returning
                    if (string.IsNullOrWhiteSpace(Contents) || (!Contents.TrimStart().StartsWith("{") && !Contents.TrimStart().StartsWith("[")))
                    {
                        _logger?.LogError("GET request to {URI} returned HTTP 200 but non-JSON content: {ContentPreview}",
                            URI, Contents?.Length > 100 ? Contents.Substring(0, 100) + "..." : Contents);
                        throw new HttpRequestException($"API returned non-JSON content: {Contents?.Substring(0, Math.Min(Contents.Length, 200))}", null, HttpStatusCode.OK);
                    }

                    _logger?.LogDebug("GET request to {URI} successful with valid JSON response", URI);
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;
                }
                else
                {
                    var Contents = await Response.Content.ReadAsStringAsync();
                    responseHeaders = Response.Headers;
                    responseCode = Response.StatusCode.ToString();

                    // Store the response content for checking token-related rate limits
                    _lastResponseContent = Contents;

                    // Enhanced error logging with comprehensive diagnostic information
                    _logger?.LogError("API Error: HTTP {StatusCode} from GET {URI}", responseCode, URI);
                    _logger?.LogError("Response content: {ResponseContent}", Contents?.Length > 500 ? Contents.Substring(0, 500) + "..." : Contents);

                    // Additional context for troubleshooting
                    _logger?.LogError("Response headers: {Headers}", string.Join(", ", Response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

                    if (_logger?.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug) == true)
                    {
                        _logger.LogDebug("Full response content: {Contents}", Contents);
                    }
                    // Special handling for Forbidden - use permission error tracker
                    if (responseCode == "Forbidden")
                    {
                        // Extract resource key from URI (use the endpoint path without query parameters)
                        string resourceKey = URI.Split('?')[0];

                        // Check for permanent permission issues (missing permissions) first
                        if (Contents.Contains("missing.division.permission") ||
                            Contents.Contains("missing.permission") ||
                            Contents.Contains("You are missing the following permission"))
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Missing required permissions. Throwing UnauthorizedAccessException", resourceKey);
                            throw new UnauthorizedAccessException($"Access Forbidden: Missing required permissions for {resourceKey}");
                        }

                        // For other forbidden errors, use the permission error tracker
                        bool isPermanentIssue = _permissionErrorTracker.RecordError(resourceKey, "Access Forbidden");

                        if (isPermanentIssue)
                        {
                            _logger?.LogError("Permanent permission issue detected for {Resource}. Throwing UnauthorizedAccessException", resourceKey);
                            throw new UnauthorizedAccessException($"Access Forbidden: Permanent permission issue for {resourceKey}");
                        }
                        else
                        {
                            _logger?.LogWarning("Temporary permission issue detected for {Resource}. Returning error JSON to allow retry.", resourceKey);
                            return $"{{\"error\": true, \"message\": \"Temporary Access Forbidden for {resourceKey}\", \"statusCode\": \"Forbidden\"}}";
                        }
                    }
                    // Handle rate limiting consistently across the codebase
                    else if (responseCode == "TooManyRequests")
                    {
                        _logger?.LogWarning("{ResponseCode}: Handling rate limit", responseCode);

                        // Extract retry-after header if available
                        string retryAfter = string.Empty;
                        if (Response.Headers.TryGetValues("Retry-After", out var values))
                        {
                            retryAfter = values.First();
                        }

                        // Use the standardized rate limit handler
                        var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                            retryAfter,
                            Contents,
                            1, // First attempt in this method
                            apiKey);

                        // Return null to signal that the request should be retried with the new API key
                        return null;
                    }
                    else if (responseCode == "BadRequest")
                    {
                        // HTTP 400 Bad Request indicates a client-side error that should halt processing
                        string errorMessage = $"HTTP 400 Bad Request from {URI}: {Contents}";
                        _logger?.LogError("API Error: {ErrorMessage}", errorMessage);

                        // Throw an exception to halt processing - HTTP 400 errors should not be silently handled
                        throw new HttpRequestException(errorMessage, null, HttpStatusCode.BadRequest);
                    }
                    // Check if this is a critical client error that should halt processing
                    else if (responseCode == "Unauthorized" || responseCode == "NotFound" || responseCode == "MethodNotAllowed" ||
                            responseCode == "NotAcceptable" || responseCode == "Conflict" || responseCode == "Gone" ||
                            responseCode == "UnprocessableEntity" || responseCode == "InternalServerError")
                    {
                        // These are critical errors that indicate fundamental issues
                        string criticalErrorMessage = $"HTTP {responseCode} from {URI}: {Contents}";
                        _logger?.LogError("Critical API Error: {ErrorMessage}", criticalErrorMessage);

                        // Throw an exception for critical errors
                        throw new HttpRequestException(criticalErrorMessage, null, (HttpStatusCode)Enum.Parse(typeof(HttpStatusCode), responseCode));
                    }
                    else
                    {
                        // For other errors (like temporary server issues), return error JSON to allow retry
                        _logger?.LogWarning("{ResponseCode}: Returning error JSON for potential retry", responseCode);
                        return $"{{\"error\": true, \"message\": \"{Contents.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "\\n")}\", \"statusCode\": \"{responseCode}\"}}";
                    }
                }
            }
            catch (TaskCanceledException ex)
            {
                // Check if this is a timeout
                bool isTimeout = ex.InnerException is TimeoutException ||
                                 (ex.InnerException != null && ex.InnerException.Message.Contains("timeout")) ||
                                 ex.Message.Contains("timed out") ||
                                 ex.Message.Contains("timeout");

                if (isTimeout)
                {
                    _logger?.LogWarning(ex, "API request timed out after {Timeout} minutes", 5);
                    // Return a valid JSON with timeout error information
                    return $"{{\"error\": true, \"message\": \"Error calling PostToken: The operation has timed out.\", \"statusCode\": \"RequestTimeout\"}}";
                }
                else
                {
                    _logger?.LogWarning(ex, "Task cancelled (not timeout related)");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception caught in JsonReturnStringGetAsync Module. Inner Exception: {InnerException}",
                    ex.InnerException?.Message ?? "None");
                return null;
            }
            }
        }

        internal Boolean DeleteNotificationSubscription(string URI, string SessionId, string apiKey)
        {

            Boolean Successful = true;

            int Attempts = 1;
            string URL = URI + "/api/v2/notifications/channels/" + SessionId + "/subscriptions";

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);

            CookieContainer cookies = new CookieContainer();
            request.UseDefaultCredentials = true;
            request.CookieContainer = cookies;
            request.ContentType = "application/json";

            // write the "Authorization" header
            request.Headers.Add("Authorization", "bearer " + apiKey);
            request.Method = "DELETE";
            request.Timeout = 200000;


            while (Attempts <= 6)
            {
                try
                {
                    HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                    CheckRateLimit(response.Headers);

                    if (response.StatusCode == HttpStatusCode.Accepted || response.StatusCode == HttpStatusCode.OK)
                    {
                        Successful = true;
                        break;
                    }
                }
                catch (WebException ex)
                {
                    Successful = false;
                    if (ex.Status == WebExceptionStatus.ProtocolError && ex.Response != null)
                    {
                        var resp = (HttpWebResponse)ex.Response;

                        // Read the response content to check for token-related rate limits
                        using (var reader = new StreamReader(resp.GetResponseStream()))
                        {
                            _lastResponseContent = reader.ReadToEnd();
                        }

                        _logger?.LogWarning(ex, "Web error returned: {StatusCode}", resp.StatusCode);

                        if (resp.StatusCode.ToString() == "429" || resp.StatusCode.ToString() == "504")
                        {
                            _logger?.LogWarning("Rate limit or gateway timeout encountered: {StatusCode}", resp.StatusCode);

                            // Use standardized rate limit handling for 429 responses
                            if (resp.StatusCode.ToString() == "429")
                            {
                                // Extract retry-after header if available
                                string retryAfter = string.Empty;
                                if (resp.Headers["Retry-After"] != null)
                                {
                                    retryAfter = resp.Headers["Retry-After"];
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    _lastResponseContent,
                                    Attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                // Update the request with the new API key
                                request.Headers.Remove("Authorization");
                                request.Headers.Add("Authorization", "bearer " + apiKey);
                            }
                            else
                            {
                                // For gateway timeout (504), wait a bit before retrying
                                _logger?.LogWarning("Gateway timeout encountered, waiting 5 seconds before retry");
                                System.Threading.Thread.Sleep(5000);
                            }
                        }
                        else
                        {
                            CheckRateLimit(resp.Headers);
                        }

                        if (resp.StatusCode == HttpStatusCode.NotFound)
                        {
                            _logger?.LogInformation("Resource not found at {URL}", URL);
                            System.Threading.Thread.Sleep(250);
                            Attempts = 7;
                        }
                        else
                        {
                            _logger?.LogWarning("Other response error: {StatusCode}", resp.StatusCode);
                        }
                    }
                    else
                    {
                        Successful = false;
                        _logger?.LogError(ex, "General web error");
                    }
                }
                catch (Exception ex)
                {
                    Successful = false;
                    _logger?.LogError(ex, "General error in DeleteNotificationSubscription");
                }
                Attempts++;
            }

            return Successful;
        }

        internal void ConvJson(dynamic json, ref DataTable DtTemp)

        {
            foreach (dynamic item in json)
            {
                DataRow newRow = DtTemp.NewRow();

                foreach (DataColumn dcTemp in DtTemp.Columns)
                {
                    if (dcTemp.ColumnName != "updated" && item[dcTemp.ToString()] != null)
                    {
                        newRow[dcTemp] = item[dcTemp.ToString()];
                    }
                }
                newRow["updated"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                DtTemp.Rows.Add(newRow);
            }
        }

        internal DataTable CreateTempUserGroupsTable()
        {
            DataTable DtTemp = new DataTable("Users");

            DtTemp.Columns.Add("id", typeof(string));
            DtTemp.Columns.Add("GroupName", typeof(String));
            DtTemp.Columns.Add("updated", typeof(DateTime));

            //Add Key For Emite Purposes
            DataColumn[] key = new DataColumn[1];
            key[0] = DtTemp.Columns[0];
            DtTemp.PrimaryKey = key;

            return DtTemp;
        }

        /// <summary>
        /// Checks rate limit headers and applies preventive throttling if needed
        /// </summary>
        internal void CheckRateLimit(WebHeaderCollection Headers)
        {
            int counter = 0;
            int timeToGo = 0;
            bool foundCount = false;
            bool foundReset = false;

            foreach (string key in Headers.AllKeys)
            {
                string value = Headers[key];
                if (key.ToString() == "inin-ratelimit-count")
                {
                    counter = Convert.ToInt32(value);
                    foundCount = true;
                }

                if (key.ToString() == "inin-ratelimit-reset")
                {
                    timeToGo = Convert.ToInt32(value);
                    foundReset = true;
                }
            }

            // Only apply throttling if we found both headers
            if (foundCount && foundReset)
            {
                // Log the current rate limit status
                _logger?.LogDebug("Current rate limit: {Count}/300, reset in {TimeToGo} seconds", counter, timeToGo);

                // Apply preventive throttling when approaching the limit
                if (counter > 290)
                {
                    _logger?.LogWarning("Rate limit count high ({Count}/300), applying preventive throttling", counter);

                    // Use a smaller wait time with a cap to avoid excessive delays
                    int waitTime = Math.Min(timeToGo, 15);
                    _logger?.LogInformation("Waiting {WaitTime} seconds before continuing", waitTime);
                    System.Threading.Thread.Sleep(1000 * waitTime);
                }
                else if (counter > 250)
                {
                    // Add a small delay when getting close to the limit
                    _logger?.LogInformation("Rate limit count approaching limit ({Count}/300), adding small delay", counter);
                    System.Threading.Thread.Sleep(500);
                }
            }
        }

        /// <summary>
        /// Performs a PUT request with standardized rate limit handling
        /// </summary>
        internal string JsonPutString(string url, string apiKey, JObject jsonData)
        {
            int attempts = 1;

            while (attempts <= 5) // Match MAX_RETRY_ATTEMPTS
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                    var content = new StringContent(jsonData.ToString(), Encoding.UTF8, "application/json");

                    try
                    {
                        var response = client.PutAsync(url, content).Result;

                        if (response.IsSuccessStatusCode)
                        {
                            return response.Content.ReadAsStringAsync().Result;
                        }
                        else
                        {
                            var responseContent = response.Content.ReadAsStringAsync().Result;
                            _lastResponseContent = responseContent;

                            // Handle rate limiting
                            if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                _logger?.LogWarning("Rate limit exceeded (PUT) - Attempt {Attempt}", attempts);

                                string retryAfter = string.Empty;
                                if (response.Headers.TryGetValues("Retry-After", out var values))
                                {
                                    retryAfter = values.First();
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    responseContent,
                                    attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                if (!rateLimitResult.shouldRetry)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                    return string.Empty;
                                }

                                attempts++;
                            }
                            else
                            {
                                _logger?.LogError("Failed to update. Status Code: {StatusCode}, Response: {Response}",
                                    response.StatusCode, responseContent);
                                return string.Empty;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Exception in JsonPutString for URL {Url}", url);
                        return string.Empty;
                    }
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// Performs a GET request with standardized rate limit handling
        /// </summary>
        internal string JsonGetString(string url, string apiKey)
        {
            int attempts = 1;

            while (attempts <= 5) // Match MAX_RETRY_ATTEMPTS
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

                    try
                    {
                        var response = client.GetAsync(url).Result;

                        if (response.IsSuccessStatusCode)
                        {
                            return response.Content.ReadAsStringAsync().Result;
                        }
                        else
                        {
                            var responseContent = response.Content.ReadAsStringAsync().Result;
                            _lastResponseContent = responseContent;

                            // Handle rate limiting
                            if (response.StatusCode == HttpStatusCode.TooManyRequests)
                            {
                                _logger?.LogWarning("Rate limit exceeded (GET) - Attempt {Attempt}", attempts);

                                string retryAfter = string.Empty;
                                if (response.Headers.TryGetValues("Retry-After", out var values))
                                {
                                    retryAfter = values.First();
                                }

                                // Use the standardized rate limit handler
                                var rateLimitResult = _rateLimitHandler.HandleRateLimit(
                                    retryAfter,
                                    responseContent,
                                    attempts,
                                    apiKey);

                                apiKey = rateLimitResult.apiKey;

                                if (!rateLimitResult.shouldRetry)
                                {
                                    _logger?.LogError("Rate limiting exceeded retry limit after {Attempts} attempts", attempts);
                                    return string.Empty;
                                }

                                attempts++;
                            }
                            else
                            {
                                _logger?.LogWarning("Failed to get resource. Status Code: {StatusCode}", response.StatusCode);
                                return string.Empty;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Exception in JsonGetString for URL {Url}", url);
                        return string.Empty;
                    }
                }
            }

            return string.Empty;
        }
    }
}
// spell-checker: ignore: resp, emite
