# Local testing 
# docker run --rm -v /c/temp/.tbls.yml:/.tbls.yml ghcr.io/k1low/tbls:latest doc

dsn: 
  "postgres://system:system@127.0.0.1:5432/contactcentredb?sslmode=disable"
name: Genesys Adapter Data Dictionary
docPath: doc/md
exclude:
  - cron.job*
  - partman.*
  - pg_stat*
  - '*_p*_*'
  - '*_default*'
  - '*timezonecalcs*'

er:
  # Skip generation of ER diagram
  skip: true

# relations:
# -
#   table: public.queueinteractiondatadaily
#   columns:
#     - queueid
#   parentTable: public.queuedetails
#   parentColumns:
#     - id
#   # Relation definition
#   # Default is `Additional Relation`
#   def: public.queueinteractiondatadaily->public.queuedetails

#lint:
#  requireColumnComment:
#    enabled: true
#    exclude:
#      - keyid
#      - updated

templates:
  md:
    index: '.tbls/index.md.tmpl'
   #table: 'tables.md.tmpl'