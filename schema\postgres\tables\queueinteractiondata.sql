CREATE TABLE IF NOT EXISTS queueinteractiondata (
    keyid varchar(255) NOT NULL,
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone NOT NULL,
    startdateltc timestamp without time zone,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    noffered integer,
    noversla integer,
    tacdcount integer,
    tacdtimesum numeric(20, 2),
    tacdtimemax numeric(20, 2),
    tacdtimemin numeric(20, 2),
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tflowoutcount integer,
    tflowouttimesum numeric(20, 2),
    tflowouttimemax numeric(20, 2),
    tflowouttimemin numeric(20, 2),
    twaitcount integer,
    twaittimesum numeric(20, 2),
    twaittimemax numeric(20, 2),
    twaittimemin numeric(20, 2),
    tabandoncount integer,
    tabandontimesum numeric(20, 2),
    tabandontimemax numeric(20, 2),
    tabandontimemin numeric(20, 2),
    servicelevelnumerator numeric(20, 2),
    serviceleveldenominator numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT queueinteractiondatanw_pkey PRIMARY KEY (keyid, startdate)
) PARTITION BY RANGE (startdate);

CREATE INDEX IF NOT EXISTS "QueueInteractionDataDate" ON queueinteractiondata USING btree (startdate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "QueueInteractionDataDateLTC" ON queueinteractiondata USING btree (startdateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "QueueInteractionDataMedia" ON queueinteractiondata USING btree (
    mediatype ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "QueueInteractionDataQueue" ON queueinteractiondata USING btree (
    queueid ASC NULLS LAST
);

ALTER TABLE IF EXISTS queueintdate_2020_01 RENAME TO queueinteractiondata_p2020_01;
ALTER TABLE IF EXISTS queueintdate_2020_02 RENAME TO queueinteractiondata_p2020_02;
ALTER TABLE IF EXISTS queueintdate_2020_03 RENAME TO queueinteractiondata_p2020_03;
ALTER TABLE IF EXISTS queueintdate_2020_04 RENAME TO queueinteractiondata_p2020_04;
ALTER TABLE IF EXISTS queueintdate_2020_05 RENAME TO queueinteractiondata_p2020_05;
ALTER TABLE IF EXISTS queueintdate_2020_06 RENAME TO queueinteractiondata_p2020_06;
ALTER TABLE IF EXISTS queueintdate_2020_07 RENAME TO queueinteractiondata_p2020_07;
ALTER TABLE IF EXISTS queueintdate_2020_08 RENAME TO queueinteractiondata_p2020_08;
ALTER TABLE IF EXISTS queueintdate_2020_09 RENAME TO queueinteractiondata_p2020_09;
ALTER TABLE IF EXISTS queueintdate_2020_10 RENAME TO queueinteractiondata_p2020_10;
ALTER TABLE IF EXISTS queueintdate_2020_11 RENAME TO queueinteractiondata_p2020_11;
ALTER TABLE IF EXISTS queueintdate_2020_12 RENAME TO queueinteractiondata_p2020_12;
ALTER TABLE IF EXISTS queueintdate_2021_01 RENAME TO queueinteractiondata_p2021_01;
ALTER TABLE IF EXISTS queueintdate_2021_02 RENAME TO queueinteractiondata_p2021_02;
ALTER TABLE IF EXISTS queueintdate_2021_03 RENAME TO queueinteractiondata_p2021_03;
ALTER TABLE IF EXISTS queueintdate_2021_04 RENAME TO queueinteractiondata_p2021_04;
ALTER TABLE IF EXISTS queueintdate_2021_05 RENAME TO queueinteractiondata_p2021_05;
ALTER TABLE IF EXISTS queueintdate_2021_06 RENAME TO queueinteractiondata_p2021_06;
ALTER TABLE IF EXISTS queueintdate_2021_07 RENAME TO queueinteractiondata_p2021_07;
ALTER TABLE IF EXISTS queueintdate_2021_08 RENAME TO queueinteractiondata_p2021_08;
ALTER TABLE IF EXISTS queueintdate_2021_09 RENAME TO queueinteractiondata_p2021_09;
ALTER TABLE IF EXISTS queueintdate_2021_10 RENAME TO queueinteractiondata_p2021_10;
ALTER TABLE IF EXISTS queueintdate_2021_11 RENAME TO queueinteractiondata_p2021_11;
ALTER TABLE IF EXISTS queueintdate_2021_12 RENAME TO queueinteractiondata_p2021_12;
ALTER TABLE IF EXISTS queueintdate_2022_01 RENAME TO queueinteractiondata_p2022_01;
ALTER TABLE IF EXISTS queueintdate_2022_02 RENAME TO queueinteractiondata_p2022_02;
ALTER TABLE IF EXISTS queueintdate_2022_03 RENAME TO queueinteractiondata_p2022_03;
ALTER TABLE IF EXISTS queueintdate_2022_04 RENAME TO queueinteractiondata_p2022_04;
ALTER TABLE IF EXISTS queueintdate_2022_05 RENAME TO queueinteractiondata_p2022_05;
ALTER TABLE IF EXISTS queueintdate_2022_06 RENAME TO queueinteractiondata_p2022_06;
ALTER TABLE IF EXISTS queueintdate_2022_07 RENAME TO queueinteractiondata_p2022_07;
ALTER TABLE IF EXISTS queueintdate_2022_08 RENAME TO queueinteractiondata_p2022_08;
ALTER TABLE IF EXISTS queueintdate_2022_09 RENAME TO queueinteractiondata_p2022_09;
ALTER TABLE IF EXISTS queueintdate_2022_10 RENAME TO queueinteractiondata_p2022_10;
ALTER TABLE IF EXISTS queueintdate_2022_11 RENAME TO queueinteractiondata_p2022_11;
ALTER TABLE IF EXISTS queueintdate_2022_12 RENAME TO queueinteractiondata_p2022_12;

-- Add comments

COMMENT ON COLUMN queueInteractionData.av10count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av10timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av10timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av10timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av1count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av1timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av1timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av1timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av2count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av2timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av2timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av2timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av3count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av3timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av3timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av3timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av4count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av4timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av4timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av4timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av5count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av5timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av5timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av5timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av6count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av6timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av6timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av6timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av7count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av7timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av7timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av7timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av8count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av8timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av8timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av8timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av9count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av9timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av9timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.av9timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN queueInteractionData.direction IS 'Direction'; 
COMMENT ON COLUMN queueInteractionData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN queueInteractionData.mediatype IS 'Media Type'; 
COMMENT ON COLUMN queueInteractionData.nblindtransferred IS 'Total Blind Transfer Count'; 
COMMENT ON COLUMN queueInteractionData.nconnected IS 'Total Conversations Connected Count'; 
COMMENT ON COLUMN queueInteractionData.nconsult IS 'Total Consult Count'; 
COMMENT ON COLUMN queueInteractionData.nconsulttransferred IS 'Total Consult Transfer Count'; 
COMMENT ON COLUMN queueInteractionData.nerror IS 'Total Error Count'; 
COMMENT ON COLUMN queueInteractionData.noffered IS 'Total Conversations Offered Count'; 
COMMENT ON COLUMN queueInteractionData.noutbound IS 'Total OutBound Conversations'; 
COMMENT ON COLUMN queueInteractionData.noversla IS 'Total Conversations Answered over the SLA Count'; 
COMMENT ON COLUMN queueInteractionData.ntransferred IS 'Total Transfer Count'; 
COMMENT ON COLUMN queueInteractionData.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN queueInteractionData.serviceleveldenominator IS 'Total Converstions Chosen for the GOS Calc by Genesys'; 
COMMENT ON COLUMN queueInteractionData.servicelevelnumerator IS 'Total Conversations Answer in SLA'; 
COMMENT ON COLUMN queueInteractionData.startdate IS 'Interval Start Date (UTC)'; 
COMMENT ON COLUMN queueInteractionData.startdateltc IS 'Interval Start Date (LTC)'; 
COMMENT ON COLUMN queueInteractionData.tabandoncount IS 'Total Abandoned Count'; 
COMMENT ON COLUMN queueInteractionData.tabandontimemax IS 'Max Abandoned Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tabandontimemin IS 'Min Abandoned Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tabandontimesum IS 'Total Abandoned Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacdcount IS 'Total Conversations Queued Count'; 
COMMENT ON COLUMN queueInteractionData.tacdtimemax IS 'Max Conversations Queued Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacdtimemin IS 'Min Conversations Queued Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacdtimesum IS 'Total Conversations Queued Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacwcount IS 'Total ACW Count'; 
COMMENT ON COLUMN queueInteractionData.tacwtimemax IS 'Max ACW Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacwtimemin IS 'Min ACW Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tacwtimesum IS 'Total ACW Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.talertcount IS 'Total Alert Count'; 
COMMENT ON COLUMN queueInteractionData.talerttimesum IS 'Total Alert Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.talerttimemax IS 'Max Alert Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.talerttimemin IS 'Min ACW Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tansweredcount IS 'Total Answered Count'; 
COMMENT ON COLUMN queueInteractionData.tansweredtimemax IS 'Max Answered Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tansweredtimemin IS 'Min Answered Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tansweredtimesum IS 'Total Answered Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tcontactingcount IS 'Total Contacting Count'; 
COMMENT ON COLUMN queueInteractionData.tcontactingtimemax IS 'Max Contacting Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tcontactingtimemin IS 'Min Contacting Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tcontactingtimesum IS 'Total Contacting Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tdialingcount IS 'Total Dialing Count'; 
COMMENT ON COLUMN queueInteractionData.tdialingtimemax IS 'Max Dialing Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tdialingtimemin IS 'Min Dialing Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tdialingtimesum IS 'Total Dialing Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tflowoutcount IS 'Total Flow Out Count'; 
COMMENT ON COLUMN queueInteractionData.tflowouttimemax IS 'Max Flow Out Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tflowouttimemin IS 'Min Flow Out Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tflowouttimesum IS 'Total Flow Out Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.thandlecount IS 'Total Handle Count'; 
COMMENT ON COLUMN queueInteractionData.thandletimemax IS 'Max Handle Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.thandletimemin IS 'Min Handle Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.thandletimesum IS 'Total Handle Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.theldcompletecount IS 'Total Hold Count for Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.theldcompletetimemax IS 'Max Hold Time for Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.theldcompletetimemin IS 'Min Hold Time for Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.theldcompletetimesum IS 'Total Hold Time for Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.theldcount IS 'Total Hold Count'; 
COMMENT ON COLUMN queueInteractionData.theldtimemax IS 'Max Hold Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.theldtimemin IS 'Min Hold Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.theldtimesum IS 'Total Hold Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tnotrespondingcount IS 'Total Not Responding Count'; 
COMMENT ON COLUMN queueInteractionData.tnotrespondingtimemax IS 'Max Not Responding Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tnotrespondingtimemin IS 'Min Not Responding Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tnotrespondingtimesum IS 'Total Not Responding Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.ttalkcompletecount IS 'Total Talk Count For Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.ttalkcompletetimemax IS 'Max Talk Time For Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.ttalkcompletetimemin IS 'Min Talk Time For Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.ttalkcompletetimesum IS 'Total Talk Time For Completed Conversations'; 
COMMENT ON COLUMN queueInteractionData.ttalkcount IS 'Total Talk Count'; 
COMMENT ON COLUMN queueInteractionData.ttalktimemax IS 'Max Talk Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.ttalktimemin IS 'Min Talk Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.ttalktimesum IS 'Total Talk Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tvoicemailcount IS 'Total Voicemail Count'; 
COMMENT ON COLUMN queueInteractionData.tvoicemailtimemax IS 'Max Voicemail Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tvoicemailtimemin IS 'Min Voicemail Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.tvoicemailtimesum IS 'Total Voicemail Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.twaitcount IS 'Total Wait Count'; 
COMMENT ON COLUMN queueInteractionData.twaittimemax IS 'Max Wait Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.twaittimemin IS 'Min Wait Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.twaittimesum IS 'Total Wait Time (Seconds)'; 
COMMENT ON COLUMN queueInteractionData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN queueInteractionData.wrapupcode IS 'Wrap Up GUID'; 
COMMENT ON TABLE queueInteractionData IS 'Queue Interaction Data Interval Data - Interval is from (15-60) Min(s)'; 