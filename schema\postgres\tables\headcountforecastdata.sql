CREATE TABLE IF NOT EXISTS headcountforecastdata(
    keyid varchar(100) NOT NULL,
    businessunitid varchar(50),
    planninggroup varchar(50),
    scheduleid varchar(50),
    weekdate timestamp without time zone,
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    requiredperinterval numeric(20, 2),
    requiredwithoutshrinkageperinterval numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT headcountforecastdata_key PRIMARY KEY (keyid)
) TABLESPACE pg_default;