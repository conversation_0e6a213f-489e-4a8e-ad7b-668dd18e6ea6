IF dbo.csg_table_exists('kq_analysis_taxonomy') = 0
CREATE TABLE [kq_analysis_taxonomy] (
    [kq_analysistaxonomyid] [uniqueidentifier] NOT NULL,
    [kq_analysisid] [uniqueidentifier],
    [taxonomy] [nvarchar](200),
    CONSTRAINT [PK_kq_analysis_taxonomy] PRIMARY KEY ([kq_analysistaxonomyid])
);



-- Create indexes on foreign key columns for performance
IF dbo.csg_index_exists('IX_kq_analysis_taxonomy_kq_analysisid', 'kq_analysis_taxonomy') = 0
CREATE INDEX [IX_kq_analysis_taxonomy_kq_analysisid] ON [kq_analysis_taxonomy]([kq_analysisid]);
