CREATE TABLE IF NOT EXISTS wrapupdetails (
    id varchar(50) NOT NULL,
    name varchar(255),
    updated timestamp without time zone,
    CONSTRAINT wrapupdetails_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

INSERT INTO wrapupdetails (id,"name")
VALUES ('00000000-0000-0000-0000-0000000000000','ININ-WRAP-UP-TIMEOUT')
ON CONFLICT DO NOTHING;

COMMENT ON COLUMN wrapupDetails.id IS 'Primary Key / Wrap Up GUID'; 
COMMENT ON COLUMN wrapupDetails.name IS 'Wrap Up Name'; 
COMMENT ON COLUMN wrapupDetails.updated IS 'Date Row Updated (UTC)';
COMMENT ON TABLE wrapupDetails IS 'Wrap Up Code Details Lookup Up Data'; 