CREATE
OR R<PERSON>LACE PROCEDURE archiveuserpresence(IN aggoffset integer, IN aggtype character) LANGUAGE 'plpgsql' AS $BODY$ DECLARE CurrentTimeUTC timestamp without time zone;

CurrentTimeLTC timestamp without time zone;

SystemTime timestamp without time zone;

OffsetTime integer;

CurrentDOW integer;

StartDate timestamp without time zone;

EndDate timestamp without time zone;

TableName varchar(50);

DelsqlCommand varchar(1000);

InssqlCommand text;

begin
select
    utctime into CurrentTimeUTC
from
    timezonecalcs('Australia/Sydney');

select
    ltctime into CurrentTimeLTC
from
    timezonecalcs('Australia/Sydney');

select
    OffSetT into OffsetTime
from
    (
        select
            datediff('minute', CurrentTimeUTC, CurrentTimeLTC) as OffsetT
    ) rawdata;

select
    currDow into CurrentDOW
from
    (
        select
            extract(
                isodow
                from
                    date(CurrentTimeLTC)
            ) - 1 as currDow
    ) rawdata;

RAISE NOTICE 'CurrentTimeUTC is : %',
CurrentTimeUTC;

RAISE NOTICE 'CurrentTimeLTC is : %',
CurrentTimeLTC;

RAISE NOTICE 'CurrDOW is : %',
CurrentDOW;

RAISE NOTICE 'OffsetTime is : %',
OffsetTime;

StartDate = CASE
    AggType
    WHEN 'M' THEN CAST(
        CurrentTimeLTC + ((AggOffSet * -1) * interval '1 month') AS DATE
    )
    WHEN 'W' THEN CAST(
        CurrentTimeLTC + ((CurrentDOW * -1) * interval '1 day') + ((AggOffSet * -1) * interval '1 week') AS DATE
    )
    WHEN 'D' THEN CAST(
        CurrentTimeLTC + ((AggOffSet * -1) * interval '1 day') AS DATE
    )
END;

RAISE NOTICE 'StartDate First Pass  is : %',
StartDate;

StartDate = CASE
    AggType
    WHEN 'M' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-01 00:00:00.000'
    )
    WHEN 'W' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-',
        CAST(
            EXTRACT(
                DAY
                FROM
                    StartDate :: timestamp
            ) AS CHAR(2)
        ),
        ' 00:00:00.000'
    )
    WHEN 'D' THEN CONCAT(
        CAST(
            EXTRACT(
                YEAR
                FROM
                    StartDate :: timestamp
            ) AS CHAR(4)
        ),
        '-',
        EXTRACT(
            MONTH
            FROM
                StartDate :: timestamp
        ),
        '-',
        CAST(
            EXTRACT(
                DAY
                FROM
                    StartDate :: timestamp
            ) AS CHAR(2)
        ),
        ' 00:00:00.000'
    )
END;

RAISE NOTICE 'StartDate Second Pass is : %',
StartDate;

EndDate = CASE
    AggType
    WHEN 'M' THEN StartDate + (1 * interval '1 month') - (1 * interval '1 minute')
    WHEN 'W' THEN StartDate + (1 * interval '1 week') - (1 * interval '1 minute')
    WHEN 'D' THEN StartDate + (1 * interval '1 day') - (1 * interval '1 minute')
END;

RAISE NOTICE 'End Date First Pass is : %',
EndDate;

TableName = CASE
    AggType
    WHEN 'M' THEN 'userPresenceDataMonthly'
    WHEN 'W' THEN 'userPresenceDataWeekly'
    WHEN 'D' THEN 'userPresenceDataDaily'
END;

RAISE NOTICE 'TableName is : %',
TableName;

DelsqlCommand = CONCAT(
    'DELETE FROM ',
    TableName,
    ' Where StartDate = ''',
    StartDate,
    ''''
);

IF DelsqlCommand <> '' THEN EXECUTE DelsqlCommand;

END IF;

RAISE NOTICE 'Delete Completed:';

InssqlCommand = CONCAT(
    'INSERT INTO ',
    TableName,
    ' SELECT CONCAT(id , ''|'' , userid , ''|'', ''',
    StartDate,
    ''',''|'', routingid,''|'',systempresenceid, ''|'', timetype) as keyid
					   ,id   				 
					   ,userid                    
					   ,''',
    StartDate,
    ''' as startDate
					   ,timetype
					   ,systempresenceid   					
					   ,presenceid   					
					   ,SUM(presencetime) as presencetime   					
					   ,routingid   					
					   ,SUM(routingtime) as routingtime   					
					   ,timezone(''UTC'', now()) as updated   					
					   FROM userPresenceData   					
					   WHERE startdateltc between ''',
    StartDate,
    ''' and ''',
    EndDate,
    '''
					   GROUP BY CONCAT(id , ''|'' , userid , ''|'', ''',
    StartDate,
    ''',''|'', routingid,''|'',systempresenceid,''|'', timetype)
					   ,id   				
					   ,userid  
					   ,timetype
					   ,systempresenceid   				
					   ,presenceid   				
					   ,routingid;'
);

IF InssqlCommand <> '' THEN EXECUTE InssqlCommand;

END IF;

RAISE NOTICE 'Insert Completed:';

end;

$BODY$;