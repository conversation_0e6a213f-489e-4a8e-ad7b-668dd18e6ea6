DROP VIEW IF EXISTS vwqueuedetails CASCADE;
CREATE OR REPLACE VIEW vwqueuedetails AS
SELECT
    qd.id
    ,qd.name
    ,qd.description
    ,SUBSTRING(qd.description, 'queuegroup=([^;]+)(;|$)') AS queuegroup
    ,SUBSTRING(qd.description, 'region=([^;]+)(;|$)') AS region
    ,SUBSTRING(qd.description, 'grouptype=([^;]+)(;|$)') AS grouptype
    ,SUBSTRING(qd.description, 'country=([^;]+)(;|$)') AS country
    ,SUBSTRING(qd.description, 'language=([^;]+)(;|$)') AS language
    ,SUBSTRING(qd.description, 'accountowner=([^;]+)(;|$)') AS accountowner
    ,qd.divisionid
    ,divisiondetails.name as divisionname
    ,divisiondetails.name as vwuserpresencedata
    ,qd.enabletranscription
    ,qd.isactive
    ,qd.callslatargetperc
    ,qd.callslatargetduration
    ,qd.callbackslatargetperc
    ,qd.callbackslatargetduration
    ,qd.chatslatargetperc
    ,qd.chatslatargetduration
    ,qd.emailslatargetperc
    ,qd.emailslatargetduration
    ,qd.messageslatargetperc
    ,qd.messageslatargetduration
FROM
    queueDetails as qd
    INNER join divisiondetails as divisiondetails on qd.divisionid = divisiondetails.id;

COMMENT ON COLUMN vwqueuedetails.id IS 'Primary Key/Queue ID';
COMMENT ON COLUMN vwqueuedetails.name IS 'Queue Name';
COMMENT ON COLUMN vwqueuedetails.description IS 'Queue Description';
COMMENT ON COLUMN vwqueuedetails.queuegroup IS 'Queue Group';
COMMENT ON COLUMN vwqueuedetails.region IS 'Region';
COMMENT ON COLUMN vwqueuedetails.grouptype IS 'Group Type';
COMMENT ON COLUMN vwqueuedetails.country IS 'Country';
COMMENT ON COLUMN vwqueuedetails.language IS 'Language';
COMMENT ON COLUMN vwqueuedetails.divisionid IS 'Queue Division ID';
COMMENT ON COLUMN vwqueuedetails.divisionname IS 'Queue Division Name';
COMMENT ON COLUMN vwqueuedetails.vwuserpresencedata IS 'User Presence Data';
COMMENT ON COLUMN vwqueuedetails.enabletranscription IS 'Enable Transcription Yes/No';
COMMENT ON COLUMN vwqueuedetails.isactive IS 'Active Yes/No';
COMMENT ON COLUMN vwqueuedetails.callslatargetperc IS 'SLA Target Percentage for Calls';
COMMENT ON COLUMN vwqueuedetails.callslatargetduration IS 'SLA Target Duraction (sec) for Calls';
COMMENT ON COLUMN vwqueuedetails.callbackslatargetperc IS 'SLA Target Percentage for Callbacks';
COMMENT ON COLUMN vwqueuedetails.callbackslatargetduration IS 'SLA Target Duraction (sec) for Callbacks';
COMMENT ON COLUMN vwqueuedetails.chatslatargetperc IS 'SLA Target Percentage for Chats';
COMMENT ON COLUMN vwqueuedetails.chatslatargetduration IS 'SLA Target Duraction (sec) for Chats';
COMMENT ON COLUMN vwqueuedetails.emailslatargetperc IS 'SLA Target Percentage for Emails';
COMMENT ON COLUMN vwqueuedetails.emailslatargetduration IS 'SLA Target Duraction (sec) for Emails';
COMMENT ON COLUMN vwqueuedetails.messageslatargetperc IS 'SLA Target Percentage for Messages';
COMMENT ON COLUMN vwqueuedetails.messageslatargetduration IS 'SLA Target Duraction (sec) for Messages';

COMMENT ON VIEW vwqueuedetails IS 'See QueueDetails: Queue Lookup data';
