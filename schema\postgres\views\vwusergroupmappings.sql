DROP VIEW IF EXISTS vwusergroupmappings;

CREATE
OR REPLACE VIEW vwusergroupmappings AS
SELECT 
    ugm.id,
    ugm.name,
    ud.id AS user_id,
    ud.name AS user_name,
    ugm.groupid AS group_id,
    ugm.updated
FROM 
    usergroupmappings ugm
JOIN 
    userdetails ud ON ugm.userid = ud.id;

COMMENT ON COLUMN vwusergroupmappings.id IS 'Primary Key / Unique Identifier for User-Group Mapping';
COMMENT ON COLUMN vwusergroupmappings.name IS 'Name of the Group';
COMMENT ON COLUMN vwusergroupmappings.user_id IS 'Unique Identifier for the User (Does not include owners)';
COMMENT ON COLUMN vwusergroupmappings.user_name IS 'Name of the User';
COMMENT ON COLUMN vwusergroupmappings.group_id IS 'Unique Identifier for the Group';
COMMENT ON COLUMN vwusergroupmappings.updated IS 'Timestamp of the Last Update for the User-Group Mapping';