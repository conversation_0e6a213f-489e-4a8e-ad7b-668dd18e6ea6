CREATE OR ALTER VIEW [vwRealTimeUserConv] AS
SELECT
    ud.userid,
    rl.name,
    ud.conversationid,
    ud.media,
    ud.queueid,
    ud.direction,
    ud.conversationstate,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN 'DIAL'
        WHEN ud.acwstate = 1 THEN 'ACW'
        WHEN ud.heldstate = 1 THEN 'HELD'
        WHEN ud.conversationstate = 'alerting' THEN 'ALERT'
        WHEN ud.conversationstate = 'contacting' THEN 'CONTACT'
        ELSE 'TALKING'
    END AS convstatus,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN DATEDIFF(s, ud.updated, GETUTCDATE())
        WHEN ud.acwstate = 1 THEN DATEDIFF(s, ud.acwtime, GETUTCDATE())
        WHEN ud.heldstate = 1 THEN DATEDIFF(s, ud.heldtime, GETUTCDATE())
        WHEN ud.conversationstate = 'alerting' THEN DATEDIFF(s, ud.updated, GETUTCDATE())
        ELSE DATEDIFF(s, ud.talktime, GETUTCDATE())
    END AS convstatustime,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN (DATEDIFF(s, ud.updated, GETUTCDATE()) / 86400.00)
        WHEN ud.acwstate = 1 THEN (DATEDIFF(s, ud.acwtime, GETUTCDATE()) / 86400.00)
        WHEN ud.heldstate = 1 THEN (
            DATEDIFF(s, ud.heldtime, GETUTCDATE()) / 86400.00
        )
        WHEN ud.conversationstate = 'alerting' THEN (DATEDIFF(s, ud.updated, GETUTCDATE()) / 86400.00)
        ELSE (
            DATEDIFF(
                s,
                ud.talktime,
                GETUTCDATE()
            ) / 86400.00
        )
    END AS convstatustimeDay,
    ud.acwstate,
    ud.acwtime,
    ud.heldstate,
    ud.heldtime,
    ud.talktime
FROM
    userRealTimeConvData AS ud
    LEFT OUTER JOIN vwUserDetail rl ON ud.userid = rl.id
WHERE
    (
        conversationstate NOT IN ('terminated', 'disconnected')
    )
    OR (acwstate = 1)