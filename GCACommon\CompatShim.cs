using System.Collections.Generic;

namespace CSG.Adapter.Compatability;

// Temporary class to allow gradual removal of dependencies
public class LegacyOptions
{
    private static readonly Dictionary<string, string> _options = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase);
    private static readonly Dictionary<string, string> _defaultOptions = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase){
        {"DateTimeZone",    "Australia/Sydney"},
        {"interval",        "30"},
        {"useraggviews",    "tHandle,0,120000,av1;tHandle,121001,300000,av2;tHandle,300001,600000,av3;tAbandon,0,30000,av4;tHandle,30001,60000,av5;tHandle,60001,120000,av6;tHandle,121001,300000,av7;tHandle,300001,600000,av8;tAbandon,0,600000,av9;tHandle,0,10000,av10"},
        {"queueaggviews",   "tHandle,0,120000,av1;tHandle,121001,300000,av2;tHandle,300001,600000,av3;tAbandon,0,30000,av4;tHandle,30001,60000,av5;tHandle,60001,120000,av6;tHandle,121001,300000,av7;tHandle,300001,600000,av8;tAbandon,0,600000,av9;tHandle,0,10000,av10"},
    };

    [System.Diagnostics.DebuggerStepThrough]
    public static void SetOption(string key, string? value)
    {
        if (value != null)
            _options.Add(key, value);
    }

    [System.Diagnostics.DebuggerStepThrough]
    public static string GetOption(string key)
    {
        if (_options.ContainsKey(key))
            return _options[key];

        if (_defaultOptions.ContainsKey(key))
            return _defaultOptions[key];

        throw new ArgumentNullException(key);
    }
}
