IF dbo.csg_table_exists('adherenceexcData') = 0
CREATE TABLE [adherenceexcData](
    [keyid] [nvarchar](100) NOT NULL,
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [enddate] [datetime],
    [enddateltc] [datetime],
    [durationsecs] [int],
    [tolerance] [int],
    [actualdurationsecs] [int],
    [scheduledActivityCategory] [nvarchar](50),
    [actualActivityCategory] [nvarchar](50),
    [systemPresence] [nvarchar](50),
    [routingStatus] [nvarchar](50),
    [impact] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_adherenceexcData] PRIMARY KEY ([keyid])
);

DECLARE @KeyidPrefix NVARCHAR(255) = 'v1_'
DECLARE @deleted_count INT
IF EXISTS (
    SELECT 1 
    FROM adherenceexcData
    WHERE LEFT(keyid, LEN(@KeyidPrefix)) <> @KeyidPrefix
)
BEGIN
    DELETE FROM adherenceexcData
    WHERE LEFT(keyid, LEN(@KeyidPrefix)) <> @KeyidPrefix;
    SET @deleted_count = @@ROWCOUNT;
    IF @deleted_count > 0
    BEGIN
        UPDATE tableDefinitions
        SET datekeyfield = DATEADD(MONTH, -12, GETDATE())
        WHERE tablename = 'adherenceexcData';
        
        PRINT 'Table definition "datekeyfield" column set back to older date.';
    END
    PRINT 'Records deleted: ' + CAST(@deleted_count AS NVARCHAR(10)) + ' and tabledefinitions updated.';
END
ELSE
BEGIN
    PRINT 'No records found with old keyid in use.';
END;
