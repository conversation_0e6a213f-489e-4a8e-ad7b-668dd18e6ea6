create
or replace view vwqueueconvrealtime as
select
    queuerealtimeconvdata.conversationid,
    queuerealtimeconvdata.media,
    queuerealtimeconvdata.actingas,
    queuerealtimeconvdata.conversationstate,
    (
        DATEDIFF(
            'second',
            queuerealtimeconvdata.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        )
    ) as StatusTimeSecs,
    (
        DATEDIFF(
            'second',
            queuerealtimeconvdata.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        )
    ) / 86400.00 as statusDays,
	cast((
        DATEDIFF(
            'second',
            queuerealtimeconvdata.startdate :: timestamp,
            timezone('utc', now()) :: timestamp
        ) * interval '1 sec'
    ) as varchar) as "StatusTimeFormatted",
    queuerealtimeconvdata.skill1,
    skill1.name as SkillName1,
    queuerealtimeconvdata.skill2,
    skill2.name as SkillName2,
    queuerealtimeconvdata.skill3,
    skill3.name as SkillName3,
    queuerealtimeconvdata.initialpriority,
    queuerealtimeconvdata.participantname,
    queuerealtimeconvdata.queueid,
    queuedetails.name as queuename,
    queuedetails.divisionid as division,
    divisiondetails.name as divisionname,
    queuerealtimeconvdata.userid,
    userdetail.name,
    userdetail.department,
    userdetail.managername,
    queuerealtimeconvdata.direction,
    queuerealtimeconvdata.ani,
    queuerealtimeconvdata.dnis,
    queuerealtimeconvdata.requestedrout1,
    queuerealtimeconvdata.requestedrout2,
    queuerealtimeconvdata.usedrout
from
    queuerealtimeconvdata queuerealtimeconvdata
    left outer join skilldetails skill1 on skill1.id = queuerealtimeconvdata.skill1
    left outer join skillDetails skill2 on skill2.id = queuerealtimeconvdata.skill2
    left outer join skillDetails skill3 on skill3.id = queuerealtimeconvdata.skill3
    left outer join queuedetails queuedetails on queuedetails.id = queuerealtimeconvdata.queueid
    left outer join divisiondetails divisiondetails on divisiondetails.id = queuedetails.divisionid
    left outer join vwUserDetail userdetail on userdetail.id = queuerealtimeconvdata.userid;

COMMENT ON COLUMN vwqueueconvrealtime.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwqueueconvrealtime.media IS 'Media';
COMMENT ON COLUMN vwqueueconvrealtime.actingas IS 'Acting As';
COMMENT ON COLUMN vwqueueconvrealtime.conversationstate IS 'Conversation State';
COMMENT ON COLUMN vwqueueconvrealtime.statustimesecs IS 'Status Time in Seconds';
COMMENT ON COLUMN vwqueueconvrealtime.statusdays IS 'Status Time in Days';
COMMENT ON COLUMN vwqueueconvrealtime.skill1 IS 'Skill 1';
COMMENT ON COLUMN vwqueueconvrealtime.skillname1 IS 'Skill Name 1';
COMMENT ON COLUMN vwqueueconvrealtime.skill2 IS 'Skill 2';
COMMENT ON COLUMN vwqueueconvrealtime.skillname2 IS 'Skill Name 2';
COMMENT ON COLUMN vwqueueconvrealtime.skill3 IS 'Skill 3';
COMMENT ON COLUMN vwqueueconvrealtime.skillname3 IS 'Skill Name 3';
COMMENT ON COLUMN vwqueueconvrealtime.initialpriority IS 'Initial Priority';
COMMENT ON COLUMN vwqueueconvrealtime.participantname IS 'Participant Name';
COMMENT ON COLUMN vwqueueconvrealtime.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwqueueconvrealtime.queuename IS 'Queue Name';
COMMENT ON COLUMN vwqueueconvrealtime.division IS 'Division GUID';
COMMENT ON COLUMN vwqueueconvrealtime.divisionname IS 'Division Name';
COMMENT ON COLUMN vwqueueconvrealtime.userid IS 'User GUID';
COMMENT ON COLUMN vwqueueconvrealtime.name IS 'User Name';
COMMENT ON COLUMN vwqueueconvrealtime.department IS 'Department';
COMMENT ON COLUMN vwqueueconvrealtime.managername IS 'Manager Name';
COMMENT ON COLUMN vwqueueconvrealtime.direction IS 'Direction';
COMMENT ON COLUMN vwqueueconvrealtime.ani IS 'ANI';
COMMENT ON COLUMN vwqueueconvrealtime.dnis IS 'DNIS';
COMMENT ON COLUMN vwqueueconvrealtime.requestedrout1 IS 'Requested Route 1';
COMMENT ON COLUMN vwqueueconvrealtime.requestedrout2 IS 'Requested Route 2';
COMMENT ON COLUMN vwqueueconvrealtime.usedrout IS 'Used Route';

COMMENT ON VIEW vwqueueconvrealtime IS 'See QueueConvRealTime: Queue Conversation Real-Time Data';
