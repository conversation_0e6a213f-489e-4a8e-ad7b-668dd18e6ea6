# PowerShell script to run the VoiceAnalysis tests

param (
    [switch]$Detailed = $false,
    [switch]$Filter = $false,
    [string]$TestName = ""
)

# Set working directory to the script location
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location -Path $scriptPath

# Build the project
Write-Host "Building the project..." -ForegroundColor Cyan
dotnet build

# Run the tests
if ($Detailed) {
    if ($Filter -and $TestName) {
        Write-Host "Running detailed tests with filter: $TestName..." -ForegroundColor Cyan
        dotnet test --logger "console;verbosity=detailed" --filter "FullyQualifiedName~$TestName"
    } else {
        Write-Host "Running all tests with detailed output..." -ForegroundColor Cyan
        dotnet test --logger "console;verbosity=detailed"
    }
} else {
    if ($Filter -and $TestName) {
        Write-Host "Running tests with filter: $TestName..." -ForegroundColor Cyan
        dotnet test --filter "FullyQualifiedName~$TestName"
    } else {
        Write-Host "Running all tests..." -ForegroundColor Cyan
        dotnet test
    }
}

# Display help information
Write-Host "`nUsage:" -ForegroundColor Yellow
Write-Host "  .\run-tests.ps1                  - Run all tests" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Detailed        - Run all tests with detailed output" -ForegroundColor Gray
Write-Host "  .\run-tests.ps1 -Filter -TestName 'VerifyQueue' - Run tests containing 'VerifyQueue' in the name" -ForegroundColor Gray
