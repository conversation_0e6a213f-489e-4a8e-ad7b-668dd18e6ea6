CREATE
OR ALTER VIEW [vwUserInteractionDataPF] AS
SELECT
    id.keyid,
    id.startdate as startdateUTC,
    CONVERT(
        datetime,
        SWITCHOFFSET(
            id.startdate,
            DATEPART(
                TZOFFSET,
                id.startdate AT TIME ZONE 'AUS Eastern Standard Time'
            )
        )
    ) AS startDateLTC,
    CONVERT(
        datetime,
        SWITCHOFFSET(id.startdate, DATEPART(tz, SYSDATETIMEOFFSET()))
    ) AS [startDateAET],
    id.startdate AT TIME ZONE 'AUS Eastern Standard Time' AS [startDateAET1],
    id.userid,
    ud.name as agentname,
    id.direction,
    id.queueid,
    qd.name as queuename,
    id.mediatype,
    id.wrapupcode,
    wd.name as wrapupdesc,
    id.talertcount,
    id.talerttimesum,
    id.talerttimesum / 86400.00 as talerttimesumDay,
    id.talerttimemax,
    id.talerttimemax / 86400.00 as talerttimemaxDay,
    id.talerttimemin,
    id.talerttimemin / 86400.00 as talerttimeminDay,
    id.tansweredcount,
    id.tansweredtimesum,
    id.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    id.tansweredtimemax,
    id.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    id.tansweredtimemin,
    id.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    id.ttalkcount,
    id.ttalktimesum,
    id.ttalktimesum / 86400.00 as ttalktimesumDay,
    id.ttalktimemax,
    id.ttalktimemax / 86400.00 as ttalktimemaxDay,
    id.ttalktimemin,
    id.ttalktimemin / 86400.00 as ttalktimeminDay,
    id.ttalkcompletecount,
    id.ttalkcompletetimesum,
    id.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    id.ttalkcompletetimemax,
    id.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    id.ttalkcompletetimemin,
    id.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    id.tnotrespondingcount,
    id.tnotrespondingtimesum,
    id.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    id.tnotrespondingtimemax,
    id.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    id.tnotrespondingtimemin,
    id.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    id.theldcount,
    id.theldtimesum,
    id.theldtimesum / 86400.00 as theldtimesumDay,
    id.theldtimemax,
    id.theldtimemax / 86400.00 as theldtimemaxDay,
    id.theldtimemin,
    id.theldtimemin / 86400.00 as theldtimeminDay,
    id.theldcompletecount,
    id.theldcompletetimesum,
    id.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    id.theldcompletetimemax,
    id.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    id.theldcompletetimemin,
    id.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    id.thandlecount,
    id.thandletimesum,
    id.thandletimesum / 86400.00 as thandletimesumDay,
    id.thandletimemax,
    id.thandletimemax / 86400.00 as thandletimemaxDay,
    id.thandletimemin,
    id.thandletimemin / 86400.00 as thandletimeminDay,
    id.tacwcount,
    id.tacwtimesum,
    id.tacwtimesum / 86400.00 as tacwtimesumDay,
    id.tacwtimemax,
    id.tacwtimemax / 86400.00 as tacwtimemaxDay,
    id.tacwtimemin,
    id.tacwtimemin / 86400.00 as tacwtimeminDay,
    id.nconsult,
    id.nconsulttransferred,
    id.noutbound,
    id.nerror,
    id.ntransferred,
    id.nblindtransferred,
    id.nconnected,
    id.tdialingcount,
    id.tdialingtimesum,
    id.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    id.tdialingtimemax,
    id.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    id.tdialingtimemin,
    id.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    id.tcontactingcount,
    id.tcontactingtimesum,
    id.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    id.tcontactingtimemax,
    id.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    id.tcontactingtimemin,
    id.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    id.tvoicemailcount,
    id.tvoicemailtimesum,
    id.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    id.tvoicemailtimemax,
    id.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    id.tvoicemailtimemin,
    id.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    id.updated
FROM
    userInteractionData id
    left outer join userDetails ud on ud.id = id.userid
    left outer join queueDetails qd on qd.id = id.queueid
    left outer join wrapupDetails wd on wd.id = id.wrapupcode