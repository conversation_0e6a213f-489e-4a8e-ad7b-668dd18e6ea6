﻿using System.Data;
using GCData;
using System.Net;
using Microsoft.Extensions.Logging;
using DBUtils;
using StandardUtils;

namespace GenesysAdapter
{
    class GCUpdatePresenceData
    {
        private readonly ILogger? _logger;
        private const int DIFFING_THRESHOLD = 100000;
        BackfillUtils BackfillUtils = new BackfillUtils();

        public GCUpdatePresenceData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCUserPresenceData(bool Backfill = false)
        {
            Boolean Successful = false;
            string CurrentJob = "userpresencedata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;
            DateTime Start = DateTime.Now;

            GCGetData GCData = new GCGetData(_logger);
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // Use ToUtcSafe to avoid double UTC conversion since DateToSyncFrom may already be UTC
            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUtcSafe();

string SQLStatement;
TimeSpan timeDifference = DateTime.UtcNow - OldUpdateTime;

    try
    {
        if (timeDifference.TotalDays <= 30)
        {
            // Complex query to fetch only relevant recent data
            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                    SQLStatement = $@"
                        SELECT *
                        FROM userdetails
                        WHERE state != 'deleted'
                        OR (state = 'deleted' AND updated >= CURRENT_DATE - INTERVAL '30 days')";
                    break;

                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    SQLStatement = $@"
                        SELECT *
                        FROM userdetails";
                    break;

                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    SQLStatement = $@"
                        SELECT *
                        FROM userDetails
                        WHERE state != 'deleted'
                        OR (state = 'deleted' AND updated >= DATEADD(day, -30, GETDATE()))";
                    break;

                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }
        else
        {
            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    SQLStatement = $"SELECT * FROM userdetails";
                    break;

                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }
    }
    catch (Exception ex)
    {
        _logger?.LogWarning(ex, "An error occurred while building the SQL query, using default query");
        SQLStatement = "SELECT * FROM userDetails";
    }

            // Fetch the data using the selected query
            DataTable Users = DBAdapter.GetSQLTableData(SQLStatement, "userDetails");

            _logger?.LogDebug("Retrieved {Count} rows from userDetails", Users.Rows.Count);

            int CurrentDataPage = 1;
            int MaxToSend = 100;
            int TotalPages = (Users.Rows.Count / MaxToSend);
            if ((Users.Rows.Count % MaxToSend) != 0)
                TotalPages++;

            Successful = false;

            DataTable UserPresenceData = new DataTable();

            // Log at debug level to reduce information log count
            _logger?.LogDebug("Processing {TotalPages} pages of user presence data with {TotalRows} total rows", TotalPages, Users.Rows.Count);

            while (CurrentDataPage <= TotalPages)
            {
                DataTable UserPresenceDataPage = new DataTable();

                // Log progress at debug level only
                if (_logger?.IsEnabled(LogLevel.Debug) ?? false)
                {
                    _logger.LogDebug("Processing page {CurrentPage} of {TotalPages}", CurrentDataPage, TotalPages);
                }

                DataTable DTUserTemp = Users.Rows.Cast<System.Data.DataRow>().Skip((CurrentDataPage - 1) * MaxToSend).Take(MaxToSend).CopyToDataTable();

                if (DTUserTemp == null || DTUserTemp.Rows.Count == 0)
                {
                    _logger?.LogWarning("No user presence data to process or error encountered");
                    break;
                }
                else
                    UserPresenceDataPage = GCData.UserPresenceData(DTUserTemp);


                if (CurrentDataPage == 1)
                {
                    UserPresenceData = UserPresenceDataPage;
                }
                else
                {
                    foreach (DataRow row in UserPresenceDataPage.Rows)
                    {
                        UserPresenceData.ImportRow(row);
                    }
                }

                //Successful = DBAdapter.WriteSQLData(UserPresenceData, SyncType + "Data");\
                CurrentDataPage++;
            }

            // Log completion at debug level
            if (_logger?.IsEnabled(LogLevel.Debug) ?? false)
            {
                _logger.LogDebug("Completed processing all {TotalPages} pages of user presence data", TotalPages);
            }

            OldUpdateTime = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            _logger?.LogDebug("User presence data: {Count} rows collected", UserPresenceData.Rows.Count);

            DataTable diffedUserPresenceData;

            // Only perform diffing if there are more than 100k rows to process
            // For smaller datasets, the overhead of diffing is not worth the performance cost
            if (UserPresenceData.Rows.Count > DIFFING_THRESHOLD)
            {
                _logger?.LogInformation("UserPresenceData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                    UserPresenceData.Rows.Count, DIFFING_THRESHOLD);

                // Create diffing helper
                DiffingHelper diffHelper = new DiffingHelper(_logger);

                // Diff the data against what's already in the database
                diffedUserPresenceData = diffHelper.DiffData(
                    CurrentJob,
                    UserPresenceData,
                    DBAdapter,
                    30, // Look back 30 days
                    "startdate"
                );
            }
            else
            {
                _logger?.LogInformation("UserPresenceData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                    UserPresenceData.Rows.Count, DIFFING_THRESHOLD);
                diffedUserPresenceData = UserPresenceData;
            }

            // If there are no rows to write, we're done
            if (diffedUserPresenceData.Rows.Count == 0)
            {
                _logger?.LogInformation("No new or updated user presence data to write. Updating last sync date to {Date}.", OldUpdateTime);
                Successful = GCData.UpdateLastSuccessDate(OldUpdateTime, SyncType);
                return Successful;
            }

            // Always write to the regular table, not the backfill table
            Successful = DBAdapter.WriteSQLDataBulk(diffedUserPresenceData, CurrentJob);

            _logger?.LogInformation("Write operation successful: {Success}", Successful);

            if (Successful)
            {
                _logger?.LogInformation("Updating last sync date to {Date}", OldUpdateTime);
                // When in backfill mode, update the backfill table's watermark
                Successful = GCData.UpdateLastSuccessDate(OldUpdateTime, SyncType);
            }
            else
            {
                _logger?.LogWarning("Will not update the last update date - failure in processing");
            }

            _logger?.LogInformation("User presence data processing completed in {ElapsedSeconds:N2} seconds", (DateTime.UtcNow - Start).TotalSeconds);

            return true;
        }

        public Boolean UpdateGCUserPresenceDetailedData(bool Backfill = false)
        {
            Boolean Successful = true;
            string CurrentJob = "userpresencedetaileddata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;

            try
            {
                DateTime Start = DateTime.Now;

                GCGetData GCData = new GCGetData(_logger);
                DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
                DBAdapter.Initialize();

                GCData.Initialize(SyncType);

                if (Backfill)
                {
                    Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                    if (!Successful){
                        return Successful;
                    }
                }

                // Use ToUtcSafe to avoid double UTC conversion since DateToSyncFrom may already be UTC
                DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUtcSafe();


                DataTable UserPresenceData = new DataTable();


                UserPresenceData = GCData.UserPresenceDetailedData();

                _logger?.LogDebug("User presence detailed data: {Count} rows collected", UserPresenceData?.Rows.Count ?? 0);

                if (UserPresenceData != null && UserPresenceData.Rows.Count > 0)
                {
                    DataTable diffedUserPresenceData;

                    // Only perform diffing if there are more than 100k rows to process
                    // For smaller datasets, the overhead of diffing is not worth the performance cost
                    if (UserPresenceData.Rows.Count > DIFFING_THRESHOLD)
                    {
                        _logger?.LogInformation("UserPresenceDetailedData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                            UserPresenceData.Rows.Count, DIFFING_THRESHOLD);

                        // Create diffing helper
                        DiffingHelper diffHelper = new DiffingHelper(_logger);

                        // Diff the data against what's already in the database
                        diffedUserPresenceData = diffHelper.DiffData(
                            CurrentJob,
                            UserPresenceData,
                            DBAdapter,
                            30, // Look back 30 days
                            "starttime"
                        );
                    }
                    else
                    {
                        _logger?.LogInformation("UserPresenceDetailedData has {RowCount} rows (<={Threshold}), skipping diffing optimization",
                            UserPresenceData.Rows.Count, DIFFING_THRESHOLD);
                        diffedUserPresenceData = UserPresenceData;
                    }

                    // If there are no rows to write, we're done
                    if (diffedUserPresenceData.Rows.Count == 0)
                    {
                        _logger?.LogInformation("No new or updated detailed user presence data to write. Updating last sync date to {Date}.", GCData.UserPresenceLastUpdate);
                        Successful = GCData.UpdateLastSuccessDate(GCData.UserPresenceLastUpdate, SyncType);
                        return Successful;
                    }

                    // Always write to the regular table, not the backfill table
                    Successful = DBAdapter.WriteSQLDataBulk(diffedUserPresenceData, CurrentJob);
                }

                _logger?.LogInformation("Write operation successful: {Success}", Successful);

                if (Successful)
                {
                    _logger?.LogInformation("Updating last sync date to {Date}", GCData.UserPresenceLastUpdate);
                    // When in backfill mode, update the backfill table's watermark
                    Successful = GCData.UpdateLastSuccessDate(GCData.UserPresenceLastUpdate, SyncType);
                }

                _logger?.LogInformation("Detailed user presence data processing completed in {ElapsedSeconds:N2} seconds", (DateTime.UtcNow - Start).TotalSeconds);
                Successful = true;

            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Exception caught while processing detailed user presence data");
                // Just rethrow the original exception after logging it
                throw;
            }

            return Successful;
        }

    }
}
