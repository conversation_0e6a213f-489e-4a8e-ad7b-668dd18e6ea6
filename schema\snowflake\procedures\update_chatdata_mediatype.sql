-- Stored procedure to update mediatype column in chatdata table
-- This procedure identifies the media type (chat vs message) for conversations in chatdata
-- by querying only the relevant conversationids from detailedinteractiondata table
-- Performance optimized: only queries detailedinteractiondata for existing chatdata records

CREATE OR REPLACE PROCEDURE update_chatdata_mediatype()
RETURNS TABLE(mediatype VARCHAR(10), record_count NUMBER)
LANGUAGE SQL
AS
$$
DECLARE
    updated_rows NUMBER := 0;
    start_time TIMESTAMP := CURRENT_TIMESTAMP();
    end_time TIMESTAMP;
    duration_seconds NUMBER;
    result_cursor CURSOR FOR 
        SELECT 
            COALESCE(mediatype, 'NULL') as mediatype,
            COUNT(*) as record_count
        FROM chatdata
        GROUP BY mediatype
        ORDER BY mediatype;
BEGIN
    -- Log start of procedure
    CALL SYSTEM$LOG('INFO', 'Starting optimized mediatype update for chatdata table at ' || start_time::STRING);
    CALL SYSTEM$LOG('INFO', 'Only querying detailedinteractiondata for conversations that exist in chatdata with NULL mediatype');
    
    -- Update mediatype for records where it's currently NULL
    -- Only query detailedinteractiondata for conversations that exist in chatdata
    MERGE INTO chatdata c
    USING (
        SELECT
            d.conversationid,
            d.mediatype,
            ROW_NUMBER() OVER (PARTITION BY d.conversationid ORDER BY d.conversationstartdate DESC) as rn
        FROM detailedinteractiondata d
        INNER JOIN chatdata c_filter ON d.conversationid = c_filter.conversationid
        WHERE d.mediatype IN ('chat', 'message')
        AND c_filter.mediatype IS NULL
        QUALIFY rn = 1
    ) d ON c.conversationid = d.conversationid
    WHEN MATCHED AND c.mediatype IS NULL THEN
        UPDATE SET mediatype = CASE
            WHEN d.mediatype = 'chat' THEN 'chat'
            WHEN d.mediatype = 'message' THEN 'message'
            ELSE 'unknown'
        END;
    
    updated_rows := SQLROWCOUNT;
    
    end_time := CURRENT_TIMESTAMP();
    duration_seconds := DATEDIFF(SECOND, start_time, end_time);
    
    -- Log completion
    CALL SYSTEM$LOG('INFO', 'Completed mediatype update. Updated ' || updated_rows::STRING || ' rows in ' || duration_seconds::STRING || ' seconds.');
    
    -- Return summary of current mediatype distribution
    OPEN result_cursor;
    RETURN TABLE(result_cursor);
    
END;
$$;

-- Grant execute permissions
GRANT USAGE ON PROCEDURE update_chatdata_mediatype() TO PUBLIC;
