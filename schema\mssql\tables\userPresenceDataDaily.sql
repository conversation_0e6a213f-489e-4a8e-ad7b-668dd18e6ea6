IF dbo.csg_table_exists('userPresenceDataDaily') = 0
CREATE TABLE [userPresenceDataDaily](
    [keyid] [nvarchar](255) NOT NULL,
    [id] [nvarchar](50),
    [userid] [nvarchar](50),
    [startdate] [datetime],
    [timetype] [nvarchar](50),
    [systempresenceid] [nvarchar](50),
    [presenceid] [nvarchar](50),
    [presencetime] [decimal](20, 2),
    [routingid] [nvarchar](50),
    [routingtime] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_userPresenceDataDaily] PRIMARY KEY ([keyid])
);
IF dbo.csg_column_exists('userPresenceDataDaily', 'timetype') = 0
    ALTER TABLE userPresenceDataDaily ADD timetype [nvarchar](50);

IF dbo.csg_index_exists('userPresenceDataDailyStartDate', 'userPresenceDataDaily') = 0
CREATE INDEX [userPresenceDataDailyStartDate] ON [userPresenceDataDaily] ([startdate]);

IF dbo.csg_index_exists('userPresenceDataDailytimetype', 'userPresenceDataDaily') = 0
CREATE INDEX [userPresenceDataDailytimetype] ON [userPresenceDataDaily] ([timetype]);

IF dbo.csg_index_exists('userPresenceDataDailyuserid', 'userPresenceDataDaily') = 0
CREATE INDEX [userPresenceDataDailyuserid] ON [userPresenceDataDaily] ([userid]);