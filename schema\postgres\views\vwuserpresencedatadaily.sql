DROP VIEW IF EXISTS vwuserpresencedatadaily;

CREATE
OR REPLACE VIEW vwuserpresencedatadaily AS
SELECT
    userpresencedatadaily.id,
    userpresencedatadaily.userid,
    userdetail.name AS agentname,
    userdetail.managerid,
    userdetail.managername,
    userdetail.divisionid,
    dd.name as division_name,
    userpresencedatadaily.startdate,
    userpresencedatadaily.startdate + (
        (
            SELECT
                timezonecalcs.diff
            FROM
                timezonecalcs('Australia/Sydney' :: text) timezonecalcs(utctime, ltctime, diff, timezonechosen)
        )
    ) :: double precision * '00:00:01' :: interval AS startdateusrtz,
    userpresencedatadaily.timetype,
    userpresencedatadaily.systempresenceid,
    CASE
        WHEN userpresencedatadaily.systempresenceid ~ '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$' THEN presencedetails.systempresence
        ELSE userpresencedatadaily.systempresenceid
    END AS systempresencename,
    userpresencedatadaily.presenceid,
    CASE
        userpresencedatadaily.timetype
        WHEN 'Presence' :: text THEN userpresencedatadaily.presencetime
        ELSE 0 :: numeric
    END AS presencetime,
    userpresencedatadaily.presencetime / 86400.00 AS presencetimeday,
    userpresencedatadaily.routingid,
    CASE
        userpresencedatadaily.timetype
        WHEN 'Routing' :: text THEN userpresencedatadaily.presencetime
        ELSE 0 :: numeric
    END AS routingtime,
    userpresencedatadaily.routingtime / 86400.00 AS routingtimeday,
    userpresencedatadaily.updated
FROM
    userpresencedatadaily userpresencedatadaily
    LEFT JOIN vwuserdetail userdetail ON userdetail.id :: text = userpresencedatadaily.userid :: text
    LEFT join divisiondetails dd ON dd.id :: text = userdetail.divisionid :: text
    LEFT JOIN presencedetails ON presencedetails.id = userpresencedatadaily.systempresenceid;

COMMENT ON COLUMN vwuserpresencedatadaily.id IS 'Primary key';
COMMENT ON COLUMN vwuserpresencedatadaily.userid IS 'User GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.agentname IS 'Agent Name';
COMMENT ON COLUMN vwuserpresencedatadaily.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.managername IS 'Manager Name';
COMMENT ON COLUMN vwuserpresencedatadaily.divisionid IS 'Division GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.division_name IS 'Division Name';
COMMENT ON COLUMN vwuserpresencedatadaily.startdate IS 'Start Date';
COMMENT ON COLUMN vwuserpresencedatadaily.startdateusrtz IS 'Start Date in User Timezone';
COMMENT ON COLUMN vwuserpresencedatadaily.timetype IS 'Time Type';
COMMENT ON COLUMN vwuserpresencedatadaily.systempresenceid IS 'System Presence GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.systempresencename IS 'System Presence Name';
COMMENT ON COLUMN vwuserpresencedatadaily.presenceid IS 'Presence GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.presencetime IS 'Presence Time';
COMMENT ON COLUMN vwuserpresencedatadaily.presencetimeday IS 'Presence Time (Days)';
COMMENT ON COLUMN vwuserpresencedatadaily.routingid IS 'Routing GUID';
COMMENT ON COLUMN vwuserpresencedatadaily.routingtime IS 'Routing Time';
COMMENT ON COLUMN vwuserpresencedatadaily.routingtimeday IS 'Routing Time (Days)';
COMMENT ON COLUMN vwuserpresencedatadaily.updated IS 'Last Updated Time';

COMMENT ON VIEW vwuserpresencedatadaily IS 'See UserPresenceDataDaily - Expands all the GUIDs with their lookups(Daily)';
