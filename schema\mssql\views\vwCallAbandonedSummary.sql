CREATE
OR ALTER VIEW [vwCallAbandonedSummary] AS
SELECT
    det.conversationid,
    det.conversationstartdate,
    det.conversationstartdateLTC,
    det.conversationenddate,
    det.conversationenddateLTC,
    det.segmentstartdate,
    det.segmentstartdateLTC,
    det.segmentenddate,
    det.segmentenddateLTC,
    det.convtosegmentendtime AS TotalCallTime,
    det.segmenttime AS QueueTime,
    (det.convtosegmentendtime / 86400.00) AS TotalCallTimeDay,
    (det.segmenttime / 86400.00) AS QueueTimeDay,
    det.ani,
    det.dnis,
    det.queueid,
    que.name AS queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    [detailedInteractionData] det
    LEFT OUTER JOIN queueDetails que ON det.queueid = que.id
WHERE
    det.segmenttype IN ('delay', 'Interact', 'alert')
    AND det.purpose = 'acd'
    AND det.disconnectiontype = 'peer'
    AND det.conversationenddate = segmentenddate