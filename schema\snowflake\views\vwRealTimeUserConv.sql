CREATE OR REPLACE VIEW vwRealTimeUserConv AS
SELECT
    conversationid,
    media,
    queueid,
    direction,
    conversationstate,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN 'DIAL'
        WHEN ud.acwstate = TRUE THEN 'ACW'
        WHEN ud.heldstate = TRUE THEN 'HELD'
        WHEN ud.conversationstate = 'alerting' THEN 'ALERT'
        WHEN ud.conversationstate = 'contacting' THEN 'CONTACT'
        ELSE 'TALKING'
    END AS convstatus,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN DATEDIFF(
            SECOND,
            TO_TIMESTAMP_NTZ(ud.updated),
            CURRENT_TIMESTAMP()
        )
        WHEN ud.acwstate = TRUE THEN DATEDIFF(
            SECOND,
            TO_TIMESTAMP_NTZ(ud.acwtime),
            CURRENT_TIMESTAMP()
        )
        WHEN ud.heldstate = TRUE THEN DATEDIFF(
            SECOND,
            TO_TIMESTAMP_NTZ(ud.heldtime),
            CURRENT_TIMESTAMP()
        )
        WHEN ud.conversationstate = 'alerting' THEN DATEDIFF(
            SECOND,
            TO_TIMESTAMP_NTZ(ud.updated),
            CURRENT_TIMESTAMP()
        )
        ELSE DATEDIFF(
            SECOND,
            TO_TIMESTAMP_NTZ(ud.talktime),
            CURRENT_TIMESTAMP()
        )
    END AS convstatustime,
    CASE
        WHEN ud.conversationstate = 'dialing' THEN (
            DATEDIFF(
                SECOND,
                TO_TIMESTAMP_NTZ(ud.updated),
                CURRENT_TIMESTAMP()
            ) / 86400.00
        )
        WHEN ud.acwstate = TRUE THEN (
            DATEDIFF(
                SECOND,
                TO_TIMESTAMP_NTZ(ud.acwtime),
                CURRENT_TIMESTAMP()
            ) / 86400.00
        )
        WHEN ud.heldstate = TRUE THEN (
            DATEDIFF(
                SECOND,
                TO_TIMESTAMP_NTZ(ud.heldtime),
                CURRENT_TIMESTAMP()
            ) / 86400.00
        )
        WHEN ud.conversationstate = 'alerting' THEN (
            DATEDIFF(
                SECOND,
                TO_TIMESTAMP_NTZ(ud.updated),
                CURRENT_TIMESTAMP()
            ) / 86400.00
        )
        ELSE (
            DATEDIFF(
                SECOND,
                TO_TIMESTAMP_NTZ(ud.talktime),
                CURRENT_TIMESTAMP()
            ) / 86400.00
        )
    END AS convstatustimeDay,
    acwstate,
    acwtime,
    heldstate,
    heldtime,
    talktime
FROM
    userRealTimeConvData AS ud;
