CREATE TABLE IF NOT EXISTS userrealtimeconvdata (
    keyid varchar(100) NOT NULL,
    id varchar(50),
    conversationid varchar(50),
    media varchar(50),
    direction varchar(50),
    conversationstate varchar(50),
    acwstate boolean,
    acwstring varchar(50),
    acwtime timestamp without time zone,
    heldstate boolean,
    heldtime timestamp without time zone,
    talktime timestamp without time zone,
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    actingas varchar(50),
    queueid varchar(50),
    skill1 varchar(50),
    skill2 varchar(50),
    skill3 varchar(50),
    intialpriority integer,
    updated timestamp without time zone,
    initialpriority integer,
    userid varchar(50),
    usedrout varchar(50),
    requestedrout1 varchar(50),
    requestedrout2 varchar(50),
    ani varchar(400),
    dnis varchar(400),
    participantname varchar(200),
    segmenttime timestamp without time zone,
    talktimeltc timestamp without time zone,
    state varchar(50),
    CONSTRAINT userrealtimeconvdata_key PRIMARY KEY (keyid)
) TABLESPACE pg_default;

ALTER TABLE userrealtimeconvdata 
ADD column IF NOT exists startdate timestamp without time zone;

ALTER TABLE userrealtimeconvdata 
ADD column IF NOT exists startdateltc timestamp without time zone;

ALTER TABLE userrealtimeconvdata 
ADD column IF NOT exists state varchar(50);

COMMENT ON COLUMN userRealTimeConvData.actingas IS 'Conversation Acting As (ACD as Queue, Customer or Agent)';
COMMENT ON COLUMN userRealTimeConvData.acwstate IS 'Conversation ACW State'; 
COMMENT ON COLUMN userRealTimeConvData.acwstring IS 'Conversation ACW GUID'; 
COMMENT ON COLUMN userRealTimeConvData.acwtime IS 'Conversation ACW Start Time (UTC)'; 
COMMENT ON COLUMN userRealTimeConvData.ani IS 'Conversation  ANI'; 
COMMENT ON COLUMN userRealTimeConvData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN userRealTimeConvData.conversationstate IS 'Conversation State'; 
COMMENT ON COLUMN userRealTimeConvData.direction IS 'Conversation  Direction'; 
COMMENT ON COLUMN userRealTimeConvData.dnis IS 'Conversation  DNIS'; 
COMMENT ON COLUMN userRealTimeConvData.heldstate IS 'Conversation  Held State'; 
COMMENT ON COLUMN userRealTimeConvData.heldtime IS 'Conversation Held Start Time (UTC)'; 
COMMENT ON COLUMN userRealTimeConvData.initialpriority IS 'Conversation Initial Priority'; 
COMMENT ON COLUMN userRealTimeConvData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN userRealTimeConvData.media IS 'Conversation Media Type'; 
COMMENT ON COLUMN userRealTimeConvData.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN userRealTimeConvData.requestedrout1 IS 'Conversation  Requested Route 1'; 
COMMENT ON COLUMN userRealTimeConvData.requestedrout2 IS 'Conversation Requested Route 2'; 
COMMENT ON COLUMN userRealTimeConvData.segmenttime IS 'Conversation Current Segment Start Time (UTC)'; 
COMMENT ON COLUMN userRealTimeConvData.skill1 IS 'Conversation Skill 1'; 
COMMENT ON COLUMN userRealTimeConvData.skill2 IS 'Conversation Skill 2'; 
COMMENT ON COLUMN userRealTimeConvData.skill3 IS 'Conversation Skill 3'; 
COMMENT ON COLUMN userRealTimeConvData.talktime IS 'Conversation Talk Time Start (UTC)'; 
COMMENT ON COLUMN userRealTimeConvData.talktimeltc IS 'Conversation Talk Time Start (LTC)'; 
COMMENT ON COLUMN userRealTimeConvData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN userRealTimeConvData.usedrout IS 'Conversation Used Route'; 
COMMENT ON COLUMN userRealTimeConvData.userid IS 'Agent GUID'; 
COMMENT ON TABLE userRealTimeConvData IS 'User Real Time Conversation Data'; 