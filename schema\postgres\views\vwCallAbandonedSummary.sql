CREATE
OR REPLACE VIEW vwCallAbandonedSummary AS
SELECT
    det.conversationid,
    det.conversationstartdate,
    det.conversationstartdateLTC,
    det.conversationenddate,
    det.conversationenddateLTC,
    det.segmentstartdate,
    det.segmentstartdateLTC,
    det.segmentenddate,
    det.segmentenddateLTC,
    det.convtosegmentendtime AS TotalCallTime,
    det.segmenttime AS QueueTime,
    (det.convtosegmentendtime / 86400.00) AS TotalCallTimeDay,
    (det.segmenttime / 86400.00) AS QueueTimeDay,
    det.ani,
    det.dnis,
    det.queueid,
    que.name AS queueName,
    det.purpose,
    det.segmenttype,
    det.disconnectiontype
FROM
    detailedInteractionData det
    LEFT OUTER JOIN queueDetails que ON det.queueid = que.id
WHERE
    det.segmenttype IN ('delay', 'Interact', 'alert')
    AND det.purpose = 'acd'
    AND det.disconnectiontype = 'peer'
    AND det.conversationenddate = segmentenddate;

COMMENT ON COLUMN vwCallAbandonedSummary.ani IS 'ANI'; 
COMMENT ON COLUMN vwCallAbandonedSummary.conversationenddate IS 'Conversation End Date(UTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.conversationenddateLTC IS 'Conversation End Date(LTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.conversationid IS 'Conversation ID'; 
COMMENT ON COLUMN vwCallAbandonedSummary.conversationstartdate IS 'Conversation Start Date(UTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.conversationstartdateLTC IS 'Conversation Start Date(LTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.disconnectiontype IS 'Disconnection Type'; 
COMMENT ON COLUMN vwCallAbandonedSummary.dnis IS 'DNIS'; 
COMMENT ON COLUMN vwCallAbandonedSummary.purpose IS 'Purpose'; 
COMMENT ON COLUMN vwCallAbandonedSummary.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN vwCallAbandonedSummary.queueName IS 'Queue Name'; 
COMMENT ON COLUMN vwCallAbandonedSummary.QueueTime IS 'Queue Time'; 
COMMENT ON COLUMN vwCallAbandonedSummary.QueueTimeDay IS 'Queue Time per Day'; 
COMMENT ON COLUMN vwCallAbandonedSummary.segmentenddate IS 'Segment End Date(UTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.segmentenddateLTC IS 'Segment End Date(LTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.segmentstartdate IS 'Segment Start Date(UTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.segmentstartdateLTC IS 'Segment Start Date(LTC)'; 
COMMENT ON COLUMN vwCallAbandonedSummary.segmenttype IS 'Segment Type'; 
COMMENT ON COLUMN vwCallAbandonedSummary.TotalCallTime IS 'Total Call Time'; 
COMMENT ON COLUMN vwCallAbandonedSummary.TotalCallTimeDay IS 'Total Call Time per Day';  

COMMENT ON VIEW vwCallAbandonedSummary IS 'Shows the details for abandoned calls in the detailed interaction data -- Expands all the GUIDs with their lookups';