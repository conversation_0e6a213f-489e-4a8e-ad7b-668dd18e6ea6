CREATE TABLE IF NOT EXISTS userrealtimeconvdata (
    keyid varchar(100) NOT NULL,
    id varchar(50),
    conversationid varchar(50),
    media varchar(50),
    direction varchar(50),
    conversationstate varchar(50),
    acwstate boolean,
    acwstring varchar(50),
    acwtime timestamp without time zone,
    heldstate boolean,
    heldtime timestamp without time zone,
    talktime timestamp without time zone,
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    actingas varchar(50),
    queueid varchar(50),
    skill1 varchar(50),
    skill2 varchar(50),
    skill3 varchar(50),
    intialpriority integer,
    updated timestamp without time zone,
    initialpriority integer,
    userid varchar(50),
    usedrout varchar(50),
    requestedrout1 varchar(50),
    requestedrout2 varchar(50),
    ani varchar(400),
    dnis varchar(400),
    participantname varchar(200),
    state varchar(50),
    segmenttime timestamp without time zone,
    talktimeltc timestamp without time zone,
    CONSTRAINT userrealtimeconvdata_key PRIMARY KEY (keyid)
);

ALTER TABLE userrealtimeconvdata
ADD column IF NOT exists startdate timestamp without time zone;

ALTER TABLE userrealtimeconvdata
ADD column IF NOT exists startdateltc timestamp without time zone;

ALTER TABLE userrealtimeconvdata
ADD column IF NOT exists state varchar(50);