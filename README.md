# Genesys Cloud Data Adapter

Synchronises Genesys metrics (both real-time and historical) to a database
allowing use by business analytics applications.

[[_TOC_]]

## Documentation

- [General Documentation](documentation/README.md)
- [Database Documentation](documentation/database/README.md)
  - [Database Functions](documentation/database/schema/DatabaseFunctionDocumentation.md)
  - [Schema Templates](documentation/database/schema/SchemaTemplates.md)
  - [Schema Review Checklist](documentation/database/schema/SchemaReviewChecklist.md)
  - [Schema Function Analysis Report](documentation/database/schema/SchemaFunctionAnalysisReport.md)
  - [Schema Function Analyzer](documentation/database/schema/SchemaFunctionAnalyzer.ps1)

## Build and Test

| Dev | Prod |
|-----|------|
| [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=dev)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=dev) | [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=master)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=master) |

### Development Practises

* [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/) branching model
* [C# Coding Style](https://github.com/dotnet/runtime/blob/main/docs/coding-guidelines/coding-style.md)
* [Clean Code concepts](https://github.com/thangchung/clean-code-dotnet)
* Commit messages follow [Conventional Commits](https://www.conventionalcommits.org/)

### Build

[Nuke](https://nuke.build) is used to generate the Azure build pipeline and also allows cross platform local execution
of the pipeline.

```powershell
./build.ps1 --help
./build.ps1
```

```bash
./build.sh --help
./build.sh
```

Install the nuke tool to allow execution using the `nuke` command.

```sh
dotnet nuke --help
```

Local building can also be done with the normal dotnet tools.

```sh
dotnet build
dotnet test
dotnet run
dotnet publish -c Release -r linux-musl-x64 -p:PublishSingleFile=True --self-contained --no-restore GenesysAdapter/GenesysAdapter.csproj
```

### dotnet local tools

Install the default dotnet local tools by running the command below. This will install nuke, dotnet-outdated, etc.

```sh
dotnet tool restore
```

### Podman

The pipeline will build a Docker image, it is recommended to use [Podman](https://podman.io/) for local testing.
Nuke does a locate on docker, so setting an alias to podman is not enough, creating a symlink is required to use Podman
in the Nuke pipeline.

* DockerHub doesn't support OCI images so need to set BUILDAH_FORMAT, [DockerHub #1871](https://github.com/docker/hub-feedback/issues/1871)

#### Setting alternate / symbolic link

```bash
sudo update-alternatives --install /usr/local/bin/docker docker /usr/bin/podman 20
```

```powershell
New-Item -ItemType SymbolicLink -Path:(Split-Path (Get-Command -Name podman.exe).Source) -Name:"docker.exe" -Target (Get-Command -Name podman.exe).Source
```

### Updating package dependencies

* [dotnet-outdated](https://github.com/dotnet-outdated/dotnet-outdated)

Check for updates:

```powershell
dotnet dotnet-outdated [--upgrade]
```

## Database Schema Management

The Genesys Adapter supports multiple database types (MSSQL, PostgreSQL, and Snowflake), each with its own set of schema files and utility functions.

### Database Functions

The adapter uses a set of utility functions to manage database objects consistently across different database types:

- `csg_table_exists`: Checks if a table exists
- `csg_column_exists`: Checks if a column exists in a table
- `csg_index_exists`: Checks if an index exists on a table
- `csg_constraint_exists`: Checks if a constraint exists on a table
- And many more...

These functions are defined in the `installfunctions.sql` file for each database type. See the [Database Function Documentation](documentation/database/schema/DatabaseFunctionDocumentation.md) for details.

### Schema Templates

To ensure consistency when creating new schema files, use the templates provided in the [Schema Templates](documentation/database/schema/SchemaTemplates.md) document. These templates include examples for creating tables, adding columns, creating indexes, and adding foreign keys for each database type.

### Best Practices

When working with the database schema, follow these best practices:

1. **Use existence checks**: Always check if objects exist before creating or modifying them.
2. **Consistent naming**: Use consistent naming conventions for tables, columns, indexes, and constraints.
3. **Documentation**: Include comments explaining the purpose of tables, columns, and complex logic.
4. **Error handling**: Implement proper error handling in procedures and functions.
5. **Database-specific features**: Leverage the specific features of each database type.
6. **Cross-database consistency**: Ensure that table and column definitions are consistent across database types.

See the [Schema Review Checklist](documentation/database/schema/SchemaReviewChecklist.md) for a comprehensive list of best practices.

## Synchronisation Jobs

This section provides details about the various synchronization jobs available in the Genesys Adapter.

### Agent Performance Jobs

#### Adherence
- **Description**: Synchronizes agent adherence to published schedules
- **Tables**:
  - adherencedaydata
  - adherenceexcdata
  - adherenceactdata
- **API**: `/api/v2/workforcemanagement/adherence/historical/bulk`

#### Evaluation
- **Description**: Retrieves evaluation data with conversation ID, evaluation ID, status, scores, and related data
- **Tables**:
  - evaldata
  - evalquestiondata
  - evalquestiongroupdata
- **APIs**:
  - `/api/v2/analytics/evaluations/aggregates/query`
  - `/api/v2/quality/evaluations`
- **Note**: Materialized views for evaluation data are updated by database procedures scheduled via cron jobs:
  - `update_mvwevaluationgroupdata`: Updates the evaluation group materialized view
  - Cron jobs also refresh `mvwevaluationoverview` and `mvwevaluationquestiondata` materialized views

#### EvaluationCatchup
- **Description**: Updates existing pending evaluations that may have changed since the last evaluation job run
- **Tables**: evaldata
- **API**: `/api/v2/quality/evaluations`

#### Shrinkage
- **Description**: Retrieves agent shrinkage data including scheduled and actual shrinkage metrics
- **Tables**: shrinkagedata
- **APIs**:
  - `/api/v2/workforcemanagement/managementunits`
  - `/api/v2/workforcemanagement/managementunits/{managementUnitId}/shrinkage/jobs`

#### Survey
- **Description**: Retrieves customer survey data including scores and completion status
- **Tables**: surveydata
- **API**: `/api/v2/analytics/surveys/aggregates/query`

### Interaction Data Jobs

#### Aggregation
- **Description**: Aggregates user presence and interaction data
- **Tables**:
  - userpresencedata
  - userinteractiondata
  - queueinteractiondata
- **APIs**:
  - `/api/v2/analytics/users/aggregates/query`
  - `/api/v2/analytics/conversations/aggregates/query`
- **Note**: Daily, weekly, and monthly aggregation tables (userpresencedatadaily, userinteractiondataweekly, etc.) are generated by database procedures scheduled via cron jobs. These procedures include:
  - `archiveuserpresence`: Aggregates presence data into daily/weekly/monthly tables
  - `archiveuserinteraction`: Aggregates user interaction data into daily/weekly/monthly tables
  - `archivequeueinteraction`: Aggregates queue interaction data into daily/weekly/monthly tables
  - `archivebacklog`: Runs all archive procedures for multiple time periods

#### Chat
- **Description**: Retrieves and analyzes chat conversation data, storing metrics like chat counts, durations, and response times
- **Tables**: chatdata
- **API**: `/api/v2/analytics/conversations/details/query`

#### Interaction
- **Description**: Retrieves detailed interaction data including conversation summaries, participant details, and custom attributes
- **Tables**:
  - convsummarydata
  - detailedinteractiondata
  - participantattributesdynamic
  - participantsummarydata
- **APIs**:
  - `/api/v2/analytics/conversations/details/query`
  - `/api/v2/analytics/conversations/details/jobs`

#### InteractionPresence
- **Description**: Combines interaction and presence data for detailed agent activity analysis
- **Tables**: userinteractionpresencedetaileddata
- **APIs**:
  - `/api/v2/analytics/users/details/query`
  - `/api/v2/analytics/conversations/details/query`

#### PresenceDetail
- **Description**: Retrieves detailed user presence data including system presence, routing status, and durations
- **Tables**: userpresencedetaileddata
- **API**: `/api/v2/analytics/users/details/query`

#### VoiceAnalysis
- **Description**: Retrieves voice analytics data including sentiment analysis, topics, and transcripts
- **Tables**:
  - convvoiceoverviewdata
  - convvoicesentimentdetaildata
  - convvoicetopicdetaildata
- **APIs**:
  - `/api/v2/analytics/conversations/details/query`
  - `/api/v2/speechandtextanalytics/conversations/{conversationId}`
- **Note**: Materialized views for voice analytics data are updated by database procedures scheduled via cron jobs:
  - `update_mvwconvvoiceoverviewdata`: Updates the voice overview materialized view
  - `update_mvwconvvoicesentimentdetaildata`: Updates the voice sentiment materialized view
  - `update_mvwconvvoicetopicdetaildata`: Updates the voice topic materialized view

### Workforce Management Jobs

#### HeadCountForecast
- **Description**: Retrieves predicted headcount requirements from schedules in 15-minute intervals
- **Tables**: headcountforecastdata
- **API**: `/api/v2/workforcemanagement/schedules`

#### HoursBlockData
- **Description**: Creates timesheet data with blocks of hours, breaking out time spent on breaks and meetings
- **Tables**: hoursblockdata
- **Uses data from**: scheduledetails

#### ScheduleDetails
- **Description**: Retrieves detailed schedule information for agents including shifts, activities, and time allocations
- **Tables**:
  - scheduledata
  - scheduledetails
- **API**: `/api/v2/workforcemanagement/schedules`

#### TimeOffReq
- **Description**: Retrieves time-off requests for agents
- **Tables**: timeoffrequestdata
- **API**: `/api/v2/workforcemanagement/timeoffrequests`

#### WFMAudit
- **Description**: Retrieves workforce management audit data for tracking changes to schedules and configurations
- **Tables**: wfmauditdata
- **API**: `/api/v2/workforcemanagement/audits`

#### WFMSchedule
- **Description**: Retrieves workforce management schedule data for long-term planning and analysis
- **Tables**: wfmscheduledata
- **API**: `/api/v2/workforcemanagement/schedules`

### Queue Management Jobs

#### OfferedForecast
- **Description**: Retrieves queue estimated wait times and forecasted metrics
- **Tables**: offeredforecastdata
- **API**: `/api/v2/routing/queues/{queueId}/estimatedwaittime`

#### QueueMembership
- **Description**: Retrieves active queue membership data showing which agents are assigned to which queues
- **Tables**: activeqmembersdata
- **API**: `/api/v2/routing/queues/{queueId}/members`

#### UserQueueAudit
- **Description**: Tracks changes to queue configurations and membership over time
- **Tables**: queueauditdata
- **APIs**:
  - `/api/v2/routing/queues`
  - `/api/v2/routing/queues/{queueId}/members`

#### UserQueueMapping
- **Description**: Maps users to queues they are assigned to
- **Tables**: userqueuemappings
- **APIs**:
  - `/api/v2/routing/queues/{queueId}/members`
  - `/api/v2/users/{userId}/queues`

### Outbound Dialing Jobs

#### ODContactLists
- **Description**: Retrieves outbound dialing contact list data, downloads a CSV and dynamically adds columns
- **Tables**: odcontactlistdata
- **API**: `/api/v2/outbound/contactlists/{contactListId}/export`

#### ODDetails
- **Description**: Retrieves outbound dialing configuration data including contact lists and campaign details
- **Tables**:
  - odcontactlistdetails
  - odcampaigndetails
- **APIs**:
  - `/api/v2/outbound/contactlists`
  - `/api/v2/outbound/campaigns`

### Real-time Data Jobs

#### Realtime
- **Description**: Retrieves real-time metrics for queues and users via WebSocket connections, updating data continuously
- **Tables**:
  - queuerealtimedata
  - queuerealtimeconvdata
  - userrealtimedata
  - userrealtimeconvdata
- **APIs**:
  - WebSocket notifications
  - `/api/v2/notifications/channels`
  - `/api/v2/analytics/conversations/details`

#### Subscription
- **Description**: Manages notification subscriptions for real-time data collection
- **Tables**: subscriptiondata
- **APIs**:
  - `/api/v2/notifications/availabletopics`
  - `/api/v2/notifications/channels`

#### SubsUsers
- **Description**: Retrieves user subscription data for monitoring user activity and license usage
- **Tables**: subuserdata
- **API**: `/api/v2/users`

### System Management Jobs

#### Information
- **Description**: Displays information about the Genesys Adapter configuration and available jobs
- **Tables**: None (informational only)
- **API**: None (uses local configuration)

#### Install
- **Description**: Installs or updates the database schema and initializes the system
- **Tables**: All schema tables
- **API**: Various endpoints for permissions setup

### Reference Data Jobs

#### FactData
- **Description**: Synchronizes reference data like activity codes, business units, divisions, groups, etc.
- **Tables**: Various lookup tables (activitycodedetails, budetails, divisiondetails, etc.)
- **APIs**: Multiple endpoints for reference data

#### KnowledgeBaseDetails
- **Description**: Retrieves knowledge base configuration data including categories and documents
- **Tables**:
  - knowledgebase
  - knowledgebasecategorydata
- **API**: `/api/v2/knowledge/knowledgebases`

#### LearningDataDetails
- **Description**: Retrieves learning module configuration data including assignments
- **Tables**:
  - learningmodules
  - learningmoduleassignments
- **API**: `/api/v2/learning/modules`

#### OAuthUsage
- **Description**: Retrieves OAuth usage data for monitoring API consumption and client application activity
- **Tables**: oauthusagedata
- **API**: `/api/v2/oauth/clients`

#### SysConvUsage
- **Description**: Retrieves system conversation usage data for monitoring system activity
- **Tables**: systemcallusage
- **API**: `/api/v2/usage/query`

#### TeamsDetails
- **Description**: Retrieves team configuration data including team details and team membership
- **Tables**:
  - teamdetails
  - teammemberdata
- **APIs**:
  - `/api/v2/groups`
  - `/api/v2/groups/{groupId}/members`

### Knowledge and Learning Jobs

#### Knowledge
- **Description**: Retrieves knowledge base document data including content and metadata
- **Tables**: knowledgebasedocument
- **API**: `/api/v2/knowledge/knowledgebases/{knowledgeBaseId}/documents`

#### Learning
- **Description**: Retrieves learning assignment results data including completion status and scores
- **Tables**: learningassignmentresults
- **API**: `/api/v2/learning/assignments`
