CREATE OR R<PERSON>LACE TABLE dimension_date (
  date_dim_id              INT NOT NULL PRIMARY KEY,
  date_actual              DATE NOT NULL,
  epoch                    BIGINT NOT NULL,
  day_suffix               VARCHAR(4) NOT NULL,
  day_name                 VARCHAR(9) NOT NULL,
  day_of_week              INT NOT NULL,
  day_of_month             INT NOT NULL,
  day_of_quarter           INT NOT NULL,
  day_of_year              INT NOT NULL,
  week_of_month            INT NOT NULL,
  week_of_year             INT NOT NULL,
  week_of_year_iso         CHAR(10) NOT NULL,
  month_actual             INT NOT NULL,
  month_name               VARCHAR(9) NOT NULL,
  month_name_abbreviated   CHAR(3) NOT NULL,
  quarter_actual           INT NOT NULL,
  quarter_name             VARCHAR(9) NOT NULL,
  year_actual              INT NOT NULL,
  first_day_of_week        DATE NOT NULL,
  last_day_of_week         DATE NOT NULL,
  first_day_of_month       DATE NOT NULL,
  last_day_of_month        DATE NOT NULL,
  first_day_of_quarter     DATE NOT NULL,
  last_day_of_quarter      DATE NOT NULL,
  first_day_of_year        DATE NOT NULL,
  last_day_of_year         DATE NOT NULL,
  mmyyyy                   CHAR(6) NOT NULL,
  mmddyyyy                 CHAR(10) NOT NULL,
  weekend_indr             BOOLEAN NOT NULL
);

INSERT INTO dimension_date
SELECT TO_NUMBER(TO_CHAR(datum,'yyyymmdd')) AS date_dim_id,
       datum AS date_actual,
       DATEDIFF(second, '1970-01-01', datum)::BIGINT AS epoch,
       TO_CHAR(datum,'Dth') AS day_suffix,
       TO_CHAR(datum,'Day') AS day_name,
       DAYOFWEEK(datum) AS day_of_week,
       EXTRACT(DAY FROM datum) AS day_of_month,
       EXTRACT(DAY FROM DATE_TRUNC('quarter',datum)::DATE) - EXTRACT(DAY FROM DATE_TRUNC('quarter',datum)::DATE) + 1 AS day_of_quarter,
       EXTRACT(DOY FROM datum) AS day_of_year,
       WEEK(datum) AS week_of_month,
       WEEKOFYEAR(datum) AS week_of_year,
       TO_CHAR(datum,'IYYY"-W"IW-D') AS week_of_year_iso,
       EXTRACT(MONTH FROM datum) AS month_actual,
       TO_CHAR(datum,'Month') AS month_name,
       TO_CHAR(datum,'Mon') AS month_name_abbreviated,
       QUARTER(datum) AS quarter_actual,
       CASE
         WHEN QUARTER(datum) = 1 THEN 'First'
         WHEN QUARTER(datum) = 2 THEN 'Second'
         WHEN QUARTER(datum) = 3 THEN 'Third'
         WHEN QUARTER(datum) = 4 THEN 'Fourth'
       END AS quarter_name,
       EXTRACT(YEAR FROM datum) AS year_actual,
       DATE_TRUNC('week', datum)::DATE AS first_day_of_week,
       DATEADD(day, 6, DATE_TRUNC('week', datum))::DATE AS last_day_of_week,
       DATE_TRUNC('month', datum) AS first_day_of_month,
       LAST_DAY(datum) AS last_day_of_month,
       DATEADD(month, (EXTRACT(QUARTER FROM datum) - 1) * 3, DATE_TRUNC('year', datum)) AS first_day_of_quarter,
       DATEADD(day, -1, DATEADD(month, EXTRACT(QUARTER FROM datum) * 3, DATE_TRUNC('year', datum))) AS last_day_of_quarter,
       DATE_TRUNC('year', datum) AS first_day_of_year,
       LAST_DAY(DATE_TRUNC('year', datum)) AS last_day_of_year,
       TO_CHAR(datum,'mmyyyy') AS mmyyyy,
       TO_CHAR(datum,'mmddyyyy') AS mmddyyyy,
       CASE
         WHEN DAYOFWEEK(datum) IN (1,7) THEN FALSE
         ELSE TRUE
       END AS weekend_indr
FROM (SELECT DATEADD(day, ROW_NUMBER() OVER(ORDER BY 1)-1, '2018-01-01') AS datum
      FROM TABLE(GENERATOR(ROWCOUNT=>29220))) DQ
ORDER BY 1;
