CREATE
OR ALTER VIEW [z_participant_transfers] as
SELECT
    *,
    LEAD(participantname) OVER (
        partition by conversationid,
        purpose
        order by
            segmentstartdate,
            gencode
    ) as nextParticipant,
    LEAD(agentname) over (
        partition by conversationid
        order by
            segmentstartdate,
            gencode
    ) as transferredBy
FROM
    vwDetailedInteractionData
WHERE
    purpose IN ('acd', 'agent')
    AND disconnectiontype = 'transfer'