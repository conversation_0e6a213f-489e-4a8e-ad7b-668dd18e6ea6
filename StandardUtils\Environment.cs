﻿using System.Reflection;

namespace StandardUtils
{
    public static class ApplicationVersion

    {
        public static Version? MajorMinorPatch
        {
            get { return Assembly.GetExecutingAssembly().GetName().Version; }
        }

        public static string? InformationalVersion
        {
            get
            {
                return Assembly
                    .GetEntryAssembly()?
                    .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?
                    .InformationalVersion;
            }
        }
    }

    public static class ApplicationEnvironment
    {
        public static string OSDescription
        {
            get { return System.Runtime.InteropServices.RuntimeInformation.OSDescription; }
        }

        public static string RuntimeIdentifier
        {
            get { return System.Runtime.InteropServices.RuntimeInformation.RuntimeIdentifier; }
        }

        public static string FrameworkDescription
        {
            get { return System.Runtime.InteropServices.RuntimeInformation.FrameworkDescription; }
        }
    }
}
