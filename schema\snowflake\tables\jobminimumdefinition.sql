-- Step 1: Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS jobminimumdefinition (
    JobID INTEGER AUTOINCREMENT PRIMARY KEY,
    JobName VARCHAR(255),
    MaxSyncSpan VARCHAR(50),
    LookBackSpan VARCHAR(50)
);

MERGE INTO jobminimumdefinition AS target USING (
    SELECT
        *
    FROM
    VALUES
        ('Adherence', '7.00:00', '14.00:00'),
        ('Aggregation', '1.00:00', '0.08:00'),
        ('Chat', '1.00:00', '0.08:00'),
        ('Evaluation', '7.00:00', '90.00:00'),
        ('EvaluationCatchup', '1.00:00', '0.08:00'),
        ('FactData', '1.00:00', '0.08:00'),
        ('HeadCountForecast', '1.00:00', '0.08:00'),
        ('HoursBlockData', '1.00:00', '0.08:00'),
        ('Information', '1.00:00', '0.08:00'),
        ('Install', '1.00:00', '0.08:00'),
        ('Interaction', '1.00:00', '0.08:00'),
        ('InteractionPresence', '1.00:00', '0.08:00'),
        ('Knowledge', '1.00:00', '0.08:00'),
        ('OAuthUsage', '1.00:00', '0.08:00'),
        ('ODContactLists', '1.00:00', '0.08:00'),
        ('ODDetails', '1.00:00', '0.08:00'),
        ('OfferedForecast', '1.00:00', '0.08:00'),
        ('PresenceDetail', '1.00:00', '0.08:00'),
        ('QueueMembership', '1.00:00', '0.08:00'),
        ('Realtime', '1.00:00', '0.08:00'),
        ('ScheduleDetails', '1.00:00', '0.08:00'),
        ('Shrinkage', '1.00:00', '0.08:00'),
        ('Subscription', '1.00:00', '0.08:00'),
        ('SubsUsers', '1.00:00', '0.08:00'),
        ('Survey', '1.00:00', '0.08:00'),
        ('SysConvUsage', '1.00:00', '0.08:00'),
        ('TimeOffReq', '1.00:00', '0.08:00'),
        ('UserQueueAudit', '1.00:00', '0.08:00'),
        ('UserQueueMapping', '1.00:00', '0.08:00'),
        ('VoiceAnalysis', '1.00:00', '0.08:00'),
        ('WFMAudit', '1.00:00', '0.08:00'),
        ('WFMSchedule', '30.00:00', '30.00:00'),
        ('Learning', '1.00:00', '2.00:00')
) AS source(JobName, MaxSyncSpan, LookBackSpan) ON target.JobName = source.JobName
WHEN NOT MATCHED THEN
INSERT
    (JobName, MaxSyncSpan, LookBackSpan)
VALUES
    (
        source.JobName,
        source.MaxSyncSpan,
        source.LookBackSpan
    );
    
UPDATE JobMinimumDefinition
SET LookBackSpan = 
    CASE
        WHEN TO_CHAR(CURRENT_DATE, 'YY-MM-DD') = '24-12-19'
             AND ((CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 1) AS INTEGER) * 86400) + 
                  (EXTRACT(HOUR FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME)) * 3600) +
                  (EXTRACT(MINUTE FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME)) * 60) +
                  EXTRACT(SECOND FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME))) < 345600
        THEN '4.00:00'
        ELSE '0.08:00'
    END
WHERE JobName = 'Interaction';


UPDATE JobMinimumDefinition
SET LookBackSpan = '90.00:00'
WHERE JobName = 'Evaluation'
  AND (CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 1) AS INTEGER) * 86400) + 
    (EXTRACT(HOUR FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME)) * 3600) +
    (EXTRACT(MINUTE FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME)) * 60) +
    EXTRACT(SECOND FROM CAST(SPLIT_PART(TO_VARCHAR(LookBackSpan), '.', 2) AS TIME)) < 7776000;
