DROP VIEW IF EXISTS VWOFFEREDFORECAST;
create or replace view VWOFFEREDFORECAST
AS
SELECT DISTINCT
ofd.KEY<PERSON>,
ofd.BUSINESSUNITID,
bu.NAME AS "BUSINESSUNITNAME",
ofd.SCHED<PERSON><PERSON><PERSON>,
scd.<PERSON><PERSON><PERSON><PERSON><PERSON>ON AS "SCH<PERSON><PERSON><PERSON><PERSON><PERSON>",
ofd.PLANNINGGROUP AS "PLANNINGGROUPID",
pgd.NAME AS "PLANNINGGROUPNAME",
ofd.SHORTTERMFORECASTID,
ofd.STARTDATE,
ofd.STARTDATELTC,
ofd.WEEKDATE,
ofd.WEEK,
ofd.AVG<PERSON>NDLEPERINTERVAL,
ofd.OFFEREDPERINTERVAL,
ofd.CANUSEFORSCHEDULING,
ofd.UPDATED
FROM
OFFEREDFORECASTDATA ofd
    LEFT JOIN VWBUDETAILS bu ON bu.id = ofd.BUSINESSUNITID
    LEFT JOIN SCHEDULEDETAILS scd ON scd.SCHEDULEID = ofd.SCHEDULEID
    LEFT JOIN PLANNINGGROUPDETAILS pgd ON pgd.id = ofd.PLANNINGGROUP;