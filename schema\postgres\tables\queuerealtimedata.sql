CREATE TABLE IF NOT EXISTS queuerealtimedata (
    keyid varchar(100) NOT NULL,
    queueid varchar(50),
    media varchar(50),
    statscount numeric(20, 2),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT queuerealtimedata_key PRIMARY KEY (keyid)
) TABLESPACE pg_default;

ALTER TABLE queuerealtimedata
ADD COLUMN IF NOT exists statscount numeric(20, 2);