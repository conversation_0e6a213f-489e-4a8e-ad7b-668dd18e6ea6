-- Learning Assignment Results Table
-- Stores detailed results and completion data for learning module assignments
--
-- Key Relationships:
-- - userid: Links to userdetails.id to identify which user completed the assignment
-- - moduleid: Links to learningmodules.id to identify which learning module was completed
-- - Correlates with learningmoduleassignments table via userid+moduleid for complete assignment lifecycle
--
-- Performance Optimization:
-- - Data retrieved using module-based API iteration (O(modules) vs O(users)) for better performance
-- - User IDs extracted from assignment result response data rather than query parameters
-- - Enables efficient user-module assignment and completion analytics
--
-- Cross-table Analytics:
-- - Join with learningmoduleassignments on userid+moduleid for assignment-to-completion tracking
-- - Supports user assignment summaries and module completion rate analysis
-- - Enables performance metrics and learning analytics across user-module relationships

CREATE TABLE IF NOT EXISTS learningassignmentresults (
    id VARCHAR(50) NOT NULL,
    userid VARCHAR(50), -- User ID from Genesys Cloud - links assignment results to specific users
    moduleid VARCHAR(50), -- Learning Module ID - links results to specific learning modules (standardized lowercase)
    assessmentId VARCHAR(50),
    assessmentFormId varchar(50),
    passPercent numeric(20, 2),
    assessmentPercentageScore numeric(20, 2),
    assessmentCompletionPercentage numeric(20, 2),
    completionPercentage numeric(20, 2),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningassignmentresults_pkey PRIMARY KEY (id)
);

-- Add missing columns if they don't exist (for existing installations)
ALTER TABLE learningassignmentresults
ADD COLUMN IF NOT EXISTS userid VARCHAR(50);

-- Handle moduleId to moduleid standardization for existing installations
-- This uses the generic column rename procedure to safely migrate the column while preserving data
CALL csg_rename_column_safe('learningassignmentresults', 'moduleId', 'moduleid', 'VARCHAR(50)');
