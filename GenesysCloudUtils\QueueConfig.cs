﻿using System;
using System.Data;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QueueDets = GenesysCloudDefQueueDetails;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class QueueConfig
    {

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            DBUtil.Initialize();
        }

        public DataTable GetQueueDataFromGC()
        {
            DataTable Queues = DBUtil.CreateInMemTable("queueDetails");

            int CurrentPage = 1;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            JsonActions.MaxPages = 1;

            while (CurrentPage <= JsonActions.MaxPages)
            {
                Console.Write("*");

                // Use JsonReturnHttpResponseGet for proper rate limit handling
                var response = JsonActions.JsonReturnHttpResponseGet(URI + "/api/v2/routing/queues?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                // Check HTTP status code before processing JSON
                if (response.StatusCode != 200)
                {
                    if (response.StatusCode == 429)
                    {
                        Console.WriteLine($"Rate limit encountered for queues page {CurrentPage}. Response: {response.Content}");
                        throw new Exception($"Rate limiting exceeded retry limit for queues page {CurrentPage}");
                    }
                    else
                    {
                        throw new Exception($"API call failed with status {response.StatusCode}: {response.Content}");
                    }
                }

                string JsonString = response.Content;
                QueueDets.QueueObject QueueData;

                try
                {
                    QueueData = JsonConvert.DeserializeObject<QueueDets.QueueObject>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"JSON deserialization failed for queues page {CurrentPage}: {jsonEx.Message}");
                    Console.WriteLine($"Response content: {JsonString}");
                    throw new Exception($"Failed to parse JSON response for queues page {CurrentPage}", jsonEx);
                }

                JsonActions.MaxPages = QueueData.pageCount;

                foreach (QueueDets.QueueEntity JSON in QueueData.entities)
                {
                    DataRow QueueRow = Queues.NewRow();

                    QueueRow["id"] = JSON.id;
                    QueueRow["name"] = JSON.name;
                    QueueRow["description"] = JSON.description;
                    QueueRow["divisionid"] = JSON.division.id;
                    QueueRow["enabletranscription"] = JSON.enableTranscription;
                    QueueRow["isactive"] = true;

                    QueueRow["callslatargetperc"] = JSON.mediaSettings.call.serviceLevel.percentage;
                    QueueRow["callslatargetduration"] = JSON.mediaSettings.call.serviceLevel.durationMs;

                    QueueRow["callbackslatargetperc"] = JSON.mediaSettings.callback.serviceLevel.percentage;
                    QueueRow["callbackslatargetduration"] = JSON.mediaSettings.callback.serviceLevel.durationMs/1000;

                    QueueRow["chatslatargetperc"] = JSON.mediaSettings.chat.serviceLevel.percentage;
                    QueueRow["chatslatargetduration"] = JSON.mediaSettings.chat.serviceLevel.durationMs/1000;

                    QueueRow["emailslatargetperc"] = JSON.mediaSettings.email.serviceLevel.percentage;
                    QueueRow["emailslatargetduration"] = JSON.mediaSettings.email.serviceLevel.durationMs/1000;

                    QueueRow["messageslatargetperc"] = JSON.mediaSettings.message.serviceLevel.percentage;
                    QueueRow["messageslatargetduration"] = JSON.mediaSettings.message.serviceLevel.durationMs/1000;

                    Queues.Rows.Add(QueueRow);
                }

                CurrentPage++;
            }

            Console.WriteLine("\nTotal Queues:{0} ", Queues.Rows.Count);

            return Queues;
        }




    }




}
