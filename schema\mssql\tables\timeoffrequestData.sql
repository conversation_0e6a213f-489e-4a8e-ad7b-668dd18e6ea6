IF dbo.csg_table_exists('timeoffrequestData') = 0
CREATE TABLE [timeoffrequestData](
    [keyid] [nvarchar](100) NOT NULL,
    [id] [nvarchar](50),
    [userid] [nvarchar](50),
    [isfulldayrequest] [bit],
    [status] [nvarchar](50),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [notes] [nvarchar](400),
    [timeoffduration] [int],
    [submittedbyid] [nvarchar](50),
    [submittedate] [datetime],
    [reviewedbyid] [nvarchar](50),
    [revieweddate] [datetime],
    [modifiedbyid] [nvarchar](50),
    [modifieddate] [datetime],
    [updated] [datetime],
    CONSTRAINT [PK_timeoffrequestData] PRIMARY KEY ([keyid])
);
