CREATE OR REPLACE VIEW vwUserInteractionPresenceDetailedData AS
SELECT
    userid,
    userdetail.name as agentname,
    userdetail.managerid as managerid,
    userdetail.managername,
    userdetail.divisionid as divisionid,
    starttime,
    starttimeltc,
    endtime,
    endtimeltc,
    systempresence,
    orgpresence,
    routingstatus,
    conversationid,
    mediatype,
    timeinstate,
    timeinstate / 86400.00 as timeinstateDay,
    userinteractionpresencedetaileddata.updated as updated
FROM
    userinteractionpresencedetaileddata
    left outer join vwUserDetail userdetail on userdetail.id = userid;

COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.conversationid IS 'Conversation GUID';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.divisionid IS 'Division GUID';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.endtime IS 'End Time';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.endtimeltc IS 'End Time (LTC)';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.managername IS 'Manager Name';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.mediatype IS 'Media Type';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.orgpresence IS 'Organization Presence';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.routingstatus IS 'Routing Status';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.starttime IS 'Start Time';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.starttimeltc IS 'Start Time (LTC)';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.systempresence IS 'System Presence';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.timeinstate IS 'Time in State';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.timeinstateDay IS 'Time in State (Days)';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.updated IS 'Last Updated Time';
COMMENT ON COLUMN vwUserInteractionPresenceDetailedData.userid IS 'User GUID';
COMMENT ON VIEW vwUserInteractionPresenceDetailedData IS 'See UserInteractionPresenceDetailedData: User Interaction Presence Detailed Data View';
