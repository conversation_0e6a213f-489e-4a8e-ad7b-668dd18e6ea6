CREATE
OR REPLACE VIEW vwtimeoffrequestData AS
SELECT
    tr.id,
    tr.userid,
    tr.isfulldayrequest,
    tr.status,
    tr.startdate,
    tr.notes,
    tr.timeoffduration,
    tr.submittedbyid,
    tr.submittedate,
    tr.reviewedbyid,
    tr.revieweddate,
    tr.modifiedbyid,
    tr.modifieddate,
    ud.name AS agentname,
    ud.managerid AS managerid,
    ud.managername
FROM
    timeoffrequestData tr
    LEFT OUTER JOIN vwUserDetail AS ud ON ud.id = tr.userid;